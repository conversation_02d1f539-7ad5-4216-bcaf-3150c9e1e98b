# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

The **lux-oms-system** is a sophisticated Order Management System (OMS) built with Kotlin and Spring Boot 3,
specializing in complex product discounting rules and logistics operations. The system handles customer orders with
advanced discount calculations for Standard Product Units (SPUs), supplier management, and multi-provider logistics
integration.

## Common Development Commands

### Build and Run

```bash
./gradlew build          # Build the project
./gradlew bootRun        # Run the application locally
./gradlew clean          # Clean build artifacts
```

### Testing

```bash
./gradlew test           # Run all tests
./gradlew test --tests "package.Class.testMethod"  # Run specific test
```

### Environment Setup

- Ensure PostgreSQL and Redis are running
- Configuration files: `application-local.yaml` for development
- Java 21 JDK required

## Architecture & Code Style

### Domain-Driven Design Structure

The codebase follows strict DDD principles with these layers:

```
src/main/kotlin/io/github/clive/luxomssystem/
├── facade/           # Interface Layer (REST Controllers, DTOs)
├── application/      # Application Layer (Use Case Orchestration)
├── domain/          # Domain Layer (Entities, Value Objects, Domain Services)
├── infrastructure/  # Infrastructure Layer (Repositories, External Services)
└── common/          # Shared Utilities and Configurations
```

### Kotlin Code Standards

- **Functional Programming**: Prefer functional approaches over imperative loops
- **Null Safety**: Always check for null pointers rigorously
- **Immutability**: Use `val` over `var`, favor immutable data structures
- **Conditional Logic**: Use `when` expressions instead of if-else chains
- **DTOs**: Use data classes for all Data Transfer Objects
- **State Management**: Use sealed classes for representing restricted hierarchies

### Entity Requirements

- All entities must inherit from `AbstractBaseEntity`
- Use `@Entity` annotation for domain entities
- Value objects must use `@Embeddable` annotation
- Repository interfaces must extend `JpaRepository`
- Use JPQL in `@Query` annotations for database queries
- Prevent N+1 problems with `@EntityGraph(attributePaths = { "relatedEntity" })`

### Logging Standards

For any state-changing operations, use KotlinLogging:

```kotlin
companion object {
    private val log = KotlinLogging.logger {}
}
// Usage: log.info { "message" }
```

## Core Business Domains

### Order Management

- **MainOrder**: Primary order container with file processing
- **SubOrder**: Individual order items with product/shipping details
- **SupplierMainOrder**: Supplier-specific order processing

### Product Catalog

- **SPU/SKU**: Product definitions with complex discount rules
- **Discount Logic**: Single SPU and combined SPU quantity-based discounts

### Logistics Integration

- **Waybill**: Multi-provider shipping management
- **Supported Providers**: CaiNiao, FPX, SDH, ShunFeng, YanWen, YunTu, ZM
- **International Shipping**: Global logistics capabilities

### Customer/Supplier Management

- Multi-tenant architecture with business-level isolation (bizId)
- Role-based access control and customer-specific configurations

## Key Technologies

### Core Stack

- **Kotlin 1.9.25** with **Spring Boot 3.3.4**
- **Java 21** runtime
- **PostgreSQL** (primary database) + **Redis** (caching/sessions)
- **Gradle Kotlin DSL** for builds

### Notable Dependencies

- **Spring Data JPA** with Hibernate ORM
- **EasyExcel** for bulk file operations
- **iText PDF** for document generation
- **Google ZXing** for QR code processing
- **Tencent Cloud COS** for file storage
- **OkHttp 4.12.0** for HTTP client operations
- **Micrometer + Prometheus** for monitoring

## API Structure

RESTful APIs following Google's design standards:

- `/api/main-orders` - Order lifecycle management
- `/api/sub-orders` - Sub-order operations
- `/api/suppliers` - Supplier management
- `/api/customers` - Customer operations
- `/api/spu` & `/api/sku` - Product catalog
- `/api/waybills` - Logistics operations
- `/api/auth/*` - Authentication and authorization
- `/api/openapi/*` - External API integrations

## Database Configuration

- **Primary**: PostgreSQL with HikariCP connection pooling
- **Cache**: Redis for distributed caching and session management
- **ORM**: JPA with Hibernate, following repository pattern
- All database access through `JpaRepository` interfaces

## External Integrations

### Logistics Providers

Multiple Chinese and international shipping providers with real-time tracking

### Notification Services

- **Lark (Feishu)** integration for team notifications
- Event-driven notification system

### File Processing

- Excel import/export for bulk operations
- PDF generation for shipping documents
- Image processing and QR code generation
- Cloud storage integration (Tencent Cloud COS)

## Security Practices

- Custom API signature validation
- JWT-based authentication
- Role-based authorization with customer context isolation
- OWASP security best practices adherence
- Never expose sensitive information in logs or commits

## Development Principles

Follow SOLID, DRY, KISS, and YAGNI principles consistently. Focus on:

- Breaking tasks into smallest units
- Step-by-step problem solving
- Declarative over imperative programming
- Rigorous null safety checks
- Comprehensive logging for state changes