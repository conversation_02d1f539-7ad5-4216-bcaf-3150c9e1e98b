# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

The **oms-lux** is the React-based frontend application for the LUX Order Management System. It provides a modern,
responsive web interface for managing orders, customers, suppliers, inventory, and logistics operations. This frontend
complements the Kotlin/Spring Boot backend API and handles complex business workflows including multi-channel logistics,
product catalog management, and financial operations.

## Common Development Commands

### Running the Application

```bash
# Install dependencies (multiple package managers supported)
bun install          # Recommended (fastest) - project has bun.lockb
npm install          # Alternative - project has package-lock.json
pnpm install         # Alternative - project has pnpm-lock.yaml
yarn install         # Alternative - project has yarn.lock

# Start development server
bun run dev          # Runs on http://localhost:5173
npm run dev          # Alternative

# Build for production
bun run build --mode production
npm run build        # Alternative

# Preview production build
bun run preview

# Testing/preview in production mode (unusual but present in scripts)
bun run test         # Actually runs vite --mode production
```

### Code Quality

```bash
# Run ESLint (uses modern flat config)
bun run lint
npm run lint

# TypeScript type checking (happens automatically during build)
tsc --noEmit
```

## Architecture & Code Style

### Directory Structure

```
src/
├── api/              # API service layer (organized by domain)
│   ├── model.ts      # TypeScript interfaces for API responses
│   └── [domain]/     # Domain-specific API calls
├── app/              # Application pages/routes
│   └── dashboard/    # Main app pages (customers, orders, etc.)
├── components/       # Reusable UI components
│   ├── ui/          # Base UI components (shadcn/ui)
│   ├── modals/      # Modal components
│   ├── table/       # Data table components
│   └── layout/      # Layout components
├── hooks/           # Custom React hooks
├── lib/             # Utilities and configuration
├── routers/         # Route configuration
└── state/           # State management (Jotai atoms)
```

### TypeScript Standards

- **Strict Mode**: Always use TypeScript strict mode
- **Interface Naming**: Prefix interfaces with 'I' (e.g., `ICustomer`)
- **Type Safety**: Avoid `any` type; use proper typing for all API responses
- **Import Paths**: Use `@/` alias for src imports (e.g., `@/components/ui/button`)

### React Component Guidelines

When creating new components:

1. **Check existing components first** - especially in `src/components/ui/`
2. Use functional components with TypeScript
3. Follow naming convention: PascalCase for components, camelCase for functions
4. Place component-specific types in the same file
5. Use shadcn/ui components as base building blocks

### Import Order

```typescript
// 1. React and third-party libraries
import React from 'react'
import {useForm} from 'react-hook-form'

// 2. UI components from shadcn/ui
import {Button} from '@/components/ui/button'
import {Input} from '@/components/ui/input'

// 3. Application components
import {CustomerTable} from '@/components/table/customer-table'

// 4. API and utilities
import {getCustomers} from '@/api/customer'
import {formatDate} from '@/lib/utils'

// 5. Types and interfaces
import type {ICustomer} from '@/api/model'
```

## Key Technologies

### UI Framework

- **shadcn/ui**: Component library built on Radix UI
- **Tailwind CSS**: Utility-first styling
- **Lucide React**: Icon library

### State Management

- **Jotai**: Atomic state management for global state
- **React Hook Form**: Form state management
- **Tanstack Query**: Server state management (if used)

### Data Handling

- **Tanstack Table**: Advanced data tables
- **Zod**: Schema validation
- **Axios**: HTTP client with interceptors

### Build Tools

- **Vite**: Fast build tool with HMR
- **TypeScript 5.5.3**: Type safety
- **PostCSS**: CSS processing with Tailwind

## API Integration

### Base Configuration

- API client configured in `src/lib/apiClient.ts` (APIClient class)
- Uses Axios with JWT authentication and automatic error handling
- Base URL from environment variable: `VITE_API_URL`
- 500-second timeout with automatic retry for network errors
- Handles both JSON and binary data responses (Excel, PDF downloads)

### API Structure

All API calls follow domain-driven organization:

- `src/api/customer/` - Customer management and platform accounts
- `src/api/order/` - Main orders, sub-orders, supplier orders, waybills
- `src/api/supplier/` - Supplier management and supplier products
- `src/api/inventory/` - Product catalog (SPU/SKU, combo products, customer products)
- `src/api/tracking/` - Logistics and tracking
- `src/api/user/` - Authentication, roles, permissions, business management
- `src/api/channel/` - Shipping channel management
- `src/api/file-api.ts` - File upload/download operations

### Authentication Flow

- JWT tokens stored in localStorage via Jotai atoms (`tokenAtom`, `userInfoAtom`)
- Automatic token injection via Axios interceptors
- Automatic redirect to `/login` on 401 responses
- Token validation on every request except login/logout endpoints

## Component Patterns

### Data Tables

The project uses a sophisticated `DataTable` component (`src/components/table/base-table.tsx`):

```typescript
// All tables use the centralized DataTable component
// Auto-calculated heights with responsive design
// Built-in pagination, sorting, filtering, and column management
// Supports both fixed headers and standard scrolling
// Advanced search capabilities with URL parameter integration
// Column drag-and-drop reordering with persistence

// Usage pattern:
const { onFetch, columns } = useTableLogic();
return <DataTable columns={columns} onFetch={onFetch} toolbar={Toolbar} />;
```

### Forms

React Hook Form is used throughout with shadcn/ui components:

```typescript
// Import form components from shadcn/ui
import { Form, FormControl, FormField } from '@/components/ui/form';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';

// Zod validation schemas co-located with forms
// Custom form components in src/components/inputs/
```

### Modals

Extensive modal system organized by domain in `src/components/modals/`:

- `customer/` - Customer-related modals (creation, editing, platform accounts)
- `supplier/` - Supplier management modals
- `waybill/` - Shipping and waybill operations
- `package/` - Package management
- `suborder/` - Order splitting and editing
- Use shadcn/ui Dialog component as base
- Modal state managed via URL parameters or parent components

## Environment Variables

Required environment variables:

- `VITE_API_URL` - Backend API base URL (configured in Vite)

## Application Architecture

### Routing Structure

React Router DOM with nested routes (`src/routers/self-route.tsx`):

- `/dashboard` - Main application with `MainLayout`
- `/login` - Authentication page with `RootLayout`
- Default redirect: `/dashboard/order/main-order`

Main sections:

- **Orders**: `main-order`, `sub-order`, `waybill`, `supplier-order`, `supplier-main-order`
- **Customers**: `management`, `platform-accounts`, financial tracking
- **Inventory**: `products`, `combo-products`, `customer-products`, `supplier-products`
- **Settings**: `user`, `role`, `permission`, `channel`, download templates
- **Tracking**: Logistics and delivery tracking

### State Management Architecture

Jotai-based atomic state management (`src/state/`):

- `tokenAtom` & `userInfoAtom` - Authentication state (persisted to localStorage)
- `toggleSidebarAtom` - UI preferences (persisted)
- `refreshTableAtom` - Global table refresh trigger
- `columnOrderAtom` - Table column order persistence

### Custom Hooks

- `useAutoTableHeight()` - Dynamic table height calculation
- `useUrlParam()` - URL parameter management
- `useExportFile()` - File download operations
- `useMobile()` - Responsive design utilities

## Development Best Practices

### Component Development

1. Check existing components in `src/components/ui/` (shadcn/ui library)
2. Follow established modal patterns in `src/components/modals/`
3. Use `DataTable` component for all tabular data
4. Extract complex logic into custom hooks in `src/hooks/`

### API Integration Patterns

1. Domain-specific API files (e.g., `src/api/customer/customer-api.ts`)
2. TypeScript interfaces for all API responses (`*-model.ts` files)
3. Use `APIClient` class for all HTTP requests
4. Handle loading states with `ahooks` `useRequest`

### State Management Guidelines

1. Global state: Jotai atoms (authentication, UI preferences)
2. Server state: `ahooks` `useRequest` with proper caching
3. Form state: React Hook Form with Zod validation
4. Table state: Built into `DataTable` component with URL persistence

## Key Architecture Patterns

### File Processing & Downloads

The application handles extensive file operations:

- Excel imports/exports for bulk data operations
- PDF generation and viewing (using `@react-pdf-viewer`)
- Image uploads and galleries with preview capabilities
- QR code generation and processing
- Template-based document generation

### Multi-Provider Logistics Integration

Complex shipping and tracking system supporting multiple carriers:

- Waybill generation for different shipping providers
- Real-time tracking status updates
- Package consolidation and splitting
- International shipping with customs documentation

### Business Domain Complexity

The system manages sophisticated business logic:

- **Product Catalog**: SPU/SKU with combo products and customer-specific variants
- **Order Processing**: Multi-level orders (main → sub → supplier orders)
- **Discount Engine**: Quantity-based discounts and promotional rules
- **Financial Tracking**: Customer-specific tax configurations and billing
- **Permission System**: Role-based access control with business-level isolation

## Common Development Tasks

### Adding New Features

1. Create page components in `src/app/dashboard/[domain]/`
2. Define API interfaces in `src/api/[domain]/[feature]-model.ts`
3. Implement API functions in `src/api/[domain]/[feature]-api.ts`
4. Add routes in `src/routers/self-route.tsx`
5. Create table columns in `table/[feature]-column.tsx`
6. Add modals in `src/components/modals/[domain]/`

### Working with Complex Tables

The `DataTable` component provides enterprise-grade functionality:

- Server-side pagination, sorting, and filtering
- Column reordering with drag-and-drop
- Advanced search with URL parameter persistence
- Export capabilities for Excel/PDF
- Bulk operations with row selection
- Responsive design with auto-height calculation

### Implementing File Operations

1. Use `src/api/file-api.ts` for uploads/downloads
2. Handle binary responses in API client
3. Implement progress indicators for large files
4. Use appropriate preview components (`PDFViewer`, `ImageGallery`)