# Repository Guidelines

## Project Structure & Module Organization

- Backend: `src/main/kotlin` (DDD: `facade/`, `application/`, `domain/`, `infrastructure/`).
- Backend resources: `src/main/resources` (profiles: `application-*.yaml`, logging: `logback-spring.xml`).
- Tests: `src/test/kotlin` and `src/test/resources`.
- Frontend app: `oms-lux/` (Vite + React + TS; static assets in `oms-lux/public`).
- Build output: `build/`; Gradle config: `build.gradle.kts`, `settings.gradle.kts`.

## Build, Test, and Development Commands

- Backend build: `./gradlew build` — compiles, runs tests, packages JAR.
- Backend run: `./gradlew bootRun` — starts Spring Boot (ensure DB/Redis; set profile via
  `SPRING_PROFILES_ACTIVE=local`).
- Backend test: `./gradlew test` — runs JUnit 5 tests; single test: `./gradlew test --tests "pkg.Class.method"`.
- Frontend dev: `cd oms-lux && npm run dev` — Vite dev server.
- Frontend build: `cd oms-lux && npm run build` — type-check + production build.
- Frontend lint: `cd oms-lux && npm run lint` — ESLint checks.
- Remote helpers (ops only): `./gradlew uploadProd`, `./gradlew launch` — use SSH; verify keys/hosts before use.

## Coding Style & Naming Conventions

- Kotlin: 4-space indent, prefer `val`, functional ops (`map/filter/fold`), exhaustive `when`.
- Packaging: `io.github.clive.luxomssystem...` by layer; controllers in `facade/*Controller.kt`.
- DTOs: data classes in `facade/.../request|response` (`CreateXRequest.kt`, `XResponse.kt`).
- Logging: `KotlinLogging.logger {}`; log all state-changing operations.
- Frontend: TypeScript + React; PascalCase components (`ComponentName.tsx`), hooks `useThing.ts`.

## Testing Guidelines

- Framework: JUnit 5 + Spring Boot test.
- Location/pattern: `src/test/kotlin/**/**/*Test.kt` (e.g., `MyServiceTest.kt`).
- Scope: unit tests for domain/services; slice tests for controllers/repos. Aim to cover core discount/ordering logic.
- Run locally via `./gradlew test`; keep tests isolated and deterministic.

## Commit & Pull Request Guidelines

- Commits: Conventional Commits (`feat:`, `fix:`, `refactor:`, `docs:`, `test:`); imperative mood; scope optional.
- Branches: `feature/<slug>`, `fix/<slug>`, `chore/<slug>`.
- PRs: clear description, linked issues (`Fixes #123`), backend logs of key flows, and UI screenshots for `oms-lux/`.
  Ensure tests pass and lint is clean.

## Security & Configuration Tips

- Do not commit secrets; prefer env vars and profile YAML. Keep `oms-lux/.env.local` local.
- Rotate any checked-in credentials and move to a secure store.
- Profiles: set `SPRING_PROFILES_ACTIVE` (`local`, `test`, `prod`) and configure DB/Redis in `application-*.yaml`.
