

### toast 通知

```
    useEffect(() => {
        toast.custom((t) => (
            <ToastNotificationWithUndo id={t} />
        ), {
            position: 'top-right',
            duration: 500000,
        })
        // toast('事件创建成功',{
        //     position: 'top-right',
        //     richColors: true,
        //     important: true,
        //     description: '事件创建成功',
        //     duration: 500000,
        //     icon: '🚀',
        //     action: (
        //         <div className="absolute right-6 top-1/2 -translate-y-1/2 bg-green-500 rounded-xl p-2 cursor-pointer">
        //             <ArrowRight className="w-6 h-4 text-white" />
        //         </div>
        //     ),
        //     // cancel: {
        //     //     label: '取消',
        //     //     onClick: () => {
        //     //         toast.dismiss()
        //     //     }
        //     // }
        // })
    }, [])

```



select 组件都要增加scollar，防止数据溢出看不到数据