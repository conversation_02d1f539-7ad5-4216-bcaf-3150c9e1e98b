# lux-oms-system

The `lux-oms-system` is a sophisticated Order Management System (OMS) focused on efficiently handling complex product discounting rules.

## Overview

The lux-oms-system is a comprehensive Order Management System that efficiently handles customer orders. A core feature of this system is its sophisticated discount calculation logic for Standard Product Units (SPUs). The system applies discounts based on the following rules:

1.  **Single SPU Discount:** If the quantity of a single SPU in an order meets its specific discount criteria, the discount is applied directly to that SPU.
2.  **Combined SPU Discount:** If individual SPUs do not meet their discount criteria, the system can combine the quantities of multiple SPUs. It then evaluates available discounts based on this combined quantity and selects the discount that provides the most advantageous price for the customer.

The system is engineered to manage these potentially complex discount scenarios, ensuring accurate pricing and maximizing customer satisfaction.

## Key Features

*   **Advanced Discount Management:**
    *   Handles complex discount scenarios based on individual or combined SPU quantities.
    *   Calculates the most optimal discount for the customer when multiple discount rules apply.
*   **Order Processing:**
    *   Manages the lifecycle of customer orders.
*   **Domain-Driven Design:**
    *   Built following DDD principles for a clear and maintainable codebase.

## Tech Stack

*   Kotlin 1.8+
*   Spring Boot 3.x
*   Java 21
*   PostgreSQL
*   Redis
*   Gradle Kotlin DSL

## Project Structure

The project follows a Domain-Driven Design (DDD) approach, organized into the following main layers:

*   `src/main/kotlin/`
    *   `facade/`: Interface layer (e.g., REST controllers with `@RestController`, request/response DTOs).
    *   `application/`: Application layer (e.g., orchestrates business logic via application services).
    *   `domain/`: Domain layer (e.g., business core including entities with `@Entity`, value objects with `@Embeddable`, domain services with `@Service`, and domain events).
    *   `infrastructure/`: Infrastructure layer (e.g., data persistence with `JpaRepository` interfaces, external service integrations, framework configurations).

## Getting Started / Development
To get the project running locally, follow these steps:

### Prerequisites
*   Java 21 (JDK)
*   Gradle
*   Access to a PostgreSQL database
*   Access to a Redis instance

### Build
To build the project, run:
```shell
./gradlew build
```

### Run
To run the application:
```shell
./gradlew bootRun
```
Ensure your PostgreSQL and Redis instances are configured and running. Configuration can be found in `src/main/resources/application-local.yaml`.

### Test
To run all tests:
```shell
./gradlew test
```
To run a single test:
```shell
./gradlew test --tests "package.Class.testMethod"
```

### Clean
To clean the build directory:
```shell
./gradlew clean
```

## Code Style and Architecture
The project adheres to specific code style guidelines and architectural principles to ensure maintainability and consistency.

### Code Style
*   **Language**: Kotlin with Spring Boot 3, Java 21.
*   **Primary Principle**: Functional programming is preferred over imperative approaches; avoid loops where functional alternatives (e.g., map, filter, fold) are suitable.
*   **Logging**: Utilize `io.github.oshai.kotlinlogging (KotlinLogging.logger {})` for all operations that change state.
*   **Null Safety**: Emphasize rigorous null pointer checks.
*   **Immutability**: Favor immutable data structures and `val` over `var` where possible.
*   Use `when` expressions for conditional logic instead of verbose if-else chains.
*   Employ data classes for Data Transfer Objects (DTOs).
*   Utilize sealed classes for representing restricted hierarchies or states.

### Architecture
*   **Pattern**: Follows Domain-Driven Design (DDD) with distinct layers:
    *   **Facade**: Handles incoming requests (e.g., REST Controllers) and outgoing responses.
    *   **Application**: Orchestrates application-specific use cases.
    *   **Domain**: Contains the core business logic, entities, value objects, and domain services.
    *   **Infrastructure**: Manages technical concerns like database interactions, external API calls, etc.
*   **Core Principles**: Adherence to SOLID, DRY, KISS, YAGNI.
*   **Entities**: All domain entities should inherit from `AbstractBaseEntity`.
*   **Repositories**: Repository interfaces must extend `JpaRepository`; use JPQL in `@Query` annotations for database queries.
*   **Controllers**: Design RESTful APIs based on Google's API design standards.
*   **Security**: Implement OWASP security best practices.

## API Documentation
Further details about the API endpoints, request/response formats, and authentication methods will be available [here](link-to-api-docs-if-available) or can be generated via [tool-if-applicable, e.g., Swagger UI].

## Contributing
We welcome contributions to `lux-oms-system`! If you'd like to contribute, please follow these general guidelines:
1.  Fork the repository.
2.  Create a new branch for your feature or bug fix: `git checkout -b feature/your-feature-name` or `bugfix/your-bug-fix`.
3.  Make your changes, adhering to the Code Style and Architecture guidelines.
4.  Ensure your changes include appropriate tests.
5.  Commit your changes with a clear and descriptive commit message.
6.  Push your branch to your fork: `git push origin feature/your-feature-name`.
7.  Open a pull request against the main repository.

Please ensure your code is well-tested and follows the project's coding standards.

## License
The license for this project is yet to be determined. Please check back later.
```
