[versions]
exposed = "0.53.0"
ktor = "2.3.11"

[libraries]
exposed-core = { module = "org.jetbrains.exposed:exposed-core", version.ref = "exposed" }
exposed-jdbc = { module = "org.jetbrains.exposed:exposed-jdbc", version.ref = "exposed" }
ktor-server-core = { module = "io.ktor:ktor-server-core", version.ref = "ktor" }
ktor-server-netty = { module = "io.ktor:ktor-server-netty", version.ref = "ktor" }
ktor-client-core = { module = "io.ktor:ktor-client-core", version.ref = "ktor" }
ktor-client-cio = { module = "io.ktor:ktor-client-cio", version.ref = "ktor" }
ktor-client-okhttp = { module = "io.ktor:ktor-client-okhttp", version.ref = "ktor" }
ktor-client-content-negotiation = { module = "io.ktor:ktor-client-content-negotiation", version.ref = "ktor" }
ktor-client-serde-jackson = { module = "io.ktor:ktor-serialization-jackson", version.ref = "ktor" }
ktor-client-logging = { module = "io.ktor:ktor-client-logging", version.ref = "ktor" }

jdbc-pg = { module = "org.postgresql:postgresql", version = "42.7.2" }

google-gcp = { module = "com.google.cloud:google-cloud-aiplatform", version = "3.52.0" }

kt-log = { module = "io.github.oshai:kotlin-logging-jvm", version = "5.1.4" }

hikari = { module = "com.zaxxer:HikariCP", version = "4.0.3" }


logback = { module = "ch.qos.logback:logback-classic", version = "1.4.12" }

jedis = { module = "redis.clients:jedis", version = "5.2.0" }

qcloud-cos = { module = "com.qcloud:cos_api", version = "5.6.233" }

hibernate-63 = { module = "io.hypersistence:hypersistence-utils-hibernate-63", version = "3.9.0" }

easyexcel = { module = "com.alibaba:easyexcel", version = "4.0.3" }

jsoup = { module = "org.jsoup:jsoup", version = "1.16.1" }

[bundles]
exposed = ["exposed-core", "exposed-jdbc"]
ktor-cleint = ["ktor-client-core", "ktor-client-okhttp", "ktor-client-content-negotiation", "ktor-client-serde-jackson", "ktor-client-logging"]