# 渠道预测系统开发指导文档

## 概述

渠道预测系统负责与各个物流渠道的API进行集成，实现运单创建、取消、打印标签等功能。本文档提供了开发新渠道集成的完整指导。

## 系统架构

### 核心接口设计

所有渠道实现都必须实现 `OrderPredictionRemoteService` 接口：

```kotlin
interface OrderPredictionRemoteService {
    val ossClient: CosInnerRemoteService
    val okHttpClient: OkHttpClient
    val subOrderRepository: SubOrderRepository
    
    fun createPredication(waybill: Waybill)
    fun deletePredication(waybill: Waybill): Boolean
    fun getPrintUrl(waybill: Waybill): String
    fun getStoreUrl(waybill: Waybill): String
    fun loadSubOrder(waybill: Waybill): List<SubOrder>
    fun uploadWaybill(waybill: Waybill, pdfUrl: String): String
}
```

### 依赖注入模式

每个渠道服务都应该使用 `@Service` 注解，并通过构造函数注入必要的依赖：

```kotlin
@Service
class XxxPredictionRemoteService(
    private val eventBus: ApplicationEventPublisher,
    override val ossClient: CosInnerRemoteService,
    override val okHttpClient: OkHttpClient,
    override val subOrderRepository: SubOrderRepository,
    private val waybillRepository: WaybillRepository,
    @Value("\${xxx.account}") private val account: String,
    @Value("\${xxx.api-secret}") private val apiSecret: String,
    @Value("\${xxx.api.url}") private val apiUrl: String,
) : OrderPredictionRemoteService
```

## 核心功能实现

### 1. 创建预报 (createPredication)

这是最核心的功能，负责向渠道API发送运单创建请求。

#### 标准实现流程：

1. **加载子订单数据**

```kotlin
val orderList = loadSubOrder(waybill)
if (orderList.isEmpty()) {
    log.error { "渠道名 创建预报失败 | 订单号: ${waybill.orderNo} | 原因: 未找到子订单" }
    waybill.failed("No suborder found")
    return
}
```

2. **构建请求参数**

```kotlin
val waybillRequests = orderList.flatMap { WaybillRequest.of(it, waybill) }
val requestBody = buildRequestBody(waybill, orderList)
```

3. **发送API请求**

```kotlin
try {
    val response = sendRequest(requestBody)
    handleResponse(response, waybill)
} catch (e: Exception) {
    log.error(e) { "渠道名 创建预报失败 | 订单号: ${waybill.orderNo} | 错误信息: ${e.message}" }
    waybill.failed(e.message ?: "Unknown error")
    waybillRepository.saveAndFlush(waybill)
}
```

4. **处理响应结果**

```kotlin
private fun handleResponse(responseBody: String, waybill: Waybill) {
    val response = JSON.parseObject<ResponseType>(responseBody)
    
    if (response.success) {
        waybill.status = WayBillStatus.PENDING
        waybill.waybillNo = response.data?.waybillNumber
        waybill.shipping.waybillLabelUrl = getPrintUrl(waybill)
        waybill.status = WayBillStatus.COMPLETED
        eventBus.publishEvent(WaybillCompletedEvent(waybill.id, waybill.subOrderId, waybill.orderNos))
        waybill.clearErrorMsg()
    } else {
        waybill.failed(response.message ?: "Unknown error")
    }
    waybillRepository.saveAndFlush(waybill)
}
```

### 2. 取消预报 (deletePredication)

```kotlin
override fun deletePredication(waybill: Waybill): Boolean {
    if (waybill.waybillNo.isNullOrBlank()) {
        return true
    }
    
    try {
        val requestBody = buildCancelRequestBody(waybill)
        val response = sendCancelRequest(requestBody)
        val cancelResponse = JSON.parseObject<CancelResponseType>(response)
        return cancelResponse.success
    } catch (e: Exception) {
        log.error(e) { "渠道名 取消预报失败 | 订单号: ${waybill.orderNo}" }
        return false
    }
}
```

### 3. 获取打印标签 (getPrintUrl)

```kotlin
override fun getPrintUrl(waybill: Waybill): String {
    try {
        val requestBody = buildPrintRequestBody(waybill)
        val response = sendPrintRequest(requestBody)
        val printResponse = JSON.parseObject<PrintResponseType>(response)
        
        if (printResponse.success) {
            val base64String = printResponse.data?.base64String
            if (base64String != null) {
                val fileBytes = Base64.getDecoder().decode(base64String)
                val inputStream = ByteArrayInputStream(fileBytes)
                return ossClient.uploadFile(
                    waybill.bizId,
                    "waybill/pdf/${waybill.bizId}/${waybill.id}/${UUID.randomUUID()}.pdf",
                    inputStream
                )
            }
        }
    } catch (e: Exception) {
        log.error(e) { "渠道名 获取打印URL失败 | 订单号: ${waybill.orderNo}" }
    }
    return ""
}
```

## 请求对象设计

### WaybillRequest 使用

系统提供了统一的 `WaybillRequest` 对象来处理订单数据：

```kotlin
val waybillRequests = orderList.flatMap { WaybillRequest.of(it, waybill) }
```

### 构建API请求体

每个渠道需要根据其API规范构建请求体：

```kotlin
private fun buildRequestBody(waybill: Waybill, orderList: List<SubOrder>): String {
    val waybillRequests = orderList.flatMap { WaybillRequest.of(it, waybill) }
    
    return JSON.toJSONString(mapOf(
        "orderNumber" to waybill.orderNo,
        "receiverInfo" to mapOf(
            "name" to waybill.recipient.receiverName,
            "phone" to waybill.recipient.phone,
            "country" to waybill.recipient.country,
            "address" to waybill.recipient.address(),
            // ... 其他字段
        ),
        "productList" to waybillRequests.map { order ->
            mapOf(
                "name" to order.name,
                "quantity" to order.qty,
                "price" to order.price(waybillRequests.sumOf { it.qty }, order.country),
                // ... 其他字段
            )
        }
    ))
}
```

## 响应对象设计

### 标准响应结构

为每个渠道创建对应的响应数据类：

```kotlin
@JsonIgnoreProperties(ignoreUnknown = true)
data class XxxCreateWaybillResponse(
    @JsonProperty("success")
    var success: Boolean,
    @JsonProperty("data")
    var data: Data?,
    @JsonProperty("message")
    var message: String?
) {
    data class Data(
        @JsonProperty("waybillNumber")
        var waybillNumber: String?,
        // ... 其他字段
    )
}
```

## 配置管理

### application.yml 配置

```yaml
xxx:
  account: ${XXX_ACCOUNT:}
  api-secret: ${XXX_API_SECRET:}
  api:
    url: ${XXX_API_URL:}
    waybill: ${XXX_WAYBILL_URL:}
    print: ${XXX_PRINT_URL:}
```

### 多账号支持

对于需要支持多个账号的渠道（如燕文），可以这样配置：

```kotlin
private fun getAccountAndSecret(channel: String): Pair<String, String> =
    when (channel) {
        "XXX_A" -> Pair(accountA, apiSecretA)
        "XXX_B" -> Pair(accountB, apiSecretB)
        else -> throw IllegalArgumentException("Invalid channel: $channel")
    }
```

## 错误处理和日志

### 日志记录标准

```kotlin
companion object {
    private val log = KotlinLogging.logger {}
}

// 请求日志
log.info { "渠道名 创建运单请求 | 订单号: ${waybill.orderNo} | 请求参数: $requestBody" }

// 响应日志
log.info { "渠道名 创建运单响应 | 订单号: ${waybill.orderNo} | 响应参数: $response" }

// 成功日志
log.info { "渠道名 创建运单成功 | 订单号: ${waybill.orderNo} | 运单号: ${waybill.waybillNo}" }

// 错误日志
log.error { "渠道名 创建运单失败 | 订单号: ${waybill.orderNo} | 错误信息: ${response.message}" }
log.error(e) { "渠道名 创建运单异常 | 订单号: ${waybill.orderNo} | 错误信息: ${e.message}" }
```

### 异常处理

```kotlin
try {
    // API调用逻辑
} catch (e: Exception) {
    log.error(e) { "渠道名 操作失败 | 订单号: ${waybill.orderNo}" }
    waybill.failed(e.message ?: "Unknown error")
    waybillRepository.saveAndFlush(waybill)
}
```

## 文件上传和OSS集成

### PDF文件上传

```kotlin
// Base64解码并上传
val fileBytes = Base64.getDecoder().decode(base64String)
val inputStream = ByteArrayInputStream(fileBytes)
val fileName = "${UUID.randomUUID()}.pdf"
return ossClient.uploadFile(
    waybill.bizId,
    "waybill/pdf/${waybill.bizId}/${waybill.id}/$fileName",
    inputStream
)

// 直接从URL下载并上传
okHttpClient.newCall(
    Request.Builder().url(pdfUrl).build()
).execute().use { pdfResponse ->
    pdfResponse.body?.byteStream()?.use { inputStream ->
        return ossClient.uploadFile(waybill.bizId, getStoreUrl(waybill), inputStream)
    } ?: throw Exception("Empty PDF response body")
}
```

## 事件发布和状态管理

### 状态流转

```kotlin
// 初始状态
waybill.status = WayBillStatus.PENDING

// 成功完成
waybill.status = WayBillStatus.COMPLETED
eventBus.publishEvent(WaybillCompletedEvent(waybill.id, waybill.subOrderId, waybill.orderNos))
waybill.clearErrorMsg()

// 失败处理
waybill.failed("错误信息")
```

### 事件发布

成功创建运单后必须发布 `WaybillCompletedEvent` 事件：

```kotlin
eventBus.publishEvent(WaybillCompletedEvent(waybill.id, waybill.subOrderId, waybill.orderNos))
```

## 开发注意事项

### 1. 数据验证

- 检查子订单是否存在
- 验证必要字段是否完整
- 处理空值和异常情况

### 2. API调用

- 使用统一的 OkHttpClient
- 正确设置请求头
- 处理超时和网络异常

### 3. 安全考虑

- API密钥通过配置文件管理
- 敏感信息不要记录到日志
- 使用HTTPS进行API调用

### 4. 性能优化

- 合理使用连接池
- 避免重复的API调用
- 适当的缓存机制

### 5. 测试支持

- 提供测试方法用于调试
- 支持沙箱环境配置
- 完整的错误场景覆盖

## 目录结构

```
prediction/
├── OrderPredictionRemoteService.kt          # 核心接口
├── channel/
│   ├── XxxPredictionRemoteService.kt        # 渠道实现
│   ├── request/
│   │   └── WaybillRequest.kt                # 通用请求对象
│   └── xxx/                                 # 渠道特定对象
│       ├── XxxCreateWaybillResponse.kt      # 创建响应
│       ├── XxxPrintUrlResponse.kt           # 打印响应
│       └── XxxTrackingResponse.kt           # 轨迹响应
```

## 开发检查清单

- [ ] 实现 OrderPredictionRemoteService 接口
- [ ] 添加 @Service 注解和依赖注入
- [ ] 实现 createPredication 方法
- [ ] 实现 deletePredication 方法
- [ ] 实现 getPrintUrl 方法
- [ ] 创建请求和响应数据类
- [ ] 添加配置项到 application.yml
- [ ] 完善日志记录
- [ ] 添加异常处理
- [ ] 测试所有功能
- [ ] 文档更新

## 完整示例代码

### 基础渠道实现模板

```kotlin
@Service
class TemplatePredictionRemoteService(
    private val eventBus: ApplicationEventPublisher,
    override val ossClient: CosInnerRemoteService,
    override val okHttpClient: OkHttpClient,
    override val subOrderRepository: SubOrderRepository,
    private val waybillRepository: WaybillRepository,
    @Value("\${template.account}") private val account: String,
    @Value("\${template.api-secret}") private val apiSecret: String,
    @Value("\${template.api.url}") private val apiUrl: String,
) : OrderPredictionRemoteService {

    override fun createPredication(waybill: Waybill) {
        val orderList = loadSubOrder(waybill)
        if (orderList.isEmpty()) {
            log.error { "Template 创建预报失败 | 订单号: ${waybill.orderNo} | 原因: 未找到子订单" }
            waybill.failed("No suborder found")
            return
        }

        try {
            val requestBody = buildRequestBody(waybill, orderList)
            log.info { "Template 创建运单请求 | 订单号: ${waybill.orderNo} | 请求参数: $requestBody" }

            val response = sendRequest(requestBody, "create")
            log.info { "Template 创建运单响应 | 订单号: ${waybill.orderNo} | 响应参数: $response" }

            handleCreateResponse(response, waybill)
        } catch (e: Exception) {
            log.error(e) { "Template 创建预报失败 | 订单号: ${waybill.orderNo} | 错误信息: ${e.message}" }
            waybill.failed(e.message ?: "Unknown error")
            waybillRepository.saveAndFlush(waybill)
        }
    }

    override fun deletePredication(waybill: Waybill): Boolean {
        if (waybill.waybillNo.isNullOrBlank()) {
            return true
        }

        try {
            val requestBody = JSON.toJSONString(mapOf("waybillNumber" to waybill.waybillNo))
            val response = sendRequest(requestBody, "cancel")
            val cancelResponse = JSON.parseObject<TemplateResponse>(response)

            return cancelResponse.success.also { success ->
                if (success) {
                    log.info { "Template 取消预报成功 | 订单号: ${waybill.orderNo}" }
                } else {
                    log.error { "Template 取消预报失败 | 订单号: ${waybill.orderNo} | 原因: ${cancelResponse.message}" }
                }
            }
        } catch (e: Exception) {
            log.error(e) { "Template 取消预报异常 | 订单号: ${waybill.orderNo}" }
            return false
        }
    }

    override fun getPrintUrl(waybill: Waybill): String {
        try {
            val requestBody = JSON.toJSONString(mapOf("waybillNumber" to waybill.waybillNo))
            val response = sendRequest(requestBody, "print")
            val printResponse = JSON.parseObject<TemplatePrintResponse>(response)

            if (printResponse.success && printResponse.data?.pdfBase64 != null) {
                val fileBytes = Base64.getDecoder().decode(printResponse.data.pdfBase64)
                val inputStream = ByteArrayInputStream(fileBytes)
                return ossClient.uploadFile(
                    waybill.bizId,
                    "waybill/pdf/${waybill.bizId}/${waybill.id}/${UUID.randomUUID()}.pdf",
                    inputStream
                )
            }
        } catch (e: Exception) {
            log.error(e) { "Template 获取打印URL失败 | 订单号: ${waybill.orderNo}" }
        }
        return ""
    }

    private fun buildRequestBody(waybill: Waybill, orderList: List<SubOrder>): String {
        val waybillRequests = orderList.flatMap { WaybillRequest.of(it, waybill) }

        return JSON.toJSONString(mapOf(
            "orderNumber" to waybill.orderNo,
            "shippingMethod" to waybill.shipping.shipMethod,
            "receiver" to mapOf(
                "name" to waybill.recipient.receiverName,
                "phone" to waybill.recipient.phone,
                "country" to waybill.recipient.country,
                "city" to waybill.recipient.city,
                "address" to waybill.recipient.address(),
                "postcode" to waybill.recipient.postcode,
                "state" to waybill.recipient.state
            ),
            "package" to mapOf(
                "weight" to waybillRequests.sumOf { it.weight },
                "totalPrice" to (waybill.totalPrice ?: waybillRequests.sumOf {
                    it.price(waybillRequests.sumOf { req -> req.qty }, it.country) * it.qty.toBigDecimal()
                }),
                "currency" to "USD",
                "items" to waybillRequests.map { item ->
                    mapOf(
                        "name" to item.name,
                        "cnName" to item.cnName,
                        "quantity" to item.qty,
                        "unitPrice" to item.price(waybillRequests.sumOf { it.qty }, item.country),
                        "weight" to item.weight,
                        "material" to item.material,
                        "hsCode" to item.hsCode
                    )
                }
            )
        ))
    }

    private fun sendRequest(requestBody: String, action: String): String {
        val timestamp = System.currentTimeMillis()
        val signature = generateSignature(requestBody, timestamp)

        val request = Request.Builder()
            .url("$apiUrl/$action")
            .header("Content-Type", "application/json")
            .header("Authorization", "Bearer $account")
            .header("Timestamp", timestamp.toString())
            .header("Signature", signature)
            .post(requestBody.toRequestBody())
            .build()

        return okHttpClient.newCall(request).execute().use { response ->
            response.body?.string() ?: throw Exception("Empty response body")
        }
    }

    private fun handleCreateResponse(responseBody: String, waybill: Waybill) {
        val response = JSON.parseObject<TemplateCreateResponse>(responseBody)

        if (response.success) {
            waybill.status = WayBillStatus.PENDING
            waybill.waybillNo = response.data?.waybillNumber
            log.info { "Template 创建运单成功 | 订单号: ${waybill.orderNo} | 运单号: ${waybill.waybillNo}" }

            waybill.shipping.waybillLabelUrl = getPrintUrl(waybill)
            waybill.status = WayBillStatus.COMPLETED
            eventBus.publishEvent(WaybillCompletedEvent(waybill.id, waybill.subOrderId, waybill.orderNos))
            waybill.clearErrorMsg()
        } else {
            log.error { "Template 创建运单失败 | 订单号: ${waybill.orderNo} | 错误信息: ${response.message}" }
            waybill.failed(response.message ?: "Unknown error")
        }
        waybillRepository.saveAndFlush(waybill)
    }

    private fun generateSignature(requestBody: String, timestamp: Long): String {
        val content = "$account$requestBody$timestamp$apiSecret"
        return MessageDigest.getInstance("MD5")
            .digest(content.toByteArray())
            .joinToString("") { "%02x".format(it) }
    }

    companion object {
        private val log = KotlinLogging.logger {}
    }
}
```

### 响应数据类示例

```kotlin
@JsonIgnoreProperties(ignoreUnknown = true)
data class TemplateCreateResponse(
    @JsonProperty("success")
    var success: Boolean,
    @JsonProperty("data")
    var data: Data?,
    @JsonProperty("message")
    var message: String?
) {
    data class Data(
        @JsonProperty("waybillNumber")
        var waybillNumber: String?,
        @JsonProperty("trackingNumber")
        var trackingNumber: String?
    )
}

@JsonIgnoreProperties(ignoreUnknown = true)
data class TemplatePrintResponse(
    @JsonProperty("success")
    var success: Boolean,
    @JsonProperty("data")
    var data: Data?,
    @JsonProperty("message")
    var message: String?
) {
    data class Data(
        @JsonProperty("pdfBase64")
        var pdfBase64: String?
    )
}

@JsonIgnoreProperties(ignoreUnknown = true)
data class TemplateResponse(
    @JsonProperty("success")
    var success: Boolean,
    @JsonProperty("message")
    var message: String?
)
```

## 特殊场景处理

### 1. 欧盟IOSS处理

```kotlin
// 检查是否需要IOSS
val iossCode = if (EUCountryEnum.needIoss(waybill.recipient.country)) {
    waybill.iossNumber
} else {
    null
}
```

### 2. 重量处理

```kotlin
// 确保重量不为零
val weight = if (totalWeight == BigDecimal.ZERO) BigDecimal("0.01") else totalWeight
```

### 3. 价格计算

```kotlin
// 使用运单中已计算好的总价，避免重复计算
val totalPrice = waybill.totalPrice ?: waybillRequests.sumOf { order ->
    order.price(waybillRequests.sumOf { it.qty }, order.country) * order.qty.toBigDecimal()
}
```

### 4. 多渠道账号管理

```kotlin
private fun getAccountAndSecret(channel: String): Pair<String, String> =
    when (channel) {
        "CHANNEL_A" -> Pair(accountA, apiSecretA)
        "CHANNEL_B" -> Pair(accountB, apiSecretB)
        "CHANNEL_C" -> Pair(accountC, apiSecretC)
        else -> throw IllegalArgumentException("Unsupported channel: $channel")
    }
```

## 测试和调试

### 1. 测试方法

```kotlin
// 添加测试方法用于调试
fun testCreateWaybill(orderNo: String, channel: String): String {
    // 测试逻辑
}

// 使用 @EventListener 进行初始化测试
@EventListener(ApplicationReadyEvent::class)
fun init(event: ApplicationReadyEvent) {
    if (isTestEnvironment()) {
        val result = testCreateWaybill("TEST001", "TEST_CHANNEL")
        log.info { "测试结果: $result" }
    }
}
```

### 2. 环境配置

```yaml
# 开发环境
template:
  account: ${TEMPLATE_DEV_ACCOUNT:dev_account}
  api-secret: ${TEMPLATE_DEV_SECRET:dev_secret}
  api:
    url: ${TEMPLATE_DEV_URL:https://dev-api.template.com}

# 生产环境
template:
  account: ${TEMPLATE_PROD_ACCOUNT:}
  api-secret: ${TEMPLATE_PROD_SECRET:}
  api:
    url: ${TEMPLATE_PROD_URL:https://api.template.com}
```

## 性能优化建议

### 1. 连接池配置

```kotlin
// 在配置类中设置 OkHttpClient
@Bean
fun okHttpClient(): OkHttpClient {
    return OkHttpClient.Builder()
        .connectTimeout(30, TimeUnit.SECONDS)
        .readTimeout(60, TimeUnit.SECONDS)
        .writeTimeout(60, TimeUnit.SECONDS)
        .connectionPool(ConnectionPool(10, 5, TimeUnit.MINUTES))
        .build()
}
```

### 2. 异步处理

```kotlin
// 对于非关键路径的操作，可以考虑异步处理
@Async
fun asyncUploadLabel(waybill: Waybill, pdfUrl: String) {
    try {
        val ossUrl = uploadWaybill(waybill, pdfUrl)
        waybill.shipping.waybillLabelUrl = ossUrl
        waybillRepository.save(waybill)
    } catch (e: Exception) {
        log.error(e) { "异步上传标签失败 | 订单号: ${waybill.orderNo}" }
    }
}
```

### 3. 缓存机制

```kotlin
// 对于频繁调用的配置信息，可以使用缓存
@Cacheable("channel-config")
fun getChannelConfig(channel: String): ChannelConfig {
    // 获取渠道配置逻辑
}
```

## 监控和告警

### 1. 指标收集

```kotlin
// 使用 Micrometer 收集指标
@Autowired
private lateinit var meterRegistry: MeterRegistry

private fun recordApiCall(channel: String, operation: String, success: Boolean) {
    Timer.Sample.start(meterRegistry)
        .stop(Timer.builder("channel.api.call")
            .tag("channel", channel)
            .tag("operation", operation)
            .tag("success", success.toString())
            .register(meterRegistry))
}
```

### 2. 健康检查

```kotlin
@Component
class ChannelHealthIndicator : HealthIndicator {
    override fun health(): Health {
        return try {
            // 检查渠道API可用性
            val isHealthy = checkChannelHealth()
            if (isHealthy) {
                Health.up().withDetail("channel", "available").build()
            } else {
                Health.down().withDetail("channel", "unavailable").build()
            }
        } catch (e: Exception) {
            Health.down(e).build()
        }
    }
}
```

## 轨迹追踪集成

### 轨迹API实现

虽然不是 `OrderPredictionRemoteService` 接口的一部分，但大多数渠道都需要提供轨迹查询功能：

```kotlin
/**
 * 获取轨迹信息
 * @param waybillNo 运单号
 * @param channel 渠道标识
 * @return 轨迹响应对象，失败时返回null
 */
fun tracking(waybillNo: String, channel: String): TemplateTrackingResponse? {
    return try {
        log.info { "开始获取轨迹信息 | 运单号: $waybillNo | 渠道: $channel" }

        val (account, apiSecret) = getAccountAndSecret(channel)
        val request = Request.Builder()
            .header("Content-Type", "application/json")
            .header("Authorization", "Bearer $account")
            .url("$trackingApiUrl?waybillNo=$waybillNo")
            .build()

        val response = okHttpClient.newCall(request).execute().use { response ->
            response.body?.string() ?: throw Exception("Empty response body")
        }

        log.info { "轨迹API响应 | 运单号: $waybillNo | 渠道: $channel | 响应: $response" }

        val trackingResponse = JSON.parseObject<TemplateTrackingResponse>(response)

        if (trackingResponse.success) {
            log.info { "轨迹信息获取成功 | 运单号: $waybillNo | 渠道: $channel" }
            trackingResponse
        } else {
            log.warn { "轨迹信息获取失败 | 运单号: $waybillNo | 渠道: $channel | 错误: ${trackingResponse.message}" }
            null
        }
    } catch (e: Exception) {
        log.error(e) { "轨迹信息获取异常 | 运单号: $waybillNo | 渠道: $channel" }
        null
    }
}
```

### 轨迹响应对象

```kotlin
@JsonIgnoreProperties(ignoreUnknown = true)
data class TemplateTrackingResponse(
    @JsonProperty("success")
    var success: Boolean,
    @JsonProperty("data")
    var data: List<TrackingEvent>?,
    @JsonProperty("message")
    var message: String?
) {
    data class TrackingEvent(
        @JsonProperty("time")
        var time: String?,
        @JsonProperty("location")
        var location: String?,
        @JsonProperty("description")
        var description: String?,
        @JsonProperty("status")
        var status: String?
    )
}
```

## 常见问题和解决方案

### 1. 字符编码问题

```kotlin
// 确保请求体使用正确的字符编码
val requestBody = requestJson.toRequestBody("application/json; charset=utf-8".toMediaType())

// 处理中文字符
val encodedName = URLEncoder.encode(waybill.recipient.receiverName, "UTF-8")
```

### 2. 时区处理

```kotlin
// 统一使用UTC时间
val timestamp = Instant.now().epochSecond

// 解析API返回的时间字符串
val dateTime = LocalDateTime.parse(timeString, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))
val zonedDateTime = dateTime.atZone(ZoneId.of("UTC"))
```

### 3. 重试机制

```kotlin
private fun sendRequestWithRetry(requestBody: String, maxRetries: Int = 3): String {
    var lastException: Exception? = null

    repeat(maxRetries) { attempt ->
        try {
            return sendRequest(requestBody)
        } catch (e: Exception) {
            lastException = e
            log.warn { "API调用失败，第${attempt + 1}次重试 | 错误: ${e.message}" }
            if (attempt < maxRetries - 1) {
                Thread.sleep((attempt + 1) * 1000L) // 递增延迟
            }
        }
    }

    throw lastException ?: Exception("重试失败")
}
```

### 4. 大文件处理

```kotlin
// 处理大型PDF文件
private fun handleLargePdf(base64String: String, waybill: Waybill): String {
    return if (base64String.length > MAX_BASE64_SIZE) {
        // 分块处理或压缩
        val compressedBytes = compressPdf(Base64.getDecoder().decode(base64String))
        val inputStream = ByteArrayInputStream(compressedBytes)
        ossClient.uploadFile(waybill.bizId, getStoreUrl(waybill), inputStream)
    } else {
        // 正常处理
        val fileBytes = Base64.getDecoder().decode(base64String)
        val inputStream = ByteArrayInputStream(fileBytes)
        ossClient.uploadFile(waybill.bizId, getStoreUrl(waybill), inputStream)
    }
}
```

### 5. API限流处理

```kotlin
// 使用令牌桶算法限制API调用频率
@Component
class ApiRateLimiter {
    private val rateLimiter = RateLimiter.create(10.0) // 每秒10次

    fun acquire(): Boolean {
        return rateLimiter.tryAcquire(1, TimeUnit.SECONDS)
    }
}

// 在服务中使用
@Autowired
private lateinit var rateLimiter: ApiRateLimiter

private fun sendRequest(requestBody: String): String {
    if (!rateLimiter.acquire()) {
        throw Exception("API调用频率超限")
    }
    // 发送请求逻辑
}
```

## 部署和运维

### 1. 配置管理

```yaml
# 使用环境变量管理敏感配置
template:
  account: ${TEMPLATE_ACCOUNT:}
  api-secret: ${TEMPLATE_API_SECRET:}
  api:
    url: ${TEMPLATE_API_URL:https://api.template.com}
    timeout: ${TEMPLATE_API_TIMEOUT:30000}
    retry-count: ${TEMPLATE_RETRY_COUNT:3}

# 功能开关
feature:
  template-channel-enabled: ${TEMPLATE_CHANNEL_ENABLED:true}
  template-tracking-enabled: ${TEMPLATE_TRACKING_ENABLED:true}
```

### 2. 日志配置

```xml
<!-- logback-spring.xml -->
<configuration>
    <appender name="CHANNEL_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>logs/channel.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>logs/channel.%d{yyyy-MM-dd}.%i.gz</fileNamePattern>
            <maxFileSize>100MB</maxFileSize>
            <maxHistory>30</maxHistory>
        </rollingPolicy>
        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n</pattern>
        </encoder>
    </appender>

    <logger name="io.github.clive.luxomssystem.infrastructure.channel.prediction" level="INFO" additivity="false">
        <appender-ref ref="CHANNEL_FILE"/>
    </logger>
</configuration>
```

### 3. 监控指标

```kotlin
// 自定义指标
@Component
class ChannelMetrics {
    private val successCounter = Counter.builder("channel.prediction.success")
        .description("成功创建预报次数")
        .register(Metrics.globalRegistry)

    private val failureCounter = Counter.builder("channel.prediction.failure")
        .description("创建预报失败次数")
        .register(Metrics.globalRegistry)

    private val responseTimer = Timer.builder("channel.prediction.response.time")
        .description("API响应时间")
        .register(Metrics.globalRegistry)

    fun recordSuccess(channel: String) {
        successCounter.increment(Tags.of("channel", channel))
    }

    fun recordFailure(channel: String, error: String) {
        failureCounter.increment(Tags.of("channel", channel, "error", error))
    }

    fun recordResponseTime(channel: String, duration: Duration) {
        responseTimer.record(duration, Tags.of("channel", channel))
    }
}
```

## 版本升级和兼容性

### 1. API版本管理

```kotlin
// 支持多个API版本
enum class ApiVersion(val version: String) {
    V1("v1"),
    V2("v2")
}

private fun getApiUrl(version: ApiVersion): String {
    return "$baseApiUrl/${version.version}"
}

// 向后兼容处理
private fun handleResponse(responseBody: String, apiVersion: ApiVersion): TemplateResponse {
    return when (apiVersion) {
        ApiVersion.V1 -> JSON.parseObject<TemplateResponseV1>(responseBody).toV2()
        ApiVersion.V2 -> JSON.parseObject<TemplateResponseV2>(responseBody)
    }
}
```

### 2. 配置迁移

```kotlin
// 配置兼容性处理
@ConfigurationProperties(prefix = "template")
data class TemplateConfig(
    var account: String = "",
    var apiSecret: String = "",
    var apiUrl: String = "",
    // 新增配置项，提供默认值
    var apiVersion: String = "v1",
    var enableRetry: Boolean = true,
    var maxRetryCount: Int = 3
) {
    // 兼容旧配置名称
    @Deprecated("使用 apiSecret 替代")
    var secretKey: String = ""
        set(value) {
            if (apiSecret.isEmpty()) {
                apiSecret = value
            }
            field = value
        }
}
```

## 总结

本文档提供了渠道预测系统开发的完整指导，包括：

1. **架构设计** - 统一的接口规范和依赖注入模式
2. **核心功能** - 创建预报、取消预报、获取打印标签的标准实现
3. **数据处理** - 请求对象构建和响应对象解析
4. **错误处理** - 异常捕获、日志记录和状态管理
5. **文件管理** - OSS集成和PDF处理
6. **事件机制** - 状态变更事件发布
7. **性能优化** - 连接池、缓存、异步处理
8. **监控运维** - 指标收集、健康检查、日志配置
9. **扩展功能** - 轨迹追踪、多版本支持

遵循这些规范和最佳实践，可以确保新渠道集成的质量和可维护性，同时保持系统的一致性和稳定性。
