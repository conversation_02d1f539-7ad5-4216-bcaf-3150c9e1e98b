servers:
  - name: 1
    host: **************
    port: 22
    username: root
    password: <PERSON><PERSON><PERSON>0719.
  - name: 2
    host: ***************
    port: 22
    username: root
    password: Hispread0719.
  - name: 3
    host: ************
    port: 22
    username: root
    password: Hispread0719.

deployments:
  - name: common-deploy
    pipeline:
      commands:
        - type: remote
          command: "kill -9 $(jps |grep cognition-spring-0.0.1-SNAPSHOT.jar | awk '{print $1}')"
        - type: scp
          local_path: "build/libs/cognition-spring-0.0.1-SNAPSHOT.jar"
          remote_path: "/home/<USER>"
        - type: remote
          command: "nohup java -Xms6G -Xmx12G -jar --enable-preview /home/<USER>/home/<USER>"
        - type: remote
          command: "jps"

deploy_plans:
  - server: 3
    deployment: common-deploy