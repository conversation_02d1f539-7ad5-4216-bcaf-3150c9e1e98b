# Task Completion Checklist

When completing any development task, ensure the following:

## Code Quality

- [ ] Follow Kotlin code standards (functional programming, null safety, immutability)
- [ ] Use proper annotations for entities (`@Entity`, `@Embeddable`, etc.)
- [ ] Add logging for state-changing operations using KotlinLogging
- [ ] Follow DDD layer separation principles

## Testing and Validation

- [ ] Run `./gradlew test` to ensure all tests pass
- [ ] Run `./gradlew build` to verify project builds successfully
- [ ] Test the functionality locally with `./gradlew bootRun`

## Database Considerations

- [ ] Ensure database migrations are created if schema changes are made
- [ ] Verify entities inherit from `AbstractBaseEntity`
- [ ] Use appropriate column annotations and constraints

## Security and Best Practices

- [ ] Never expose sensitive information in logs or commits
- [ ] Follow OWASP security best practices
- [ ] Use proper validation annotations
- [ ] Ensure proper null safety checks