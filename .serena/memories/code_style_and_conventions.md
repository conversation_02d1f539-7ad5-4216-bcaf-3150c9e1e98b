# Code Style and Conventions

## Kotlin Code Standards

- **Functional Programming**: Prefer functional approaches over imperative loops
- **Null Safety**: Always check for null pointers rigorously
- **Immutability**: Use `val` over `var`, favor immutable data structures
- **Conditional Logic**: Use `when` expressions instead of if-else chains
- **DTOs**: Use data classes for all Data Transfer Objects
- **State Management**: Use sealed classes for representing restricted hierarchies

## Entity Requirements

- All entities must inherit from `AbstractBaseEntity`
- Use `@Entity` annotation for domain entities
- Value objects must use `@Embeddable` annotation
- Repository interfaces must extend `JpaRepository`
- Use JPQL in `@Query` annotations for database queries
- Prevent N+1 problems with `@EntityGraph(attributePaths = { "relatedEntity" })`

## Logging Standards

For any state-changing operations, use KotlinLogging:

```kotlin
companion object {
    private val log = KotlinLogging.logger {}
}
// Usage: log.info { "message" }
```

## Design Principles

- Follow SOLID, DRY, KISS, and YAGNI principles consistently
- Domain-Driven Design structure with strict layer separation
- Breaking tasks into smallest units
- Step-by-step problem solving
- Declarative over imperative programming
- Comprehensive logging for state changes