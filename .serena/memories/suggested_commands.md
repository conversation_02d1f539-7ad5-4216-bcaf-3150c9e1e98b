# Suggested Commands

## Build and Run Commands

```bash
./gradlew build          # Build the project
./gradlew bootRun        # Run the application locally
./gradlew clean          # Clean build artifacts
```

## Testing Commands

```bash
./gradlew test           # Run all tests
./gradlew test --tests "package.Class.testMethod"  # Run specific test
```

## Deployment Commands

```bash
./gradlew uploadProd     # Upload JAR to production server
./gradlew launch         # Launch application on remote server
```

## System Commands (Darwin)

```bash
ls                       # List directory contents
find . -name "*.kt"      # Find Kotlin files
grep -r "pattern" src/   # Search for pattern in source
cd directory/            # Change directory
pwd                      # Show current directory
```

## Environment Setup

- Ensure PostgreSQL and Redis are running locally
- Configuration files: `application-local.yaml` for development
- Java 21 JDK required