# Project Overview: lux-oms-system

## Purpose

The lux-oms-system is a sophisticated Order Management System (OMS) built with Kotlin and Spring Boot 3, specializing in
complex product discounting rules and logistics operations. The system handles customer orders with advanced discount
calculations for Standard Product Units (SPUs), supplier management, and multi-provider logistics integration.

## Key Business Features

- Advanced discount management for single SPU and combined SPU quantity-based discounts
- Order lifecycle management (MainOrder, SubOrder, SupplierMainOrder)
- Product catalog management (SPU/SKU with complex discount rules)
- Multi-provider logistics integration (CaiNiao, FPX, SDH, ShunFeng, YanWen, YunTu, ZM)
- Customer/Supplier management with multi-tenant architecture
- File processing and bulk operations with Excel import/export
- Notification integration (Lark/Feishu)

## Tech Stack

- **Language**: Kotlin 1.9.25
- **Framework**: Spring Boot 3.3.4
- **Runtime**: Java 21
- **Database**: PostgreSQL (primary) + Redis (caching/sessions)
- **Build Tool**: Gradle Kotlin DSL
- **ORM**: JPA with Hibernate
- **Notable Libraries**: EasyExcel, iText PDF, Google ZXing, Tencent Cloud COS, OkHttp, Micrometer + Prometheus

## Architecture

Follows strict Domain-Driven Design (DDD) with clear layer separation:

- `facade/` - Interface Layer (REST Controllers, DTOs)
- `application/` - Application Layer (Use Case Orchestration)
- `domain/` - Domain Layer (Entities, Value Objects, Domain Services)
- `infrastructure/` - Infrastructure Layer (Repositories, External Services)