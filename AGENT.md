# Agent Guidelines for lux-oms-system

## Build and Test Commands
- Build: `./gradlew build`
- Run: `./gradlew bootRun`
- Test all: `./gradlew test`
- Run single test: `./gradlew test --tests "package.Class.testMethod"`
- Clean: `./gradlew clean`

## Code Style Guidelines
- **Language**: Kotlin with Spring Boot 3, Java 21
- **Architecture**: Follow DDD with layers (Controller, Service, Model, Infrastructure)
- **Principles**: SOLID, DRY, KISS, YAGNI, OWASP security best practices
- **Programming Style**: Functional programming preferred over imperative, avoid loops
- **Logging**: Use io.github.oshai.kotlinlogging (KotlinLogging.logger {}) for all operations that change state
- **Null Safety**: Always check for null pointers
- **Entities**: All entities inherit from AbstractBaseEntity, use @Entity annotation
- **Repositories**: Must be interfaces extending JpaRepository, use JPQL in @Query annotations
- **Controllers**: RESTful API design based on Google's standards
- **Project Structure**: Follow the domain-driven directory structure in .cursorrules