---
description: 
globs: 
alwaysApply: false
---
# OMS项目开发指南

## 项目架构说明

这是一个基于React + TypeScript + Vite的订单管理系统(OMS)，采用以下技术栈：

### 核心技术栈
- **前端框架**: React 18 + TypeScript
- **构建工具**: Vite
- **状态管理**: Jotai
- **路由**: React Router DOM
- **样式**: Tailwind CSS + shadcn/ui组件库
- **表格**: @tanstack/react-table
- **HTTP客户端**: Axios
- **表单**: React Hook Form + Zod
- **数据请求**: ahooks

### 项目结构

```
src/
├── api/                    # API接口定义
│   ├── channel/           # 渠道相关
│   ├── customer/          # 客户相关  
│   ├── inventory/         # 库存相关
│   ├── order/             # 订单相关
│   ├── supplier/          # 供应商相关
│   └── user/              # 用户相关
├── app/                   # 页面组件
│   └── dashboard/         # 仪表板页面
├── components/            # 公共组件
│   ├── ui/               # shadcn/ui基础组件
│   ├── table/            # 表格相关组件
│   ├── modals/           # 模态框组件
│   ├── form/             # 表单组件
│   └── ...               # 其他组件分类
├── hooks/                 # 自定义hooks
├── lib/                   # 工具库和配置
└── state/                 # 状态管理
```

## 开发规范

### 1. API接口规范

所有API接口都在 [src/api](mdc:src/api) 目录下按功能模块组织：

```typescript
// API函数命名规范
export async function pageXxx(params: FetchParams): Promise<FetchResponse<XxxResponse>>
export async function createXxx(request: CreateXxxRequest): Promise<void>  
export async function updateXxx(id: string, request: UpdateXxxRequest): Promise<void>
export async function deleteXxx(id: string): Promise<void>
export async function findXxx(id: string): Promise<XxxResponse>
```

统一使用 [src/lib/apiClient.ts](mdc:src/lib/apiClient.ts) 进行HTTP请求。

### 2. 组件开发规范

#### 2.1 页面组件
页面组件位于 [src/app](mdc:src/app) 目录下，按路由结构组织：
- 使用默认导出
- 文件名统一为 `page.tsx`
- 组件名使用PascalCase + Page后缀

#### 2.2 公共组件
公共组件位于 [src/components](mdc:src/components) 目录下，按功能分类：

**UI组件**: 使用shadcn/ui组件库，位于 [src/components/ui](mdc:src/components/ui)
**表格组件**: 基于@tanstack/react-table，位于 [src/components/table](mdc:src/components/table)
**模态框组件**: 位于 [src/components/modals](mdc:src/components/modals)，按功能模块分类

#### 2.3 导入路径规范
```typescript
// UI组件
import { Button } from "@/components/ui/button"
import { DataTable } from "@/components/table/base-table"

// API函数
import { pageWaybill } from "@/api/order/waybill/waybill-api"

// 工具函数
import { cn } from "@/lib/utils"

// Hooks
import useSearchParamsManager from "@/hooks/use-url-param"
```

### 3. 表格开发规范

使用统一的表格组件 [src/components/table/base-table.tsx](mdc:src/components/table/base-table.tsx)：

```typescript
<DataTable
    columns={XxxColumns}                    // 列定义
    onFetch={param => pageXxx(param)}      // 数据获取函数
    isFixedHeader={false}                  // 是否固定表头
    containerHeight="620px"                // 容器高度
    isNeedSelect={true}                    // 是否需要选择功能
    toolbar={(table, tableId) => (         // 工具栏
        <DataTableToolbar table={table} tableId={tableId}>
            // 工具栏内容
        </DataTableToolbar>
    )}
    dependencies={[]}                      // 依赖项
/>
```

表格列定义文件命名为 `xxx-column.tsx`，位于对应的 `table/` 子目录。

### 4. 模态框开发规范

模态框组件位于 [src/components/modals](mdc:src/components/modals) 目录，按业务模块分类：

```typescript
// 模态框使用pushModal函数调用
import { pushModal } from "@/components/modals"

// 调用示例
pushModal('ChannelSelectModels', {
    onConfirm: (company: string, route: string) => {
        // 确认回调
    }
})
```

### 5. 状态管理规范

使用Jotai进行状态管理，状态文件位于 [src/state](mdc:src/state) 目录：

```typescript
import { atom } from 'jotai'
import { useAtom, useAtomValue, useSetAtom } from 'jotai'

// 原子状态定义
export const refreshTableAtom = atom(0)

// 组件中使用
const refresh = useSetAtom(refreshTableAtom)
refresh(prev => prev + 1)
```

### 6. 样式规范

统一使用Tailwind CSS，配置文件为 [tailwind.config.js](mdc:tailwind.config.js)：
- 优先使用Tailwind工具类
- 复杂样式使用shadcn/ui组件
- 自定义样式使用CSS变量

### 7. TypeScript规范

- 严格使用TypeScript类型
- API请求/响应类型统一定义在对应的 `xxx-model.ts` 文件中
- 组件Props使用interface定义
- 优先使用类型推导，避免显式any类型

### 8. 表单处理规范

使用React Hook Form + Zod进行表单处理：

```typescript
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import { z } from "zod"

const formSchema = z.object({
    // 表单字段定义
})

const form = useForm({
    resolver: zodResolver(formSchema),
    defaultValues: {
        // 默认值
    }
})
```

### 9. 错误处理规范

- 使用sonner进行消息提示
- API错误统一在apiClient中处理
- 组件级错误使用try-catch捕获

```typescript
import { toast } from "sonner"

// 成功提示
toast.success('操作成功')

// 错误提示  
toast.error('操作失败: ' + error.message)

// Promise提示
toast.promise(apiCall(), {
    loading: '处理中...',
    success: '操作成功',
    error: (error) => '操作失败: ' + error.message
})
```

## 开发工作流

1. **新功能开发**：
   - 先创建API接口和类型定义
   - 实现页面组件和业务逻辑
   - 添加表格/表单/模态框等交互组件
   - 进行测试和调试

2. **代码提交**：
   - 遵循约定式提交规范
   - 确保TypeScript编译通过
   - 确保ESLint检查通过

3. **组件复用**：
   - 优先使用已有的公共组件
   - 新组件考虑通用性，适当抽象
   - 遵循单一职责原则
