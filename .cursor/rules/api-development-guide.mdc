---
description: 
globs: 
alwaysApply: false
---
# API开发指南

## API结构规范

### 1. 目录结构

API接口按业务模块组织在 [src/api](mdc:src/api) 目录下：

```
src/api/
├── channel/           # 渠道管理
├── customer/          # 客户管理
├── inventory/         # 库存管理
│   ├── customer/      # 客户库存
│   ├── sku/          # SKU管理
│   └── spu/          # SPU管理
├── order/            # 订单管理
│   ├── image-task/   # 图片任务
│   ├── mainorder/    # 主订单
│   ├── sub-order/    # 子订单
│   ├── supplier-main-order/  # 供应商主订单
│   ├── templete/     # 模板
│   └── waybill/      # 运单
├── supplier/         # 供应商管理
├── user/            # 用户管理
└── common-api.ts    # 公共API
```

### 2. 文件命名规范

每个模块包含两个文件：
- `xxx-api.ts` - API函数定义
- `xxx-model.ts` - TypeScript类型定义

例如：
```
waybill/
├── waybill-api.ts     # 运单API函数
└── waybill-model.ts   # 运单相关类型
```

### 3. API函数命名规范

基础CRUD操作：

```typescript
// 分页查询 - page前缀
export async function pageXxx(params: FetchParams): Promise<FetchResponse<XxxResponse>>

// 创建 - create前缀
export async function createXxx(request: CreateXxxRequest): Promise<void>

// 更新 - update前缀  
export async function updateXxx(id: string, request: UpdateXxxRequest): Promise<void>

// 删除 - delete前缀
export async function deleteXxx(id: string): Promise<void>

// 查询单个 - find前缀
export async function findXxx(id: string): Promise<XxxResponse>

// 列表查询 - list前缀
export async function listXxx(): Promise<XxxResponse[]>

// 业务操作 - 动词开头
export async function retryWaybillBatch(ids: string[]): Promise<void>
export async function changeWaybillChannel(ids: string[], company: string, route: string): Promise<void>
```

### 4. 统一的API客户端

所有API调用都通过 [src/lib/apiClient.ts](mdc:src/lib/apiClient.ts) 进行：

```typescript
import apiClient from "@/lib/apiClient"

// GET请求
export async function pageWaybill(params: FetchParams): Promise<FetchResponse<WaybillPageResponse>> {
    return await apiClient.get({
        url: '/api/waybills/page',
        params: {
            ...params.pagination,
            ...params.searchParams
        },
    })
}

// POST请求
export async function createWaybill(request: CreateWaybillRequest): Promise<void> {
    return await apiClient.post({
        url: '/api/waybills',
        data: request
    })
}

// PUT请求
export async function updateWaybill(id: string, request: UpdateWaybillRequest): Promise<void> {
    return await apiClient.put({
        url: `/api/waybills/${id}`,
        data: request
    })
}

// DELETE请求
export async function deleteWaybill(id: string): Promise<void> {
    return await apiClient.delete({
        url: `/api/waybills/${id}`
    })
}
```

### 5. 通用类型定义

基础分页类型：

```typescript
// 请求参数类型
export interface FetchParams {
    pagination: {
        pageIndex: number
        pageSize: number
    }
    searchParams: Record<string, any>
    filters?: Record<string, any>
}

// 响应结果类型
export interface FetchResponse<T> {
    data: T[]
    rowCount: number
    pageCount: number
}
```

### 6. 业务类型定义示例

参考 [src/api/order/waybill/waybill-model.ts](mdc:src/api/order/waybill/waybill-model.ts)：

```typescript
// 响应数据类型
export interface WaybillPageResponse {
    id: string
    orderNo: string
    waybillNo: string
    status: WaybillStatus
    createdAt: string
    updatedAt: string
}

// 枚举类型
export enum WaybillStatus {
    CREATED = 'CREATED',
    PENDING = 'PENDING', 
    COMPLETED = 'COMPLETED',
    FAILED = 'FAILED',
    CANCELLED = 'CANCELLED'
}

// 请求数据类型
export interface CreateWaybillRequest {
    orderNo: string
    channel: string
    shipMethod: string
}

export interface UpdateWaybillRequest {
    channel?: string
    shipMethod?: string
    notes?: string
}

// 特殊业务请求类型
export interface WaybillTaxSetRequest {
    waybillIds: string[]
    taxRate: number
    currency: string
}
```

### 7. 错误处理规范

API错误统一在apiClient中处理，业务层面处理特定错误：

```typescript
export async function retryWaybillBatch(ids: string[]): Promise<void> {
    try {
        return await apiClient.post({
            url: '/api/waybills/retry/batch',
            data: { waybillIds: ids }
        })
    } catch (error) {
        // 可以在这里处理特定的业务错误
        throw error
    }
}
```

### 8. 批量操作规范

对于批量操作，统一使用数组传递ID：

```typescript
// 批量删除
export async function deleteWaybillBatch(ids: string[]): Promise<void> {
    return await apiClient.delete({
        url: '/api/waybills/batch',
        data: { waybillIds: ids }
    })
}

// 批量更新状态
export async function updateWaybillStatusBatch(ids: string[], status: WaybillStatus): Promise<void> {
    return await apiClient.put({
        url: '/api/waybills/status/batch',
        data: { 
            waybillIds: ids,
            status 
        }
    })
}
```

### 9. 文件上传/下载规范

```typescript
// 文件上传
export async function uploadWaybillFile(file: File): Promise<string> {
    const formData = new FormData()
    formData.append('file', file)
    
    return await apiClient.post({
        url: '/api/waybills/upload',
        data: formData,
        headers: {
            'Content-Type': 'multipart/form-data'
        }
    })
}

// 文件下载
export async function downloadWaybillExcel(ids: string[]): Promise<Blob> {
    return await apiClient.post({
        url: '/api/waybills/export/excel',
        data: { waybillIds: ids },
        responseType: 'blob'
    })
}

// PDF代理下载
export async function exportPdf(url: string): Promise<Blob> {
    return await apiClient.get({
        url: '/api/file/proxy-pdf?url=' + url,
        responseType: 'blob'
    })
}
```

### 10. 查询参数处理

对于复杂查询条件，支持多种参数格式：

```typescript
export async function pageWaybill(params: FetchParams): Promise<FetchResponse<WaybillPageResponse>> {
    return await apiClient.get({
        url: '/api/waybills/page',
        params: {
            // 分页参数
            ...params.pagination,
            // 搜索参数
            ...params.searchParams,
            // 过滤参数会合并到searchParams中
        },
    })
}

// 支持的查询参数示例
const searchParams = {
    orderNo: 'ORD123',           // 精确匹配
    status: 'PENDING',           // 状态过滤
    createdAtFrom: '2024-01-01', // 日期范围
    createdAtTo: '2024-12-31',
    orderNos: 'ORD1\nORD2\nORD3', // 多个订单号（换行分隔）
    waybillNos: 'WB1\nWB2',      // 多个运单号
}
```

## API使用最佳实践

### 1. 在组件中使用API

```typescript
import { useRequest } from 'ahooks'
import { pageWaybill } from '@/api/order/waybill/waybill-api'

// 在表格组件中使用
<DataTable
    columns={WaybillColumns}
    onFetch={param => pageWaybill(param)}
    // 其他props...
/>

// 在普通组件中使用
const { data, loading, error } = useRequest(
    () => pageWaybill(searchParams),
    {
        refreshDeps: [searchParams],
        onError: (error) => {
            toast.error('加载失败: ' + error.message)
        }
    }
)
```

### 2. 错误处理和用户反馈

```typescript
import { toast } from 'sonner'

const handleAction = async () => {
    toast.promise(
        retryWaybillBatch(selectedIds),
        {
            loading: '重试中...',
            success: () => {
                refresh(prev => prev + 1)
                return '重试成功'
            },
            error: (error) => '重试失败: ' + error.message
        }
    )
}
```

### 3. 类型安全

确保所有API调用都有正确的TypeScript类型：

```typescript
// 正确：明确的类型定义
const response: FetchResponse<WaybillPageResponse> = await pageWaybill(params)

// 错误：避免使用any
const response: any = await pageWaybill(params)
```

### 4. API组织方式

复杂模块可以使用对象组织API：

```typescript
// 主订单API对象
export const mainOrderApi = {
    async findAll(searchParams: FetchParams): Promise<FetchResponse<MainOrder>> {
        return await apiClient.get({
            url: '/api/main-orders',
            params: {
                ...searchParams.pagination,
                ...searchParams.searchParams,
            },
        })
    },

    async create(data: CreateMainOrderRequest): Promise<void> {
        return await apiClient.post({
            url: '/api/main-orders',
            data
        })
    },

    // 更多方法...
}
```
