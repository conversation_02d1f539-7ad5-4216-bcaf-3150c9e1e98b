---
description: 
globs: 
alwaysApply: false
---
# React组件开发指南

## 组件创建流程

### 1. 检查现有组件
在创建新组件前，必须检查是否已有类似组件：

**UI基础组件**: [src/components/ui](mdc:src/components/ui) - shadcn/ui组件库
**业务组件**: [src/components](mdc:src/components) - 按功能分类的业务组件
**页面组件**: [src/app](mdc:src/app) - 页面级组件

### 2. 组件命名规范

```typescript
// 文件命名：kebab-case
data-table-toolbar.tsx
waybill-column.tsx
supplier-order-modal.tsx

// 组件命名：PascalCase
export function DataTableToolbar() {}
export function WaybillColumn() {}
export function SupplierOrderModal() {}

// 页面组件：固定命名
export default function WaybillPage() {}
```

### 3. 组件结构模板

```typescript
// 导入顺序：React -> 第三方库 -> 本地组件 -> 类型
import React from 'react'
import { Button } from "@/components/ui/button"
import { DataTable } from "@/components/table/base-table"
import type { ComponentProps } from './types'

// Props接口定义
interface ComponentNameProps {
    title: string
    onAction?: (data: any) => void
    className?: string
    children?: React.ReactNode
}

// 组件实现
export function ComponentName({ 
    title, 
    onAction, 
    className,
    children 
}: ComponentNameProps) {
    // Hooks调用
    const [state, setState] = useState()
    
    // 事件处理函数
    const handleAction = () => {
        onAction?.(data)
    }
    
    // 渲染
    return (
        <div className={cn("base-styles", className)}>
            {/* 组件内容 */}
        </div>
    )
}
```

### 4. 表格组件规范

创建表格相关组件时，参考 [src/app/dashboard/order/waybill/page.tsx](mdc:src/app/dashboard/order/waybill/page.tsx)：

```typescript
// 列定义文件：xxx-column.tsx
export const WaybillColumns: ColumnDef<WaybillPageResponse>[] = [
    {
        accessorKey: "orderNo",
        header: "订单号",
        cell: ({ row }) => {
            return <span>{row.getValue("orderNo")}</span>
        },
    },
    // 更多列定义...
]

// 页面组件中使用
<DataTable
    columns={WaybillColumns}
    onFetch={param => pageWaybill(param)}
    isFixedHeader={false}
    containerHeight="620px"
    isNeedSelect={true}
    toolbar={(table, tableId) => (
        <DataTableToolbar table={table} tableId={tableId}>
            <ToolbarLeft>
                {/* 左侧工具栏 */}
            </ToolbarLeft>
            <ToolbarRight>
                {/* 右侧工具栏 */}
            </ToolbarRight>
        </DataTableToolbar>
    )}
/>
```

### 5. 模态框组件规范

模态框组件位于 [src/components/modals](mdc:src/components/modals)，按业务模块分类：

```typescript
// 模态框组件结构
export function XxxModal({ 
    data, 
    onConfirm, 
    onClose 
}: XxxModalProps) {
    return (
        <Dialog open={true} onOpenChange={onClose}>
            <DialogContent>
                <DialogHeader>
                    <DialogTitle>标题</DialogTitle>
                </DialogHeader>
                
                {/* 模态框内容 */}
                
                <DialogFooter>
                    <Button variant="outline" onClick={onClose}>
                        取消
                    </Button>
                    <Button onClick={handleConfirm}>
                        确认
                    </Button>
                </DialogFooter>
            </DialogContent>
        </Dialog>
    )
}
```

### 6. 表单组件规范

使用React Hook Form + Zod进行表单验证：

```typescript
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import { z } from "zod"
import {
    Form,
    FormControl,
    FormField,
    FormItem,
    FormLabel,
    FormMessage,
} from "@/components/ui/form"

const formSchema = z.object({
    name: z.string().min(1, "名称不能为空"),
    email: z.string().email("邮箱格式不正确"),
})

export function XxxForm({ onSubmit }: XxxFormProps) {
    const form = useForm<z.infer<typeof formSchema>>({
        resolver: zodResolver(formSchema),
        defaultValues: {
            name: "",
            email: "",
        },
    })

    return (
        <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
                <FormField
                    control={form.control}
                    name="name"
                    render={({ field }) => (
                        <FormItem>
                            <FormLabel>名称</FormLabel>
                            <FormControl>
                                <Input {...field} />
                            </FormControl>
                            <FormMessage />
                        </FormItem>
                    )}
                />
                <Button type="submit">提交</Button>
            </form>
        </Form>
    )
}
```

### 7. Hooks使用规范

项目中常用的Hooks：

```typescript
// URL参数管理
import useSearchParamsManager from "@/hooks/use-url-param"
const { addParam, getParam } = useSearchParamsManager()

// 状态管理
import { useAtom, useAtomValue, useSetAtom } from 'jotai'
import { refreshTableAtom } from "@/state/common"
const refresh = useSetAtom(refreshTableAtom)

// 数据请求
import { useRequest } from 'ahooks'
const { data, loading, error } = useRequest(() => apiCall())
```

### 8. 样式规范

使用Tailwind CSS + shadcn/ui组件：

```typescript
import { cn } from "@/lib/utils"

// 条件样式
className={cn(
    "base-styles",
    condition && "conditional-styles",
    className
)}

// 响应式设计
className="flex flex-col md:flex-row gap-4"

// 组件变体
<Button variant="outline" size="sm">按钮</Button>
```

### 9. 错误处理和加载状态

```typescript
import { toast } from "sonner"

// 加载状态
{loading && <Skeleton className="h-4 w-full" />}

// 错误处理
const handleAction = async () => {
    try {
        await apiCall()
        toast.success("操作成功")
    } catch (error) {
        toast.error("操作失败: " + error.message)
    }
}

// Promise状态提示
toast.promise(apiCall(), {
    loading: "处理中...",
    success: "操作成功",
    error: (error) => "操作失败: " + error.message
})
```

### 10. 组件性能优化

```typescript
// 使用React.memo优化性能
export const OptimizedComponent = React.memo(function Component(props) {
    return <div>{/* 组件内容 */}</div>
})

// 使用useCallback缓存函数
const handleClick = useCallback((id: string) => {
    // 处理逻辑
}, [dependency])

// 使用useMemo缓存计算结果
const computedValue = useMemo(() => {
    return expensiveCalculation(data)
}, [data])
```

## 最佳实践

1. **组件职责单一**: 每个组件只负责一个功能
2. **Props接口清晰**: 使用TypeScript接口定义Props
3. **错误边界处理**: 适当添加错误处理逻辑
4. **可访问性**: 考虑键盘导航和屏幕阅读器
5. **性能优化**: 避免不必要的重渲染
6. **测试友好**: 组件结构便于测试
