# Kotlin DDD项目实现指南

## 项目结构
```
src/main/kotlin/
├── Application.kt                 # 应用程序入口
├── facade/                        # 接口层
│   └── rest/                     
│       ├── controllers/          # @RestController注解
│       ├── requests/            
│       └── responses/           
├── application/                   # 应用层
│   └── service/                  # 业务编排
├── domain/                       # 领域层
│   ├── model/
│   │   ├── entity/              # @Entity注解
│   │   └── vo/                  # @Embeddable注解
│   ├── service/                 # @Service注解
│   └── event/                   # ApplicationEvent
└── infrastructure/              # 基础设施层
    ├── repository/
    │   ├── jpa/                # 继承JpaRepository
    │   └── redis/              # Redis实现
    └── config/                 # 配置类
```

## 代码模板

### 1. Entity模板
```kotlin
@Entity
@Table(name = "table_name")
class EntityName(
    @Id
    val id: Long = nextId(),
    
    @Column(nullable = false)
    var field: String,
    
    @Enumerated(EnumType.STRING)
    var status: BaseStatus = BaseStatus.ENABLED
) : AbstractBaseEntity()
```

### 2. Value Object模板
```kotlin
@Embeddable
class ValueObjectName(
    @Column(nullable = false)
    val field1: String,
    
    @Column(nullable = false)
    val field2: String
)
```

### 3. Domain Service模板
```kotlin
@Service
class DomainServiceName {
    companion object {
        private val log = KotlinLogging.logger {}
    }

    fun businessOperation(entity: Entity) {
        log.info { "执行业务操作: ${entity.id}" }
        // 业务逻辑
    }
}
```

### 4. Repository模板
```kotlin
@Repository
interface RepositoryName : JpaRepository<Entity, Long> {
    @Query("""
        select e from Entity e
        where e.bizId = :bizId
        and (:param is null or e.field = :param)
        order by e.createdAt desc
    """)
    fun pageQuery(bizId: Long, param: String?, pageable: Pageable): Page<Entity>
}
```

### 5. Controller模板
```kotlin
@RestController
@RequestMapping("/api/v1/resource")
class ControllerName(
    private val applicationService: ApplicationService
) {
    companion object {
        private val log = KotlinLogging.logger {}
    }

    @PostMapping
    fun create(@RequestBody @Valid request: Request): Response {
        log.info { "创建资源: $request" }
        return applicationService.create(request)
    }
}
```

## 关键实现要点

### 1. 必须遵循的规范
- 所有Entity必须继承AbstractBaseEntity
- 所有Repository必须继承JpaRepository
- 所有Service必须使用KotlinLogging记录日志
- 查询必须使用JPQL而不是原生SQL
- 所有外部输入必须做数据校验

### 2. 性能优化规范
```kotlin
// 避免N+1问题
@EntityGraph(attributePaths = ["relation"])
fun findWithRelation(): List<Entity>

// 分页查询
fun pageQuery(pageable: Pageable): Page<Entity>

// Redis缓存
@Cacheable(value = "cache-name")
fun getData(): Data
```

### 3. 事务处理
```kotlin
@Transactional
fun businessOperation() {
    // 事务操作
}
```

### 4. 异常处理
```kotlin
throw CognitionWebException(ErrorCode.BUSINESS_ERROR)
```

### 5. 领域事件
```kotlin
class DomainEvent(val entityId: Long) : ApplicationEvent(entityId)
```

## 技术栈要求
- Kotlin 1.8+
- Spring Boot 3.x
- Java 21
- PostgreSQL
- Redis
- Gradle Kotlin DSL

## 代码风格
1. 使用函数式编程而不是命令式
2. 使用when表达式替代if-else
3. 使用数据类存储DTO
4. 使用密封类处理状态
5. 使用空安全特性避免NPE
