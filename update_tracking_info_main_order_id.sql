-- 更新 TrackingInfo 表的 main_order_id 字段
-- 根据 waybill_id 关联 waybills 表获取对应的 main_order_id

-- 首先添加 main_order_id 列（如果还没有添加的话）
-- ALTER TABLE tracking_info ADD COLUMN main_order_id BIGINT;

-- 查看更新前的数据状态
SELECT
    COUNT(*) as total_records,
    COUNT(main_order_id) as records_with_main_order_id,
    COUNT(*) - COUNT(main_order_id) as records_without_main_order_id
FROM tracking_info;

-- 更新现有数据的 main_order_id
UPDATE tracking_info
SET main_order_id = w.main_order_id
FROM waybills w
WHERE tracking_info.waybill_id = w.id
  AND (tracking_info.main_order_id IS NULL OR tracking_info.main_order_id = 0);

-- 验证更新结果
SELECT
    COUNT(*) as total_records,
    COUNT(main_order_id) as records_with_main_order_id,
    COUNT(*) - COUNT(main_order_id) as records_without_main_order_id,
    COUNT(CASE WHEN main_order_id = 0 THEN 1 END) as records_with_zero_main_order_id
FROM tracking_info;

-- 查看更新后的数据示例
SELECT 
    ti.id,
    ti.waybill_id,
    ti.main_order_id,
    ti.waybill_no,
    ti.channel,
    ti.current_status,
    w.main_order_id as waybill_main_order_id
FROM tracking_info ti
LEFT JOIN waybills w ON ti.waybill_id = w.id
LIMIT 10;

-- 检查是否有无法匹配的记录
SELECT
    ti.id,
    ti.waybill_id,
    ti.main_order_id,
    ti.waybill_no
FROM tracking_info ti
LEFT JOIN waybills w ON ti.waybill_id = w.id
WHERE w.id IS NULL
LIMIT 10;

-- 如果需要设置 main_order_id 为 NOT NULL，在确认所有数据都已更新后执行：
-- ALTER TABLE tracking_info ALTER COLUMN main_order_id SET NOT NULL;

-- 添加索引（如果还没有添加的话）
-- CREATE INDEX IF NOT EXISTS idx_tracking_main_order_id ON tracking_info(main_order_id);
-- CREATE INDEX IF NOT EXISTS idx_tracking_main_order_query ON tracking_info(main_order_id, current_status);
