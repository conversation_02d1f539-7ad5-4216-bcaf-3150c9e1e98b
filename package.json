{"name": "oms-lux", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "test": "vite --mode production", "build": "tsc && vite build --mode production", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@dnd-kit/core": "^6.3.1", "@dnd-kit/modifiers": "^7.0.0", "@dnd-kit/sortable": "^8.0.0", "@dnd-kit/utilities": "^3.2.2", "@hookform/resolvers": "^3.9.0", "@radix-ui/react-accordion": "^1.2.3", "@radix-ui/react-alert-dialog": "^1.1.2", "@radix-ui/react-avatar": "^1.1.1", "@radix-ui/react-checkbox": "^1.1.2", "@radix-ui/react-collapsible": "^1.1.1", "@radix-ui/react-dialog": "^1.1.2", "@radix-ui/react-dropdown-menu": "^2.1.2", "@radix-ui/react-hover-card": "^1.1.2", "@radix-ui/react-icons": "^1.3.0", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-popover": "^1.1.2", "@radix-ui/react-progress": "^1.1.0", "@radix-ui/react-radio-group": "^1.2.1", "@radix-ui/react-scroll-area": "^1.2.0", "@radix-ui/react-select": "^2.1.2", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-slider": "^1.2.1", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-switch": "^1.1.1", "@radix-ui/react-tabs": "^1.1.1", "@radix-ui/react-toast": "^1.1.5", "@radix-ui/react-tooltip": "^1.1.3", "@react-pdf-viewer/core": "^3.12.0", "@react-pdf-viewer/default-layout": "^3.12.0", "@tanstack/react-table": "^8.20.5", "@tanstack/react-virtual": "^3.10.8", "ahooks": "^3.8.0", "avatar": "^0.1.0", "axios": "^1.7.4", "breadcrumb": "^1.0.0", "class-variance-authority": "^0.7.0", "clipboard-polyfill": "^4.1.0", "clsx": "^2.1.1", "cmdk": "^1.0.0", "date-fns": "^3.6.0", "dayjs": "^1.11.13", "dropdown-menu": "^0.1.1", "framer-motion": "^11.17.0", "jotai": "^2.9.1", "lucide-react": "^0.453.0", "next-themes": "^0.3.0", "pdfjs-dist": "^3.11.174", "pushmodal": "^1.0.4", "radix-ui": "^1.4.2", "react": "^18.3.1", "react-day-picker": "^8.10.1", "react-dom": "^18.3.1", "react-hook-form": "^7.53.1", "react-router-dom": "^6.23.1", "recharts": "^2.12.7", "sonner": "^1.5.0", "tailwind-merge": "^2.5.4", "tailwindcss-animate": "^1.0.7", "ts-pattern": "^5.3.1", "usehooks-ts": "^3.1.0", "vaul": "^0.9.1", "zod": "^3.23.8"}, "devDependencies": {"@eslint/js": "^9.11.1", "@types/node": "^22.7.7", "@types/react": "^18.3.10", "@types/react-dom": "^18.3.0", "@types/ssh2": "^1.15.5", "@vitejs/plugin-react": "^4.3.2", "autoprefixer": "^10.4.20", "eslint": "^9.11.1", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.12", "globals": "^15.9.0", "node-ssh": "^13.2.1", "postcss": "^8.4.47", "tailwindcss": "^3.4.14", "typescript": "^5.5.3", "typescript-eslint": "^8.7.0", "vite": "^5.4.8"}}