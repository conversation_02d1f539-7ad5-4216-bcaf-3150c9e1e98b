import { UserInfo } from '@/api/user/user-api'
import { atom } from 'jotai'
import { atomWithStorage } from 'jotai/utils'


export const tokenAtom = atomWithStorage<string | null>("token", null)

export const userInfoAtom = atomWithStorage<UserInfo | null>("userInfo", null)

export const toggleSidebarAtom = atomWithStorage<boolean>("toggleSidebar", true)

export const refreshTableAtom = atom<number>(0)

