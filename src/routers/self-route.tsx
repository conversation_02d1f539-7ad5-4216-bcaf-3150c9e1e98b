import CustomerManagementPage from "@/app/dashboard/customers/management/page";
// import CustomerPlatformAccountManagementPage from "@/app/dashboard/customers/platform-accounts/page";

import CustomerProductPage from "@/app/dashboard/inventory/customer-products/page";
import ComboCustomerProductPage from "@/app/dashboard/inventory/combo-customer-products/page";
import { HomePage } from "@/app/dashboard/home/<USER>";
import ProductDetail from "@/app/dashboard/inventory/products/details/page";
import ProductPage from "@/app/dashboard/inventory/products/page";
import SupplierSpuDetailPage from "@/app/dashboard/inventory/supplier-products/detail/page";
import SupplierProductPage from "@/app/dashboard/inventory/supplier-products/page";
import MainOrderPage from "@/app/dashboard/order/main-order/page";
import SubOrderPage from "@/app/dashboard/order/sub-order/page";
import ExcelTemplateHeaderCreatePage from "@/app/dashboard/settings/download/template/create/page";
import ExcelTemplateListPage from "@/app/dashboard/settings/download/template/page";
import PermissionPage from "@/app/dashboard/settings/permission/page";
import RolePage from "@/app/dashboard/settings/role/page";
import UserPage from "@/app/dashboard/settings/user/page";
import SupplierPage from "@/app/dashboard/supplier-management/supplier/page";
import LoginPage from "@/app/login/page";
import MainLayout from "@/components/layout/main-layout";
import RootLayout from "@/components/layout/root-layout";
import { createBrowserRouter, Navigate } from "react-router-dom";
import CustomerSpuDetailPage from "@/app/dashboard/inventory/customer-products/detail/page";
import ComboCustomerSpuDetailPage from "@/app/dashboard/inventory/combo-customer-products/detail/page";
import WaybillPage from "@/app/dashboard/order/waybill/page";
import SupplierOrderPage from "@/app/dashboard/order/supplier-order/page";
import TrackingPage from "@/app/dashboard/tracking/page";
import DownloadTaskPage from "@/app/dashboard/settings/download/task/page";
import DashboardPage from "@/app/dashboard/main/page";
import SupplierOrderPackagePage from "@/app/dashboard/order/supplier-order/package/page";
import SupplierOrderPackageDetailPage from "@/app/dashboard/order/supplier-order/package/detail/page";
import SupplierMainOrderPage from "@/app/dashboard/order/supplier-main-order/page";
import OrderFinancePage from "@/app/dashboard/order/main-order/financial/page";
import FinancialPage from "@/app/dashboard/customers/management/financial/page";
import ComboProductDetail from "@/app/dashboard/inventory/combo-products/details/page.tsx";
import ComboProductPage from "@/app/dashboard/inventory/combo-products/page.tsx";
import CustomerPlatformAccountManagementPage from "@/app/dashboard/customers/platform-accounts/page";
import CustomerTaxDetailPage from "@/app/dashboard/customers/management/tax-config/page.tsx";
import ChannelPage from "@/app/dashboard/settings/channel/page";
// import AccountSettings from "@/App";

export const router = createBrowserRouter([
    {
        path: "/",
        element: <RootLayout />,
        children: [
            {
                path: "/",
                element: <Navigate to="/dashboard/order/main-order" replace />,
            },
            {
                path: "/login",
                element: <LoginPage />,
            },
            {
                path: "/dashboard",
                element: <MainLayout />,
                children: [
                    {
                        path: "main",
                        element: <DashboardPage />,
                    },
                    {
                        path: "order",
                        children: [
                            {
                                path: "main-order",
                                element: <MainOrderPage />,
                            },
                            {
                                path: "sub-order",
                                element: <SubOrderPage />,
                            },
                            {
                                path: "image-download-task",
                                element: <HomePage />,
                            }
                        ]
                    },
                    {
                        path: "customers",
                        children: [
                            {
                                path: "management",
                                element: <CustomerManagementPage />,
                            },
                            {
                                path: "management/financial/:customerId",
                                element: <FinancialPage />,
                            },
                            {
                                path: "platform-accounts",
                                element: <CustomerPlatformAccountManagementPage />,
                            },
                            {
                                path: "management/tax-config/:customerId",
                                element: <CustomerTaxDetailPage />,
                            }
                        ]
                    },
                    {
                        path: "inventory",
                        children: [
                            {
                                path: "products",
                                element: <ProductPage />,
                            },
                            {
                                path: "combo-products",
                                element: <ComboProductPage />,
                            },
                            {
                                path: "supplier/products",
                                element: <SupplierProductPage />,
                            },
                            {
                                path: "customer/products",
                                element: <CustomerProductPage />,
                            },
                            {
                                path: "customer/combo-products",
                                element: <ComboCustomerProductPage />,
                            }
                        ]
                    },
                    {
                        path: "supplier",
                        children: [
                            {
                                path: "management",
                                element: <SupplierPage />,
                            }
                        ]
                    },
                    {
                        path: "order",
                        children: [
                            {
                                path: "waybill",
                                element: <WaybillPage />,
                            },
                            {
                                path: "supplier-order",
                                element: <SupplierOrderPage />,
                            },
                            {
                                path: "supplier-order/package",
                                element: <SupplierOrderPackagePage />,
                            },
                            {
                                path: "supplier-main-order",
                                element: <SupplierMainOrderPage />,
                            },
                            {
                                path: "tracking",
                                element: <TrackingPage />,
                            }

                        ]
                    },
                    {
                        path: "download",
                        children: [
                            {
                                path: "task",
                                element: <DownloadTaskPage />,
                            },
                            {
                                path: "template",
                                element: <ExcelTemplateListPage />
                            },
                            {
                                path: "template/create",
                                element: <ExcelTemplateHeaderCreatePage />,
                            }
                        ]
                    },
                    {
                        path: "settings",
                        children: [
                            {
                                path: "permission",
                                element: <PermissionPage />,
                            },
                            {
                                path: "role",
                                element: <RolePage />,
                            },
                            {
                                path: "user",
                                element: <UserPage />,
                            },
                            {
                                path: "channel",
                                element: <ChannelPage />,
                            }
                        ]
                    }
                ]
            },
            {
                path: "dashboard/inventory/products/details",
                element: <ProductDetail />,
            },
            {
                path: "dashboard/inventory/combo-products/details",
                element: <ComboProductDetail />,
            },
            {
                path: "dashboard/inventory/supplier/products/detail",
                element: <SupplierSpuDetailPage />,
            },
            {
                path: "dashboard/inventory/customer/products/detail",
                element: <CustomerSpuDetailPage />,
            },
            {
                path: "dashboard/inventory/customer/combo-products/detail",
                element: <ComboCustomerSpuDetailPage />,
            },
            {
                path: "dashboard/order/supplier-order/package/detail/:id",
                element: <SupplierOrderPackageDetailPage />,
            },
            {
                path: "dashboard/order/main-order/financial/:id",
                element: <OrderFinancePage />,
            },
        ]
    }
]);
