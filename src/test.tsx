import { CSSProperties } from 'react'

import {
    <PERSON>,
    Header,
    flexRender
} from '@tanstack/react-table'
import './index.css'

import { useSortable } from '@dnd-kit/sortable'
import { CSS } from '@dnd-kit/utilities'
import { DragHandleDots2Icon } from '@radix-ui/react-icons'
import { TableCell, TableHead } from './components/ui/table'


export const CommonTableHeader = ({
    header,
}: {
    header: Header<any, unknown>
}) => {
    return <TableHead>{header.isPlaceholder ? null : flexRender(header.column.columnDef.header, header.getContext())}</TableHead>
}


export const DraggableTableHeader = ({
    header,
}: {
    header: Header<any, unknown>
}) => {
    const { attributes, isDragging, listeners, setNodeRef, transform } = useSortable({
        id: header.column.id,
    })

    const style: CSSProperties = {
        opacity: isDragging ? 0.8 : 1,
        position: 'relative',
        transform: CSS.Translate.toString(transform), // translate instead of transform to avoid squishing
        transition: 'width transform 0.2s ease-in-out',
        whiteSpace: 'nowrap',
        width: header.column.getSize(),
        zIndex: isDragging ? 1 : 0,
    }

    return (
        <TableHead key={header.id} ref={setNodeRef} style={style}>
            <div className="flex flex-row items-center justify-start">
                {header.isPlaceholder
                    ? null
                    : flexRender(header.column.columnDef.header, header.getContext())}
                <button {...attributes} {...listeners} className="w-4 h-4">
                    <DragHandleDots2Icon />
                </button>
            </div>
        </TableHead>
    )
}


export const CommonTableCell = ({ cell }: { cell: Cell<any, unknown> }) => {
    return <TableCell>{flexRender(cell.column.columnDef.cell, cell.getContext())}</TableCell>
}

export const DragAlongCell = ({ cell }: { cell: Cell<any, unknown> }) => {
    const { isDragging, setNodeRef, transform } = useSortable({
        id: cell.column.id,
    })

    const style: CSSProperties = {
        opacity: isDragging ? 0.8 : 1,
        position: 'relative',
        transform: CSS.Translate.toString(transform), // translate instead of transform to avoid squishing
        transition: 'width transform 0.2s ease-in-out',
        width: cell.column.getSize(),
        zIndex: isDragging ? 1 : 0,
    }

    return (
        <TableCell style={style} ref={setNodeRef}>
            {flexRender(cell.column.columnDef.cell, cell.getContext())}
        </TableCell>
    )
}