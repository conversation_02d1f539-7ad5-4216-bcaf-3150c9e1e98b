import apiClient from "@/lib/apiClient";

export type QueryStatisticResponse = {
    waybillAll: number;
    waybillFailed: number;
    supplierOrderAll: number;
    supplierOrderFailed: number;
}


export function queryStatistic(createdAtFrom: string, createdAtTo: string) {
    return apiClient.get({
        url: '/api/statistics/failed',
        params: {
            createdAtFrom,
            createdAtTo
        }
    })
}