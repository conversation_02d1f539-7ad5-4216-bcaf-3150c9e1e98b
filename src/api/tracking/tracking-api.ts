import { FetchParams, FetchResponse } from "@/components/table/base-table";
import apiClient from "@/lib/apiClient";
import {
    TrackingInfoResponse,
    TrackingUpdateResponse,
    BatchTrackingUpdateResponse,
    TrackingStatisticsResponse,
    CleanupResponse,
    ChannelInfo
} from "./tracking-model";

/**
 * 分页查询轨迹信息
 */
export async function pageTracking(params: FetchParams): Promise<FetchResponse<TrackingInfoResponse>> {
    return await apiClient.post({
        url: '/api/tracking/page',
        params: {
            pageIndex: params.pagination.pageIndex,
            pageSize: params.pagination.pageSize,
            waybillNo: params.searchParams.waybillNo,
            trackingNumber: params.searchParams.trackingNumber,
            orderNo: params.searchParams.orderNo,
            channel: params.searchParams.channel,
            status: params.searchParams.status,
            completed: params.searchParams.completed,
            destinationCountry: params.searchParams.destinationCountry,
            destinationCountryCode: params.searchParams.destinationCountryCode,
            waybillId: params.searchParams.waybillId,
            createdAtFrom: params.searchParams.createdAtFrom,
            createdAtTo: params.searchParams.createdAtTo
        },
        data: {
            orderNos: params.filters?.orderNos,
            waybillNos: params.filters?.waybillNos,
            trackingNumbers: params.filters?.trackingNumbers
        }
    });
}

/**
 * 根据运单号查询轨迹信息
 */
export async function getTrackingByWaybillNo(waybillNo: string): Promise<TrackingInfoResponse> {
    return await apiClient.get({
        url: `/api/tracking/waybill/${waybillNo}`
    });
}

/**
 * 根据订单号查询轨迹信息
 */
export async function getTrackingByOrderNo(orderNo: string): Promise<TrackingInfoResponse[]> {
    return await apiClient.get({
        url: `/api/tracking/order/${orderNo}`
    });
}

/**
 * 根据运单ID查询轨迹信息
 */
export async function getTrackingByWaybillId(waybillId: string): Promise<TrackingInfoResponse> {
    return await apiClient.get({
        url: `/api/tracking/waybill-id/${waybillId}`
    });
}

/**
 * 手动更新指定运单的轨迹信息
 */
export async function updateTrackingByWaybillNo(waybillNo: string): Promise<TrackingUpdateResponse> {
    return await apiClient.post({
        url: `/api/tracking/update/waybill/${waybillNo}`
    });
}

/**
 * 强制更新指定运单的轨迹信息（忽略15天限制）
 */
export async function forceUpdateTracking(waybillNo: string): Promise<TrackingUpdateResponse> {
    return await apiClient.post({
        url: `/api/tracking/force-update/waybill/${waybillNo}`
    });
}

/**
 * 手动更新指定渠道的所有轨迹信息
 */
export async function updateTrackingByChannel(channel: string): Promise<BatchTrackingUpdateResponse> {
    return await apiClient.post({
        url: `/api/tracking/update/channel/${channel}`
    });
}

/**
 * 手动触发轨迹更新定时任务
 */
export async function triggerTrackingUpdate(): Promise<string> {
    return await apiClient.post({
        url: '/api/tracking/update/all'
    });
}

/**
 * 获取轨迹统计信息
 */
export async function getTrackingStatistics(
    startDate?: string,
    endDate?: string
): Promise<TrackingStatisticsResponse> {
    const params: any = {};
    if (startDate) params.startDate = startDate;
    if (endDate) params.endDate = endDate;

    return await apiClient.get({
        url: '/api/tracking/statistics',
        params
    });
}

/**
 * 清理过期轨迹信息
 */
export async function cleanupStaleTracking(): Promise<CleanupResponse> {
    return await apiClient.post({
        url: '/api/tracking/cleanup'
    });
}

/**
 * 获取支持的渠道列表
 */
export async function getSupportedChannels(): Promise<ChannelInfo[]> {
    return await apiClient.get({
        url: '/api/tracking/channels'
    });
}

/**
 * 根据运单ID刷新轨迹信息
 */
export async function refreshTrackingByWaybillId(waybillId: string): Promise<TrackingUpdateResponse> {
    return await apiClient.post({
        url: `/api/tracking/refresh/waybill-id/${waybillId}`
    });
}

/**
 * 批量根据运单ID刷新轨迹信息
 */
export async function batchRefreshTrackingByWaybillIds(waybillIds: string[]): Promise<BatchTrackingUpdateResponse> {
    return await apiClient.post({
        url: '/api/tracking/refresh/waybill-ids/batch',
        data: waybillIds
    });
}
