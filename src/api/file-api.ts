import apiClient from "@/lib/apiClient";

// 定义允许的图片类型
const AllowedImgTypes = [
    "image/jpeg",
    "image/png",
    "image/gif",
    "image/bmp",
    "image/tiff"
] as const;

type AllowedImgType = typeof AllowedImgTypes[number];

// 检查文件是否为有效的图片文件
function isValidImageFile(file: File): boolean {
    return AllowedImgTypes.includes(file.type as AllowedImgType);
}

// 检查文件大小是否在限制内（30 MiB）
function isValidFileSize(file: File): boolean {
    const MAX_SIZE = 30 * 1024 * 1024; // 30 MiB in bytes
    return file.size <= MAX_SIZE;
}

export async function uploadImage(file: File): Promise<string> {
    if (!file) {
        throw new Error("File is empty");
    }

    if (!file.name) {
        throw new Error("File name is empty");
    }

    if (!isValidImageFile(file)) {
        throw new Error("Invalid image file");
    }

    if (!isValidFileSize(file)) {
        throw new Error("File size exceeds 30 MiB");
    }

    const formData = new FormData();
    formData.append('file', file);

    try {
        return await apiClient.post<string>({
            url: '/api/file/img:upload',
            data: formData,
            headers: {
                'Content-Type': 'multipart/form-data'
            }
        });
    } catch (error) {
        console.error('Error uploading image:', error);
        throw error;
    }
}

// 定义允许的附件类型
const AllowedAttachmentTypes = [
    ".zip", ".rar", ".7z", ".tar", ".gz",
    ".xlsx", ".xls", ".csv",
    ".pdf", ".doc", ".docx",
    ".jpg", ".jpeg", ".png", ".gif", ".bmp", ".webp",
    ".txt", ".json", ".xml"
] as const;

// 检查文件是否为有效的附件文件
function isValidAttachmentFile(file: File): boolean {
    const fileName = file.name.toLowerCase();
    return AllowedAttachmentTypes.some(type => fileName.endsWith(type));
}

// 检查附件文件大小是否在限制内（50 MiB）
function isValidAttachmentFileSize(file: File): boolean {
    const MAX_SIZE = 50 * 1024 * 1024; // 50 MiB in bytes
    return file.size <= MAX_SIZE;
}

export async function uploadAttachment(file: File): Promise<string> {
    if (!file) {
        throw new Error("File is empty");
    }

    if (!file.name) {
        throw new Error("File name is empty");
    }

    if (!isValidAttachmentFile(file)) {
        throw new Error("Invalid attachment file type");
    }

    if (!isValidAttachmentFileSize(file)) {
        throw new Error("File size exceeds 50 MiB");
    }

    const formData = new FormData();
    formData.append('file', file);

    try {
        return await apiClient.post<string>({
            url: '/api/file/attachment:upload',
            data: formData,
            headers: {
                'Content-Type': 'multipart/form-data'
            }
        });
    } catch (error) {
        console.error('Error uploading attachment:', error);
        throw error;
    }
}
