import { FetchParams, FetchResponse } from "@/components/table/base-table";
import apiClient from "@/lib/apiClient";
import { FinanceStatisticResponse } from "../order/waybill/waybill-model";


export type CustomerInfo = {
    id: string;
    name: string;
    email: string;
    token: string;
    status: number;
    createdAt: string;
    createdBy: string;
    openapiKey: string | null;
}

export async function pageQueryCustomer(params: FetchParams): Promise<FetchResponse<CustomerInfo>> {
    return await apiClient.get({
        url: '/api/customer/page',
        params: {
            ...params.pagination,
            ...params.searchParams
        }
    })
}


export async function getGoogleToken(email: string, customerId: string): Promise<string> {
    return await apiClient.post({
        url: '/api/google/drive/client?email=' + email + '&customerId=' + customerId,
    })
}


export async function addCustomer(name: string, email: string): Promise<void> {
    return await apiClient.post({
        url: '/api/customer',
        data: {
            name: name,
            email: email
        }
    })
}


export async function listCustomer(): Promise<CustomerInfo[]> {
    return await apiClient.get({
        url: '/api/customer/list',
    })
}


export async function deleteCustomer(id: string): Promise<void> {
    return await apiClient.delete({
        url: '/api/customer/' + id,
    })
}


export async function changeCustomerStatus(id: string, status: number) {
    return await apiClient.post({
        url: `/api/customer/${id}/status:change?status=${status}`,
    })
}

export async function getCustomer(customerId: string): Promise<CustomerInfo> {
    return await apiClient.get({
        url: `/api/customer/${customerId}`,
    })
}

export async function getCustomerFinancial(customerId: string): Promise<FinanceStatisticResponse> {
    return await apiClient.get({
        url: `/api/customer/${customerId}/financial`,
    })
}

export async function createApiKey(customerId: string): Promise<string> {
    return await apiClient.post({
        url: `/api/customer/${customerId}/generate/open-api/key`,
    })
}