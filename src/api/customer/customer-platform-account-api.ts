import { FetchParams, FetchResponse } from "@/components/table/base-table";
import apiClient from "@/lib/apiClient";

export type CustomerPlatformAccountInfo = {
    id: string;
    customerId: string;
    customerName: string;
    email: string;
    accountName: string;
    description: string;
    status: 'ENABLED' | 'DISABLED';
    lastLoginAt: number | null;
    createdAt: number;
    updatedAt: number;
}

export type CreateCustomerPlatformAccountRequest = {
    customerId: string;
    email: string;
    password: string;
    accountName: string;
    description?: string;
}

export type UpdateCustomerPlatformAccountRequest = {
    email: string;
    password?: string;
    accountName: string;
    description?: string;
    status: 'ENABLED' | 'DISABLED';
}

export type CustomerPlatformAccountPageParams = {
    email?: string;
    accountName?: string;
    customerId?: number;
    statuses?: ('ENABLED' | 'DISABLED')[];
}

export async function pageQueryCustomerPlatformAccounts(
    params: FetchParams & { searchParams?: CustomerPlatformAccountPageParams }
): Promise<FetchResponse<CustomerPlatformAccountInfo>> {
    return await apiClient.get({
        url: '/api/customer-platform-accounts/page',
        params: {
            ...params.pagination,
            ...params.searchParams
        }
    })
}

export async function createCustomerPlatformAccount(
    request: CreateCustomerPlatformAccountRequest
): Promise<CustomerPlatformAccountInfo> {
    return await apiClient.post({
        url: '/api/customer-platform-accounts',
        data: request
    })
}

export async function updateCustomerPlatformAccount(
    id: string,
    request: UpdateCustomerPlatformAccountRequest
): Promise<CustomerPlatformAccountInfo> {
    return await apiClient.put({
        url: `/api/customer-platform-accounts/${id}`,
        data: request
    })
}

export async function getCustomerPlatformAccount(id: string): Promise<CustomerPlatformAccountInfo> {
    return await apiClient.get({
        url: `/api/customer-platform-accounts/${id}`
    })
}

export async function getCustomerPlatformAccounts(customerId: string): Promise<CustomerPlatformAccountInfo[]> {
    return await apiClient.get({
        url: `/api/customer-platform-accounts/customer/${customerId}`
    })
}

export async function deleteCustomerPlatformAccount(id: string): Promise<void> {
    return await apiClient.delete({
        url: `/api/customer-platform-accounts/${id}`
    })
}

export async function changeCustomerPlatformAccountStatus(
    id: string,
    status: 'ENABLED' | 'DISABLED'
): Promise<void> {
    return await apiClient.post({
        url: `/api/customer-platform-accounts/${id}/status:change`,
        params: { status }
    })
}
