export type CreateSkuRequest = {
    spuId: string;
    spuCode: string;
    size: string;
    color: string;
    purchaseCost: number;
    purchaseCostCurrency: string;
    weight: number;
    volume: number;
    salePrice: number;
}

export type UpdateSkuRequest = {
    id?: string;
    skuCode: string;
    size: string;
    color: string;
    purchaseCost: number;
    purchaseCostCurrency: string;
    weight: number;
    volume: number;
    salePrice: number;
}

export type SkuResponse = {
    id: string;
    spuId: string;
    skuCode: string;
    size: string;
    color: string;
    purchaseCost: number;
    purchaseCostCurrency: string;
    weight: number;
    volume: number;
    status: string;
    updatedAt: number;
    createdAt: number;
    salePrice: number;
}
