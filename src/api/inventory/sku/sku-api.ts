import {CreateSkuRequest, SkuResponse, UpdateSkuRequest} from "@/api/inventory/sku/sku-model";
import { FetchParams } from "@/components/table/base-table";
import apiClient from "@/lib/apiClient";


export async function pageSku(
    params:FetchParams
): Promise<SkuResponse> {
    return await apiClient.get({
        url: '/api/skus/page',
        params: {
            ...params.pagination,
            ...params.searchParams
        }
    })
}

export async function findSku(id: string) {
    return await apiClient.get({
        url: '/api/skus/' + id,
    })
}


export async function deleteSku(id: string) {
    return await apiClient.delete({
        url: '/api/skus/' + id
    })
}

export async function createSku(request: CreateSkuRequest) {
    return await apiClient.post({
        url: '/api/skus',
        data: request
    })
}

export async function updateSku(id: string, request: UpdateSkuRequest) {
    return await apiClient.put({
        url: '/api/skus/' + id,
        data: request
    })
}