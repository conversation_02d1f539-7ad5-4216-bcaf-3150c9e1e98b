import apiClient from "@/lib/apiClient";

export interface CustomerTax {
  id: string;
  customerId: string;
  countryId: string;
  countryName: string;
  additionalTax: number;
  vatTax: number;
  createdAt: string;
  updatedAt: string;
  createdByName: string;
  updatedByName: string;
  stateTaxConfigs: CountryStateTaxConfig[];
}

export interface CustomerTaxRequest {
  additionalTax?: number;
  vatTax?: number;
  countryId: string;
  countryName: string;
  stateTaxConfigs: CountryStateTaxConfig[];
}

export interface CountryStateTaxConfig {
  enName: string;
  cnName: string;
  isoAlphaTwo: string;
  additionalTax: number;
}

export interface BatchCustomerTaxRequest {
  customerIds: string[];
  customerTaxReq: CustomerTaxRequest[];
}


export async function batchCreateCustomerTax(request: BatchCustomerTaxRequest) {
  return await apiClient.post({
    url: '/api/customer-tax-config/batch',
    data: request
  })
}

export async function batchUpdateCustomerTax(request: BatchCustomerTaxRequest) {
  return await apiClient.put({
    url: '/api/customer-tax-config/batch',
    data: request
  })
}

export async function deleteCustomerTax(customerId: string, countryId: string): Promise<void> {
  return await apiClient.delete({
    url: `/api/customer-tax-config/${customerId}/${countryId}`
  })
}

export async function getCustomerTax(id: string): Promise<CustomerTax[]> {
  return await apiClient.get({
    url: `/api/customer-tax-config/by-customer/${id}`
  })
}
