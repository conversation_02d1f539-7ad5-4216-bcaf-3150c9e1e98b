import {Fetch<PERSON>ara<PERSON>, FetchResponse} from "@/components/table/base-table";
import apiClient from "@/lib/apiClient";
import {SkuCountryPrices, SkuPcsPrices} from "./types";
import {BaseStatus} from "@/api/order/templete/download-templete";

export interface ComboCustomerSpuView {
  id: string
  bizId: string;
  customerId: string;
  systemSpuIds: string[];
  systemSpuCodes: string[];
  systemComboSpuId: string;
  triggerDiscountQuantity: number;
  discount: number;
  status: string;
  skus: ComboCustomerSkuView[];

  createdAt: string,
  createdBy: string,
  createdByName: string,
  updatedAt: string,
  updatedBy: string,
  updatedByName: string,
}

export interface ComboCustomerSkuView {
  id: string,
  bizId: string,
  customerId: string,
  comboCustomerSpuId: string,
  systemSpuIdsOfCombo: string[];
  systemSpuCodesOfCombo: string[];
  comboSpuId: string;
  comboSkuId: string;
  systemSpuId: string;
  systemSpuCode: string;
  systemSkuId: string;
  systemSkuCode: string;
  size: string,
  color: string,
  offlinePrice: number,
  offlinePriceCurrency: string,
  status: BaseStatus,
  skuCountryPrices: SkuCountryPrices,
  skuPcsPrices: SkuPcsPrices,
  triggerDiscountQuantity?: number,
  discount?: number,

  createdAt: string,
  createdBy: string,
  createdByName: string,
  updatedAt: string,
  updatedBy: string,
  updatedByName: string,
}

export type RenderableComboCustomerSkuView = Omit<ComboCustomerSkuView, 'id' | 'status' | 'bizId' | 'comboCustomerSpuId' | 'createdAt' | 'createdBy' | 'createdByName' | 'updatedByName' | 'updatedBy' | 'updatedAt'>

export type RenderableComboCustomerSpuView = Omit<ComboCustomerSpuView, 'skus' | 'id' | 'status' | 'bizId' | 'createdAt' | 'createdBy' | 'createdByName' | 'updatedByName' | 'updatedBy' | 'updatedAt'> & {
  "skus": RenderableComboCustomerSkuView[]
}

export type CreateComboCustomerSkuRequest = {
  customerId: string;
  systemSpuIdsOfCombo: string[];
  systemSpuCodesOfCombo: string[];
  comboSpuId: string;
  comboSkuId: string;
  systemSpuId: string;
  systemSpuCode: string;
  systemSkuId: string;
  systemSkuCode: string;
  size: string;
  color: string;
  offlinePrice: number;
  offlinePriceCurrency: string;
  skuCountryPrices: SkuCountryPrices;
  skuPcsPrices: SkuPcsPrices;
  triggerDiscountQuantity?: number;
  discount?: number;
};

export type CreateCustomerSpuRequest = {
  customerId: string;
  systemSpuIds: string[];
  systemSpuCodes: string[];
  systemComboSpuId: string;
  triggerDiscountQuantity: number;
  discount: number;
  skus: CreateComboCustomerSkuRequest[];
};

export type UpdateComboCustomerSkuRequest = {
  id?: string;
  offlinePrice: number;
  offlinePriceCurrency: string;
};

export type UpdateCustomerSpuRequest = {
  triggerDiscountQuantity?: number;
  discount?: number;
  skus: UpdateSkuRequest[];
};

export type UpdateSkuRequest = {
  id?: string | undefined;
  systemSkuId: string;
  systemSkuCode: string;
  size: string;
  color: string;
  offlinePrice: number;
  offlinePriceCurrency: string;
  skuCountryPrices?: SkuCountryPrices;
  skuPcsPrices?: SkuPcsPrices;
};

export async function createComboCustomerSpu(request: CreateCustomerSpuRequest) {
  return await apiClient.put<ComboCustomerSpuView>({
    url: '/api/combo-customer-spu/',
    data: request,
  });
}

export async function updateComboCustomerSpu(id: string, request: UpdateCustomerSpuRequest) {
  return await apiClient.post<ComboCustomerSpuView>({
    url: `/api/combo-customer-spu/${id}`,
    data: request,
  });
}

export async function getComboCustomerSpu(id: string | null) {
  if (id === null) return undefined
  return await apiClient.get<ComboCustomerSpuView>({
    url: `/api/combo-customer-spu/${id}`,
  });
}

export async function deleteComboCustomerSpu(id: string): Promise<void> {
  await apiClient.delete({
    url: `/api/combo-customer-spu/${id}`,
  });
}

export async function pageComboCustomerSpu(params: FetchParams) {
  return await apiClient.get<FetchResponse<ComboCustomerSpuView>>({
    url: '/api/combo-customer-spu/-',
    params: {
      ...params.pagination,
      ...params.searchParams
    }
  });
}

export async function updateComboCustomerSpuStatus(id: string, status: string) {
  return await apiClient.post<void>({
    url: `/api/combo-customer-spu/${id}/status/:change?wantedStatus=${status}`,
  })
}
