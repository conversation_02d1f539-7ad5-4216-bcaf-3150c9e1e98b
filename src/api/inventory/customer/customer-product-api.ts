import { Fetch<PERSON>ara<PERSON>, FetchResponse } from "@/components/table/base-table";
import apiClient from "@/lib/apiClient";
import {SkuCountryPrices, SkuPcsPrices} from "./types";

export type CustomerSkuResponse = {
    id: string;
    bizId: string;
    branchId: string;
    customerId: string;
    systemSpuId: string;
    systemSkuId: string;
    systemSpuCode: string;
    systemSkuCode: string;
    customerSpuCode?: string;
    customerSkuCode?: string;
    size: string;
    color: string;
    offlinePrice: number;
    offlinePriceCurrency: string;
    status: string;
    createdAt: string;
    updatedAt: string;
    createdBy: string;
    updatedBy: string;
    skuCountryPrices?: SkuCountryPrices;
    skuPcsPrices?: SkuPcsPrices;
    discount?: number;
    triggerDiscountQuantity?: number;
};

// src/api/customer/customer-spu-model.ts
export type CreateCustomerSpuRequest = {
    customerId: string;
    systemSpuId: string;
    systemSpuCode?: string;
    customerSpuCode?: string;
    triggerDiscountQuantity: number;
    discount: number;
    skus: CreateSkuRequest[];
};

export type CreateSkuRequest = {
    id?: string | undefined;
    systemSkuId: string;
    systemSkuCode: string;
    size: string;
    color: string;
    offlinePrice: number;
    offlinePriceCurrency: string;
    skuCountryPrices?: SkuCountryPrices;
    skuPcsPrices?: SkuPcsPrices;
};

export type UpdateCustomerSpuRequest = {
    triggerDiscountQuantity?: number;
    discount?: number;
    skus: UpdateSkuRequest[];
    supplierCapacities?: SupplierCapacity[];
};

export type UpdateSkuRequest = {
    id?: string | undefined;
    systemSkuId: string;
    systemSkuCode: string;
    size: string;
    color: string;
    offlinePrice: number;
    offlinePriceCurrency: string;
    skuCountryPrices?: SkuCountryPrices;
    skuPcsPrices?: SkuPcsPrices;
};

export type CustomerSpuResponse = {
    id: string;
    bizId: string;
    customerId: string;
    systemSpuId: string;
    systemSpuCode?: string;
    customerSpuCode?: string;
    triggerDiscountQuantity: number;
    discount: number;
    status: string;
    updatedByName?: string;
    updatedAt?: string;
    createdByName?: string;
    createdAt?: string;
    skus?: CustomerSkuResponse[];
    supplierCapacities?: SupplierCapacity[];
};

export interface SupplierCapacity {
    supplierId: string | null;
    supplierName: string | null;
    totalCapacity: number | null;
}

export type CustomerSpuPageResponse = Omit<CustomerSpuResponse, 'skus'>;

export async function createCustomerSpu(request: CreateCustomerSpuRequest): Promise<CustomerSpuPageResponse> {
    return await apiClient.post({
        url: '/api/customer-spus',
        data: request,
    });
}

export async function updateCustomerSpu(id: string, request: UpdateCustomerSpuRequest): Promise<CustomerSpuPageResponse> {
    return await apiClient.put({
        url: `/api/customer-spus/${id}`,
        data: request,
    });
}

export async function getCustomerSpu(id: string | null): Promise<CustomerSpuResponse | undefined> {
    if (id === null) return undefined
    return await apiClient.get({
        url: `/api/customer-spus/${id}`,
    });
}

export async function deleteCustomerSpu(id: string): Promise<void> {
    await apiClient.delete({
        url: `/api/customer-spus/${id}`,
    });
}

export async function pageCustomerSpu(params: FetchParams): Promise<FetchResponse<CustomerSpuPageResponse>> {
    return await apiClient.get({
        url: '/api/customer-spus/page',
        params: {
            ...params.pagination,
            ...params.searchParams
        }
    });
}

export async function updateCustomerSpuStatus(id: string, status: string) {
    return await apiClient.post({
        url: '/api/customer-spus/' + id + '/status:change?wantedStatus=' + status,
    })
}
