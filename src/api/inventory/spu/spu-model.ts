import {SkuResponse, UpdateSkuRequest} from "../sku/sku-model";

export type SpuPageResponse = {
    id: string;
    spuCode: string;
    title: string;
    name: string;
    cnName: string;
    category: string;
    productImage: string;
    packageQuantity: number;
    showImages: string[];
    description: string;
    sizes: string[];
    colors: string[];
    status: string;
    hsCode?: string;
    isSet: boolean;
    setQuantity?: number;
    updatedByName: string;
    createdByName: string;
    updatedAt: string;
    createdAt: string;
}

export type CreateSpuRequest = {
    spuCode: string;
    title: string;
    name: string;
    cnName: string;
    category: string;
    productImage: string;
    packageQuantity: number;
    showImages: string[];
    description: string;
    sizes: string[];
    colors: string[];
    hsCode?: string;
    isSet: boolean;
    setQuantity?: number;
    skus: UpdateSkuRequest[];
}

export type UpdateSpuRequest = {
    spuCode: string;
    title: string;
    name: string;
    cnName: string;
    category: string;
    productImage: string;
    packageQuantity: number;
    showImages: string[];
    description?: string;
    sizes: string[];
    colors: string[];
    hsCode?: string;
    isSet: boolean;
    setQuantity?: number;
    skus: UpdateSkuRequest[];
}


export type SpuResponse = {
    id: number;
    spuCode: string;
    title: string;
    name: string;
    cnName: string;
    category: string;
    productImage: string;
    packageQuantity: number;
    showImages: string[];
    description: string;
    sizes: string[];
    colors: string[];
    status: number;
    updatedAt: number;
    createdAt: number;
    hsCode?: string;
    isSet: boolean;
    setQuantity?: number;
    skus?: SkuResponse[]
}


export type SpuSelectResponse = {
    id: string,
    spuCode: string,
    title: string;
    name: string;
    cnName: string;
    category: string;
}