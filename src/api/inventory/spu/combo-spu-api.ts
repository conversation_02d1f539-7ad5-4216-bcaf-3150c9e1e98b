import apiClient from "@/lib/apiClient.ts";
import {BaseStatus} from "@/api/order/templete/download-templete.tsx";
import {FetchParams, FetchResponse} from "@/components/table/base-table.tsx";

export interface FlatSpuSku {
  id: string,
  spuId: string,
  spuCode: string,
  skuCode: string,
  size: string,
  color: string,
  purchaseCost: number,
  purchaseCostCurrency: string,
  weight: number,
  volume: number,
  salePrice: number
  status: string
}

export interface FlatSpu {
  id: string,
  spuCode: string,
  title: string,
  name: string,
  cnName: string,
  category: string,
  productImage: string,
  packageQuantity: number,
  skus: FlatSpuSku[]
}

export interface ComboSpuCreateRequest {
  spuCodes: string[]
  title: string,
  name: string,
  cnName: string,
  category: string,
  productImage: string,
  packageQuantity: number,
  showImages: string[],
  description: string,
  status: BaseStatus,
  hsCode?: string,
  skus: SkuOfComboSkuCreateRequest[]
}

export interface SkuOfComboSkuCreateRequest {
  spuId: string,
  spuCode: string,
  skuCode: string,
  systemSkuId: string,
  size: string,
  color: string,
  purchaseCost: number,
  purchaseCostCurrency: string,
  salePrice: number,
  weight: number,
  volume: number
}

export interface ComboSpuView {
  id: string,
  spuIds: string[],
  spuCodes: string[],
  title: string,
  name: string,
  cnName: string,
  category: string,
  productImage: string,
  packageQuantity: number,
  showImages: string[],
  description: string,
  status: BaseStatus,
  hsCode?: string,
  skus: ComboSkuView[]

  createdAt: string,
  createdBy: string,
  createdByName: string,
  updatedAt: string,
  updatedBy: string,
  updatedByName: string,
}

export interface ComboSkuView {
  id: string,
  comboSpuId: string,
  spuRefId: string,

  spuId: string,
  spuCode: string,
  skuCode: string,
  systemSkuId: string,
  size: string,
  color: string,
  purchaseCost: number,
  purchaseCostCurrency: string,
  salePrice: number,
  weight: number,
  volume: number

  createdAt?: string,
  createdBy: string,
  createdByName: string,
  updatedAt: string,
  updatedBy: string,
  updatedByName: string,
}

export type RenderAbleComboSkuView = Omit<ComboSkuView, 'id' | 'spuRefId' | 'comboSpuId' | 'createdAt' | 'createdBy' | 'createdByName' | 'updatedAt' | 'updatedBy' | 'updatedByName'>

export type RenderAbleComboSpuView = Omit<ComboSpuView, 'id' | 'skus' | 'createdAt' | 'createdBy' | 'createdByName' | 'updatedAt' | 'updatedBy' | 'updatedByName'> & {
  skus: RenderAbleComboSkuView[]
}

export async function fetchFlatSpu(ids: ArrayLike<string>) {
  if (ids.length === 0) {
    return []
  }
  return await apiClient.post<FlatSpu[]>({
    url: '/api/combo-spu/flat-spu:fetch',
    data: ids
  })
}

export async function searchFlatSpu(search: string) {
  return await apiClient.get<FlatSpu[]>({
    url: '/api/combo-spu/flat-spu:search',
    params: {
      search
    }
  })
}

export async function createComboSpu(data: ComboSpuCreateRequest) {
  return await apiClient.put<string>({
    url: '/api/combo-spu/',
    data
  })
}

export async function updateComboSpu(id: string, data: ComboSpuCreateRequest) {
  return await apiClient.post<string>({
    url: `/api/combo-spu/${id}`,
    data
  })
}

export async function deleteComboSpu(id: string) {
  return await apiClient.delete<string>({
    url: `/api/combo-spu/${id}`,
  })
}

export async function pageComboSpu(
  params: FetchParams,
) {
  return await apiClient.get<FetchResponse<ComboSpuView>>({
    url: '/api/combo-spu/-',
    params: {
      ...params.pagination,
      ...params.searchParams
    }
  })
}

export async function getComboSpu(id?: string | null) {
  if (!id) {
    return null
  }
  return await apiClient.get<ComboSpuView>({
    url: `/api/combo-spu/${id}`
  })
}

export async function selectComboCustomerSpu(customerId: string | null): Promise<ComboSpuView[]> {
  return await apiClient.get({
    url: '/api/combo-spu/-/select',
    params: {
      customerId
    }
  })
}