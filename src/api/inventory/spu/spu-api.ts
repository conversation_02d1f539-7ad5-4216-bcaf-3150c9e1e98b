import { SpuPageResponse, SpuSelectResponse, UpdateSpuRequest } from "@/api/inventory/spu/spu-model";
import { SkuResponse } from "@/api/inventory/sku/sku-model";
import apiClient from "@/lib/apiClient";
import { FetchParams, FetchResponse } from "@/components/table/base-table";


export async function pageSpu(
    params: FetchParams
): Promise<FetchResponse<SpuPageResponse>> {
    return await apiClient.get({
        url: '/api/spus/page',
        params: {
            ...params.pagination,
            ...params.searchParams
        }
    })
}

export async function findSpu(id: string | null) {
    if (id == null) return null
    return await apiClient.get({
        url: '/api/spus/' + id,
    })
}

export async function deleteSpu(id: string) {
    return await apiClient.delete({
        url: '/api/spus/' + id
    })
}

export async function createSpu(request: UpdateSpuRequest) {
    return await apiClient.post({
        url: '/api/spus',
        data: request
    })
}

export async function updateSpu(id: string, request: UpdateSpuRequest) {
    return await apiClient.put({
        url: '/api/spus/' + id,
        data: request
    })
}

export async function findSkus(spuId: string): Promise<SkuResponse[]> {
    return await apiClient.get({
        url: '/api/spus/' + spuId + '/skus/-',
    })
}

export async function selectSpu(supplierId: string | null): Promise<SpuSelectResponse[]> {
    const param = supplierId ? 'supplierId=' + supplierId : ''
    return await apiClient.get({
        url: '/api/spus/select/list?' + param,
    })
}


export async function selectCustomerSpu(customerId: string | null): Promise<SpuSelectResponse[]> {
    const param = customerId ? 'customerId=' + customerId : ''
    return await apiClient.get({
        url: '/api/spus/select/list/customer?' + param,
    })
}


export async function downloadSpuExcel({ ids }: { ids: string[] }) {
    return await apiClient.post({
        url: '/api/spus/export-spu',
        data: { ids },
        responseType: 'blob',  // This is crucial for handling binary data
    })
}

export async function importSpuExcel(file: File) {
    const formData = new FormData();
    formData.append('file', file);

    return await apiClient.post({
        url: '/api/spus/import-spu',
        data: formData,
        headers: {
            'Content-Type': 'multipart/form-data'
        }
    });
}
