import { Fetch<PERSON>ara<PERSON>, FetchResponse } from "@/components/table/base-table";
import apiClient from "@/lib/apiClient";

export enum BusinessType {
    TRAIL = "TRAIL",
    SUBSCRIPTION = "SUBSCRIPTION",
    INNER = "INNER",
}

export enum PricingType {
    YEARLY = "YEARLY",
    QUARTERLY = "QUARTERLY",
    MONTHLY = "MONTHLY",
    UNDEFINED = "UNDEFINED",
}

export interface UserInfo {
    id: string;
    name: string;
    account: string;
    roleId: string | null;
    permissionNames: string[];
    supplierId: string | null;
    pricingType: PricingType;
    bizName: string;
    bizType: BusinessType;
    subscribeFrom: string;
    subscribeTo: string;
}

export const userOnNotSupplier = (user: UserInfo | null) => {
    if (user == null) {
        return false;
    }
    return user.supplierId == undefined || user.supplierId == null;
}




export type CreateUserCmd = {
    name: string;
    roleId?: string;
    account: string;
    password: string;
    supplierName?: string;
    supplierDescription?: string;
    supplierPhone?: string;
}


export interface User {
    id: string;
    name: string;
    account: string;
    password: string;
    bizId: string;
    roleId: string;
    roleName: string;
    status: number;
    supplierId?: string;
    createdAt?: string;
}

export async function createUser(request: CreateUserCmd): Promise<void> {
    return await apiClient.post({
        url: "/api/v1/users",
        data: request
    });
}


export async function loginUser(_account: string, _password: string): Promise<string> {
    return await apiClient.post({
        url: "/api/v1/users/login",
        data: {
            account: _account,
            password: _password
        }
    })
}

export async function registerUser(params: any) {
    return await apiClient.post({
        url: "/api/v1/users",
        data: params
    })
}


export async function logoutUser(): Promise<void> {
    return await apiClient.post({
        url: "/api/v1/users/logout"
    })
}


export async function getUserInfo(): Promise<UserInfo> {
    return await apiClient.get({
        url: "/api/v1/users/info"
    })
}

export async function changePassword(_oldPassword: string, _newPassword: string): Promise<void> {
    return await apiClient.post({
        url: "/api/v1/users/passwd:change",
        data: {
            oldPassword: _oldPassword,
            newPassword: _newPassword
        }
    })
}

export async function changeRole(userId: string, roleId: string): Promise<void> {
    return await apiClient.post({
        url: `/api/v1/users/role:change`,
        data: { userId, roleId }
    })
}

export async function pageUser(params: FetchParams): Promise<FetchResponse<User>> {
    return await apiClient.get({
        url: "/api/v1/users/page",
        params: {
            ...params.pagination,
        }
    })
}


export async function deleteUsers(userIds: string[]): Promise<void> {
    return await apiClient.delete({
        url: "/api/v1/users/batch",
        data: userIds
    })
}