import { FetchResponse } from "@/components/table/base-table";
import { FetchParams } from "@/components/table/base-table";
import apiClient from "@/lib/apiClient";


export interface Role {
    id?: string;
    name: string;
    bizId: string;
    permissions: string[];
    permissionNames: string[];
    status: number;
    limit: RoleLimit;
    createdAt?: string;
}

export enum RoleLimit {
    SELF = 'SELF',
    ALL = 'ALL',
}

export type CreateRoleCmd = {
    name: string;
    limit: RoleLimit;
    permissions: string[];
}


export async function pageRole(params: FetchParams): Promise<FetchResponse<Role>> {
    return await apiClient.get({
        url: "/api/v1/roles/page",
        params: {
            ...params.pagination,
        }
    })
}


export async function getRoleList(): Promise<Role[]> {
    return await apiClient.get({
        url: "/api/v1/roles",
    })
}


export async function createRole(cmd: CreateRoleCmd): Promise<void> {
    await apiClient.post({
        url: "/api/v1/roles",
        data: cmd,
    })
}


export async function deleteRole(id: string): Promise<void> {
    await apiClient.delete({
        url: `/api/v1/roles/${id}`,
    })
}


export type UpdateRoleCmd = {
    id: string,
    name: string;
    permissions: string[];
}

export async function updateRole(cmd: UpdateRoleCmd): Promise<void> {
    await apiClient.put({
        url: `/api/v1/roles/${cmd.id}`,
        data: cmd,
    })
}