import { FetchParams, FetchResponse } from "@/components/table/base-table";
import apiClient from "@/lib/apiClient";


interface Permission {
  id: string;
  name: string;
  description: string;
  resource: string;
  parentId: string;
  type: string;
  icon: string;
}

interface PermissionTreeNode extends Permission {
  children?: PermissionTreeNode[];
}

function buildPermissionTree(permissions: Permission[]): PermissionTreeNode[] {
  const permissionMap = new Map<string, PermissionTreeNode>();
  const rootNodes: PermissionTreeNode[] = [];

  // 首先将所有权限转换为树节点并存储在 Map 中
  permissions.forEach(permission => {
    permissionMap.set(permission.id, {
      ...permission,
      children: []
    });
  });

  // 构建树结构
  permissions.forEach(permission => {
    const node = permissionMap.get(permission.id);
    if (permission.parentId === '0') {
      rootNodes.push(node!);
    } else {
      const parentNode = permissionMap.get(permission.parentId.toString());
      if (parentNode) {
        parentNode.children = parentNode.children || [];
        parentNode.children.push(node!);
      }
    }
  });
  return rootNodes;
}

async function getPermissionList(): Promise<Permission[]> {
  return  await apiClient.get({
    url: "/api/v1/permissions/search",
  })
}

async function pagePermission(params: FetchParams): Promise<FetchResponse<Permission>> {
  return await apiClient.get({
    url: "/api/v1/permissions/page",
    params: {
      ...params.pagination,
    }
  })
}

export async function updatePermissions(permissionIds: string[]) {
  const response = await fetch('/api/permissions', {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({ permissionIds }),
  });

  if (!response.ok) {
    throw new Error('Failed to update permissions');
  }

  return response.json();
}

export {
  type Permission,
  type PermissionTreeNode,
  getPermissionList,
  buildPermissionTree,
  pagePermission
};
