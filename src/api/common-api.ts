import apiClient from "@/lib/apiClient";


export type DictType = 'color' | 'size' | 'spu-category'

export interface Country {
    id: string;
    countryName: string;
    isoAlphaTwo: string;
    isoAlphaThree: string;
    createdAt: string;
    updatedAt: string;
    createdByName: string;
    updatedByName: string;
    states: State[];
}

export interface State {
    enName: string;
    cnName: string;
    isoAlphaTwo: string;
}



export async function getDictValue(type: DictType) {
    return await apiClient.get({
        url: '/api/common/dict/value?type=' + type,
    })
}


interface SystemNameContainer {
    userNames: { [key: string]: string };
    supplierNames: { [key: string]: string };
    customerNames: { [key: string]: string };
}

export async function getDictName(): Promise<SystemNameContainer> {
    return await apiClient.get({
        url: '/api/common/dict/names',
    })
}
export async function addDictValue(type: string, value: string) {
    return await apiClient.put({
        url: '/api/common/dict/value',
        params: {
            type,
            value
        }
    })
}

export async function exportPdf(url: string) {
    return await apiClient.get({
        url: '/api/file/proxy-pdf?url=' + url,
        responseType: 'blob'
    })
}


export async function findCountries(): Promise<Country[]> {
    return await apiClient.get<Country[]>({
        url: '/api/common/countries',
    })
}