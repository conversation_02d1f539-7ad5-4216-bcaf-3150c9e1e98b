import apiClient from "@/lib/apiClient";
import { Channel } from "./channel-model";

export interface UpdateChannelRequest {
    displayName: string;
    methodCode: string;
    methodName: string;
    enabled: boolean;
    iossNumber?: string;
}

export async function getChannels(): Promise<Channel[]> {
    return await apiClient.get({
        url: '/api/channels/list',
    });
}

export async function getChannel(id: string): Promise<Channel> {
    return await apiClient.get({
        url: `/api/channels/${id}`,
    });
}

export async function updateChannel(id: string, data: UpdateChannelRequest): Promise<Channel> {
    return await apiClient.put({
        url: `/api/channels/${id}`,
        data: data,
    });
}