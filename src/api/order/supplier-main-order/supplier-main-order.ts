import {FetchParams, FetchResponse} from "@/components/table/base-table"
import apiClient from "@/lib/apiClient"


export interface SupplierMainOrder {
    id: string
    supplierId: string
    supplierName: string
    mainOrderId: string
    fileName: string
    bizId: string
    createdAt?: string
    updatedAt?: string
    accepted: boolean
    attachmentUrls: string[]
}




export const supplierMainOrderApi = {

    pageSupplierMainOrder: (params: FetchParams) => {
        return apiClient.get<FetchResponse<SupplierMainOrder>>({
            url: '/api/supplier-main-orders/page',
            params: {
                ...params.pagination,
                ...params.searchParams,
            }
        })
    },

    changeSupplier: (orderId: string, newSupplierId: string) => {
        return apiClient.post({
            url: '/api/supplier-main-orders/change-supplier',
            data: {
                orderId,
                newSupplierId
            }
        })
    },

    exportBill: (params: { createdAtFrom: string, createdAtTo: string, supplierIds: string[] }) => {
        return apiClient.post({
            url: '/api/supplier-main-orders/export-bill',
            data: params,
        })
    },

    exportBill2: (params: { supplierMainOrderIds: string[] }) => {
        return apiClient.post({
            url: '/api/supplier-main-orders/export-bill2',
            data: params,
        })
    },

    acceptSupplierMainOrder: (id: string) => {
        return apiClient.post<FetchResponse<SupplierMainOrder>>({
            url: `/api/supplier-main-orders/accept/${id}`,
        })
    },

    uploadAttachment: (id: string, file: File) => {
        const formData = new FormData();
        formData.append('file', file);
        return apiClient.post<string>({
            url: `/api/supplier-main-orders/${id}/upload-attachment`,
            data: formData,
            headers: {
                'Content-Type': 'multipart/form-data'
            }
        })
    }
}