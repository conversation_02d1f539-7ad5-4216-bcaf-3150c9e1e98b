import { Fetch<PERSON>arams, FetchResponse } from '@/components/table/base-table'
import apiClient from '@/lib/apiClient'
import axios from 'axios'

// 定义状态枚举
export const MainOrderStatus = {
    CREATED: 'CREATED',
    COMPLETED: 'COMPLETED',
    MATCHING: 'MATCHING',
    PARTIALLY_MATCHED: 'PARTIALLY_MATCHED',
    MATCHED: 'MATCHED',
    CANCELD: 'CANCELD',
    FAILED: 'FAILED',
} as const

export const MainOrderImageDownloadStatus = {
    IMAGE_DOWNING: 'IMAGE_DOWNING',
    IMAGE_DOWNED: 'IMAGE_DOWNED',
    ZIP_DOING: 'ZIP_DOING',
    ZIP_DONE: 'ZIP_DONE',
    FAILED: 'FAILED',
} as const

export type MainOrderStatus = typeof MainOrderStatus[keyof typeof MainOrderStatus]
export type MainOrderImageDownloadStatus = typeof MainOrderImageDownloadStatus[keyof typeof MainOrderImageDownloadStatus]
type FailedOverView = {
    errorMessage: string
    orderNos: string
}
type DownloadTaskCount = {
    created: number
    processing: number
    completed: number
    failed: number
}

export enum MainOrderType {
    IMAGE = 'IMAGE',
    ORDER = 'ORDER',
}
// 定义接口
export interface MainOrder {
    id: string
    fileName: string
    type: MainOrderType
    status: MainOrderStatus
    imgDownloadStatus: MainOrderImageDownloadStatus
    imgZipDownloadUrl: string
    downloadTask: DownloadTaskCount
    customerName: string
    createdByName: string
    updatedByName: string
    updatedAt: string
    createdAt: string
    wayBillPushed: boolean
    supplierPushed: boolean
    supplierNotified: boolean
    attachmentUrls: string[]
}

export interface CreateMainOrderRequest {
    fileName: string
}

export interface UpdateMainOrderRequest {
    fileName?: string
    status?: MainOrderStatus
    imgDownloadStatus?: MainOrderImageDownloadStatus
}

export interface PageResponse<T> {
    content: T[]
    totalElements: number
    totalPages: number
    size: number
    number: number
}

export interface FinanceStatisticResponse {
    id: string
    createdAt: string
    fileName: string
    totalCustomerNeedPayCost: number;
    totalCustomerNeedPayTax: number;
    totalQuickChannelTax: number;
    totalWaybillCost: number;
}

export interface MainOrderStatisticsResponse {
    mainOrderStatus: string
    totalSubOrders: number
    completedSubOrders: number
    subOrderStatusCounts: Record<string, number>
}

// API 方法
export const mainOrderApi = {
    // 获取分页列表
    async findAll(searchParams: FetchParams): Promise<FetchResponse<MainOrder>> {
        return await apiClient.get({
            url: '/api/main-orders',
            params: {
                ...searchParams.pagination,
                ...searchParams.searchParams,
            },
        })
    },

    // 根据ID获取详情
    async findById(id: number): Promise<MainOrder> {
        const { data } = await axios.get<MainOrder>(`/api/main-orders/${id}`)
        return data
    },

    // 创建订单
    async create(request: CreateMainOrderRequest): Promise<MainOrder> {
        const { data } = await axios.post<MainOrder>('/api/main-orders', request)
        return data
    },

    // 更新订单
    async update(id: number, request: UpdateMainOrderRequest): Promise<MainOrder> {
        const { data } = await axios.put<MainOrder>(`/api/main-orders/${id}`, request)
        return data
    },

    // 删除订单
    async deleteById(id: number): Promise<void> {
        await axios.delete(`/api/main-orders/${id}`)
    },



    async downloadTaskOverview(id: string): Promise<{
        statusMap: Record<string, number>,
        failedOverView: FailedOverView[]
    }> {
        return await apiClient.get({
            url: `/api/main-orders/download-task/overview/${id}`,
        })
    },

    async retryMainOrderFailedDownloadTask(id: string): Promise<void> {
        await apiClient.post({
            url: `/api/main-orders/download-task/${id}/retry`,
        })
    },

    async compressMainOrder(id: string): Promise<void> {
        await apiClient.post({
            url: `/api/main-orders/download-task/${id}/compress`,
        })
    },

    async deleteMainOrder(id: string): Promise<void> {
        await apiClient.delete({
            url: `/api/main-orders/${id}`,
        })
    },

    async supplyPush(orderId: string): Promise<void> {
        return await apiClient.post({
            url: '/api/main-orders/' + orderId + '/supply:push',
        })
    },

    async supplyNtfy(orderId: string): Promise<void> {
        return await apiClient.post({
            url: '/api/main-orders/' + orderId + '/supply:ntfy',
        })
    },

    async wayBillPush(orderId: string): Promise<void> {
        return await apiClient.post({
            url: '/api/waybills/push/' + orderId,
        })
    },

    async pageFinanceStatistic(searchParams: FetchParams): Promise<FetchResponse<FinanceStatisticResponse>> {
        return await apiClient.get({
            url: '/api/main-orders/page/financial',
            params: {
                ...searchParams.pagination,
                ...searchParams.searchParams
            },
        })
    },

    // 获取统计数据
    async getStatistics(mainOrderId: string): Promise<MainOrderStatisticsResponse> {
        return await apiClient.get({
            url: `/api/main-orders/${mainOrderId}/statistics`
        })
    }
}
