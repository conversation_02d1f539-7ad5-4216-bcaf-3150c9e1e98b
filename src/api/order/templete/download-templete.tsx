import apiClient from '@/lib/apiClient'
export enum BaseStatus {
    ENABLED = 'ENABLED',
    DISABLED = 'DISABLED'
}

export enum TemplateType {
    IMAGE = 'IMAGE',
    ORDER = 'ORDER'
}

export interface DownloadExcelColumn {
    name: string
    type: string
}

export interface DownloadTemplate {
    id: string
    name: string
    type: TemplateType
    content: DownloadExcelColumn[]
    onDefault: boolean
    status: BaseStatus
    createdAt: string
    updatedAt: string
    createdBy: string
    updatedBy: string
}

// 创建模板请求参数
export interface CreateDownloadTemplateRequest {
    name: string
    type: TemplateType
    content: DownloadExcelColumn[]
    status?: BaseStatus
}

// 更新模板请求参数
export interface UpdateDownloadTemplateRequest {
    id: string
    name?: string
    content?: DownloadExcelColumn[]
    status?: BaseStatus
}




// 分页查询
export async function queryDownloadTemplate(name?: string, type?: TemplateType): Promise<DownloadTemplate[]> {
    return await apiClient.get({
        url: '/download-templates/all',
        params: {
            name,
            type
        }
    })
}

// 获取单个模板
export async function getDownloadTemplate(id: string | undefined): Promise<DownloadTemplate | null> {
    if (!id) {
        return null
    }
    return await apiClient.get({
        url: `/download-templates/${id}`
    })
}

// 创建模板
export async function createDownloadTemplate(data: CreateDownloadTemplateRequest): Promise<DownloadTemplate> {
    return await apiClient.post({
        url: '/download-templates',
        data
    })
}

// 更新模板
export async function updateDownloadTemplate(data: UpdateDownloadTemplateRequest): Promise<DownloadTemplate> {
    return await apiClient.put({
        url: `/download-templates/${data.id}`,
        data
    })
}

// 删除模板
export async function deleteDownloadTemplate(id: string): Promise<void> {
    return await apiClient.delete({
        url: `/download-templates/${id}`
    })
}


export async function downloadTemplate(id: string) {
    return await apiClient.get({
        url: `/download-templates/download/template/${id}`,
        responseType: 'blob'
    })
}

export async function setDefaultTemplate(id: string) {
    return await apiClient.put({
        url: `/download-templates/${id}/star`
    })
}