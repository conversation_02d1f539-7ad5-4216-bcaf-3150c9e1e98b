import { FetchResponse } from "@/components/table/base-table";
import apiClient from "@/lib/apiClient";


export interface OrderImageTask {
    id: string;
    orderId: string;
    downloadUrl: string;
    tag: string;
    errorMessage: string;
    fileId: string;
    uploadUrl: string;
    status: 'CREATED' | string; // 假设 'CREATED' 是一个可能的状态值，其他状态值也可以添加
    createdAt: string;
    orderNo: string;
}

export async function pageQueryOrderImageTask(searchParams: any): Promise<FetchResponse<OrderImageTask>> {
    return await apiClient.get({
        url: '/order-image-tasks/download/page',
        params: {
            ...searchParams.pagination,
            ...searchParams.searchParams,
        },
    })
}

export async function retryDownload(id: string): Promise<void> {
    return await apiClient.post({
        url: `/order-image-tasks/download/${id}/retry`
    })
}

