import { FetchParams, FetchResponse } from "@/components/table/base-table";
import apiClient from "@/lib/apiClient";
import { Product, Recipient, Shipping } from "../order-model-type";


export type SubOrder = {
    id: string;
    designUrl: string | null,
    effectUrl: string | null,
    designImgSearchFiled: string | null,
    effectImgSearchFiled: string | null,
    parentId: string;
    fileName: string;
    orderNo: string;
    recipient: Recipient;
    product: Product;
    shipping: Shipping;
    customerId: string;
    status: SubOrderStatus;
    bizId: string;
    waybillStatus: string;
    supplierOrderStatus: string;
    orderDownloadTaskIds: string[];
    createdAt: string;
    updatedAt: string;
    createdBy: string;
    updatedBy: string;
    orderNos: string;
    failedReason: string | null;
    customerOrderNo: string;
}


export type SubOrderStatus = 'CREATED' | 'CANCELLED' | 'COMPLETED' | 'SPLIT' | 'SUPPLIER_MATCHED' | 'FAILED' |
    // 轨迹相关状态
    'TRACKING_NOT_FOUND' | 'TRACKING_PRE_ADVICE_RECEIVED' | 'TRACKING_PICKED_UP' | 'TRACKING_IN_TRANSIT' |
    'TRACKING_ARRIVED_DESTINATION_COUNTRY' | 'TRACKING_IN_CUSTOMS' | 'TRACKING_CUSTOMS_CLEARED' |
    'TRACKING_ARRIVED_FOR_PICKUP' | 'TRACKING_OUT_FOR_DELIVERY' | 'TRACKING_DELIVERY_FAILED' |
    'TRACKING_DELIVERED' | 'TRACKING_EXCEPTION' | 'TRACKING_RETURNED' | 'TRACKING_CANCELLED' | 'TRACKING_UNKNOWN'


export async function pageQuerySubOrder(params: FetchParams): Promise<FetchResponse<SubOrder>> {
    return await apiClient.post({
        url: '/api/sub-order/page',
        params: {
            ...params.pagination,
            ...params.searchParams,
        },
        data: {
            ...params.filters
        }
    })
}

export async function importSubOrders(customerId: string, file: File): Promise<void> {
    const formData = new FormData();
    formData.append('file', file);
    return await apiClient.post({
        url: '/api/pics/upload?customerId=' + customerId + '&fileName=' + file.name,
        data: formData,
        headers: {
            'Content-Type': 'multipart/form-data'
        }
    })
}

export async function updateSubOrder(
    id: string,
    updateRequest: {
        designUrl: string | null,
        effectUrl: string | null,
        designImgSearchFiled: string | null,
        effectImgSearchFiled: string | null,
        color: string | null,
        size: string | null,
    }
) {
    return await apiClient.post({
        url: '/api/sub-order/update',
        data: {
            id,
            ...updateRequest,
        }
    })
}

export async function retryDownload(subOrderId: string) {
    return await apiClient.post({
        url: '/api/sub-order/' + subOrderId + '/retry/download',
    })
}

export async function cancelSubOrder(subOrderId: string) {
    return await apiClient.post({
        url: '/api/sub-order/' + subOrderId + '/:cancel',
    })
}

export async function deleteSubOrder(subOrderId: string) {
    return await apiClient.delete({
        url: '/api/sub-order/' + subOrderId,
    })
}

export async function batchDeleteSubOrder(subOrderIds: string[]) {
    return await apiClient.delete({
        url: '/api/sub-order/batch',
        data: subOrderIds
    })
}

export async function mergeSubOrders(subOrderIds: string[]) {
    return await apiClient.post({
        url: '/api/sub-order/merge',
        data: {
            ids: subOrderIds
        }
    })
}

export async function getSubOrderStatusCount(select: string): Promise<any[]> {
    return await apiClient.get({
        url: '/api/sub-order/day/status/count',
        params: {
            select: select
        }
    })
}


export async function subOrderSelectSupplier(supplierId: string, subOrderIds: string[]): Promise<any> {
    return await apiClient.post({
        url: '/api/sub-order/select/supplier',
        data: {
            supplierId: supplierId,
            subOrderIds: subOrderIds
        }
    })
}


export async function removeImgs(subOrderId: string, imgUrl: string) {
    await apiClient.post({
        url: '/api/sub-order/remove/img/' + subOrderId,
        params: {
            imgUrl
        }
    })
}


export async function getSubOrderImages(orderNo: string): Promise<string[]> {
    return await apiClient.get({
        url: `/api/sub-order/${orderNo}/img-urls`,
    })
}


export async function deleteSubOrders(subOrderIds: string[]) {
    return await apiClient.delete({
        url: '/api/sub-order/batch',
        data: subOrderIds
    })
}


export async function changeSubOrdersChannel(subOrderIds: string[], channel: string, method: string) {
    return await apiClient.post({
        url: '/api/sub-order/channel:change',
        data: {
            ids: subOrderIds,
            channel,
            method
        }
    })
}

export async function fetchMergedOrder(orderId: string): Promise<SubOrder[]> {
    return await apiClient.get({
        url: `/api/sub-order/${orderId}/merged-orders`,
    })
}


export async function splitOrder(data: Record<string, number>[]) {
    return await apiClient.post({
        url: `/api/sub-order/split`,
        data
    })
}

export async function fetchSplitOrder(orderId: string): Promise<SubOrder[]> {
    return await apiClient.get({
        url: `/api/sub-order/${orderId}/split-orders`,
    })
}


export async function retryMatchSupplier(id: string) {
    return await apiClient.post({
        url: `/api/sub-order/${id}/match-supplier`,
    })
}