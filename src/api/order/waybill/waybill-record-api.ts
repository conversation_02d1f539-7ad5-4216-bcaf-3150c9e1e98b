import apiClient from "@/lib/apiClient"





const base = '/api/waybill/remote-call/'


export type WaybillRecord = {
    id: string
    waybillId: string
    waybillNo: string
    waybillLabelUrl: string
    channel: string | null
    shipMethod: string | null
    createdAt: string
}


export const waybillRecordApi = {


    list: async (waybillId: string): Promise<WaybillRecord[]> => {
        return await apiClient.get({
            url: base + 'list/' + waybillId,
        })
    }
}