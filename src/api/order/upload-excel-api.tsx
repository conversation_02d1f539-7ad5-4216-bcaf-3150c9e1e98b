import apiClient from "@/lib/apiClient";



export async function uploadImgDownloadExcel(
    file: File,
    templateId: string,
    selectedUser: string,
    action: string,
    needAuthorization: boolean,
    namingType: string,
    urlSplitToken: string,
    needDownloadImage: boolean,
    attachments?: File[]
) {
    const formData = new FormData();
    formData.append('file', file);
    formData.append('templateId', templateId);
    formData.append('selectedUser', selectedUser);
    formData.append('action', action);
    formData.append('needAuthorization', needAuthorization.toString());
    formData.append('namingType', namingType);
    formData.append('urlSplitToken', urlSplitToken);
    formData.append('needDownloadImage', needDownloadImage.toString());

    // 添加附件
    if (attachments && attachments.length > 0) {
        attachments.forEach(attachment => {
            formData.append('attachments', attachment);
        });
    }

    return await apiClient.post<string>({
        url: '/excel/upload',
        data: formData,
        headers: {
            'Content-Type': 'multipart/form-data'
        }
    });
}