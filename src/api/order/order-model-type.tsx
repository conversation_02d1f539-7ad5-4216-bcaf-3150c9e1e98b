export type Recipient = {
    userName: string;
    receiverName: string;
    country: string;
    state: string;
    city: string;
    address1: string;
    address2: string;
    postcode: string;
    phone: string;
    email: string;
}

export type Product = {
    spu: string;
    size: string;
    color: string;
    qty: number;
    spuId: string;
    skuId: string;
    weight: number;
    price: number;
    currency: string;
    name: string;
    cnName: string;
    title: string;
    supplierId: string | null;
    supplierName: string | null;
    detail: string;
    customName: string;
}

export type Shipping = {
    channel: string;
    shipMethod: string;
    wayBillRelation: string;
    deliveryMethod: string;
    waybillLabelUrl: string;
}