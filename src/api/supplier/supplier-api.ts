import {
    CreateSupplierRequest,
    SupplierPageResponse,
    SupplierSelectedResponse,
    UpdateSupplierRequest
} from "@/api/supplier/supplier-model";
import { FetchParams, FetchResponse } from "@/components/table/base-table";
import apiClient from "@/lib/apiClient";


export async function pageSupplier(params: FetchParams): Promise<FetchResponse<SupplierPageResponse>> {
    return await apiClient.get({
        url: '/api/suppliers/page?' ,
        params: {
            ...params.pagination,
            ...params.searchParams
        }
    })
}


export async function findSupplier(id: string | null) {
    if (id == null) return null
    return await apiClient.get({
        url: '/api/suppliers/' + id,
    })
}


export async function deleteSupplier(id: string) {
    return await apiClient.delete({
        url: '/api/suppliers/' + id
    })
}

export async function createSupplier(request: CreateSupplierRequest) {
    return await apiClient.post({
        url: '/api/suppliers',
        data: request
    })
}

export async function updateSupplier(id: string, request: UpdateSupplierRequest) {
    return await apiClient.put({
        url: '/api/suppliers/' + id,
        data: request
    })
}

export async function enableSupplier(id: string) {
    return await apiClient.post({
        url: '/api/suppliers/' + id + '/enable',
    })
}

export async function disableSupplier(id: string) {
    return await apiClient.post({
        url: '/api/suppliers/' + id + '/disable',
    })
}

export async function selectSupplier(): Promise<SupplierSelectedResponse[]> {
    return await apiClient.get({
        url: '/api/suppliers/select/list',
    })
}


export async function updateSupplierPriority(id: string, priority: number) {
    return await apiClient.post({
        url: '/api/suppliers/priority:change',
        data: {
            id: id,
            priority: priority
        }
    })
}