export type SupplierOrderPageResponse = {
	id: string;
	subOrderId: string;
	supplierId: string;
	waybillId: string;
	orderNo: string;
	size: string;
	color: string;
	qty: string;
	address1: string;
	address2: string;
	country: string;
	state: string;
	city: string;
	postcode: string;
	channel: string;
	shipMethod: string;
	shipMethodAlias: string;
	phone: string;
	errorMsg: string;
	status: string;
	failAtStatus: string;
	updatedByName: string;
	updatedAt: string;
	createdByName: string;
	createdAt: string;
	waybillLabelUrl: string;
	skuCode: string;
	productImageUrls: string[];
	weight?: number;
	price?: number;
	spu?: string;
	fileName?: string;
	mainOrderId?: string;
	title?: string;
	supplierName?: string;
	canPrintWayBillPdf?: boolean;
	stopPrintWayBillWarning?: string;
	scanAt?: string;
}

export enum DownloadFileStatus {
	PROCESSING = 'PROCESSING',
	SUCCESS = 'SUCCESS',
	FAILED = 'FAILED'
}

export type DownloadFile = {
	id: string;
	fileName: string;
	zipUrl: string;
	type: string;
	fileUrl: string;
	wayBillPdfMergeUrl: string;
	bizId: string;
	userId: string;
	status: DownloadFileStatus;
	createdAt: string;
	updatedAt: string;
}


export type DownloadSupplierOrderRequest = {
	fileName: string;
	ids: string[];
}