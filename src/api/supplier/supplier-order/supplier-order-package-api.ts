import { FetchParams, FetchResponse } from "@/components/table/base-table";
import apiClient from "@/lib/apiClient";

// Enum for PackageStatus
export enum PackageStatus {
    CREATED = "CREATED",
    PROCESSING = "PROCESSING",
    COMPLETED = "COMPLETED",
}

// Interface for SameOrderPackageOrder
export interface SameOrderPackageOrder {
    orderNo: string;
    title: string;
    count: number;
    scanCount: number;
    onComplete: boolean;
    //是否是套装订单
    onSet: boolean;
}

// Interface for SamePackageOrder
export interface SamePackageOrder {
    index: number;
    wayBillRelation: string;
    wayBillUrl: string;
    canPrintWayBillPdf: boolean;
    stopPrintWayBillWarning: string;
    shipMethod: string;
    channel: string;
    completed: Record<string, SameOrderPackageOrder>; // orderNo to SameOrderPackageOrder
    nonCompleted: Record<string, SameOrderPackageOrder>; // orderNo to SameOrderPackageOrder
    totalCount: number;
    scanCount: number;
    onComplete: boolean;
}

export const orderNoAndQuantity = (samePackageOrder: SamePackageOrder | null | undefined): string[] => {
    // 添加空值检查，防止访问 undefined 对象的属性
    if (!samePackageOrder) {
        return [];
    }

    const completedOrderNoAndQuantity = Object.entries(samePackageOrder.completed || {}).map(([orderNo, sameOrderPackageOrder]) => {
        return `${orderNo}*${sameOrderPackageOrder.count}`;
    });

    const nonCompletedOrderNoAndQuantity = Object.entries(samePackageOrder.nonCompleted || {}).map(([orderNo, sameOrderPackageOrder]) => {
        return `${orderNo}*${sameOrderPackageOrder.count}`;
    });

    return [...completedOrderNoAndQuantity, ...nonCompletedOrderNoAndQuantity];
}

// Interface for SupplierOrderPackage
export interface SupplierOrderPackage {
    id: string;
    supplierId: string;
    packageNo: string;
    scanCount: number;
    packageContent: Record<string, SamePackageOrder>; // wayBillRelation to SamePackageOrder
    status: PackageStatus;
    updatedAt: string; // Unix timestamp for updated time
    updatedByName: string; // Name of the user who last updated
    createdAt: string; // Unix timestamp for created time
    createdByName: string; // Name of the user who created
}

export type ScanPackageResponse = {
    waybillUrl: string | undefined;
    index: number;
    canPrintWayBillPdf: boolean;
    stopPrintWayBillWarning: string;
    orderNos?: string[];
    packageContent: SamePackageOrder
}


export const supplierOrderPackageApi = {
    pageQuerySupplierOrderPackage: async (params: FetchParams): Promise<FetchResponse<SupplierOrderPackage>> => {
        return await apiClient.get({
            url: '/api/supplier-order/package/page',
            params: {
                ...params.pagination,
                ...params.searchParams
            }
        })
    },
    createSupplierOrderPackage: async (name: string) => {
        return await apiClient.post({
            url: '/api/supplier-order/package',
            data: {
                name
            }
        })
    },

    scanSupplierOrderPackage: async (orderNo: string, packageNo: string) => {
        return await apiClient.post<ScanPackageResponse>({
            url: '/api/supplier-order/package/' + packageNo + '/scan',
            params: {
                orderNo
            }
        })
    },

    forceCompleteBox: async (packageId: string, wayBillRelation: string) => {
        return await apiClient.post({
            url: `/api/supplier-order/package/${packageId}/force-complete-box`,
            params: {
                wayBillRelation
            }
        })
    },

    completeSupplierOrderPackage: async (packageNo: string) => {
        return await apiClient.post({
            url: '/api/supplier-order/package/' + packageNo + '/complete'
        })
    },

    getSupplierOrderPackageDetail: async (id: string) => {
        return await apiClient.get<SupplierOrderPackage>({
            url: '/api/supplier-order/package/' + id
        })
    },

    editSupplierOrderPackage: async (id: string, name: string) => {
        return await apiClient.put({
            url: '/api/supplier-order/package/' + id,
            data: {
                name
            }
        })
    }
}