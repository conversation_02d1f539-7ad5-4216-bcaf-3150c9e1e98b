import { DownloadFile, DownloadSupplierOrderRequest, SupplierOrderPageResponse } from "@/api/supplier/supplier-order/supplier-order-model";
import { FetchParams, FetchResponse } from "@/components/table/base-table";
import apiClient from "@/lib/apiClient";

export async function pageSupplierOrder(params: FetchParams): Promise<FetchResponse<SupplierOrderPageResponse>> {
    return await apiClient.post({
        url: '/api/supplier-orders/page',
        params: {
            ...params.pagination,
            ...params.searchParams
        },
        data: {
            ...params.filters
        }
    })
}

export async function failedPage(params: FetchParams): Promise<FetchResponse<SupplierOrderPageResponse>> {
    return await apiClient.get({
        url: '/api/supplier-orders/failed/page',
        params: {
            ...params.pagination,
            ...params.searchParams
        }
    })
}


export async function listSupplierOrder(wayBillRelation: string): Promise<SupplierOrderPageResponse[]> {
    return await apiClient.get({
        url: '/api/supplier-orders/list',
        params: {
            wayBillRelation: wayBillRelation,
        },
    })
}

export async function handleSupplierOrderException(orderId: string) {
    return await apiClient.post({
        url: '/api/supplier-orders/' + orderId + '/exception:handle',
    })
}


export async function batchSupplierOrderExceptionHandler(orderIds: string[]) {
    return await apiClient.post({
        url: '/api/supplier-orders/batch/exception:handle',
        data: orderIds
    })
}
export async function cancelSupplierOrder(orderId: string) {
    return await apiClient.post({
        url: '/api/supplier-orders/' + orderId + '/cancel',
    })
}


export async function completeSupplierOrder(orderId: string) {
    return await apiClient.post({
        url: '/api/supplier-orders/' + orderId + '/complete',
    })
}

export async function deliverySupplierOrder(orderId: string) {
    return await apiClient.post({
        url: '/api/supplier-orders/' + orderId + '/delivery',
    })
}

export async function pageDownloadFile(params: FetchParams): Promise<FetchResponse<DownloadFile>> {
    return await apiClient.get({
        url: '/api/supplier-orders/export-excel/page',
        params: {
            ...params.pagination,
            ...params.searchParams
        }
    })
}

export async function downloadSupplierOrder(request: DownloadSupplierOrderRequest) {
    return await apiClient.post({
        url: '/api/supplier-orders/export-excel',
        data: request,
    })
}



export async function downloadSupplierOrderLabelPdf(request: DownloadSupplierOrderRequest) {
    return await apiClient.post({
        url: '/api/supplier-orders/export-label',
        data: request,
        responseType: 'blob',
    })
}


export async function getSupplierOrder(mainOrderId: string): Promise<SupplierOrderPageResponse[]> {
    return await apiClient.get({
        url: '/api/supplier-orders/list/by-order',
        params: {
            mainOrderId: mainOrderId,
        },
    })
}
