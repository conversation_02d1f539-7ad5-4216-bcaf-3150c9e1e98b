export type SupplierSpuPageResponse = {
	id: string;
	bizId: string;
	branchId: string;
	supplierId: string;
	systemSpuId: string;
	systemSpuCode: string;
	supplierSpuCode: string;
	status: string;
	createdByName: string;
	createdAt: string;
	updatedByName: string;
	updatedAt: string;
	title: string;
}



export type CreateSupplierSpuRequest = {
	title: string;
	name: string;
	cnName: string;
	category: string;
	supplierId: string;
	systemSpuId: string;
	supplierSpuCode: string;
	skus: CreateSupplierSpuRequestSkus[];
}

export type CreateSupplierSpuRequestSkus = {
	systemSkuId: string;
	systemSkuCode: string;
	size: string;
	color: string;
	purchaseCost: number;
	purchaseCostCurrency: string;
}

export type SupplierSpuAndSkuResponse = {
	id: string;
	bizId: string;
	branchId: string;
	supplierId: string;
	systemSpuId: string;
	systemSpuCode: string;
	supplierSpuCode: string;
	status: string;
	title: string;
	name: string;
	cnName: string;
	category: string;
	skus: SupplierSpuAndSkuResponseSkus[];
}
export type SupplierSpuAndSkuResponseSkus = {
	id: string;
	bizId: string;
	size: string;
	color: string;
	branchId: string;
	supplierId: string;
	supplierSpuId: string;
	systemSpuId: string;
	systemSkuId: string;
	systemSkuCode: string;
	purchaseCost: number;
	purchaseCostCurrency: string;
	status: string;
}


export type UpdateSupplierSpuRequest = {
	skus: UpdateSupplierSpuRequestSkus[];
}

export type UpdateSupplierSpuRequestSkus = {
	id: number;
	systemSkuId: number;
	systemSkuCode: string;
	size: string;
	color: string;
	purchaseCost: number;
	purchaseCostCurrency: string;
}