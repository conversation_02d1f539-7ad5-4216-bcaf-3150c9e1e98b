import {
    CreateSupplierSpuRequest,
    SupplierSpuAndSkuResponse,
    SupplierSpuPageResponse,
    UpdateSupplierSpuRequest
} from "@/api/supplier/supplier-spu/supplier-spu-model";
import { FetchParams, FetchResponse } from "@/components/table/base-table";
import apiClient from "@/lib/apiClient";


export async function createSupplierSpu(req: CreateSupplierSpuRequest) {
    return await apiClient.post({
        url: "/api/supplier/spu",
        data: req
    })
}
export async function updateSupplierSpu(id: string, req: UpdateSupplierSpuRequest) {
    return await apiClient.put({
        url: "/api/supplier/spu/" + id,
        data: req
    })
}

export async function pageSupplierSpu(param: FetchParams): Promise<FetchResponse<SupplierSpuPageResponse>> {
    return await apiClient.get({
        url: '/api/supplier/spu/page',
        params: {
            ...param.searchParams,
            ...param.pagination,
        }
    })
}

export async function findSupplierSpu(id: string | null): Promise<SupplierSpuAndSkuResponse | null> {
    if (id == null) return null
    return await apiClient.get({
        url: '/api/supplier/spu/' + id,
    })
}

export async function deleteSupplierSpu(id: string) {
    return await apiClient.delete({
        url: '/api/supplier/spu/' + id
    })
}


export async function updateSupplierSpuStatus(id: string, status: string) {
    return await apiClient.post({
        url: '/api/supplier/spu/' + id + '/status:change?wantedStatus=' + status,
    })
}


export async function syncSupplierSpu(id: string) {
    return await apiClient.post({
        url: '/api/supplier/spu/' + id + '/sync',
    })
}