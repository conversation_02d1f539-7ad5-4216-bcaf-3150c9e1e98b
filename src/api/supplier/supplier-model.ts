export type SupplierPageResponse = {
    id: string;
    bizId: string;
    branchId: string;
    name: string;
    phone: string;
    description: string;
    status: string;
    createdBy: string;
    createdAt: string;
    updatedBy: string;
    updatedAt: string;
    priority: string;
    config: SupplierConfig;
}

export type SupplierConfig = {
    larkRobotWebhook?: string;
}

export type CreateSupplierRequest = {
    name: string;
    phone: string;
    description: string | undefined;
}

export type UpdateSupplierRequest = {
    name: string;
    phone: string;
    description: string | undefined;
    config: SupplierConfig;
}


export type SupplierSelectedResponse = {
    id: string;
    name: string
}