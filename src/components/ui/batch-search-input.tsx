import { But<PERSON> } from "@/components/ui/button"
import {
  <PERSON><PERSON>,
  <PERSON>alog<PERSON>ontent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import { Ta<PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { Textarea } from "@/components/ui/textarea"
import { cn } from "@/lib/utils"
import { Hash, Package2, Search, Truck } from "lucide-react"
import { useEffect, useState } from "react"

export interface BatchSearchOption {
  label: string
  value: string
  icon?: React.ReactNode
  placeholder?: string
}

interface BatchSearchInputProps {
  options?: BatchSearchOption[]
  className?: string
  onValueChange?: (type: string, value: string) => void
  defaultType?: string
  defaultValue?: string
  placeholder?: string
}

export function BatchSearchInput({
  options = [
    {
      label: "订单号",
      value: "orderNos",
      icon: <Hash className="h-4 w-4" />,
      placeholder: "请输入订单号，多个订单号用换行分隔..."
    },
    {
      label: "运单号",
      value: "waybillNos",
      icon: <Package2 className="h-4 w-4" />,
      placeholder: "请输入运单号，多个运单号用换行分隔..."
    },
    {
      label: "跟踪号",
      value: "trackingNumbers",
      icon: <Truck className="h-4 w-4" />,
      placeholder: "请输入跟踪号，多个跟踪号用换行分隔..."
    }
  ],
  className,
  onValueChange,
  defaultType,
  defaultValue = '',
  placeholder = "点击进行批量搜索..."
}: BatchSearchInputProps) {
  const [isModalOpen, setIsModalOpen] = useState(false)
  const [selectedType, setSelectedType] = useState(defaultType || options[0].value)
  const [inputValues, setInputValues] = useState<Record<string, string>>({})
  const [displayValue, setDisplayValue] = useState('')

  // 初始化输入值
  useEffect(() => {
    if (defaultValue && defaultType) {
      setInputValues(prev => ({
        ...prev,
        [defaultType]: defaultValue
      }))
      // 计算显示值
      const lines = defaultValue.split('\n').filter(line => line.trim())
      if (lines.length > 0) {
        const currentOption = options.find(opt => opt.value === defaultType)
        setDisplayValue(`${currentOption?.label || ''}: ${lines.length}项`)
      }
    }
  }, [defaultValue, defaultType, options])

  const handleModalOpen = () => {
    setIsModalOpen(true)
  }

  const handleTabChange = (value: string) => {
    setSelectedType(value)
  }

  const handleTextareaChange = (value: string) => {
    setInputValues(prev => ({
      ...prev,
      [selectedType]: value
    }))
  }

  const handleConfirm = () => {
    const currentValue = inputValues[selectedType] || ''
    const lines = currentValue.split('\n').filter(line => line.trim())

    // 更新显示值
    if (lines.length > 0) {
      const currentOption = options.find(opt => opt.value === selectedType)
      setDisplayValue(`${currentOption?.label || ''}: ${lines.length}项`)
    } else {
      setDisplayValue('')
    }

    // 调用回调
    onValueChange?.(selectedType, currentValue)
    setIsModalOpen(false)
  }

  const handleClear = () => {
    setInputValues({})
    setDisplayValue('')
    onValueChange?.('', '')
  }

  return (
    <>
      <div className={cn("relative", className)}>
        <Input
          placeholder={placeholder}
          value={displayValue}
          onClick={handleModalOpen}
          readOnly
          className="cursor-pointer pr-10"
        />
        <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
      </div>

      <Dialog open={isModalOpen} onOpenChange={setIsModalOpen}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>批量搜索</DialogTitle>
          </DialogHeader>

          <Tabs value={selectedType} onValueChange={handleTabChange} className="w-full">
            <TabsList className="grid w-full grid-cols-3">
              {options.map((option) => (
                <TabsTrigger key={option.value} value={option.value} className="flex items-center gap-2">
                  {option.icon}
                  {option.label}
                </TabsTrigger>
              ))}
            </TabsList>

            {options.map((option) => (
              <TabsContent key={option.value} value={option.value} className="mt-4">
                <div className="space-y-4">
                  <div className="flex items-center gap-2 text-sm text-muted-foreground">
                    {option.icon}
                    <span>批量输入{option.label}</span>
                  </div>
                  <Textarea
                    placeholder={option.placeholder}
                    value={inputValues[option.value] || ''}
                    onChange={(e) => handleTextareaChange(e.target.value)}
                    className="min-h-[200px] resize-none"
                  />
                  <div className="text-xs text-muted-foreground">
                    {inputValues[option.value] ?
                      `已输入 ${inputValues[option.value].split('\n').filter(line => line.trim()).length} 项` :
                      '请输入搜索内容，每行一个'}
                  </div>
                </div>
              </TabsContent>
            ))}
          </Tabs>

          <div className="flex justify-end space-x-2 mt-6">
            <Button variant="outline" onClick={handleClear}>
              清空
            </Button>
            <Button variant="outline" onClick={() => setIsModalOpen(false)}>
              取消
            </Button>
            <Button onClick={handleConfirm}>
              确定
            </Button>
          </div>
        </DialogContent>
      </Dialog>
    </>
  )
}
