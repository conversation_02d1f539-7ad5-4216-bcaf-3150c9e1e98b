"use client";

import { cn } from "@/lib/utils";
import { AnimatePresence, motion } from "framer-motion";
import { LucideIcon } from "lucide-react";
import * as React from "react";
import { useOnClickOutside } from "usehooks-ts";

interface Tab {
  title: string;
  icon: LucideIcon;
  type?: never;
  onClick?: () => void;
}

interface Separator {
  type: "separator";
  title?: never;
  icon?: never;
}

type TabItem = Tab | Separator;

interface ExpandableTabsProps {
  tabs: TabItem[];
  className?: string;
  activeColor?: string;
  onChange?: (index: number | null) => void;
}

const buttonVariants = {
  initial: {
    gap: 0,
    paddingLeft: ".5rem",
    paddingRight: ".5rem",
    scale: 1,
  },
  animate: (isExpanded: boolean) => ({
    gap: isExpanded ? ".5rem" : 0,
    paddingLeft: isExpanded ? "1rem" : ".5rem",
    paddingRight: isExpanded ? "1rem" : ".5rem",
    scale: isExpanded ? 1.05 : 1,
  }),
};

const spanVariants = {
  initial: {
    width: 0,
    opacity: 0,
  },
  animate: {
    width: "auto",
    opacity: 1,
  },
  exit: {
    width: 0,
    opacity: 0,
  }
};

const transition = {
  type: "spring",
  bounce: 0,
  duration: 0.3
};

export function ExpandableTabs({
  tabs,
  className,
  activeColor = "text-primary",
  onChange,
}: ExpandableTabsProps) {
  const [selected, setSelected] = React.useState<number | null>(null);
  const [hoveredIndex, setHoveredIndex] = React.useState<number | null>(null);
  const outsideClickRef = React.useRef(null);

  useOnClickOutside(outsideClickRef, () => {
    setSelected(null);
    onChange?.(null);
  });

  const handleSelect = (index: number) => {
    setSelected(index === selected ? null : index);
    onChange?.(index === selected ? null : index);
  };

  const Separator = () => (
    <div className="mx-1 h-[24px] w-[1.2px] bg-border" aria-hidden="true" />
  );

  return (
    <div className="fixed bottom-10 left-0 right-0 z-50 flex items-center justify-center p-4 pointer-events-none">
      <div
        ref={outsideClickRef}
        className={cn(
          "flex items-center justify-between gap-2 rounded-2xl border bg-background/80 backdrop-blur-md p-1.5 shadow-lg pointer-events-auto min-w-[250px]",
          "transition-all duration-10 ease-in-out",
          className
        )}
      >
        {tabs.map((tab, index) => {
          if (tab.type === "separator") {
            return <Separator key={`separator-${index}`} />;
          }

          const Icon = tab.icon;
          const isExpanded = hoveredIndex === index || selected === index;

          return (
            <motion.button
              key={tab.title}
              variants={buttonVariants}
              initial="initial"
              animate="animate"
              custom={isExpanded}
              onClick={() => {
                handleSelect(index);
                if ('onClick' in tab && tab.onClick) {
                  tab.onClick();
                }
              }}
              onMouseEnter={() => setHoveredIndex(index)}
              onMouseLeave={() => setHoveredIndex(null)}
              transition={transition}
              className={cn(
                "relative flex flex-1 items-center justify-center rounded-xl px-4 py-2 text-sm font-medium",
                selected === index
                  ? cn("bg-muted shadow-sm", activeColor)
                  : "text-muted-foreground hover:bg-muted/50 hover:text-foreground"
              )}
            >
              <div className="flex items-center justify-center">
                <Icon size={20} />
                <AnimatePresence initial={false}>
                  {isExpanded && (
                    <motion.span
                      variants={spanVariants}
                      initial="initial"
                      animate="animate"
                      exit="exit"
                      transition={transition}
                      className="overflow-hidden whitespace-nowrap"
                    >
                      {tab.title}
                    </motion.span>
                  )}
                </AnimatePresence>
              </div>
            </motion.button>
          );
        })}
      </div>
    </div>
  );
}