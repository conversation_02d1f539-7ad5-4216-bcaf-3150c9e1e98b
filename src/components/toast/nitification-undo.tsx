import { <PERSON><PERSON><PERSON><PERSON>, X } from "lucide-react";
import { useCallback, useEffect, useRef, useState } from "react";
import { toast } from "sonner";
import { Button } from "../ui/button";

interface UseProgressTimerProps {
    duration: number;
    interval?: number;
    onComplete?: () => void;
}

function useProgressTimer({ duration, interval = 100, onComplete }: UseProgressTimerProps) {
    const [progress, setProgress] = useState(duration);
    const timerRef = useRef(0);
    const timerState = useRef({
        startTime: 0,
        remaining: duration,
        isPaused: false,
    });

    const cleanup = useCallback(() => {
        window.clearInterval(timerRef.current);
    }, []);

    const reset = useCallback(() => {
        cleanup();
        setProgress(duration);
        timerState.current = {
            startTime: 0,
            remaining: duration,
            isPaused: false,
        };
    }, [duration, cleanup]);

    const start = useCallback(() => {
        const state = timerState.current;
        state.startTime = Date.now();
        state.isPaused = false;

        timerRef.current = window.setInterval(() => {
            const elapsedTime = Date.now() - state.startTime;
            const remaining = Math.max(0, state.remaining - elapsedTime);

            setProgress(remaining);

            if (remaining <= 0) {
                cleanup();
                onComplete?.();
            }
        }, interval);
    }, [interval, cleanup, onComplete]);

    const pause = useCallback(() => {
        const state = timerState.current;
        if (!state.isPaused) {
            cleanup();
            state.remaining = Math.max(0, state.remaining - (Date.now() - state.startTime));
            state.isPaused = true;
        }
    }, [cleanup]);

    const resume = useCallback(() => {
        const state = timerState.current;
        if (state.isPaused && state.remaining > 0) {
            start();
        }
    }, [start]);

    useEffect(() => {
        return cleanup;
    }, [cleanup]);

    return {
        progress,
        start,
        pause,
        resume,
        reset,
    };
}

export function ToastNotificationWithUndo({ id }: { id: number | string }) {
    const toastDuration = 50000;
    const [open, setOpen] = useState(false);
    const { progress, start, pause, resume, reset } = useProgressTimer({
        duration: toastDuration,
        onComplete: () => setOpen(false),
    });

    const handleOpenChange = useCallback(
        (isOpen: boolean) => {
            setOpen(isOpen);
            if (isOpen) {
                reset();
                start();
            }
        },
        [reset, start],
    );

    const handleButtonClick = useCallback(() => {
        if (open) {
            setOpen(false);
            window.setTimeout(() => {
                handleOpenChange(true);
            }, 150);
        } else {
            handleOpenChange(true);
        }
    }, [open, handleOpenChange]);

    useEffect(() => {
        handleButtonClick();
    }, [])

    return (
        <div
            className="bg-white rounded-full shadow-sm p-4"
            onMouseEnter={pause}
            onMouseLeave={resume}
        >
            <div className="flex w-full justify-between gap-3 items-start">
                <CircleCheck
                    className="mt-1 shrink-0 text-emerald-500"
                    size={16}
                    strokeWidth={2}
                    aria-hidden="true"
                />
                <div className="flex grow flex-col gap-3">
                    <div className="space-y-1">
                        <div className="text font-medium">Your request was completed!</div>
                        <div className="text-sm text-gray-500">
                            It demonstrates that the task or request has been processed.
                        </div>
                    </div>
                    <div className="flex justify-end">
                        <Button size="sm" onClick={() => toast.dismiss(id)}>Undo changes</Button>
                    </div>
                </div>
                <Button
                    variant="ghost"
                    className="group -my-1.5 -me-2 size-8 shrink-0 p-0 hover:bg-transparent"
                    aria-label="Close notification"
                    onClick={() => toast.dismiss(id)}
                >
                    <X
                        size={16}
                        strokeWidth={2}
                        className="opacity-60 transition-opacity group-hover:opacity-100"
                        aria-hidden="true"
                    />
                </Button>
            </div>
            <div className="contents" aria-hidden="true">
                <div
                    className="pointer-events-none absolute bottom-0 left-0 h-1 w-full bg-emerald-500"
                    style={{
                        width: `${(progress / toastDuration) * 100}%`,
                        transition: "width 100ms linear",
                    }}
                />
            </div>
        </div>
    )
}