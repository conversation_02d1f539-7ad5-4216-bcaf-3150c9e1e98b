import { <PERSON><PERSON> } from "@/components/ui/button";
import { ArrowRight, CircleAlert, CircleCheck, TriangleAlert, X } from "lucide-react";
import { toast } from "sonner";

export function DefaultSuccessNotification({ id, message, needLink = false }: { id: number | string, message: string, needLink?: boolean }) {
    return (
        <div className="z-[100] max-w-[500px] min-w-[350px] rounded-lg bg-background/95 backdrop-blur px-4 py-3 shadow-lg">
            <div className="flex gap-2">
                <div className="flex grow gap-3">
                    <CircleCheck
                        className="mt-[0.8px] shrink-0 text-emerald-500"
                        size={16}
                        strokeWidth={2}
                        aria-hidden="true"
                    />
                    <div className="flex grow justify-between gap-12">
                        <p className="text-xs">{message}</p>
                        {needLink && (
                            <a href="#" className="group whitespace-nowrap text-sm font-medium">
                                Link
                                <ArrowRight
                                    className="-mt-0.5 ms-1 inline-flex opacity-60 transition-transform group-hover:translate-x-0.5"
                                    size={16}
                                    strokeWidth={2}
                                    aria-hidden="true"
                                />
                            </a>
                        )}
                    </div>
                </div>
                <Button
                    variant="ghost"
                    className="group -my-1.5 -me-2 size-8 shrink-0 p-0 hover:bg-transparent"
                    aria-label="Close banner"
                    onClick={() => toast.dismiss(id)}
                >
                    <X
                        size={16}
                        strokeWidth={2}
                        className="opacity-60 transition-opacity group-hover:opacity-100"
                        aria-hidden="true"
                    />
                </Button>
            </div>
        </div>
    );
}


export function DefaultErrorNotification({ id, message, needLink = false }: { id: number | string, message: string, needLink: boolean }) {
    return (
        <div className="">
            <div className="flex gap-2">
                <div className="flex grow gap-3">
                    <CircleAlert
                        className="mt-[0.8px] shrink-0 text-red-500"
                        size={16}
                        strokeWidth={2}
                        aria-hidden="true"
                    />
                    <div className="flex grow justify-between gap-12">
                        <p className="text-sm text-foreground/90">{message}</p>
                        {needLink && (
                            <a href="#" className="group whitespace-nowrap text-sm font-medium hover:text-foreground/90">
                                Link
                                <ArrowRight
                                    className="-mt-0.5 ms-1 inline-flex opacity-60 transition-transform group-hover:translate-x-0.5"
                                    size={16}
                                    strokeWidth={2}
                                    aria-hidden="true"
                                />
                            </a>
                        )}
                    </div>
                </div>
                <Button
                    variant="ghost"
                    className="group -my-1.5 -me-2 size-8 shrink-0 p-0 hover:bg-transparent"
                    aria-label="Close banner"
                    onClick={() => toast.dismiss(id)}
                >
                    <X
                        size={16}
                        strokeWidth={2}
                        className="opacity-60 transition-opacity group-hover:opacity-100"
                        aria-hidden="true"
                    />
                </Button>
            </div>
        </div>
    );
}




export function DefaultWarningNotification({ id, message, needLink = false }: { id: number | string, message: string, needLink: boolean }) {
    return (
        <div className="z-[100] max-w-[400px] rounded-lg bg-background/95 backdrop-blur px-4 py-3 shadow-lg">
            <div className="flex gap-2">
                <div className="flex grow gap-3">
                    <TriangleAlert
                        className="mt-[0.8px] shrink-0 text-amber-500"
                        size={16}
                        strokeWidth={2}
                        aria-hidden="true"
                    />
                    <div className="flex grow justify-between gap-12">
                        <p className="text-xs ">{message}</p>
                        {needLink && (
                            <a href="#" className="group whitespace-nowrap text-sm font-medium">
                                Link
                                <ArrowRight
                                    className="-mt-0.5 ms-1 inline-flex opacity-60 transition-transform group-hover:translate-x-0.5"
                                    size={16}
                                    strokeWidth={2}
                                    aria-hidden="true"
                                />
                            </a>
                        )}
                    </div>
                </div>
                <Button
                    variant="ghost"
                    className="group -my-1.5 -me-2 size-8 shrink-0 p-0 hover:bg-transparent"
                    aria-label="Close banner"
                >
                    <X
                        size={16}
                        strokeWidth={2}
                        className="opacity-60 transition-opacity group-hover:opacity-100"
                        aria-hidden="true"
                        onClick={() => toast.dismiss(id)}
                    />
                </Button>
            </div>
        </div>
    );
}
