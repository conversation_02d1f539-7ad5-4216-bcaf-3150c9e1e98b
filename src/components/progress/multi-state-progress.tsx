import { Badge } from "@/components/ui/badge"

interface ProgressState {
    success: number
    failed: number
    processing: number
    queued: number
}

export default function MultiStateProgress({ states = { success: 0, failed: 0, processing: 0, queued: 0 } }: { states?: ProgressState }) {
    const total = Object.values(states).reduce((sum, value) => sum + value, 0)

    const normalizedStates = Object.entries(states).map(([key, value]) => ({
        name: key,
        value: Math.round((value / total) * 100)
    }))

    const getColor = (stateName: string) => {
        switch (stateName) {
            case 'success':
                return 'bg-green-300'
            case 'failed':
                return 'bg-red-300'
            case 'processing':
                return 'bg-yellow-300'
            case 'queued':
                return 'bg-blue-300'
            default:
                return 'bg-gray-300'
        }
    }

    const getBadgeColor = (stateName: string) => {
        switch (stateName) {
            case 'success':
                return 'bg-green-100 text-green-800 border-green-300'
            case 'failed':
                return 'bg-red-100 text-red-800 border-red-300'
            case 'processing':
                return 'bg-yellow-100 text-yellow-800 border-yellow-300'
            case 'queued':
                return 'bg-blue-100 text-blue-800 border-blue-300'
            default:
                return 'bg-gray-100 text-gray-800 border-gray-300'
        }
    }

    return (
        <div className="w-full max-w-md mx-auto space-y-4">
            <div className="flex h-6 w-full overflow-hidden bg-gray-200 rounded-full">
                {normalizedStates.map((state, index) => (
                    <div
                        key={state.name}
                        className={`h-full ${getColor(state.name)} ${index === 0 ? 'rounded-l-full' : ''} ${index === normalizedStates.length - 1 ? 'rounded-r-full' : ''}`}
                        style={{ width: `${state.value}%` }}
                    />
                ))}
            </div>
            <div className="flex flex-wrap gap-2 justify-center">
                {normalizedStates.map((state) => (
                    <Badge key={state.name} variant="outline" className={`${getBadgeColor(state.name)}`}>
                        {state.name.charAt(0).toUpperCase() + state.name.slice(1)}: {state.value}%
                    </Badge>
                ))}
            </div>
        </div>
    )
}