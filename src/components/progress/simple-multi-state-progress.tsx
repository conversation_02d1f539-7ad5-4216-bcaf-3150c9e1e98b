import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { Check<PERSON>ircle, Clock, Loader2, XCircle } from "lucide-react"

interface ProgressState {
    success: number
    failed: number
    processing: number
    queued: number
}

export default function SimpleMultiStateProgress({ states = { success: 0, failed: 0, processing: 0, queued: 0 } }: { states?: ProgressState }) {
    const total = Object.values(states).reduce((sum, value) => sum + value, 0)

    const normalizedStates = Object.entries(states).map(([key, value]) => ({
        name: key,
        value: Math.round((value / total) * 100),
        rawValue: value
    }))

    const getColor = (stateName: string) => {
        switch (stateName) {
            case 'success':
                return 'bg-green-300'
            case 'failed':
                return 'bg-red-300'
            case 'processing':
                return 'bg-yellow-300'
            case 'queued':
                return 'bg-blue-300'
            default:
                return 'bg-gray-300'
        }
    }

    const getTextColor = (stateName: string) => {
        switch (stateName) {
            case 'success':
                return 'text-green-700'
            case 'failed':
                return 'text-red-700'
            case 'processing':
                return 'text-yellow-700'
            case 'queued':
                return 'text-blue-700'
            default:
                return 'text-gray-700'
        }
    }
    const getStateLabel = (stateName: string) => {
        switch (stateName) {
            case 'success':
                return '成功'
            case 'failed':
                return '失败'
            case 'processing':
                return '处理中'
            case 'queued':
                return '等待中'
            default:
                return stateName
        }
    }

    const getStateIcon = (stateName: string) => {
        switch (stateName) {
            case 'success':
                return <CheckCircle className="w-4 h-4 mr-1 text-green-600" />
            case 'failed':
                return <XCircle className="w-4 h-4 mr-1 text-red-600" />
            case 'processing':
                return <Loader2 className="w-4 h-4 mr-1 text-yellow-600 animate-spin" />
            case 'queued':
                return <Clock className="w-4 h-4 mr-1 text-blue-600" />
            default:
                return null
        }
    }

    return (
        <div className="w-full max-w-md mx-auto space-y-4">
            <Popover>
                <PopoverTrigger asChild>
                    <div className="flex h-4 w-full overflow-hidden bg-gray-200 rounded-full cursor-pointer">
                        {normalizedStates.map((state, index) => (
                            <div
                                key={state.name}
                                className={`h-full ${getColor(state.name)} ${index === 0 ? 'rounded-l-full' : ''} ${index === normalizedStates.length - 1 ? 'rounded-r-full' : ''}`}
                                style={{ width: `${state.value}%` }}
                            />
                        ))}
                    </div>
                </PopoverTrigger>
                <PopoverContent className="w-48">
                    <div className="space-y-2">
                        {normalizedStates.map((state) => (
                            <div key={state.name} className="flex justify-between items-center">
                                <span className={`text-xs font-medium ${getTextColor(state.name)} flex items-center`}>
                                    {getStateIcon(state.name)}
                                    {getStateLabel(state.name)}
                                </span>
                                <span className="text-xs text-gray-500">
                                    {state.rawValue} ({state.value}%)
                                </span>
                            </div>
                        ))}
                    </div>
                </PopoverContent>
            </Popover>
        </div>
    )
}