import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from "@/components/ui/select";
import { useState } from 'react';
import { Input } from "../ui/input";

const SearchComponent = () => {
    const [, setCategory] = useState('');
    const [searchTerm, setSearchTerm] = useState('');

    const handleCategoryChange = (value: any) => {
        setCategory(value);
        console.log(`Selected category: ${value}`);
    };

    const handleSearchChange = (e: any) => {
        setSearchTerm(e.target.value);
        console.log(`Search term: ${e.target.value}`);
    };

    return (
        <div className="flex items-center z-10">
            <Select onValueChange={handleCategoryChange}>
                <SelectTrigger className="w-[180px] rounded-r-none h-8 text-xs">
                    <SelectValue placeholder="选择类目" />
                </SelectTrigger>
                <SelectContent>
                    <SelectItem value="category1">类目1</SelectItem>
                    <SelectItem value="category2">类目2</SelectItem>
                    <SelectItem value="category3">类目3</SelectItem>
                </SelectContent>
            </Select>

            <Input
                type="text"
                placeholder="搜索..."
                value={searchTerm}
                onChange={handleSearchChange}
                className="border p-2 rounded-l-none border-l-0 h-8 text-xs"
            />
        </div>
    );
};

export default SearchComponent;