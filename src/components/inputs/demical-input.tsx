import {Input} from "@/components/ui/input";
import {userInfoAtom} from "@/state/common";
import {useAtomValue} from 'jotai';
import React from 'react';

interface DecimalInputProps {
    value: number | undefined;
    onChange: (value: number | '') => void;
    step?: string;
    placeholder?: string;
    disabled?: boolean;
    min?: number;
    className?: string
}

const DecimalInput: React.FC<DecimalInputProps> = ({value, className, onChange, step = "0.01", placeholder, disabled, min = 0.01}) => {


    const userInfo = useAtomValue(userInfoAtom);

    const isSupplier = userInfo?.supplierId != undefined;


    const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const inputValue = e.target.value;
        const regex = /^\d*\.?\d{0,2}$/;
        if (regex.test(inputValue) || inputValue === '') {
            onChange(inputValue === '' ? '' : parseFloat(inputValue));
        }
    };

    const handleBlur = (e: React.FocusEvent<HTMLInputElement>) => {
        const inputValue = e.target.value;
        if (inputValue !== '') {
            const formattedValue = parseFloat(inputValue).toFixed(2);
            onChange(parseFloat(formattedValue));
        }
    };

    return (
        <Input
            className={className}
            disabled={disabled || isSupplier}
            type="number"
            step={step}
            value={value}
            min={min}
            onChange={handleChange}
            onBlur={handleBlur}
            placeholder={placeholder}
        />
    );
};

export default DecimalInput;