import { useState } from "react";
import { Input } from "@/components/ui/input";
import { EyeClosedIcon, EyeOpenIcon } from "@radix-ui/react-icons";
import { UseFormRegisterReturn } from "react-hook-form";
import {
    Tooltip,
    TooltipContent,
    TooltipProvider,
    TooltipTrigger,
} from "@/components/ui/tooltip";

interface PasswordInputProps {
    id: string;
    register: UseFormRegisterReturn;
    error?: string;
}

export function PasswordInput({ id, register, error }: PasswordInputProps) {
    const [showPassword, setShowPassword] = useState(false);

    return (
        <div>
            <div className="relative">
                <Input
                    id={id}
                    placeholder="请输入您的密码"
                    type={showPassword ? "text" : "password"}
                    {...register}
                    className="bg-white bg-opacity-75 pr-10"
                    autoComplete="current-password"
                />
                <TooltipProvider>
                    <Tooltip>
                        <TooltipTrigger asChild>
                            <button
                                type="button"
                                className="absolute inset-y-0 right-0 pr-3 flex items-center"
                                onClick={() => setShowPassword(!showPassword)}
                            >
                                {showPassword ? (
                                    <EyeOpenIcon className="h-5 w-5 text-gray-400" />
                                ) : (
                                    <EyeClosedIcon className="h-5 w-5 text-gray-400" />
                                )}
                            </button>
                        </TooltipTrigger>
                        <TooltipContent>
                            <p>{showPassword ? "隐藏密码" : "显示密码"}</p>
                        </TooltipContent>
                    </Tooltip>
                </TooltipProvider>
            </div>
            {error && <p className="text-red-500 text-xs mt-1">{error}</p>}
        </div>
    );
}