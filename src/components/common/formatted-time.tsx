import dayjs from 'dayjs';
import { useMemo } from 'react';

interface FormattedTimeProps {
    timestamp: string | number;
    formatter?: string;
    className?: string;
}

export function FormattedTime({
    timestamp,
    formatter = 'YYYY-MM-DD HH:mm:ss',
    className,
}: FormattedTimeProps) {

    const formattedTime = useMemo(() => {
        return dayjs(parseInt(timestamp.toString())).format(formatter);
    }, [timestamp, formatter]);

    return <span className={className}>{formattedTime}</span>;
}
