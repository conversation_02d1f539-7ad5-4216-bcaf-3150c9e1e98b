import { LucideIcon, MapPinned } from "lucide-react";
import { Too<PERSON><PERSON>, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";

interface InfoItemProps {
    icon: LucideIcon;
    text: string;
    tooltip?: string;
}

export const InfoItem = ({ icon: Icon, text, tooltip }: InfoItemProps) => (
    <TooltipProvider>
        <Tooltip>
            <TooltipTrigger asChild>
                <div className="flex items-center space-x-2 hover:bg-gray-50/50 p-1 rounded-sm transition-colors cursor-default">
                    <Icon className="h-4 w-4" />
                    <span className="text-sm">{text}</span>
                </div>
            </TooltipTrigger>
            {tooltip && (
                <TooltipContent>
                    <p>{tooltip}</p>
                </TooltipContent>
            )}
        </Tooltip>
    </TooltipProvider>
);

interface AddressInfoProps {
    recipient: {
        address1: string;
        address2?: string;
        city: string;
        state: string;
        country: string;
        postcode: string;
    };
}

export const AddressInfo = ({ recipient }: AddressInfoProps) => (
    <TooltipProvider>
        <Tooltip>
            <TooltipTrigger asChild>
                <div className="flex items-center space-x-2 hover:bg-gray-50/50 p-1 rounded-sm transition-colors cursor-default max-w-[500px]">
                    <MapPinned className="h-4 w-4 text-red-500" />
                    <span className="text-sm truncate">
                        {recipient.address1} {recipient.city} {recipient.state} {recipient.country} {recipient.postcode}
                    </span>
                </div>
            </TooltipTrigger>
            <TooltipContent className="p-3 bg-white border shadow-lg rounded-md">
                <div className="space-y-3">
                    <div className="border-b pb-2">
                        <span className="text-xs text-gray-500">详细地址1</span>
                        <span className="block text-sm font-medium text-gray-800">{recipient.address1}</span>
                    </div>
                    {recipient.address2 && (
                        <div className="border-b pb-2">
                            <span className="text-xs text-gray-500">详细地址2</span>
                            <span className="block text-sm font-medium text-gray-800">{recipient.address2}</span>
                        </div>
                    )}
                    <div className="grid grid-cols-2 gap-4 border-b pb-2">
                        <div>
                            <span className="text-xs text-gray-500">城市</span>
                            <span className="block text-sm font-medium text-gray-800">{recipient.city}</span>
                        </div>
                        <div>
                            <span className="text-xs text-gray-500">州/省</span>
                            <span className="block text-sm font-medium text-gray-800">{recipient.state}</span>
                        </div>
                    </div>
                    <div className="grid grid-cols-2 gap-4">

                        <div>
                            <span className="text-xs text-gray-500">国家</span>
                            <span className="block text-sm font-medium text-gray-800">{recipient.country}</span>
                        </div>
                        <div>
                            <span className="text-xs text-gray-500">邮编</span>
                            <span className="block text-sm font-medium text-gray-800">{recipient.postcode}</span>
                        </div>
                    </div>
                </div>
            </TooltipContent>
        </Tooltip>
    </TooltipProvider>
); 