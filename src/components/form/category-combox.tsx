import {useState} from 'react'
import {Check, ChevronsUpDown} from "lucide-react"
import {cn} from "@/lib/utils"
import {But<PERSON>} from "@/components/ui/button"
import {Command, CommandEmpty, CommandGroup, CommandInput, CommandItem, CommandList,} from "@/components/ui/command"
import {Popover, PopoverContent, PopoverTrigger,} from "@/components/ui/popover"
import {ScrollArea} from "@/components/ui/scroll-area";

interface CategoryComboboxProps {
    value: string;
    onChange: (value: string) => void;
    categoryOptions: string[];
}

export function CategoryCombobox({ value, onChange, categoryOptions }: CategoryComboboxProps) {
    const [open, setOpen] = useState(false)

    return (
        <Popover open={open} onOpenChange={setOpen}>
            <PopoverTrigger asChild>
                <Button
                    variant="outline"
                    role="combobox"
                    aria-expanded={open}
                    className="w-full justify-between"
                >
                    {value
                        ? categoryOptions.find((category) => category === value)
                        : "全部"}
                    <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                </Button>
            </PopoverTrigger>
            <PopoverContent className="w-[--radix-popover-trigger-width] p-0 min-w-[200px]">
                <Command>
                    <CommandInput placeholder="选择分类..." />
                    <CommandList>
                        <CommandEmpty>No category found.</CommandEmpty>
                        <CommandGroup>
                            <ScrollArea className="h-72">
                                <CommandItem
                                    key="all"
                                    value="all"
                                    onSelect={() => {
                                        onChange("")
                                        setOpen(false)
                                    }}
                                >
                                    <Check
                                        className={cn(
                                            "mr-2 h-4 w-4",
                                            !value ? "opacity-100" : "opacity-0"
                                        )}
                                    />
                                    全部
                                </CommandItem>
                                {categoryOptions.map((category) => (
                                    <CommandItem
                                        key={category}
                                        value={category}
                                        onSelect={() => {
                                            onChange(category === value ? "" : category)
                                            setOpen(false)
                                        }}
                                    >
                                        <Check
                                            className={cn(
                                                "mr-2 h-4 w-4",
                                                value === category ? "opacity-100" : "opacity-0"
                                            )}
                                        />
                                        {category}
                                    </CommandItem>
                                ))}
                            </ScrollArea>
                        </CommandGroup>
                    </CommandList>
                </Command>
            </PopoverContent>
        </Popover>
    )
}