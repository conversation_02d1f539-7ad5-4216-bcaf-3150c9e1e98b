import {
  BadgeCheck,
  Bell,
  Box,
  Building2,
  ChevronRight,
  ChevronsUpDown,
  CreditCard,
  Download,
  Folder,
  Forward,
  Home,
  LogOut,
  MoreHorizontal,
  Package,
  Settings2,
  Trash2,
  Upload,
  Users,
  Crown,
  Calendar,
  Infinity,
  Pizza
} from "lucide-react"

import { getUserInfo, logoutUser, UserInfo } from "@/api/user/user-api"
import {
  Avatar,
  AvatarFallback,
  AvatarImage,
} from "@/components/ui/avatar"
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/components/ui/collapsible"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger
} from "@/components/ui/dropdown-menu"
import { Separator } from "@/components/ui/separator"
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarGroup,
  SidebarGroupLabel,
  SidebarHeader,
  SidebarInset,
  SidebarMenu,
  SidebarMenuAction,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarMenuSub,
  SidebarMenuSubButton,
  SidebarMenuSubItem,
  SidebarProvider,
  SidebarRail,
  SidebarTrigger
} from "@/components/ui/sidebar"
import { toggleSidebarAtom, userInfoAtom } from "@/state/common"
import { useAtom, useAtomValue } from "jotai"
import { useEffect } from "react"
import { Outlet, useLocation } from "react-router-dom"
import { CommandMenu } from "../command/command-menu"
import { pushModal } from "../modals"
import { Badge } from "../ui/badge"
// This is sample data.
export const data = {
  user: {
    name: "shadcn",
    email: "<EMAIL>",
    avatar: "/avatars/shadcn.jpg",
  },
  navMain: [
    {
      title: "工作台",
      url: "/dashboard",
      icon: Home,
      items: [
        {
          title: "数据统计",
          url: "/dashboard/main",
        },
        {
          title: "主订单管理",
          url: "/dashboard/order/main-order",
        },
        {
          title: "子订单管理",
          url: "/dashboard/order/sub-order",
        },
        {
          title: "订单图片管理",
          url: "/dashboard/order/image-download-task",
        },
      ],
    },
    {
      title: "供应商",
      url: "/dashboard/supplier",
      icon: Building2,
      items: [
        {
          title: "供应商管理",
          url: "/dashboard/supplier/management",
        },
      ],
    },
    {
      title: "客户",
      url: "/dashboard/customers",
      icon: Users,
      items: [
        {
          title: "客户管理",
          url: "/dashboard/customers/management",
        },
        {
          title: "客户平台账户管理",
          url: "/dashboard/customers/platform-accounts",
        },
        {
          title: "客户产品管理",
          url: "/dashboard/inventory/customer/products",
        },
        {
          title: "客户套装产品管理",
          url: "/dashboard/inventory/customer/combo-products",
        },
      ],
    },
    {
      title: "产品",
      url: "/dashboard/inventory",
      icon: Box,
      items: [
        {
          title: "平台产品管理",
          url: "/dashboard/inventory/products",
        },

        {
          title: "平台套装产品管理",
          url: "/dashboard/inventory/combo-products",
        },

        {
          title: "供应商产品管理",
          url: "/dashboard/inventory/supplier/products",
        },
      ],
    },
    {
      title: "下载任务",
      url: "/dashboard/download",
      icon: Download,
      items: [
        {
          title: "文件下载管理",
          url: "/dashboard/download/task",
        },
        {
          title: "下载模版管理",
          url: "/dashboard/download/template",
        },
      ],
    },
    {
      title: "订单",
      url: "/dashboard/order",
      icon: Package,
      items: [
        {
          title: "运单管理",
          url: "/dashboard/order/waybill",
        },
        {
          title: "包裹管理",
          url: "/dashboard/order/supplier-order/package",
        },
        {
          title: "供应商主订单管理",
          url: "/dashboard/order/supplier-main-order",
        },
        {
          title: "供应商订单管理",
          url: "/dashboard/order/supplier-order",
        },
        {
          title: "轨迹管理",
          url: "/dashboard/order/tracking",
        },
      ],
    },
    {
      title: "设置",
      url: "#",
      icon: Settings2,
      items: [
        {
          title: "用户管理",
          url: "/dashboard/settings/user",
        },
        {
          title: "角色管理",
          url: "/dashboard/settings/role",
        },
        {
          title: "权限管理",
          url: "/dashboard/settings/permission",
        },
        {
          title: "渠道管理",
          url: "/dashboard/settings/channel",
        },
      ],
    },
  ],
  projects: [
    // {
    //   name: "Design Engineering",
    //   url: "#",
    //   icon: Frame,
    // },
    // {
    //   name: "Sales & Marketing",
    //   url: "#",
    //   icon: PieChart,
    // },
    // {
    //   name: "Travel",
    //   url: "#",
    //   icon: Map,
    // },
  ],
}

// 添加权限检查函数
const hasPermission = (resource: string, permissionNames: string[] | undefined) => {
  if (!permissionNames) return false;
  return permissionNames.includes(resource);
}

// 过滤菜单项函数
const filterMenuItems = (items: any[], permissionNames: string[] | undefined) => {
  if (!items) return [];
  return items.filter(item => hasPermission(item.url, permissionNames));
}

export default function MainLayout() {

  const location = useLocation()

  const [userInfo, setUserInfo] = useAtom(userInfoAtom)

  const isActive = (url: string) => {
    return location.pathname === url
  }

  const isMenuActive = (items: { url: string }[]) => {
    return items.some(item => location.pathname === item.url)
  }

  const logout = () => {
    logoutUser()
    setUserInfo(null)
  }

  useEffect(() => {
    getUserInfo().then((res: UserInfo) => {
      setUserInfo(res)
    })
  }, [])

  useEffect(() => {
    const down = (e: KeyboardEvent) => {
      if (e.key === "k" && (e.metaKey || e.ctrlKey)) {
        e.preventDefault()
        pushModal('ImportImageOrderModal')
      }
    }
    document.addEventListener("keydown", down)
    return () => document.removeEventListener("keydown", down)
  }, [])

  //@ts-ignore
  const _value = useAtomValue(toggleSidebarAtom)


  return (
    <SidebarProvider open={localStorage.getItem('toggleSidebar') === "true"}>
      <CommandMenu />
      <Sidebar collapsible="icon">
        <SidebarHeader>
          <SidebarMenu>
            <SidebarMenuItem>
              <SidebarMenuButton size="lg" asChild>
                <a href="#">
                  <div className="flex aspect-square size-8 items-center justify-center">
                    <img src="/logo.svg" alt="logo" className="w-6" />
                  </div>
                  <div className="flex flex-col gap-0.5 leading-none text-sidebar-accent-foreground">
                    <span className="font-semibold">Luxury Pro</span>
                    <span className="">企业版</span>
                  </div>
                </a>
              </SidebarMenuButton>
            </SidebarMenuItem>
          </SidebarMenu>
        </SidebarHeader>
        <SidebarContent>
          <SidebarGroup>
            <SidebarGroupLabel>平台</SidebarGroupLabel>
            <SidebarMenu>
              {data.navMain
                .filter(item => {
                  // 检查父菜单权限
                  const hasParentPermission = item.url === '#' || hasPermission(item.url, userInfo?.permissionNames);
                  // 检查是否有可访问的子菜单
                  const hasAccessibleChildren = item.items && filterMenuItems(item.items, userInfo?.permissionNames).length > 0;
                  return hasParentPermission || hasAccessibleChildren;
                })
                .map((item) => (
                  <Collapsible
                    key={item.title}
                    asChild
                    defaultOpen={isMenuActive(item.items)}
                    className="group/collapsible"
                  >
                    <SidebarMenuItem>
                      <CollapsibleTrigger asChild>
                        <SidebarMenuButton tooltip={item.title}>
                          {item.icon && <item.icon />}
                          <span>{item.title}</span>
                          <ChevronRight className="ml-auto transition-transform duration-200 group-data-[state=open]/collapsible:rotate-90" />
                        </SidebarMenuButton>
                      </CollapsibleTrigger>
                      <CollapsibleContent>
                        <SidebarMenuSub>
                          {filterMenuItems(item.items, userInfo?.permissionNames).map((subItem) => (
                            <SidebarMenuSubItem
                              key={subItem.title}
                              className={isActive(subItem.url) ? "bg-sidebar-accent text-sidebar-accent-foreground" : ""}
                            >
                              <SidebarMenuSubButton asChild>
                                <a href={subItem.url}>
                                  <span className={isActive(subItem.url) ? "text-sidebar-accent-foreground" : ""}>{subItem.title}</span>
                                </a>
                              </SidebarMenuSubButton>
                            </SidebarMenuSubItem>
                          ))}
                        </SidebarMenuSub>
                      </CollapsibleContent>
                    </SidebarMenuItem>
                  </Collapsible>
                ))}
            </SidebarMenu>
          </SidebarGroup>
          <SidebarGroup className="group-data-[collapsible=icon]:hidden">
            <SidebarGroupLabel>项目</SidebarGroupLabel>
            <SidebarMenu>
              {data.projects.map((item: any) => (
                <SidebarMenuItem key={item.name}>
                  <SidebarMenuButton asChild>
                    <a href={item.url}>
                      <item.icon />
                      <span>{item.name}</span>
                    </a>
                  </SidebarMenuButton>
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <SidebarMenuAction showOnHover>
                        <MoreHorizontal />
                        <span className="sr-only">More</span>
                      </SidebarMenuAction>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent
                      className="w-48 rounded-lg"
                      side="bottom"
                      align="end"
                    >
                      <DropdownMenuItem>
                        <Folder className="text-muted-foreground" />
                        <span>View Project</span>
                      </DropdownMenuItem>
                      <DropdownMenuItem>
                        <Forward className="text-muted-foreground" />
                        <span>Share Project</span>
                      </DropdownMenuItem>
                      <DropdownMenuSeparator />
                      <DropdownMenuItem>
                        <Trash2 className="text-muted-foreground" />
                        <span>Delete Project</span>
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </SidebarMenuItem>
              ))}
              <SidebarMenuItem>
                <SidebarMenuButton className="text-sidebar-foreground/70" onClick={() => {
                  pushModal('ImportImageOrderModal')
                }}>
                  <div className="flex flex-row justify-between w-full items-center">
                    <div className="flex flex-row items-center gap-1">
                      <Upload className="text-sidebar-foreground/70 h-4 w-4 " />
                      <span>导入订单</span>
                    </div>
                    <Badge variant='outline' className="text-xs text-sidebar-foreground/70">
                      ⌘ K
                    </Badge>
                  </div>
                </SidebarMenuButton>
                {userInfo?.name === 'tao' && (
                  <SidebarMenuButton className="text-sidebar-foreground/70" onClick={() => {
                    pushModal('CreateInitialBusinessModal')
                  }}>
                    <div className="flex flex-row justify-between w-full items-center">
                      <div className="flex flex-row items-center gap-1">
                        <Pizza className="text-sidebar-foreground/70 h-4 w-4 " />
                        <span>创建业务</span>
                      </div>
                    </div>
                  </SidebarMenuButton>
                )}
              </SidebarMenuItem>
            </SidebarMenu>
          </SidebarGroup>
        </SidebarContent>
        <SidebarFooter>
          <SidebarMenu>
            <SidebarMenuItem>
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <SidebarMenuButton
                    size="lg"
                    className="data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground"
                  >
                    <Avatar className="h-8 w-8 rounded-lg">
                      <AvatarImage
                        alt={userInfo?.name?.toUpperCase()}
                      />
                      <AvatarFallback className="rounded-lg">
                        {userInfo?.name?.toUpperCase()?.charAt(0)}
                      </AvatarFallback>
                    </Avatar>
                    <div className="grid flex-1 text-left text-sm leading-tight">
                      <div className="flex items-center gap-1">
                        <span className="truncate font-semibold">
                          {userInfo?.name}
                        </span>
                      </div>
                      <span className="truncate text-xs">
                        {userInfo?.bizName}
                      </span>
                    </div>
                    <ChevronsUpDown className="ml-auto size-4" />
                  </SidebarMenuButton>
                </DropdownMenuTrigger>
                <DropdownMenuContent
                  className="w-[--radix-dropdown-menu-trigger-width] min-w-56 rounded-lg"
                  side="bottom"
                  align="end"
                  sideOffset={4}
                >
                  <DropdownMenuLabel className="p-0 font-normal">
                    <div className="flex flex-col gap-2 px-1 py-1.5 text-left text-sm">
                      <div className="flex items-center justify-between gap-4">
                        <div className="flex items-center gap-2">
                          <Avatar className="h-8 w-8 rounded-lg">
                            <AvatarImage
                              // src={userInfo?.avatar}
                              alt={userInfo?.name}
                            />
                            <AvatarFallback className="rounded-lg">
                              {userInfo?.name?.charAt(0)?.toUpperCase()}
                            </AvatarFallback>
                          </Avatar>
                          <div className="flex flex-col leading-tight">
                            <span className="truncate font-semibold">
                              {userInfo?.name}
                            </span>
                            <span className="truncate text-xs text-muted-foreground">
                              {userInfo?.bizName}
                            </span>
                          </div>
                        </div>

                        {userInfo?.bizType != 'INNER' && userInfo?.subscribeFrom && userInfo?.subscribeTo && (
                          <div className="flex flex-col text-xs text-muted-foreground">
                            <div className="flex items-center gap-2">
                              <span>从:</span>
                              <span>
                                {new Date(parseInt(userInfo.subscribeFrom)).toLocaleDateString('zh-CN', {
                                  year: 'numeric',
                                  month: 'numeric',
                                  day: 'numeric'
                                })}
                              </span>
                            </div>
                            <div className="flex items-center gap-2">
                              <span>至:</span>
                              <span>
                                {new Date(parseInt(userInfo.subscribeTo)).toLocaleDateString('zh-CN', {
                                  year: 'numeric',
                                  month: 'numeric',
                                  day: 'numeric'
                                })}
                              </span>
                            </div>
                          </div>
                        )}
                      </div>

                      <Separator className="my-0.5" />

                      <div className="flex items-center gap-3 text-xs text-muted-foreground">
                        <div className="flex items-center gap-1">
                          <Badge
                            variant={
                              userInfo?.bizType === 'INNER' ? 'default' :
                                userInfo?.bizType === 'SUBSCRIPTION' ? 'secondary' :
                                  'outline'
                            }
                            className="gap-1"
                          >
                            <Crown className="h-3 w-3" />
                            <span>
                              {userInfo?.bizType === 'INNER' ? '内部用户' :
                                userInfo?.bizType === 'SUBSCRIPTION' ? '订阅用户' : '体验用户'}
                            </span>
                          </Badge>
                        </div>
                        {userInfo?.bizType !== 'INNER' ? (
                          <div className="flex items-center gap-1">
                            <Badge variant="outline" className="gap-1">
                              <Calendar className="h-3 w-3" />
                              <span>{userInfo?.pricingType === 'YEARLY' ? '年付' :
                                userInfo?.pricingType === 'QUARTERLY' ? '季付' :
                                  userInfo?.pricingType === 'MONTHLY' ? '月付' : '无限'}</span>
                            </Badge>
                          </div>
                        ) : (
                          <div className="flex items-center gap-1">
                            <Badge variant="outline" className="gap-1">
                              <Infinity className="h-3 w-3" />
                              <span>无限制</span>
                            </Badge>
                          </div>
                        )}
                      </div>
                    </div>
                  </DropdownMenuLabel>
                  <DropdownMenuSeparator />
                  <DropdownMenuGroup>
                    <DropdownMenuItem>
                      <BadgeCheck />
                      Account
                    </DropdownMenuItem>
                    <DropdownMenuItem>
                      <CreditCard />
                      Billing
                    </DropdownMenuItem>
                    <DropdownMenuItem>
                      <Bell />
                      Notifications
                    </DropdownMenuItem>
                  </DropdownMenuGroup>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem onClick={logout}>
                    <LogOut />
                    Log out
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </SidebarMenuItem>
          </SidebarMenu>
        </SidebarFooter>
        <SidebarRail />
      </Sidebar>
      <SidebarInset className="flex flex-col">
        <div className="sticky top-0 z-10 bg-background">
          <header className="flex h-12 shrink-0 items-center justify-between">
            <div className="flex items-center gap-2 px-4">
              <SidebarTrigger className="-ml-1" />
            </div>
            <div className="flex items-center gap-2 px-4">
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <button className="inline-flex items-center justify-center rounded-md w-10 h-10 text-sm font-medium transition-colors hover:bg-accent hover:text-accent-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50">
                    <Bell className="h-5 w-5" />
                    <span className="sr-only">通知</span>
                  </button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <DropdownMenuLabel>通知</DropdownMenuLabel>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem>暂无通知</DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </header>
          <Separator className="mb-2" />
        </div>
        <div className="px-4 mt-[10px]">
          <Outlet />
        </div>
      </SidebarInset>
    </SidebarProvider>
  )
}
