import {
  Calculator,
  ChevronRight,
  FolderIcon,
} from "lucide-react"

import {
  Command,
  CommandDialog,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
  CommandSeparator,
} from "@/components/ui/command"
import { useEffect, useState } from "react"
import { useNavigate } from "react-router-dom"
import { pushModal } from "../modals"
import { data } from "../layout/main-layout" // 从主布局导入导航数据

// 定义操作命令
const commands = [
  {
    title: "导入订单",
    icon: Calculator,
    action: () => {
      pushModal('ImportImageOrderModal')
    },
    keywords: ['import', 'order', '导入', '订单', '导入订单']
  },
  // 可以添加更多命令...
]

export function CommandMenu() {
  const [open, setOpen] = useState(false)
  const [search, setSearch] = useState("")
  const [expandedSections, setExpandedSections] = useState<string[]>([])
  const navigate = useNavigate()

  useEffect(() => {
    const down = (e: KeyboardEvent) => {
      if (e.key === "p" && (e.metaKey || e.ctrlKey)) {
        e.preventDefault()
        setOpen((open) => !open)
      }
    }
    document.addEventListener("keydown", down)
    return () => document.removeEventListener("keydown", down)
  }, [])

  const toggleSection = (title: string) => {
    setExpandedSections(prev => 
      prev.includes(title)
        ? prev.filter(t => t !== title)
        : [...prev, title]
    )
  }

  // 扁平化搜索结果，考虑展开状态
  const flattenedPages = data.navMain.flatMap(section => {
    const isExpanded = expandedSections.includes(section.title) || search.length > 0
    return [
      { 
        title: section.title,
        url: section.url,
        icon: section.icon,
        isParent: true,
        items: section.items,
        keywords: [section.title] // 添加关键词
      },
      ...(isExpanded ? section.items.map(item => ({
        title: item.title,
        url: item.url,
        parentTitle: section.title,
        icon: section.icon,
        isParent: false,
        keywords: [item.title, section.title] // 添加关键词，包括父级标题
      })) : [])
    ]
  })

  // 改进的搜索逻辑
  const searchTerm = search.toLowerCase().trim()
  
  // 过滤命令
  const filteredCommands = searchTerm
    ? commands.filter(command => 
        command.keywords.some(keyword => 
          keyword.toLowerCase().includes(searchTerm)
        )
      )
    : commands

  // 过滤页面
  const filteredPages = searchTerm
    ? flattenedPages.filter(page => {
        const keywords = page.keywords || []
        return keywords.some(keyword =>
          keyword.toLowerCase().includes(searchTerm)
        )
      })
    : flattenedPages

  return (
    <CommandDialog open={open} onOpenChange={setOpen}>
      <Command className="rounded-lg border shadow-md">
        <CommandInput 
          placeholder="搜索命令或页面..." 
          value={search}
          onValueChange={setSearch}
        />
        <CommandList>
          <CommandEmpty>未找到结果</CommandEmpty>
          {filteredCommands.length > 0 && (
            <>
              <CommandGroup heading="命令">
                {filteredCommands.map((command) => (
                  <CommandItem
                    key={command.title}
                    onSelect={() => {
                      command.action()
                      setOpen(false)
                    }}
                  >
                    <command.icon className="mr-2 h-4 w-4" />
                    {command.title}
                  </CommandItem>
                ))}
              </CommandGroup>
              <CommandSeparator />
            </>
          )}
          {filteredPages.length > 0 && (
            <CommandGroup heading="页面导航">
              {filteredPages.map((page, index) => (
                <CommandItem
                  key={page.url + index}
                  onSelect={() => {
                    if (page.isParent) {
                      toggleSection(page.title)
                    } else if (page.url) {
                      navigate(page.url)
                      setOpen(false)
                    }
                  }}
                  className={page.isParent ? "font-medium" : "pl-6"}
                >
                  {page.isParent ? (
                    <>
                      <FolderIcon className="mr-2 h-4 w-4" />
                      {page.title}
                      <ChevronRight 
                        className={`ml-auto h-4 w-4 transition-transform duration-200 ${
                          expandedSections.includes(page.title) ? 'rotate-90' : ''
                        }`} 
                      />
                    </>
                  ) : (
                    <>
                      <div className="mr-2 h-4 w-4" />
                      {page.title}
                    </>
                  )}
                </CommandItem>
              ))}
            </CommandGroup>
          )}
        </CommandList>
      </Command>
    </CommandDialog>
  )
} 