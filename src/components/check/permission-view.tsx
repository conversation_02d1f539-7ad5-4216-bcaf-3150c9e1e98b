import { buildPermissionTree, getPermissionList, Permission } from "@/api/user/permission-api";
import { CheckboxTree } from "./checkbox-tree";
import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";
import { Fragment, useEffect, useState, useCallback } from "react";
import { useAtomValue } from "jotai";
import { userInfoAtom } from "@/state/common";

interface TreeNode {
  id: string;
  label: string;
  defaultChecked?: boolean;
  children?: TreeNode[];
}

function convertPermissionTreeToTreeNode(permissions: Permission[], _permissionIds?: string[]): TreeNode {
    const permissionTree = buildPermissionTree(permissions);
    
    function convert(node: any): TreeNode {
      return {
        id: node.id || '',
        label: node.name,
        defaultChecked:false,
        children: node.children?.map(convert) || []
      };
    }
  
    return convert(permissionTree[0]);
  }

function updateTreeNodeChecked(node: TreeNode, checkedNodes: Set<string>): TreeNode {
  return {
    ...node,
    defaultChecked: checkedNodes.has(node.id),
    children: node.children?.map(child => updateTreeNodeChecked(child, checkedNodes))
  };
}

export default function PermissionView() {
    const [treeData, setTreeData] = useState<TreeNode | null>(null);

  const userInfo = useAtomValue(userInfoAtom);

  useEffect(() => {
    const permissionIds = userInfo?.permissionNames;
    
    getPermissionList().then((permissions: Permission[]) => {
      const treeNode = convertPermissionTreeToTreeNode(permissions, permissionIds);
      setTreeData(treeNode);
    });
  }, [userInfo?.permissionNames]);

  const handleCheckedNodesChange = useCallback((checkedNodes: Set<string>) => {
    if (treeData) {
      const updatedTreeData = updateTreeNodeChecked(treeData, checkedNodes);
      setTreeData(updatedTreeData);
    }
  }, [treeData]);

  if (!treeData) {
    return <div>Loading...</div>;
  }

  console.log(JSON.stringify(treeData));

  return (
    <div className="space-y-3">
      <CheckboxTree
        tree={treeData}
        onCheckedNodesChange={handleCheckedNodesChange}
        renderNode={({ node, isChecked, onCheckedChange, children }: { node: TreeNode, isChecked: boolean | "indeterminate", onCheckedChange: (checked: boolean) => void, children: React.ReactNode }) => {
            return (
                <Fragment key={node.id}>
                <div className="flex items-center gap-2">
                  <Checkbox id={node.id} checked={isChecked} onCheckedChange={onCheckedChange}/>
                  <Label htmlFor={node.id}>{node.label}</Label>
                </div>
                {children && <div className="ms-6 space-y-3">{children}</div>}
              </Fragment>
            )
        }}
      />
    </div>
  );
}
