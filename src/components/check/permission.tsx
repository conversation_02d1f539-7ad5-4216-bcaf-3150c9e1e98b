import React, { useEffect, useState, useCallback } from 'react';
import { Checkbox } from "@/components/ui/checkbox";
import {
    Dialog,
    DialogContent,
    DialogDescription,
    DialogFooter,
    DialogHeader,
    DialogTitle,
} from "@/components/ui/dialog";
import { But<PERSON> } from "@/components/ui/button";
import { CheckedState } from "@radix-ui/react-checkbox";
import { getPermissionList, Permission } from '@/api/user/permission-api';
import { useRequest } from 'ahooks';

interface PermissionListProps {
    onPermissionChange: (selectedPermissions: Permission[]) => void;
    parentId?: string;
    level?: number;
    defaultSelectedPermissionIds?: string[];
}

const PermissionListModal: React.FC<PermissionListProps> = ({
    onPermissionChange,
    parentId = '0',
    level = 0,
    defaultSelectedPermissionIds = [],
}) => {
    const [isLoading] = useState(false);
    const { data: permissions } = useRequest(getPermissionList);
    const [checkedPermissions, setCheckedPermissions] = useState<string[]>([]);
    const [open, setOpen] = useState(true);

    useEffect(() => {
        if (defaultSelectedPermissionIds && defaultSelectedPermissionIds.length > 0) {
            const allParentIds = defaultSelectedPermissionIds.reduce((acc: string[], id) => {
                const parentIds = getParentPermissionIds(id);
                return [...acc, ...parentIds];
            }, []);
            
            const uniqueIds = [...new Set([...defaultSelectedPermissionIds, ...allParentIds])];
            setCheckedPermissions(uniqueIds);
        }
    }, [defaultSelectedPermissionIds, permissions]);

    const getParentPermissionIds = useCallback((parentId: string): string[] => {
        const parentPermission = permissions?.find((permission) => permission.id === parentId);
        if (!parentPermission || parentPermission.parentId === '0') return [];
        return [parentPermission.id, ...getParentPermissionIds(parentPermission.parentId)];
    }, [permissions]);

    const handleCheckboxChange = useCallback((checked: CheckedState, permission: Permission) => {
        setCheckedPermissions(prevChecked => {
            if (checked) {
                const parentIds = getParentPermissionIds(permission.parentId);
                return [...new Set([...prevChecked, permission.id, ...parentIds])];
            } else {
                const childIds = getChildPermissionIds(permission.id);
                return prevChecked.filter((permId) => 
                    permId !== permission.id && !childIds.includes(permId)
                );
            }
        });
    }, [getParentPermissionIds]);

    const getChildPermissionIds = useCallback((permissionId: string): string[] => {
        const children = permissions?.filter(p => p.parentId === permissionId) || [];
        return children.reduce((acc: string[], child) => {
            return [...acc, child.id, ...getChildPermissionIds(child.id)];
        }, []);
    }, [permissions]);

    const handleSave = (e: React.MouseEvent) => {
        e.preventDefault();
        const selectedPermissions = permissions?.filter((permission) =>
            checkedPermissions.includes(permission.id)
        );
        onPermissionChange(selectedPermissions || []);
        setOpen(false);
    };

    const isChecked = useCallback((permission: Permission) => {
        return checkedPermissions.includes(permission.id);
    }, [checkedPermissions]);

    const renderPermissions = useCallback((parentId: string, level: number) => {
        const filteredPermissions = permissions?.filter((permission) => permission.parentId === parentId);

        return filteredPermissions?.map((permission) => (
            <div key={permission.id} style={{ marginLeft: `${level * 20}px` }}>
                <div className="flex flex-row justify-start items-center space-x-4 my-2">
                    <Checkbox
                        id={permission.id.toString()}
                        checked={isChecked(permission)}
                        onCheckedChange={(checked) => handleCheckboxChange(checked, permission)}
                    />
                    <label htmlFor={permission.id.toString()} className="font-sans text-sm cursor-pointer">
                        {permission.description}
                    </label>
                </div>
                {renderPermissions(permission.id, level + 1)}
            </div>
        ));
    }, [permissions, isChecked, handleCheckboxChange]);

    return (
        <Dialog open={open} onOpenChange={setOpen}>
            <DialogContent className="sm:max-w-[425px] max-h-[80vh] overflow-y-auto">
                <DialogHeader>
                    <DialogTitle>选择权限</DialogTitle>
                    <DialogDescription>
                        请选择要分配的权限
                    </DialogDescription>
                </DialogHeader>
                <div className="py-4">
                    {isLoading ? (
                        <div>Loading...</div>
                    ) : (
                        renderPermissions(parentId, level)
                    )}
                </div>
                <DialogFooter>
                    <Button variant="outline" onClick={() => setOpen(false)}>
                        取消
                    </Button>
                    <Button onClick={handleSave}>保存</Button>
                </DialogFooter>
            </DialogContent>
        </Dialog>
    );
};

export default PermissionListModal;