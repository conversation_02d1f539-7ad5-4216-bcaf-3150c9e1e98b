import {Checkbox} from "@/components/ui/checkbox";
import {ScrollArea} from "@/components/ui/scroll-area";
import {Separator} from "@/components/ui/separator";
import {Table, TableBody, TableCell, TableHead, TableHeader, TableRow} from "@/components/ui/table";
import {refreshTableAtom} from '@/state/common';
import {columnOrderAtom} from '@/state/table-state';
import {horizontalListSortingStrategy, SortableContext} from '@dnd-kit/sortable';
import {ColumnDef, ColumnFiltersState, flexRender, getCoreRowModel, PaginationState, SortingState, Table as TableInstance, useReactTable, VisibilityState} from "@tanstack/react-table";
import {useRequest} from 'ahooks';
import {useAtom, useAtomValue} from 'jotai';
import {Inbox} from 'lucide-react';
import React from 'react';
import {useLocation} from 'react-router-dom';
import {DataTablePagination} from './data-table-pagination';

interface DataTableProps<TData, TValue> {
    columns: ColumnDef<TData, TValue>[];
    onFetch: (params: FetchParams) => Promise<FetchResponse<TData>>;
    toolbar?: (table: TableInstance<TData>, tableId: string) => React.ReactNode;
    advancedSearch?: React.ReactNode;
    isNeedSelect?: boolean;
    dependencies?: any[];
    isFixedHeader?: boolean;
    containerHeight?: string; // 可选的自定义高度
}

export interface FetchParams {
    filters: Record<string, any>;
    sorting: { id: string; desc: boolean }[];
    pagination: { pageIndex: number; pageSize: number };
    advancedFilters?: Record<string, any>;
    searchParams: Record<string, any>;
}

export interface FetchResponse<TData> {
    content: TData[];
    total: number;
}

// 自动计算表格容器高度的 hook
function useAutoTableHeight() {
    const [height, setHeight] = React.useState("600px");

    React.useEffect(() => {
        function calculateHeight() {
            // 获取视窗高度
            const viewportHeight = window.innerHeight;
            // 预留空间给其他元素(头部导航、工具栏、分页等)
            const reservedSpace = 280;
            // 计算表格可用高度
            const availableHeight = Math.max(viewportHeight - reservedSpace, 400); // 设置最小高度为400px
            setHeight(`${availableHeight}px`);
        }

        calculateHeight();
        window.addEventListener('resize', calculateHeight);
        return () => window.removeEventListener('resize', calculateHeight);
    }, []);

    return height;
}

export function DataTable<TData, TValue>({
    columns,
    onFetch,
    toolbar,
    advancedSearch,
    isNeedSelect = false,
    dependencies = [],
    isFixedHeader = false,
    containerHeight: customHeight,
}: DataTableProps<TData, TValue>) {
    // 使用自动计算的高度
    const autoHeight = useAutoTableHeight();
    // 优先使用自定义高度，否则使用自动计算的高度
    const finalHeight = customHeight || autoHeight;

    const [columnOrderState] = useAtom(columnOrderAtom);
    const tableId = React.useMemo(() => {
        return columns.map(c => c.id).join('-');
    }, [columns]);

    const [columnOrder, setColumnOrder] = React.useState<string[]>(() =>
        columns.map(c => c.id!)
    );

    React.useEffect(() => {
        if (columnOrderState[tableId]) {
            setColumnOrder(columnOrderState[tableId]);
        }
    }, [columnOrderState, tableId]);

    const [data, setData] = React.useState<TData[]>([]);
    const [rowCount, setRowCount] = React.useState(0);
    const [sorting, setSorting] = React.useState<SortingState>([]);
    const [columnFilters, setColumnFilters] = React.useState<ColumnFiltersState>([]);
    const [columnVisibility, setColumnVisibility] = React.useState<VisibilityState>({});
    const [{ pageIndex, pageSize }, setPagination] = React.useState<PaginationState>({
        pageIndex: 0,
        pageSize: 30,
    });
    const [advancedFilters, setAdvancedFilters] = React.useState<Record<string, any>>({});
    const [rowSelection, setRowSelection] = React.useState({});
    const { search } = useLocation();

    const refreshTable = useAtomValue(refreshTableAtom);

    const fetchDataOptions = React.useMemo(
        () => {
            const searchParams = new URLSearchParams(search);
            const searchParamsObject: Record<string, any> = {};
            for (const [key, value] of searchParams.entries()) {
                searchParamsObject[key] = value;
            }

            return {
                filters: columnFilters.reduce((acc, filter) => {
                    if (Array.isArray(filter.value)) {
                        acc[filter.id] = filter.value.join(',');
                    } else {
                        acc[filter.id] = filter.value;
                    }
                    return acc;
                }, {} as Record<string, any>),
                sorting,
                pagination: {
                    pageIndex,
                    pageSize,
                },
                advancedFilters,
                searchParams: searchParamsObject,
                refreshKey: refreshTable, // 添加refreshKey确保当refreshTable变化时fetchDataOptions也会变化
            };
        },
        [sorting, columnFilters, pageIndex, pageSize, advancedFilters, refreshTable, search, ...dependencies]
    );

    const { run: fetchData } = useRequest(
        async (options: typeof fetchDataOptions) => {
            const response = await onFetch(options);
            setData(response.content);
            setRowCount(response.total);
            return response;
        },
        {
            debounceWait: 300,
            manual: true,
        }
    );

    const table = useReactTable({
        data,
        columns: isNeedSelect
            ? [
                {
                    id: 'select',
                    header: ({ table }) => (
                        <div className="h-2 flex items-center justify-center pr-5">
                            <Checkbox
                                checked={table.getIsAllPageRowsSelected()}
                                onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
                                aria-label="Select all"
                            />
                        </div>
                    ),
                    cell: ({ row }) => (
                        <div className="h-2 flex items-center justify-center pr-5">
                            <Checkbox
                                checked={row.getIsSelected()}
                                onCheckedChange={(value) => row.toggleSelected(!!value)}
                                aria-label="Select row"
                            />
                        </div>
                    ),
                    enableSorting: false,
                    enableHiding: false,
                },
                ...columns,
            ]
            : columns,
        pageCount: Math.ceil(rowCount / pageSize),
        state: {
            sorting,
            columnFilters,
            columnVisibility,
            pagination: {
                pageIndex,
                pageSize,
            },
            columnOrder,
            rowSelection,
        },
        onColumnOrderChange: setColumnOrder,
        onSortingChange: setSorting,
        onColumnFiltersChange: setColumnFilters,
        onPaginationChange: setPagination,
        onColumnVisibilityChange: setColumnVisibility,
        onRowSelectionChange: setRowSelection,
        getCoreRowModel: getCoreRowModel(),
        manualPagination: true,
        manualFiltering: true,
        manualSorting: true,
        meta: {
            searchParams: fetchDataOptions.searchParams,
        }
    });

    React.useEffect(() => {
        fetchData(fetchDataOptions);
    }, [fetchData, fetchDataOptions]);

    const handleAdvancedSearch = (filters: Record<string, any>) => {
        setAdvancedFilters(filters);
        setPagination({ pageIndex: 0, pageSize });
    };

    const handleAdvancedReset = () => {
        setAdvancedFilters({});
        setPagination({ pageIndex: 0, pageSize });
    };

    return (
        <div className="space-y-4 text-xs">
            {advancedSearch && (
                <>
                    <div className="p-4">
                        {React.cloneElement(advancedSearch as React.ReactElement, {
                            onSearch: handleAdvancedSearch,
                            onReset: handleAdvancedReset,
                        })}
                    </div>
                    <Separator />
                </>
            )}
            {toolbar && toolbar(table, tableId)}
            <div className="rounded-md">
                {isFixedHeader ? (
                    <ScrollArea className="relative" style={{ height: finalHeight }}>
                        <Table>
                            <TableHeader className="sticky top-0 z-10 bg-background">
                                {table.getHeaderGroups().map((headerGroup) => (
                                    <TableRow key={headerGroup.id}>
                                        {headerGroup.headers.map((header) => (
                                            <TableHead
                                                key={header.id}
                                                className="text-nowrap bg-background min-w-[80px]"
                                            >
                                                {header.isPlaceholder ? null :
                                                    flexRender(header.column.columnDef.header, header.getContext())
                                                }
                                            </TableHead>
                                        ))}
                                    </TableRow>
                                ))}
                            </TableHeader>
                            <TableBody>
                                {table.getRowModel().rows?.length ? (
                                    table.getRowModel().rows.map((row) => (
                                        <TableRow
                                            key={row.id}
                                            data-state={row.getIsSelected() && "selected"}
                                        >
                                            {row.getVisibleCells().map((cell) => (
                                                <TableCell key={cell.id}>{flexRender(cell.column.columnDef.cell, cell.getContext())}</TableCell>
                                            ))}
                                        </TableRow>
                                    ))
                                ) : (
                                    <TableRow>
                                        <TableCell colSpan={columns.length} className="h-96">
                                            <div className="flex flex-col items-center justify-center h-full">
                                                <Inbox className="w-12 h-12 text-gray-400" />
                                                <p className="mt-4 text-lg font-medium text-gray-500">暂无数据</p>
                                                <p className="mt-2 text-sm text-gray-400">当前没有可显示的数据，请尝试调整筛选条件或添加新数据。</p>
                                            </div>
                                        </TableCell>
                                    </TableRow>
                                )}
                            </TableBody>
                        </Table>
                    </ScrollArea>
                ) : (
                    <Table>
                        <TableHeader>
                            <SortableContext
                                items={columnOrder}
                                strategy={horizontalListSortingStrategy}
                            >
                                {table.getHeaderGroups().map((headerGroup) => (
                                    <TableRow key={headerGroup.id}>
                                        {headerGroup.headers.map((header) => (
                                            <TableHead key={header.id} className="text-nowrap">
                                                {header.isPlaceholder ? null :
                                                    flexRender(header.column.columnDef.header, header.getContext())
                                                }
                                            </TableHead>
                                        ))}
                                    </TableRow>
                                ))}
                            </SortableContext>
                        </TableHeader>
                        <TableBody>
                            {table.getRowModel().rows?.length ? (
                                table.getRowModel().rows.map((row) => (
                                    <TableRow
                                        key={row.id}
                                        data-state={row.getIsSelected() && "selected"}
                                    >
                                        {row.getVisibleCells().map((cell) => (
                                            <TableCell key={cell.id}>{flexRender(cell.column.columnDef.cell, cell.getContext())}</TableCell>
                                        ))}
                                    </TableRow>
                                ))
                            ) : (
                                <TableRow>
                                    <TableCell colSpan={columns.length} className="h-96">
                                        <div className="flex flex-col items-center justify-center h-full">
                                            <Inbox className="w-12 h-12 text-gray-400" />
                                            <p className="mt-4 text-lg font-medium text-gray-500">暂无数据</p>
                                            <p className="mt-2 text-sm text-gray-400">当前没有可显示的数据，请尝试调整筛选条件或添加新数据。</p>
                                        </div>
                                    </TableCell>
                                </TableRow>
                            )}
                        </TableBody>
                    </Table>
                )}
            </div>
            <DataTablePagination table={table} />
        </div>
    );
}
