"use client"

import { <PERSON><PERSON> } from "@/components/ui/button";
import {
    DropdownMenu,
    DropdownMenuCheckboxItem,
    DropdownMenuContent,
    DropdownMenuLabel,
    DropdownMenuSeparator
} from "@/components/ui/dropdown-menu";
import { columnOrderAtom } from "@/state/table-state";
import { closestCenter, DndContext, KeyboardSensor, PointerSensor, useSensor, useSensors } from '@dnd-kit/core';
import { arrayMove, SortableContext, useSortable, verticalListSortingStrategy } from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import { DropdownMenuTrigger } from "@radix-ui/react-dropdown-menu";
import { DragHandleDots2Icon, MixerHorizontalIcon } from "@radix-ui/react-icons";
import { Table } from "@tanstack/react-table";
import { useAtom } from "jotai";
import { atomWithStorage } from "jotai/utils";
import { useEffect } from "react";

// 创建一个新的 atom 来存储列的可见性状态
const columnVisibilityAtom = atomWithStorage<Record<string, Record<string, boolean>>>('columnVisibility', {});

interface DataTableViewOptionsProps<TData> {
    table: Table<TData>;
    tableId: string;
    canSort?: boolean; // 新增参数
}

function SortableItem({ id, children, canSort }: { id: string; children: React.ReactNode; canSort: boolean }) {
    const { attributes, listeners, setNodeRef, transform, transition } = useSortable({ id });

    const style = {
        transform: CSS.Transform.toString(transform),
        transition,
    };

    return (
        <div ref={setNodeRef} style={style} className="flex items-center justify-between hover:bg-gray-100">
            {children}
            {canSort && (
                <DragHandleDots2Icon className="h-4 w-4 cursor-move" {...attributes} {...listeners} />
            )}
        </div>
    );
}

export function DataTableViewOptions<TData>({
    table,
    tableId,
    canSort = true // 默认为 true
}: DataTableViewOptionsProps<TData>) {
    const [columnVisibility, setColumnVisibility] = useAtom(columnVisibilityAtom);
    const [columnOrder, setColumnOrder] = useAtom(columnOrderAtom);

    const sensors = useSensors(
        useSensor(PointerSensor, {
            activationConstraint: {
                distance: 8,
            },
        }),
        useSensor(KeyboardSensor)
    );

    // 初始化时,从 localStorage 加载列的可见性状态和顺序
    useEffect(() => {
        const storedVisibility = columnVisibility[tableId];
        if (storedVisibility) {
            table.setColumnVisibility(storedVisibility);
        }

        const storedOrder = columnOrder[tableId];
        if (storedOrder) {
            table.setColumnOrder(storedOrder);
        }
    }, [table, columnVisibility, columnOrder, tableId]);

    const updateColumnVisibility = (columnId: string, isVisible: boolean) => {
        const newVisibility = {
            ...table.getState().columnVisibility,
            [columnId]: isVisible,
        };
        table.setColumnVisibility(newVisibility);
        setColumnVisibility(prev => ({
            ...prev,
            [tableId]: newVisibility,
        }));
    };

    const handleDragEnd = (event: any) => {
        if (!canSort) return;

        const { active, over } = event;

        if (active.id !== over.id) {
            const oldIndex = table.getAllLeafColumns().findIndex(col => col.id === active.id);
            const newIndex = table.getAllLeafColumns().findIndex(col => col.id === over.id);

            const newOrder = arrayMove(table.getAllLeafColumns().map(col => col.id), oldIndex, newIndex);
            table.setColumnOrder(newOrder);
            setColumnOrder(prev => ({
                ...prev,
                [tableId]: newOrder,
            }));
        }
    };

    return (
        <DropdownMenu>
            <DropdownMenuTrigger asChild>
                <Button
                    variant="outline"
                    size="sm"
                    className="ml-auto hidden h-8 lg:flex"
                >
                    <MixerHorizontalIcon className="mr-1 h-4 w-4" />
                    查看
                </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-[180px]">
                <DropdownMenuLabel>切换列显示</DropdownMenuLabel>
                <DropdownMenuSeparator />
                <DndContext sensors={sensors} collisionDetection={closestCenter} onDragEnd={handleDragEnd}>
                    <SortableContext items={table.getAllLeafColumns().map(column => column.id)} strategy={verticalListSortingStrategy}>
                        {table
                            .getAllLeafColumns()
                            .filter(
                                (column) =>
                                    typeof column.accessorFn !== "undefined" && column.getCanHide()
                            )
                            .map((column) => {
                                return (
                                    <SortableItem key={column.id} id={column.id} canSort={canSort}>
                                        <DropdownMenuCheckboxItem
                                            className="capitalize"
                                            checked={column.getIsVisible()}
                                            onCheckedChange={(value) => updateColumnVisibility(column.id, !!value)}
                                        >
                                            {/* @ts-ignore */}
                                            {column.columnDef.meta?.title}
                                        </DropdownMenuCheckboxItem>
                                    </SortableItem>
                                )
                            })}
                    </SortableContext>
                </DndContext>
            </DropdownMenuContent>
        </DropdownMenu>
    )
}
