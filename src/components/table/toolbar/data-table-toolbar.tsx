import { cn } from "@/lib/utils"
import { Table } from "@tanstack/react-table"
import { ReactNode } from "react"

export interface DataTableToolbarProps<TData> {
    table: Table<TData>
    tableId?: string
    className?: string
    children?: ReactNode
}

interface ToolbarGroupProps {
    children?: ReactNode
    className?: string
}

interface ToolbarItemProps {
    children?: ReactNode
    className?: string
}

export function DataTableToolbar<TData>({
    className,
    children,
}: DataTableToolbarProps<TData>) {
    return (
        <div className={cn("flex flex-wrap items-center gap-4 p-2", className)}>
            {children}
        </div>
    )
}

// 左侧组件组
export function ToolbarLeft({ children, className }: ToolbarGroupProps) {
    return (
        <div className={cn("flex flex-1 items-center space-x-2", className)}>
            {children}
        </div>
    )
}

// 中间组件组
export function ToolbarCenter({ children, className }: ToolbarGroupProps) {
    return (
        <div className={cn("flex items-center space-x-2", className)}>
            {children}
        </div>
    )
}

// 右侧组件组
export function ToolbarRight({ children, className }: ToolbarGroupProps) {
    return (
        <div className={cn("flex items-center space-x-2 ml-auto", className)}>
            {children}
        </div>
    )
}

// 工具栏项包装器
export function ToolbarItem({ children, className }: ToolbarItemProps) {
    return (
        <div className={cn("flex items-center", className)}>
            {children}
        </div>
    )
}
