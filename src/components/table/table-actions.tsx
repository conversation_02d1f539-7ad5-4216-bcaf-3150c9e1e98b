import React from 'react';
import { But<PERSON> } from "@/components/ui/button";
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuLabel,
    DropdownMenuSeparator,
    DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
    AlertDialog,
    AlertDialogAction,
    AlertDialogCancel,
    AlertDialogContent,
    AlertDialogDescription,
    AlertDialogFooter,
    AlertDialogHeader,
    AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { Row } from "@tanstack/react-table"
import { Checkbox } from "@/components/ui/checkbox";
import { MoreHorizontalIcon } from 'lucide-react';

export enum ActionType {
    DIRECT = 'direct',
    MODAL = 'modal',
    ALERT = 'alert',
}

interface BaseAction {
    type: ActionType;
    label: string;
    icon?: React.ElementType;
}

interface DirectAction<TData> extends BaseAction {
    type: ActionType.DIRECT;
    onClick: (row: Row<TData>) => void;
}

interface ModalAction<TData> extends BaseAction {
    type: ActionType.MODAL;
    openModal: (row: Row<TData>) => void;
}

interface AlertAction<TData> extends BaseAction {
    type: ActionType.ALERT;
    alertTitle: string;
    alertDescription: string;
    onClick: (row: Row<TData>) => void;
}

export type Action<TData> = DirectAction<TData> | ModalAction<TData> | AlertAction<TData>;

interface TableActionsProps<TData> {
    actions: Action<TData>[];
    row: Row<TData>;
    isCanSelect?: boolean;
}

function TableActions<TData>({ actions, row, isCanSelect }: TableActionsProps<TData>) {
    const [openAlert, setOpenAlert] = React.useState(false);
    const [currentAlertAction, setCurrentAlertAction] = React.useState<AlertAction<TData> | null>(null);

    const handleAction = (action: Action<TData>) => {
        switch (action.type) {
            case ActionType.DIRECT:
                action.onClick(row);
                break;
            case ActionType.MODAL:
                action.openModal(row);
                break;
            case ActionType.ALERT:
                setCurrentAlertAction(action);
                setOpenAlert(true);
                break;
            default:
                return action;
        }
    };

    return (
        <>
            {isCanSelect && (
                <Checkbox
                    checked={row.getIsSelected()}
                    onCheckedChange={(value) => row.toggleSelected(!!value)}
                    aria-label="Select row"
                />
            )}
            <DropdownMenu modal={false}>
                <DropdownMenuTrigger asChild>
                    <Button variant="ghost" className="h-8 w-8 p-0">
                        <span className="sr-only">打开菜单</span>
                        <MoreHorizontalIcon className="h-4 w-4" />
                    </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                    <DropdownMenuLabel>操作</DropdownMenuLabel>
                    <DropdownMenuSeparator />
                    {actions.map((action, index) => (
                        <DropdownMenuItem key={index} onClick={() => handleAction(action)}>
                            {action.icon && <action.icon className="mr-2 h-4 w-4" />}
                            {action.label}
                        </DropdownMenuItem>
                    ))}
                </DropdownMenuContent>
            </DropdownMenu>

            <AlertDialog open={openAlert} onOpenChange={setOpenAlert}>
                <AlertDialogContent>
                    <AlertDialogHeader>
                        <AlertDialogTitle>{currentAlertAction?.alertTitle}</AlertDialogTitle>
                        <AlertDialogDescription>
                            {currentAlertAction?.alertDescription}
                        </AlertDialogDescription>
                    </AlertDialogHeader>
                    <AlertDialogFooter>
                        <AlertDialogCancel>取消</AlertDialogCancel>
                        <AlertDialogAction onClick={() => currentAlertAction?.onClick(row)}>
                            继续
                        </AlertDialogAction>
                    </AlertDialogFooter>
                </AlertDialogContent>
            </AlertDialog>
        </>
    );
};

export default TableActions;