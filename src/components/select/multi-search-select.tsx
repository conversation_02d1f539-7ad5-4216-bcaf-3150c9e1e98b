"use client"

import React, {useState} from "react"
import {Check, Plus, Search, X} from "lucide-react"
import {<PERSON><PERSON>} from "@/components/ui/button"
import {Input} from "@/components/ui/input"
import {<PERSON>alog, DialogContent, DialogHeader, DialogTitle} from "@/components/ui/dialog"
import {Badge} from "@/components/ui/badge"
import {ScrollArea} from "@/components/ui/scroll-area"
import {AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle,} from "@/components/ui/alert-dialog"
import {Label} from "@/components/ui/label.tsx";

interface MultiSearchSelectProps<T extends { id: string }> {
  label?: string
  value?: T[]
  onChange?: (value: T[]) => void
  placeholder?: string
  searchItems?: (query: string) => Promise<T[]>
  renderSearchItem?: (item: T) => React.ReactNode
  renderPreviewItem?: (item: T) => React.ReactNode
  itemToString?: (item: T) => string
  disabled?: boolean
}

export default function MultiSearchSelect<T extends { id: string }>(props: MultiSearchSelectProps<T>) {
  const [internalSelectedItems, setInternalSelectedItems] = useState<T[]>([])
  const [searchQuery, setSearchQuery] = useState("")
  const [searchResults, setSearchResults] = useState<T[]>([])
  const [isSearching, setIsSearching] = useState(false)
  const [isSearchDialogOpen, setIsSearchDialogOpen] = useState(false)
  const [selectedItemForDetail, setSelectedItemForDetail] = useState<T | null>(null)
  const [itemToRemove, setItemToRemove] = useState<T | null>(null)

  const isControlled = props.value !== undefined

  const selectedItems = isControlled ? props.value! : internalSelectedItems

  const setSelectedItems = (newValue: T[]) => {
    if (isControlled) {
      props.onChange?.(newValue)
    } else {
      setInternalSelectedItems(newValue)
    }
  }

  // 执行搜索
  const handleSearch = async () => {
    setIsSearching(true)
    try {
      const results = await props.searchItems?.(searchQuery)
      if (results) {
        setSearchResults(results)
      } else {
        setSearchResults([])
      }
    } catch (error) {
      console.error("搜索失败:", error)
    } finally {
      setIsSearching(false)
    }
  }

  // 添加选中项
  const handleAddItem = (item: T) => {
    if (!selectedItems.find((selected) => selected.id === item.id)) {
      setSelectedItems([...selectedItems, item])
    }
  }

  // 移除选中项
  const handleRemoveItem = (item: T) => {
    setSelectedItems(selectedItems.filter((selected) => selected.id !== item.id))
    setItemToRemove(null)
  }

  // 检查项目是否已选中
  const isItemSelected = (item: T) => {
    return selectedItems.some((selected) => selected.id === item.id)
  }

  // 打开搜索对话框时初始化搜索结果
  const handleOpenSearchDialog = async () => {
    setIsSearchDialogOpen(true)
    setSearchQuery("")
    setIsSearching(true)
    try {
      const results = await props.searchItems?.("")
      if (results) {
        setSearchResults(results)
      } else {
        setSearchResults([])
      }
    } catch (error) {
      console.error("加载数据失败:", error)
    } finally {
      setIsSearching(false)
    }
  }

  return (
    <div className="w-full max-w-4xl mx-auto space-y-6">
      <div className="space-y-2">
        {props.label && <Label className="text-sm font-medium">{props.label}</Label>}

        {/* 主输入框区域 */}
        <div className="relative border rounded-md p-2 min-h-[48px] flex items-center gap-2 bg-background">
          {/* 选中项滚动区域 */}
          <ScrollArea className="flex-1">
            <div className="flex gap-2 pb-1">
              {selectedItems.map((item) => (
                <Badge
                  key={item.id}
                  variant="secondary"
                  className="flex items-center gap-1 cursor-pointer hover:bg-secondary/80 whitespace-nowrap"
                >
                  <span onClick={() => setSelectedItemForDetail(item)}>{props.itemToString?.(item)}</span>
                  {!props.disabled && (
                    <Button
                      variant="ghost"
                      size="sm"
                      className="h-4 w-4 p-0 hover:bg-destructive hover:text-destructive-foreground"
                      onClick={() => setItemToRemove(item)}
                    >
                      <X className="h-3 w-3"/>
                    </Button>
                  )}
                </Badge>
              ))}
              {selectedItems.length === 0 && (
                <span className="text-muted-foreground text-sm">点击"选择"按钮添加项目</span>
              )}
            </div>
          </ScrollArea>

          {/* 选择按钮 */}
          <Button disabled={props.disabled} variant="outline" type="button" size="sm" onClick={handleOpenSearchDialog} className="shrink-0 bg-transparent">
            选择
          </Button>
        </div>
      </div>

      {/* 搜索对话框 */}
      <Dialog open={isSearchDialogOpen} onOpenChange={setIsSearchDialogOpen}>
        <DialogContent className="max-w-2xl max-h-[80vh]">
          <DialogHeader>
            <DialogTitle>添加项目</DialogTitle>
          </DialogHeader>

          <div className="space-y-4">
            {/* 搜索框 */}
            <div className="flex gap-2">
              <Input
                placeholder={props.placeholder}
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                onKeyDown={(e) => e.key === "Enter" && handleSearch()}
              />
              <Button onClick={handleSearch} disabled={isSearching}>
                <Search className="h-4 w-4"/>
                {isSearching ? "搜索中..." : "搜索"}
              </Button>
            </div>

            {/* 搜索结果 */}
            <ScrollArea className="h-[400px]">
              <div className="space-y-2">
                {isSearching ? (
                  <div className="text-center py-8 text-muted-foreground">搜索中...</div>
                ) : searchResults.length === 0 ? (
                  <div className="text-center py-8 text-muted-foreground">没有找到相关结果</div>
                ) : (
                  searchResults.map((item) => (
                    <div
                      key={item.id}
                      className="flex items-center justify-between p-3 border rounded-lg hover:bg-muted/50"
                    >
                      <div className="flex-1">
                        {props.renderSearchItem && props.renderSearchItem(item)}
                      </div>
                      <Button
                        size="sm"
                        variant={isItemSelected(item) ? "secondary" : "default"}
                        disabled={isItemSelected(item)}
                        onClick={() => handleAddItem(item)}
                      >
                        {isItemSelected(item) ? (
                          <>
                            <Check className="h-4 w-4 mr-1"/>
                            已选
                          </>
                        ) : (
                          <>
                            <Plus className="h-4 w-4 mr-1"/>
                            添加
                          </>
                        )}
                      </Button>
                    </div>
                  ))
                )}
              </div>
            </ScrollArea>
          </div>
        </DialogContent>
      </Dialog>

      {/* 项目详情对话框 */}
      <Dialog open={!!selectedItemForDetail} onOpenChange={() => setSelectedItemForDetail(null)}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>{selectedItemForDetail && props.itemToString?.(selectedItemForDetail)}</DialogTitle>
          </DialogHeader>
          {selectedItemForDetail && props.renderPreviewItem && props.renderPreviewItem(selectedItemForDetail)}
        </DialogContent>
      </Dialog>

      {/* 确认删除对话框 */}
      <AlertDialog open={!!itemToRemove} onOpenChange={() => setItemToRemove(null)}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>确认移除</AlertDialogTitle>
            <AlertDialogDescription>确定要移除 "{itemToRemove ? props.itemToString?.(itemToRemove) : ''}" 吗？。</AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>取消</AlertDialogCancel>
            <AlertDialogAction
              onClick={() => itemToRemove && handleRemoveItem(itemToRemove)}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              确认移除
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  )
}
