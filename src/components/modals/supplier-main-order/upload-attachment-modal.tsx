import React, { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, DialogTitle } from "@/components/ui/dialog";
import { But<PERSON> } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Upload, X, FileText, AlertCircle } from "lucide-react";
import { toast } from "sonner";
import { supplierMainOrderApi } from "@/api/order/supplier-main-order/supplier-main-order";
import { Alert, AlertDescription } from "@/components/ui/alert";

interface UploadAttachmentModalProps {
    onClose: () => void;
    orderId: string;
    onSuccess?: () => void;
}

const UploadAttachmentModal: React.FC<UploadAttachmentModalProps> = ({
    onClose,
    orderId,
    onSuccess
}) => {
    const [selectedFile, setSelectedFile] = useState<File | null>(null);
    const [isUploading, setIsUploading] = useState(false);
    const [dragActive, setDragActive] = useState(false);

    const allowedTypes = [
        '.zip', '.rar', '.7z', '.tar', '.gz',
        '.xlsx', '.xls', '.csv',
        '.pdf', '.doc', '.docx',
        '.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp',
        '.txt', '.json', '.xml'
    ];

    const [isOpen, setIsOpen] = useState(true);

    const maxSizeInMB = 5000;

    const validateFile = (file: File): string | null => {
        // 检查文件大小
        if (file.size > maxSizeInMB * 1024 * 1024) {
            return `文件大小不能超过 ${maxSizeInMB}MB`;
        }

        // 检查文件类型
        const fileName = file.name.toLowerCase();
        const isValidType = allowedTypes.some(type => fileName.endsWith(type));
        if (!isValidType) {
            return `不支持的文件类型。支持的类型：${allowedTypes.join(', ')}`;
        }

        return null;
    };

    const handleFileSelect = (file: File) => {
        const error = validateFile(file);
        if (error) {
            toast.error(error);
            return;
        }
        setSelectedFile(file);
    };

    const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
        const file = event.target.files?.[0];
        if (file) {
            handleFileSelect(file);
        }
    };

    const handleDrag = (e: React.DragEvent) => {
        e.preventDefault();
        e.stopPropagation();
        if (e.type === "dragenter" || e.type === "dragover") {
            setDragActive(true);
        } else if (e.type === "dragleave") {
            setDragActive(false);
        }
    };

    const handleDrop = (e: React.DragEvent) => {
        e.preventDefault();
        e.stopPropagation();
        setDragActive(false);

        const file = e.dataTransfer.files?.[0];
        if (file) {
            handleFileSelect(file);
        }
    };

    const handleUpload = async () => {
        if (!selectedFile) {
            toast.error('请选择要上传的文件');
            return;
        }

        setIsUploading(true);
        try {
            await supplierMainOrderApi.uploadAttachment(orderId, selectedFile);
            toast.success('附件上传成功');
            onSuccess?.();
            onClose();
            setIsOpen(false);
            setSelectedFile(null);
        } catch (error: any) {
            toast.error(`上传失败: ${error.message || '未知错误'}`);
        } finally {
            setIsUploading(false);
        }
    };

    const handleClose = () => {
        setIsOpen(false);
        if (!isUploading) {
            setSelectedFile(null);
            onClose();
        }
    };

    const formatFileSize = (bytes: number): string => {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    };

    return (
        <Dialog open={isOpen} onOpenChange={handleClose}>
            <DialogContent className="sm:max-w-md">
                <DialogHeader>
                    <DialogTitle>上传附件</DialogTitle>
                </DialogHeader>

                <div className="space-y-4">
                    <Alert>
                        <AlertCircle className="h-4 w-4" />
                        <AlertDescription>
                            支持的文件类型：压缩包、Excel、PDF、Word、图片、文本文件等
                            <br />
                            最大文件大小：{maxSizeInMB}MB
                        </AlertDescription>
                    </Alert>

                    <div className="space-y-2">
                        <Label>选择文件</Label>
                        <div
                            className={`border-2 border-dashed rounded-lg p-6 text-center transition-colors ${dragActive
                                ? 'border-blue-500 bg-blue-50'
                                : 'border-gray-300 hover:border-gray-400'
                                }`}
                            onDragEnter={handleDrag}
                            onDragLeave={handleDrag}
                            onDragOver={handleDrag}
                            onDrop={handleDrop}
                        >
                            {selectedFile ? (
                                <div className="space-y-2">
                                    <div className="flex items-center justify-center space-x-2">
                                        <FileText className="h-8 w-8 text-blue-500" />
                                        <div className="text-left">
                                            <p className="text-sm font-medium">{selectedFile.name}</p>
                                            <p className="text-xs text-gray-500">
                                                {formatFileSize(selectedFile.size)}
                                            </p>
                                        </div>
                                        <Button
                                            variant="ghost"
                                            size="sm"
                                            onClick={() => setSelectedFile(null)}
                                            disabled={isUploading}
                                        >
                                            <X className="h-4 w-4" />
                                        </Button>
                                    </div>
                                </div>
                            ) : (
                                <div className="space-y-2">
                                    <Upload className="h-8 w-8 text-gray-400 mx-auto" />
                                    <div>
                                        <p className="text-sm text-gray-600">
                                            拖拽文件到此处或
                                            <label className="text-blue-600 hover:text-blue-700 cursor-pointer">
                                                点击选择
                                                <input
                                                    type="file"
                                                    className="hidden"
                                                    onChange={handleFileChange}
                                                    accept={allowedTypes.join(',')}
                                                />
                                            </label>
                                        </p>
                                    </div>
                                </div>
                            )}
                        </div>
                    </div>

                    <div className="flex justify-end space-x-2">
                        <Button
                            variant="outline"
                            onClick={handleClose}
                            disabled={isUploading}
                        >
                            取消
                        </Button>
                        <Button
                            onClick={handleUpload}
                            disabled={!selectedFile || isUploading}
                        >
                            {isUploading ? '上传中...' : '上传'}
                        </Button>
                    </div>
                </div>
            </DialogContent>
        </Dialog>
    );
};

export default UploadAttachmentModal;
