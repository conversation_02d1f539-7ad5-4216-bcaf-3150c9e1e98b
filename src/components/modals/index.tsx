// file: src/modals/index.tsx (alias '@/modals')
import {createPushModal} from 'pushmodal'
import PermissionListModal from '../check/permission'
import AssignPermissionModal from './assign-permission'
import {CreateInitialBusinessModal} from './create-biz-modal'
import CreateCustomerModal from './customer/create-customer-modal'
import {CustomerSelected} from './customer/customer-selected'
import {SelectCustomerModal} from './customer/customer-spu-modal'
import {TaxSettingsDialog} from './customer/spu/create/tax-settings-dialog'
import {DoubleCheckModal} from './double-check-modal'
import {ImageGalleryModal} from './image-gallery-modal'
import ImportImageOrderModal from './import-image-order-modal'
import EnhancedOrderTaskModal from './main-order-tasks-modal'
import PDFViewerModal from './pdf-modal'
import RegisterModal from './register-modal'
import EditSupplierModal from './supplier/edit-supplier-modal'
import {SelectSupplierModal} from './supplier/supplier-select'
import UserCreationModal from './user/create-supplier'
import EditWaybillModal from './waybill/edit-waybill'
import {SetWaybillTaxModal} from './waybill/set-waybill-tax'
import ChannelSelectModels from './channel/channel-select-models'
import {TipSupplierPackageModals} from './package/tip-supplier-package-modals'
import {StopWaybillPrintModal} from './waybill/stop-print-modals'
import {WaybillRecordShowModal} from './waybill/waybill-record-show-modal'
import {SplitOrderModal} from './suborder/split/split-order-modals'
import {SplitedOrdersPreviewModal} from './suborder/split/split-orders-prview-modals'
import {CancelStopWaybillPrintModal} from './waybill/cancel-stop-print-modals'
import {CreateApiKeyModal} from './customer/create-api-key-modal'
import {WaybillExcelExportModels} from './waybill/waybill-excel-export-modal'
import ChangeSupplierOrderSupplierModals from './supplier-order/change-supplier-order-supplier-modals'
import UploadAttachmentModal from './supplier-main-order/upload-attachment-modal'
import EditSubOrderModal from "@/components/modals/suborder/edit-suborder-modal.tsx";
import {CreatePackageModal} from './package/create-package-modal'
import {EditPackageModal} from './package/edit-package-modal'
import ExtendWaybillModals from './waybill/extend-order-modal'
import {TrackingDetailModal} from './tracking/tracking-detail-modal'
import {TrackingStatisticsModal} from './tracking/tracking-statistics-modal'
import {MainOrderStatisticsModal} from './main-order/main-order-statistics-modal'
import {SubmitWaybillModal} from './waybill/submit-waybill-modal'
import CreateCustomerPlatformAccountModal from './customer/create-customer-platform-account-modal'
import EditCustomerPlatformAccountModal from './customer/edit-customer-platform-account-modal'
import EditChannelModal from './channel/edit-channel-modal'
import {SimpleInputModal, SimpleNumberInputModal, SimplePromptModal, SimpleSelectModal} from "@/components/modals/simple-prompt-modal.tsx";
// import CreateCustomerPlatformAccountModal from './customer/create-customer-platform-account-modal'
// import EditCustomerPlatformAccountModal from './customer/edit-customer-platform-account-modal'

export const {
    pushModal,
    popModal,
    popAllModals,
    replaceWithModal,
    useOnPushModal,
    onPushModal,
    ModalProvider
} = createPushModal({
    modals: {
        // Short hand
        RegisterModal,
        ImportImageOrderModal,
        DoubleCheckModal,
        ImageGalleryModal,
        EnhancedOrderTaskModal,
        AssignPermissionModal,
        PermissionListModal,
        UserCreationModal,
        CreateCustomerModal,
        EditSupplierModal,
        SelectSupplierModal,
        CustomerSelected,
        SelectCustomerModal,
        EditWaybillModal,
        CreateInitialBusinessModal,
        PDFViewerModal,
        TaxSettingsDialog,
        SetWaybillTaxModal,
        ChannelSelectModels,
        TipSupplierPackageModals,
        StopWaybillPrintModal,
        WaybillRecordShowModal,
        SplitOrderModal,
        SplitedOrdersPreviewModal,
        CancelStopWaybillPrintModal,
        CreateApiKeyModal,
        WaybillExcelExportModels,
        ChangeSupplierOrderSupplierModals,
        UploadAttachmentModal,
        EditSubOrderModal,
        CreatePackageModal,
        EditPackageModal,
        ExtendWaybillModals,
        TrackingDetailModal,
        TrackingStatisticsModal,
        MainOrderStatisticsModal,
        SubmitWaybillModal,
        CreateCustomerPlatformAccountModal,
        EditCustomerPlatformAccountModal,
      EditChannelModal,
      SimplePromptModal,
      SimpleInputModal,
      SimpleNumberInputModal,
      SimpleSelectModal
    },
})