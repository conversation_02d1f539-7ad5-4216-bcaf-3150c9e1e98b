import { SupplierConfig, UpdateSupplierRequest } from '@/api/supplier/supplier-model';
import { updateSupplier, updateSupplierPriority } from '@/api/supplier/supplier-api';
import { Button } from '@/components/ui/button';
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Form, FormControl, FormField, FormItem, FormLabel } from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { toast } from 'sonner';
import { useState } from 'react';

const editFormSchema = z.object({
    name: z.string().min(1, '供应商名称不能为空'),
    phone: z.string().min(1, '联系电话不能为空'),
    description: z.string(),
    priority: z.coerce.number().min(0, '优先级不能小于0'),
    larkRobotWebhook: z.string().optional(),
});

type EditFormValues = z.infer<typeof editFormSchema>;

interface EditSupplierModalProps {
    supplier: {
        id: string;
        name: string;
        phone: string;
        description: string;
        priority: number;
        config: SupplierConfig;
    };
    onSuccess?: () => void;
    onClose: () => void;
}

export default function EditSupplierModal({ supplier, onSuccess }: EditSupplierModalProps) {

    const [open, setOpen] = useState(true);
    const form = useForm<EditFormValues>({
        resolver: zodResolver(editFormSchema),
        defaultValues: {
            name: supplier.name,
            phone: supplier.phone,
            description: supplier.description || '',
            priority: supplier.priority,
            larkRobotWebhook: supplier.config?.larkRobotWebhook || '',
        },
    });

    const onSubmit = async (values: EditFormValues) => {
        try {
            // 更新基本信息
            const request: UpdateSupplierRequest = {
                name: values.name,
                phone: values.phone,
                description: values.description,
                config: {
                    larkRobotWebhook: values.larkRobotWebhook || undefined,
                },
            };
            await updateSupplier(supplier.id, request);

            // 更新优先级
            await updateSupplierPriority(supplier.id, values.priority);

            toast.success('更新成功');
            onSuccess?.();
            setOpen(false);
        } catch (error: any) {
            toast.error('更新失败: ' + error.message);
        }
    };

    return (
        <Dialog open={open} onOpenChange={setOpen}>
            <DialogContent>
                <DialogHeader>
                    <DialogTitle>编辑供应商</DialogTitle>
                </DialogHeader>
                <Form {...form}>
                    <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
                        <FormField
                            control={form.control}
                            name="name"
                            render={({ field }) => (
                                <FormItem>
                                    <FormLabel>供应商名称</FormLabel>
                                    <FormControl>
                                        <Input {...field} />
                                    </FormControl>
                                </FormItem>
                            )}
                        />
                        <FormField
                            control={form.control}
                            name="phone"
                            render={({ field }) => (
                                <FormItem>
                                    <FormLabel>联系电话</FormLabel>
                                    <FormControl>
                                        <Input {...field} />
                                    </FormControl>
                                </FormItem>
                            )}
                        />
                        <FormField
                            control={form.control}
                            name="description"
                            render={({ field }) => (
                                <FormItem>
                                    <FormLabel>描述</FormLabel>
                                    <FormControl>
                                        <Input {...field} />
                                    </FormControl>
                                </FormItem>
                            )}
                        />
                        <FormField
                            control={form.control}
                            name="priority"
                            render={({ field }) => (
                                <FormItem>
                                    <FormLabel>优先级</FormLabel>
                                    <FormControl>
                                        <Input
                                            type="number"
                                            {...field}
                                            onChange={e => field.onChange(parseInt(e.target.value))}
                                        />
                                    </FormControl>
                                </FormItem>
                            )}
                        />
                        <FormField
                            control={form.control}
                            name="larkRobotWebhook"
                            render={({ field }) => (
                                <FormItem>
                                    <FormLabel>Lark机器人Webhook</FormLabel>
                                    <FormControl>
                                        <Input {...field} />
                                    </FormControl>
                                </FormItem>
                            )}
                        />
                        <div className="flex justify-end space-x-2">
                            <Button variant="outline" type="button" onClick={() => setOpen(false)}>
                                取消
                            </Button>
                            <Button type="submit">
                                保存
                            </Button>
                        </div>
                    </form>
                </Form>
            </DialogContent>
        </Dialog>
    );
} 