import {useState} from 'react';
import * as dialog from "@/components/ui/dialog";
import {But<PERSON>} from "@/components/ui/button";
import {Select, SelectContent, SelectItem, SelectTrigger, SelectValue} from "@/components/ui/select";
import {useRequest} from "ahooks";
import {selectSupplier} from "@/api/supplier/supplier-api";
import {SupplierSelectedResponse} from "@/api/supplier/supplier-model";

interface SelectSupplierModalProps {
    onSelect: (supplierId: string) => void;
    onClose: () => void;
}

export function SelectSupplierModal({onSelect, onClose}: SelectSupplierModalProps) {
    const [selectedSupplierId, setSelectedSupplierId] = useState<string | null>(null);

    const [isOpen, setIsOpen] = useState(true);

    const {data: suppliers = []} = useRequest<SupplierSelectedResponse[], any>(selectSupplier, {
        // Add any necessary options
    });

    const handleConfirm = () => {
        if (selectedSupplierId) {
            onSelect(selectedSupplierId);
            setIsOpen(false)
        }
    };

    return (
        <dialog.Dialog open={isOpen} onOpenChange={setIsOpen}>
            <dialog.DialogContent>
                <dialog.DialogHeader>
                    <dialog.DialogTitle>选择供应商</dialog.DialogTitle>
                </dialog.DialogHeader>
                <Select
                    value={selectedSupplierId || ''}
                    onValueChange={(value) => setSelectedSupplierId(value)}
                >
                    <SelectTrigger>
                        <SelectValue placeholder="选择供应商"/>
                    </SelectTrigger>
                    <SelectContent>
                        {suppliers.map((supplier) => (
                            <SelectItem key={supplier.id} value={supplier.id}>
                                {supplier.name}
                            </SelectItem>
                        ))}
                    </SelectContent>
                </Select>
                <dialog.DialogFooter>
                    <Button onClick={onClose} variant="outline">取消</Button>
                    <Button onClick={handleConfirm} disabled={!selectedSupplierId}>确定</Button>
                </dialog.DialogFooter>
            </dialog.DialogContent>
        </dialog.Dialog>
    );
}