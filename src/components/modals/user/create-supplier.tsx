import React, { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { useRequest } from "ahooks";
import { useSet<PERSON>tom } from "jotai";
import { toast } from 'sonner';
import {
    Dialog,
    DialogContent,
    DialogDescription,
    DialogFooter,
    DialogHeader,
    DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import {
    Form,
    FormControl,
    FormDescription,
    FormField,
    FormItem,
    FormLabel,
    FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Checkbox } from "@/components/ui/checkbox";
import {
    Select,
    SelectContent,
    SelectGroup,
    SelectItem,
    SelectLabel,
    SelectTrigger,
    SelectValue,
} from "@/components/ui/select";
import { refreshTableAtom } from '@/state/common';
import { createUser, CreateUserCmd } from '@/api/user/user-api';
import { getRoleList } from '@/api/user/role-api';

const userSchema = z.object({
    name: z.string().min(1, '姓名是必填项'),
    account: z.string().min(1, '账号是必填项'),
    password: z.string().min(6, '密码至少需要6个字符'),
    roleId: z.string().min(1, '必须选择角色'),
    isSupplier: z.boolean().default(false),
    supplierName: z.string().optional(),
    supplierDescription: z.string().optional(),
    phone: z.string().optional(),
});

type UserFormValues = z.infer<typeof userSchema>;

const UserCreationModal: React.FC = () => {
    const [open, setOpen] = useState(true);
    const setRefresh = useSetAtom(refreshTableAtom);

    const form = useForm<UserFormValues>({
        resolver: zodResolver(userSchema),
        defaultValues: {
            name: '',
            account: '',
            password: '',
            roleId: '',
            isSupplier: false,
            supplierName: '',
            supplierDescription: '',
        },
    });

    const { loading: createUserLoading, runAsync: createUserAsync } = useRequest(createUser, {
        manual: true,
        onSuccess: () => {
            toast.success("用户创建成功");
            setOpen(false);
            setRefresh((prev) => prev + 1);
        },
        onError: (error) => {
            toast.error(error.message);
        },
    });


    const { data: roles, loading: rolesLoading } = useRequest(getRoleList, {});

    const handleSubmit = async (data: UserFormValues) => {
        const createUserCmd: CreateUserCmd = {
            name: data.name,
            account: data.account,
            password: data.password,
            roleId: data.roleId,
        };

        if (data.isSupplier) {
            createUserCmd.supplierName = data.supplierName;
            createUserCmd.supplierDescription = data.supplierDescription;
            createUserCmd.supplierPhone = data.phone;
        }

        await createUserAsync(createUserCmd);
    };

    return (
        <Dialog open={open} onOpenChange={setOpen}>
            <DialogContent className="sm:max-w-[700px]">
                <DialogHeader>
                    <DialogTitle>创建新用户</DialogTitle>
                    <DialogDescription>
                        请填写以下信息以创建新用户
                    </DialogDescription>
                </DialogHeader>
                <Form {...form}>
                    <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-4">
                        <div className="grid grid-cols-2 gap-4">
                            <FormField
                                control={form.control}
                                name="name"
                                render={({ field }) => (
                                    <FormItem>
                                        <FormLabel>姓名</FormLabel>
                                        <FormControl>
                                            <Input {...field} />
                                        </FormControl>
                                        <FormMessage />
                                    </FormItem>
                                )}
                            />
                            <FormField
                                control={form.control}
                                name="account"
                                render={({ field }) => (
                                    <FormItem>
                                        <FormLabel>账号</FormLabel>
                                        <FormControl>
                                            <Input {...field} />
                                        </FormControl>
                                        <FormMessage />
                                    </FormItem>
                                )}
                            />
                            <FormField
                                control={form.control}
                                name="password"
                                render={({ field }) => (
                                    <FormItem>
                                        <FormLabel>密码</FormLabel>
                                        <FormControl>
                                            <Input type="password" {...field} />
                                        </FormControl>
                                        <FormMessage />
                                    </FormItem>
                                )}
                            />
                            <FormField
                                control={form.control}
                                name="roleId"
                                render={({ field }) => (
                                    <FormItem>
                                        <FormLabel>角色</FormLabel>
                                        <Select disabled={rolesLoading} onValueChange={field.onChange}>
                                            <FormControl>
                                                <SelectTrigger>
                                                    <SelectValue placeholder="选择角色" />
                                                </SelectTrigger>
                                            </FormControl>
                                            <SelectContent>
                                                <SelectGroup>
                                                    <SelectLabel>角色列表</SelectLabel>
                                                    {roles?.map((role) => (
                                                        <SelectItem key={role.id} value={role.id?.toString() ?? ''}>
                                                            {role.name}
                                                        </SelectItem>
                                                    ))}
                                                </SelectGroup>
                                            </SelectContent>
                                        </Select>
                                        <FormMessage />
                                    </FormItem>
                                )}
                            />
                        </div>
                        <FormField
                            control={form.control}
                            name="isSupplier"
                            render={({ field }) => (
                                <FormItem className="flex flex-row items-start space-x-3 space-y-0">
                                    <FormControl>
                                        <Checkbox
                                            checked={field.value}
                                            onCheckedChange={field.onChange}
                                        />
                                    </FormControl>
                                    <div className="space-y-1 leading-none">
                                        <FormLabel>该用户是供应商吗？</FormLabel>
                                        <FormDescription>
                                            如果该用户同时也是供应商，请勾选此项。
                                        </FormDescription>
                                    </div>
                                </FormItem>
                            )}
                        />
                        {form.watch('isSupplier') && (
                            <div className="grid grid-cols-2 gap-4">
                                <FormField
                                    control={form.control}
                                    name="supplierName"
                                    render={({ field }) => (
                                        <FormItem>
                                            <FormLabel>供应商名称</FormLabel>
                                            <FormControl>
                                                <Input {...field} />
                                            </FormControl>
                                            <FormMessage />
                                        </FormItem>
                                    )}
                                />
                                <FormField
                                    control={form.control}
                                    name="supplierDescription"
                                    render={({ field }) => (
                                        <FormItem>
                                            <FormLabel>供应商描述</FormLabel>
                                            <FormControl>
                                                <Input {...field} />
                                            </FormControl>
                                            <FormMessage />
                                        </FormItem>
                                    )}
                                />
                                <FormField
                                    control={form.control}
                                    name="phone"
                                    render={({ field }) => (
                                        <FormItem>
                                            <FormLabel>手机号</FormLabel>
                                            <FormControl>
                                                <Input {...field} />
                                            </FormControl>
                                            <FormMessage />
                                        </FormItem>
                                    )}
                                />
                            </div>
                        )}
                        <DialogFooter>
                            <Button type="submit" disabled={createUserLoading}>
                                {createUserLoading ? '创建中...' : '创建用户'}
                            </Button>
                        </DialogFooter>
                    </form>
                </Form>
            </DialogContent>
        </Dialog>
    );
};

export default UserCreationModal;