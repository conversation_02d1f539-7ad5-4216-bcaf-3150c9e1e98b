import { selectSupplier } from "@/api/supplier/supplier-api";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { useRequest } from "ahooks";
import { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Button } from "@/components/ui/button";
import { supplierMainOrderApi } from "@/api/order/supplier-main-order/supplier-main-order";
import { toast } from "sonner";
import { useSetAtom } from "jotai";
import { refreshTableAtom } from "@/state/common";

const formSchema = z.object({
    supplierId: z.string().min(1, "请选择一个工厂"),
});

type ChangeSupplierOrderSupplierModalsProps = {
    orderId: string
}

export default function ChangeSupplierOrderSupplierModals({ orderId }: ChangeSupplierOrderSupplierModalsProps) {

    const [open, setOpen] = useState(true);


    const refresh = useSetAtom(refreshTableAtom)

    const form = useForm<z.infer<typeof formSchema>>({
        resolver: zodResolver(formSchema),
        defaultValues: {
            supplierId: "",
        },
    });

    const { data: suppliers, loading: suppliersLoading } = useRequest(selectSupplier, {
        manual: false,
    });

    const { run: changeSupplier, loading: changeSupplierLoading } = useRequest(
        async (values: z.infer<typeof formSchema>) => {
            await supplierMainOrderApi.changeSupplier(orderId, values.supplierId);
        },
        {
            manual: true,
            onSuccess: () => {
                toast.success("切换工厂成功");
                setOpen(false);
                refresh(prev => prev + 1)
            },
            onError: (error) => {
                toast.error(error.message || "切换工厂失败");
            },
        }
    );

    function onSubmit(values: z.infer<typeof formSchema>) {
        changeSupplier(values);
    }

    return (
        <Dialog open={open} onOpenChange={setOpen}>
            <DialogContent>
                <DialogHeader>
                    <DialogTitle>切换工厂</DialogTitle>
                    <DialogDescription>请选择需要切换的工厂</DialogDescription>
                </DialogHeader>
                <Form {...form}>
                    <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
                        <FormField
                            control={form.control}
                            name="supplierId"
                            render={({ field }) => (
                                <FormItem>
                                    <FormLabel>工厂</FormLabel>
                                    <Select onValueChange={field.onChange} defaultValue={field.value} disabled={suppliersLoading}>
                                        <FormControl>
                                            <SelectTrigger>
                                                <SelectValue placeholder="选择一个工厂" />
                                            </SelectTrigger>
                                        </FormControl>
                                        <SelectContent>
                                            {suppliers?.map((supplier) => (
                                                <SelectItem key={supplier.id} value={supplier.id}>
                                                    {supplier.name}
                                                </SelectItem>
                                            ))}
                                        </SelectContent>
                                    </Select>
                                    <FormMessage />
                                </FormItem>
                            )}
                        />
                        <Button type="submit" disabled={changeSupplierLoading || suppliersLoading}>
                            {changeSupplierLoading ? "提交中..." : "确认切换"}
                        </Button>
                    </form>
                </Form>
            </DialogContent>
        </Dialog>
    )
}