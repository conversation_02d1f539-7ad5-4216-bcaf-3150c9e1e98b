import { PasswordInput } from "@/components/inputs/password-input"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>alog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { zodResolver } from "@hookform/resolvers/zod"
import { AnimatePresence, motion } from "framer-motion"
import { useState } from "react"
import { useForm } from "react-hook-form"
import * as z from "zod"

import { registerUser } from "@/api/user/user-api"
import { useRequest } from "ahooks"
import { Loader2 } from "lucide-react"
import { toast } from "sonner"

const registerSchema = z.object({
    account: z.string().min(1, "账户不能为空"),
    password: z.string().min(6, "密码至少需要6个字符"),
    confirmPassword: z.string().min(6, "确认密码至少需要6个字符")
}).refine((data) => data.password === data.confirmPassword, {
    message: "两次输入的密码不一致",
    path: ["confirmPassword"],
})

type RegisterFormValues = z.infer<typeof registerSchema>

export default function RegisterModal() {
    const [isOpen, setIsOpen] = useState(true)

    const {
        register,
        handleSubmit,
        formState: { errors },
    } = useForm<RegisterFormValues>({
        resolver: zodResolver(registerSchema)
    })


    const { runAsync: registerRun, loading } = useRequest(registerUser, {
        manual: true,
        onSuccess: () => {
            toast.success("注册成功")
            setIsOpen(false)
        },
        onError: (error) => {
            toast.error(error.message)
        }
    })

    const onSubmit = async (data: RegisterFormValues) => {
        console.log(data)
        // 这里处理注册逻辑
        await registerRun({
            username: data.account,
            password: data.password,
        })
    }

    return (
        <Dialog open={isOpen} onOpenChange={setIsOpen}>
            <AnimatePresence>
                {isOpen && (
                    <DialogContent className="sm:max-w-[425px] p-0 bg-transparent border-none shadow-none">
                        <motion.div
                            initial={{ scale: 0.95, opacity: 0 }}
                            animate={{ scale: 1, opacity: 1 }}
                            exit={{ scale: 0.95, opacity: 0 }}
                            transition={{ duration: 0.3 }}
                            className="bg-white bg-opacity-90 backdrop-blur-sm rounded-lg shadow-xl overflow-hidden"
                        >
                            <DialogHeader className="p-6 pb-0">
                                <DialogTitle className="text-2xl font-semibold text-gray-800">注册</DialogTitle>
                                <DialogDescription className="text-sm text-gray-600">
                                    创建一个新账户以开始使用我们的服务
                                </DialogDescription>
                            </DialogHeader>
                            <form onSubmit={handleSubmit(onSubmit)} className="space-y-4 p-6">
                                <motion.div
                                    initial={{ y: 20, opacity: 0 }}
                                    animate={{ y: 0, opacity: 1 }}
                                    transition={{ delay: 0.1, duration: 0.3 }}
                                    className="space-y-2"
                                >
                                    <Label htmlFor="account" className="text-sm font-medium text-gray-700">
                                        账户
                                    </Label>
                                    <Input
                                        id="account"
                                        placeholder="请输入您的账户"
                                        {...register("account")}
                                        className="bg-white bg-opacity-75"
                                    />
                                    {errors.account && <p className="text-red-500 text-xs mt-1">{errors.account.message}</p>}
                                </motion.div>
                                <motion.div
                                    initial={{ y: 20, opacity: 0 }}
                                    animate={{ y: 0, opacity: 1 }}
                                    transition={{ delay: 0.3, duration: 0.3 }}
                                    className="space-y-2"
                                >
                                    <Label htmlFor="password" className="text-sm font-medium text-gray-700">
                                        密码
                                    </Label>
                                    <PasswordInput
                                        id="password"
                                        register={register("password")}
                                        error={errors.password?.message}
                                    />
                                </motion.div>
                                <motion.div
                                    initial={{ y: 20, opacity: 0 }}
                                    animate={{ y: 0, opacity: 1 }}
                                    transition={{ delay: 0.4, duration: 0.3 }}
                                    className="space-y-2"
                                >
                                    <Label htmlFor="confirmPassword" className="text-sm font-medium text-gray-700">
                                        确认密码
                                    </Label>
                                    <PasswordInput
                                        id="confirmPassword"
                                        register={register("confirmPassword")}
                                        error={errors.confirmPassword?.message}
                                    />
                                </motion.div>
                                <motion.div
                                    initial={{ y: 20, opacity: 0 }}
                                    animate={{ y: 0, opacity: 1 }}
                                    transition={{ delay: 0.5, duration: 0.3 }}
                                >
                                    <Button type="submit" disabled={loading} className="w-full bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-md transition duration-300 ease-in-out">
                                        {loading ? <Loader2 className="mr-2 h-4 w-4 animate-spin" /> : "注册"}
                                    </Button>
                                </motion.div>
                            </form>
                            <motion.div
                                initial={{ y: 20, opacity: 0 }}
                                animate={{ y: 0, opacity: 1 }}
                                transition={{ delay: 0.6, duration: 0.3 }}
                                className="px-6 py-4 bg-gray-50 bg-opacity-75 border-t border-gray-200 text-center"
                            >
                                <p className="text-sm text-gray-600">
                                    已有账号？
                                    <Button variant="link" className={"text-blue-600 hover:text-blue-800 transition duration-300"} onClick={() => setIsOpen(false)} disabled={loading}>
                                        立即登录
                                    </Button>
                                </p>
                            </motion.div>
                        </motion.div>
                    </DialogContent>
                )}
            </AnimatePresence>
        </Dialog>
    )
}