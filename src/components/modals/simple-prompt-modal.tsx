import React, {useEffect} from "react";
import {Input} from "@/components/ui/input.tsx";
import {Popover, PopoverContent, PopoverTrigger} from "@/components/ui/popover.tsx";
import {But<PERSON>} from "@/components/ui/button.tsx";
import {CheckIcon, ChevronsUpDownIcon} from "lucide-react";
import {Command, CommandEmpty, CommandGroup, CommandInput, CommandItem, CommandList} from "@/components/ui/command.tsx";
import {cn} from "@/lib/utils.ts";
import {Dialog, DialogContent, DialogHeader, DialogTitle} from "@/components/ui/dialog.tsx";
import {twMerge} from "tailwind-merge";

interface SimplePromptModalProps {
  onConfirm: () => void;
  canConfirm?: boolean;
  onCancel?: () => void;
  title: string;
  description?: string;
  children?: React.ReactNode;
}

export function SimplePromptModal(props: SimplePromptModalProps) {
  const {onConfirm, canConfirm, onCancel, title, description, children} = props;

  const [open, setOpen] = React.useState(true);

  const handleConfirm = () => {
    onConfirm();
    setOpen(false);
  }

  const handleCancel = () => {
    if (onCancel) {
      onCancel();
    }
    setOpen(false);
  }

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle className="text-xl font-semibold">{title}</DialogTitle>
        </DialogHeader>
        <div className="flex items-center justify-center">
          <div className="w-full">
            {description && <p className="mb-4">{description}</p>}
            {children && <div className="mb-4">{children}</div>}
            <div className="flex justify-end space-x-2">
              <button
                onClick={handleCancel}
                type="button"
                className="px-4 py-2 bg-gray-200 text-gray-800 rounded hover:bg-gray-300"
              >
                取消
              </button>
              <button
                onClick={handleConfirm}
                type="button"
                disabled={canConfirm === false}
                className={twMerge("px-4 py-2 text-white rounded", canConfirm === false ? "bg-blue-200 opacity-50 cursor-not-allowed" : "bg-blue-600 hover:bg-blue-700")}
              >
                确认
              </button>
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}

interface SimpleInputModalProps {
  onConfirm: (value: string) => void;
  canEmpty?: boolean;
  onCancel?: () => void;
  initialValue?: string;
  title: string;
  description?: string;
  placeholder?: string;
  pattern?: RegExp
}

export function SimpleInputModal(props: SimpleInputModalProps) {
  const {onConfirm, canEmpty, onCancel, title, description, placeholder, pattern, initialValue} = props;
  const [inputValue, setInputValue] = React.useState(initialValue ?? "");
  const [canConfirm, setCanConfirm] = React.useState(true);

  const handleConfirm = () => {
    onConfirm(inputValue);
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setInputValue(e.target.value);
    if (!canEmpty && e.target.value.trim() === "") {
      setCanConfirm(false);
      return;
    }
    if (pattern && !pattern.test(e.target.value)) {
      setCanConfirm(false)
      return;
    }
    setCanConfirm(true)
  }

  return (
    <SimplePromptModal
      onConfirm={handleConfirm}
      canConfirm={canConfirm}
      onCancel={onCancel}
      title={title}
      description={description}
    >
      <Input
        type="text"
        value={inputValue}
        onChange={handleInputChange}
        placeholder={placeholder}
        className="w-full p-2 border rounded"
      />
    </SimplePromptModal>
  );
}

interface SimpleNumberInputModalProps {
  onConfirm: (value: number | undefined) => void;
  allowEmpty?: boolean;
  onCancel?: () => void;
  initialValue?: number;
  max?: number;
  min?: number;
  decimalPlaces?: number;
  title: string;
  description?: string;
  placeholder?: string;
}

export function SimpleNumberInputModal(props: SimpleNumberInputModalProps) {
  const {onConfirm, allowEmpty, onCancel, max, min, decimalPlaces, title, description, placeholder, initialValue} = props;
  const [finalValue, setFinalValue] = React.useState<number | undefined>(initialValue);
  const [inputValue, setInputValue] = React.useState(initialValue?.toString() ?? "");
  const [canConfirm, setCanConfirm] = React.useState(true);

  useEffect(() => {
    if (finalValue === undefined && allowEmpty === false) {
      setCanConfirm(false)
    } else {
      setCanConfirm(true)
    }
  }, [allowEmpty, finalValue])

  const handleConfirm = () => {
    onConfirm(finalValue);
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    // 禁止 e/E
    if (e.key === "e" || e.key === "E") {
      e.preventDefault()
      return
    }

    // 允许空值时直接输入负号
    if (e.key === "-" && (inputValue === "" || inputValue === "-")) {
      return
    }
  }

  const handleInput = (e: React.ChangeEvent<HTMLInputElement>) => {
    let val = e.target.value

    // 允许空值
    if (val === "") {
      setInputValue("")
      setFinalValue(undefined)
      return
    }

    // 保留负号（首位）
    val = val.replace(/[^0-9.-]/g, "")

    // 如果开头有负号，后面不能再有负号
    val = val.replace(/(?!^)-/g, "")

    // 小数点只保留一个
    val = val.replace(/(\..*)\./g, "$1")

    // 限制小数位数
    if (decimalPlaces !== undefined && decimalPlaces >= 0 && val.includes(".")) {
      const [intPart, decPart] = val.split(".")
      val = intPart + "." + decPart.slice(0, decimalPlaces)
    }

    // 转成 number 检查 min/max
    let numVal = Number(val)
    if (!isNaN(numVal)) {
      if (min !== undefined && numVal < min) numVal = min
      if (max !== undefined && numVal > max) numVal = max
    }

    setInputValue(val)
    setFinalValue(val === "-" || val === "." || val === "-." ? undefined : numVal)
  }
  //
  // const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
  //   console.log("input change: ", e.target.value);
  //   const value = e.target.value
  //   // if has '.' , calc precision
  //   if (precision && value.includes(".")) {
  //     const parts = value.split(".");
  //     if (parts.length > 2 || (parts[1] && parts[1].length > precision)) {
  //       return;
  //     }
  //   }
  //   setInputValue(value);
  //   if ((value.trim() === "" && allowEmpty === false) || isNaN(Number(value))) {
  //     console.log("Invalid input: ", value);
  //     setCanConfirm(false);
  //     return;
  //   }
  //   const numValue = Number(value);
  //   if ((max !== undefined && numValue > max) || (min !== undefined && numValue < min)) {
  //     setCanConfirm(false);
  //     return;
  //   }
  //   setCanConfirm(true);
  // };

  return (
    <SimplePromptModal
      onConfirm={handleConfirm}
      canConfirm={canConfirm}
      onCancel={onCancel}
      title={title}
      description={description}
    >
      <Input
        type="text"
        inputMode="decimal"
        max={max}
        min={min}
        value={inputValue}
        onKeyDown={handleKeyDown}
        onChange={handleInput}
        placeholder={placeholder}
        className="w-full p-2 border rounded"
      />
    </SimplePromptModal>
  );
}

interface SimpleSelectModalProps1 {
  onConfirm: (value: string | undefined) => void;
  canEmpty?: true;
  onCancel?: () => void;
  title: string;
  description?: string;
  options: { value: string, label: string }[];
}

interface SimpleSelectModalProps2 {
  onConfirm: (value: string) => void;
  canEmpty: false
  onCancel: () => void;
  title: string;
  description?: string;
  options: { value: string, label: string }[];
}

type SimpleSelectModalProps = SimpleSelectModalProps1 | SimpleSelectModalProps2

export function SimpleSelectModal(props: SimpleSelectModalProps) {
  const {onConfirm, onCancel, title, description, options, canEmpty} = props;

  const [open, setOpen] = React.useState(false)
  const [value, setValue] = React.useState<string | undefined>(undefined)

  const handleConfirm = () => {
    onConfirm(value as any);
  };

  return (
    <SimplePromptModal
      onConfirm={handleConfirm}
      canConfirm={canEmpty !== false || value !== undefined}
      onCancel={onCancel}
      title={title}
      description={description}
    >
      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild>
          <Button
            variant="outline"
            role="combobox"
            aria-expanded={open}
            className="w-[200px] justify-between rounded-xl"
          >
            {value
              ? options.find((option) => option.value === value)?.label
              : "选择一项..."}
            <ChevronsUpDownIcon className="ml-2 h-4 w-4 shrink-0 opacity-50"/>
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-[200px] p-0">
          <Command className="rounded-xl">
            <CommandInput placeholder="搜索..."/>
            <CommandEmpty>没有结果</CommandEmpty>
            <CommandGroup>
              <CommandList>
                {options.map((option) => (
                  <CommandItem
                    key={option.value}
                    value={option.value}
                    onSelect={(currentValue) => {
                      setValue(currentValue === value ? undefined : currentValue)
                      setOpen(false)
                    }}
                  >
                    <CheckIcon className={cn(
                      "mr-2 h-4 w-4",
                      value === option.value ? "opacity-100" : "opacity-0"
                    )}/>
                    {option.label}
                  </CommandItem>
                ))}</CommandList>
            </CommandGroup>
          </Command>
        </PopoverContent>
      </Popover>
    </SimplePromptModal>
  );
}