import {
    toast
} from "sonner"
import {
    useForm
} from "react-hook-form"
import {
    zodResolver
} from "@hookform/resolvers/zod"
import * as z from "zod"
import {
    <PERSON><PERSON>
} from "@/components/ui/button"
import {
    Form,
    FormControl,
    FormField,
    FormItem,
    FormLabel,
    FormMessage,
} from "@/components/ui/form"
import {
    Input
} from "@/components/ui/input"
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { useState } from "react"
import { addCustomer } from "@/api/customer/customer-api"
import { useSetAtom } from "jotai"
import { refreshTableAtom } from "@/state/common"

const formSchema = z.object({
    name: z.string(),
    email: z.string().email()
});

export default function CreateCustomerModal({ isEdit = false }: { isEdit: boolean }) {

    const refersh = useSetAtom(refreshTable<PERSON>tom)

    const [open, setOpen] = useState(true);

    const form = useForm<z.infer<typeof formSchema>>({
        resolver: zodResolver(formSchema),

    })

    async function onSubmit(values: z.infer<typeof formSchema>) {
        toast.promise(addCustomer(values.name, values.email), {
            loading: "正在创建客户...",
            success: () => {
                setOpen(false)
                refersh(prev => prev + 1)
                return "客户创建成功"
            },
            error: "客户创建失败"
        });
    }

    return (
        <Dialog open={open} onOpenChange={setOpen}>
            <DialogContent>
                <DialogHeader className="pb-0">
                    <DialogTitle className="text-2xl font-semibold text-gray-800">
                        {isEdit ? "编辑客户" : "创建客户"}
                    </DialogTitle>
                    <DialogDescription className="text-sm text-gray-600">
                        {isEdit ? "编辑客户信息" : "创建一个新客户"}
                    </DialogDescription>
                </DialogHeader>
                <Form {...form}>
                    <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4 max-w-2xl  flex flex-col ">
                        <FormField
                            control={form.control}
                            name="name"
                            render={({ field }) => (
                                <FormItem>
                                    <FormLabel>客户名称</FormLabel>
                                    <FormControl>
                                        <Input
                                            placeholder="shadcn"

                                            type=""
                                            {...field} />
                                    </FormControl>
                                    <FormMessage />
                                </FormItem>
                            )}
                        />
                        <FormField
                            control={form.control}
                            name="email"
                            render={({ field }) => (
                                <FormItem>
                                    <FormLabel>客户邮箱</FormLabel>
                                    <FormControl>
                                        <Input
                                            placeholder="<EMAIL>"
                                            type="email"
                                            {...field} />
                                    </FormControl>
                                    <FormMessage />
                                </FormItem>
                            )}
                        />
                        <Button type="submit">提交</Button>
                    </form>
                </Form>
            </DialogContent>
        </Dialog>
    )
}