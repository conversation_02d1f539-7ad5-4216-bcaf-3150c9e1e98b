import { toast } from "sonner";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { Button } from "@/components/ui/button";
import {
    Form,
    FormControl,
    FormField,
    FormItem,
    FormLabel,
    FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { useState, useEffect } from "react";
import { 
    getCustomerPlatformAccount, 
    updateCustomerPlatformAccount,
    CustomerPlatformAccountInfo 
} from "@/api/customer/customer-platform-account-api";
import { useSet<PERSON>tom } from "jotai";
import { refreshTable<PERSON><PERSON> } from "@/state/common";
import { Eye, EyeOff } from "lucide-react";
import { useRequest } from "ahooks";

const formSchema = z.object({
    email: z.string().email("请输入有效的邮箱地址"),
    password: z.string()
        .optional()
        .refine((val) => {
            if (!val || val === "") return true; // 允许空密码（不修改）
            return val.length >= 8 && val.length <= 32 &&
                   /[A-Z]/.test(val) &&
                   /[a-z]/.test(val) &&
                   /[0-9]/.test(val) &&
                   /[!@#$%^&*()_+\-=\[\]{}|;:,.<>?]/.test(val);
        }, "密码必须包含大小写字母、数字和特殊字符，长度8-32位"),
    accountName: z.string().min(1, "请输入账户名称"),
    description: z.string().optional(),
    status: z.enum(['ENABLED', 'DISABLED']),
});

interface EditCustomerPlatformAccountModalProps {
    accountId: string;
}

export default function EditCustomerPlatformAccountModal({ accountId }: EditCustomerPlatformAccountModalProps) {
    const refresh = useSetAtom(refreshTableAtom);
    const [open, setOpen] = useState(true);
    const [showPassword, setShowPassword] = useState(false);

    const { data: accountData, loading } = useRequest(
        () => getCustomerPlatformAccount(accountId),
        {
            ready: !!accountId
        }
    );

    const form = useForm<z.infer<typeof formSchema>>({
        resolver: zodResolver(formSchema),
        defaultValues: {
            email: "",
            password: "",
            accountName: "",
            description: "",
            status: 'ENABLED' as const,
        }
    });

    useEffect(() => {
        if (accountData) {
            form.reset({
                email: accountData.email,
                password: "", // 不显示现有密码
                accountName: accountData.accountName,
                description: accountData.description,
                status: accountData.status,
            });
        }
    }, [accountData, form]);

    async function onSubmit(values: z.infer<typeof formSchema>) {
        const updateData = {
            ...values,
            password: values.password || undefined, // 如果密码为空，则不更新密码
        };

        toast.promise(updateCustomerPlatformAccount(accountId, updateData), {
            loading: "正在更新客户平台账户...",
            success: () => {
                setOpen(false);
                refresh(prev => prev + 1);
                return "客户平台账户更新成功";
            },
            error: (error) => {
                console.error('更新失败:', error);
                return error?.response?.data?.msg || "客户平台账户更新失败";
            }
        });
    }

    if (loading) {
        return (
            <Dialog open={open} onOpenChange={setOpen}>
                <DialogContent>
                    <div className="flex items-center justify-center p-8">
                        <div className="text-center">
                            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
                            <p className="mt-2 text-sm text-muted-foreground">加载中...</p>
                        </div>
                    </div>
                </DialogContent>
            </Dialog>
        );
    }

    return (
        <Dialog open={open} onOpenChange={setOpen}>
            <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
                <DialogHeader className="pb-0">
                    <DialogTitle className="text-2xl font-semibold text-gray-800">
                        编辑客户平台账户
                    </DialogTitle>
                    <DialogDescription className="text-sm text-gray-600">
                        修改客户平台账户信息
                    </DialogDescription>
                </DialogHeader>
                <Form {...form}>
                    <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4 max-w-2xl flex flex-col">
                        <FormField
                            control={form.control}
                            name="email"
                            render={({ field }) => (
                                <FormItem>
                                    <FormLabel>邮箱 *</FormLabel>
                                    <FormControl>
                                        <Input
                                            placeholder="请输入邮箱地址"
                                            type="email"
                                            {...field}
                                        />
                                    </FormControl>
                                    <FormMessage />
                                </FormItem>
                            )}
                        />
                        
                        <FormField
                            control={form.control}
                            name="password"
                            render={({ field }) => (
                                <FormItem>
                                    <FormLabel>密码</FormLabel>
                                    <FormControl>
                                        <div className="relative">
                                            <Input
                                                placeholder="留空则不修改密码"
                                                type={showPassword ? "text" : "password"}
                                                {...field}
                                            />
                                            <Button
                                                type="button"
                                                variant="ghost"
                                                size="sm"
                                                className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                                                onClick={() => setShowPassword(!showPassword)}
                                            >
                                                {showPassword ? (
                                                    <EyeOff className="h-4 w-4" />
                                                ) : (
                                                    <Eye className="h-4 w-4" />
                                                )}
                                            </Button>
                                        </div>
                                    </FormControl>
                                    <div className="text-xs text-muted-foreground">
                                        留空则不修改密码。如需修改：8-32位，包含大小写字母、数字和特殊字符
                                    </div>
                                    <FormMessage />
                                </FormItem>
                            )}
                        />
                        
                        <FormField
                            control={form.control}
                            name="accountName"
                            render={({ field }) => (
                                <FormItem>
                                    <FormLabel>账户名称 *</FormLabel>
                                    <FormControl>
                                        <Input
                                            placeholder="请输入账户名称"
                                            {...field}
                                        />
                                    </FormControl>
                                    <FormMessage />
                                </FormItem>
                            )}
                        />
                        
                        <FormField
                            control={form.control}
                            name="description"
                            render={({ field }) => (
                                <FormItem>
                                    <FormLabel>描述</FormLabel>
                                    <FormControl>
                                        <Textarea
                                            placeholder="请输入账户描述（可选）"
                                            className="resize-none"
                                            rows={3}
                                            {...field}
                                        />
                                    </FormControl>
                                    <FormMessage />
                                </FormItem>
                            )}
                        />
                        
                        <FormField
                            control={form.control}
                            name="status"
                            render={({ field }) => (
                                <FormItem>
                                    <FormLabel>状态 *</FormLabel>
                                    <Select onValueChange={field.onChange} defaultValue={field.value}>
                                        <FormControl>
                                            <SelectTrigger>
                                                <SelectValue placeholder="选择状态" />
                                            </SelectTrigger>
                                        </FormControl>
                                        <SelectContent>
                                            <SelectItem value="ENABLED">启用</SelectItem>
                                            <SelectItem value="DISABLED">禁用</SelectItem>
                                        </SelectContent>
                                    </Select>
                                    <FormMessage />
                                </FormItem>
                            )}
                        />
                        
                        <div className="flex justify-end space-x-2 pt-4">
                            <Button
                                type="button"
                                variant="outline"
                                onClick={() => setOpen(false)}
                            >
                                取消
                            </Button>
                            <Button type="submit">
                                更新账户
                            </Button>
                        </div>
                    </form>
                </Form>
            </DialogContent>
        </Dialog>
    );
}
