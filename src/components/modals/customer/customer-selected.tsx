import { listCustomer } from "@/api/customer/customer-api"
import { But<PERSON> } from "@/components/ui/button"
import { Command, CommandEmpty, CommandGroup, CommandInput, CommandItem, CommandList } from "@/components/ui/command"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { cn } from "@/lib/utils"
import { useRequest } from "ahooks"
import { CheckIcon, ChevronsUpDownIcon } from "lucide-react"
import * as React from "react"

interface CustomerSelectedProps {
    select: (value: string) => void
}

export function CustomerSelected({ select }: CustomerSelectedProps) {
    const [open, setOpen] = React.useState(false)
    const [value, setValue] = React.useState("")

    let { data, refreshAsync } = useRequest(listCustomer, {
        onSuccess: (data) => {
            // 如果有数据且当前没有选择值，则默认选择第一个客户
            if (data && data.length > 0 && !value) {
                const firstCustomerId = data[0].id.toString();
                setValue(firstCustomerId);
                select(firstCustomerId);
            }
        }
    })
    if (data === undefined) {
        data = []
    }

    return (
        <Popover open={open} onOpenChange={setOpen}>
            <PopoverTrigger asChild>
                <Button
                    variant="outline"
                    role="combobox"
                    aria-expanded={open}
                    className="w-[200px] justify-between rounded-xl"
                    onClick={() => {
                        // 确保在打开下拉框时刷新数据
                        refreshAsync();
                    }}
                >
                    {value
                        ? (data ?? []).find((framework) => framework.id.toString() === value)?.name
                        : "选择客户..."}
                    <ChevronsUpDownIcon className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                </Button>
            </PopoverTrigger>
            <PopoverContent className="w-[200px] p-0">
                <Command className="rounded-xl">
                    <CommandInput placeholder="请先选择用户..." />
                    <CommandEmpty>没有客户.</CommandEmpty>
                    <CommandGroup>
                        <CommandList>
                            {(data ?? []).map((framework) => (
                                <CommandItem
                                    key={framework.id}
                                    value={framework.name}
                                    onSelect={(currentValue) => {
                                        const selectedFramework = data?.find(f => f.name === currentValue);
                                        const selectedId = selectedFramework ? selectedFramework.id.toString() : "";
                                        setValue(selectedId === value ? "" : selectedId)
                                        select(selectedId === value ? "" : selectedId)
                                        setOpen(false)
                                    }}
                                >
                                    <CheckIcon className={cn(
                                        "mr-2 h-4 w-4",
                                        value === framework.id.toString() ? "opacity-100" : "opacity-0"
                                    )} />
                                    {framework.name}
                                </CommandItem>
                            ))}</CommandList>
                    </CommandGroup>
                </Command>
            </PopoverContent>
        </Popover>
    )
}
