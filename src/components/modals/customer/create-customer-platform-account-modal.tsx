import { toast } from "sonner";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { But<PERSON> } from "@/components/ui/button";
import {
    Form,
    FormControl,
    FormField,
    FormItem,
    FormLabel,
    FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { useState } from "react";
import { createCustomerPlatformAccount } from "@/api/customer/customer-platform-account-api";
import { useSetAtom } from "jotai";
import { refreshTableAtom } from "@/state/common";
import { CustomerSelected } from "@/components/modals/customer/customer-selected";
import { Eye, EyeOff } from "lucide-react";

const formSchema = z.object({
    customerId: z.string().min(1, "请选择客户"),
    email: z.string().email("请输入有效的邮箱地址"),
    password: z.string()
        .min(8, "密码长度不能少于8位")
        .max(32, "密码长度不能超过32位")
        .regex(/[A-Z]/, "密码必须包含至少一个大写字母")
        .regex(/[a-z]/, "密码必须包含至少一个小写字母")
        .regex(/[0-9]/, "密码必须包含至少一个数字")
        .regex(/[!@#$%^&*()_+\-=\[\]{}|;:,.<>?]/, "密码必须包含至少一个特殊字符"),
    accountName: z.string().min(1, "请输入账户名称"),
    description: z.string().optional(),
});

interface CreateCustomerPlatformAccountModalProps {
    customerId?: string;
}

export default function CreateCustomerPlatformAccountModal({ customerId }: CreateCustomerPlatformAccountModalProps) {
    const refresh = useSetAtom(refreshTableAtom);
    const [open, setOpen] = useState(true);
    const [showPassword, setShowPassword] = useState(false);

    const form = useForm<z.infer<typeof formSchema>>({
        resolver: zodResolver(formSchema),
        defaultValues: {
            customerId: customerId || "",
            email: "",
            password: "",
            accountName: "",
            description: "",
        }
    });

    const handleCustomerSelect = (customerId: string) => {
        form.setValue('customerId', customerId);
    };

    async function onSubmit(values: z.infer<typeof formSchema>) {
        toast.promise(createCustomerPlatformAccount(values), {
            loading: "正在创建客户平台账户...",
            success: () => {
                setOpen(false);
                refresh(prev => prev + 1);
                return "客户平台账户创建成功";
            },
            error: (error) => {
                console.error('创建失败:', error);
                return error?.response?.data?.msg || "客户平台账户创建失败";
            }
        });
    }

    return (
        <Dialog open={open} onOpenChange={setOpen}>
            <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
                <DialogHeader className="pb-0">
                    <DialogTitle className="text-2xl font-semibold text-gray-800">
                        创建客户平台账户
                    </DialogTitle>
                    <DialogDescription className="text-sm text-gray-600">
                        为客户创建一个新的平台登录账户
                    </DialogDescription>
                </DialogHeader>
                <Form {...form}>
                    <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4 max-w-2xl flex flex-col">
                        {!customerId && (
                            <FormField
                                control={form.control}
                                name="customerId"
                                render={({ field }) => (
                                    <FormItem>
                                        <FormLabel>选择客户 *</FormLabel>
                                        <FormControl>
                                            <CustomerSelected select={handleCustomerSelect} />
                                        </FormControl>
                                        <FormMessage />
                                    </FormItem>
                                )}
                            />
                        )}

                        <FormField
                            control={form.control}
                            name="email"
                            render={({ field }) => (
                                <FormItem>
                                    <FormLabel>邮箱 *</FormLabel>
                                    <FormControl>
                                        <Input
                                            placeholder="请输入邮箱地址"
                                            type="email"
                                            {...field}
                                        />
                                    </FormControl>
                                    <FormMessage />
                                </FormItem>
                            )}
                        />

                        <FormField
                            control={form.control}
                            name="password"
                            render={({ field }) => (
                                <FormItem>
                                    <FormLabel>密码 *</FormLabel>
                                    <FormControl>
                                        <div className="relative">
                                            <Input
                                                placeholder="请输入密码"
                                                type={showPassword ? "text" : "password"}
                                                {...field}
                                            />
                                            <Button
                                                type="button"
                                                variant="ghost"
                                                size="sm"
                                                className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                                                onClick={() => setShowPassword(!showPassword)}
                                            >
                                                {showPassword ? (
                                                    <EyeOff className="h-4 w-4" />
                                                ) : (
                                                    <Eye className="h-4 w-4" />
                                                )}
                                            </Button>
                                        </div>
                                    </FormControl>
                                    <div className="text-xs text-muted-foreground">
                                        密码要求：8-32位，包含大小写字母、数字和特殊字符
                                    </div>
                                    <FormMessage />
                                </FormItem>
                            )}
                        />

                        <FormField
                            control={form.control}
                            name="accountName"
                            render={({ field }) => (
                                <FormItem>
                                    <FormLabel>账户名称 *</FormLabel>
                                    <FormControl>
                                        <Input
                                            placeholder="请输入账户名称"
                                            {...field}
                                        />
                                    </FormControl>
                                    <FormMessage />
                                </FormItem>
                            )}
                        />

                        <FormField
                            control={form.control}
                            name="description"
                            render={({ field }) => (
                                <FormItem>
                                    <FormLabel>描述</FormLabel>
                                    <FormControl>
                                        <Textarea
                                            placeholder="请输入账户描述（可选）"
                                            className="resize-none"
                                            rows={3}
                                            {...field}
                                        />
                                    </FormControl>
                                    <FormMessage />
                                </FormItem>
                            )}
                        />

                        <div className="flex justify-end space-x-2 pt-4">
                            <Button
                                type="button"
                                variant="outline"
                                onClick={() => setOpen(false)}
                            >
                                取消
                            </Button>
                            <Button type="submit">
                                创建账户
                            </Button>
                        </div>
                    </form>
                </Form>
            </DialogContent>
        </Dialog>
    );
}
