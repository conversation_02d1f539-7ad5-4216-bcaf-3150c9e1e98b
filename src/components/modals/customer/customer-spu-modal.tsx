// src/components/modals/select-customer-modal.tsx
import {useState} from 'react';
import {<PERSON><PERSON>, DialogContent, DialogFooter, DialogHeader, DialogTitle} from "@/components/ui/dialog";
import {Button} from "@/components/ui/button";
import {Select, SelectContent, SelectItem, SelectTrigger, SelectValue} from "@/components/ui/select";
import {useRequest} from "ahooks";
import { listCustomer } from '@/api/customer/customer-api';

interface SelectCustomerModalProps {
    onSelect: (customerId: string) => void;
    onClose: () => void;
}

export function SelectCustomerModal({ onSelect, onClose }: SelectCustomerModalProps) {
    const [selectedCustomerId, setSelectedCustomerId] = useState<string | null>(null);
    const [isOpen, setIsOpen] = useState(true);

    const { data: customers = [] } = useRequest(listCustomer, {
        // Add any necessary options
    });

    const handleConfirm = () => {
        if (selectedCustomerId) {
            onSelect(selectedCustomerId);
            setIsOpen(false);
        }
    };

    return (
        <Dialog open={isOpen} onOpenChange={setIsOpen}>
            <DialogContent>
                <DialogHeader>
                    <DialogTitle>选择客户</DialogTitle>
                </DialogHeader>
                <Select
                    value={selectedCustomerId || ''}
                    onValueChange={(value) => setSelectedCustomerId(value)}
                >
                    <SelectTrigger>
                        <SelectValue placeholder="选择客户..."/>
                    </SelectTrigger>
                    <SelectContent>
                        {customers.map((customer) => (
                            <SelectItem key={customer.id} value={customer.id}>
                                {customer.name}
                            </SelectItem>
                        ))}
                    </SelectContent>
                </Select>
                <DialogFooter>
                    <Button onClick={onClose} variant="outline">取消</Button>
                    <Button onClick={handleConfirm} disabled={!selectedCustomerId}>确认</Button>
                </DialogFooter>
            </DialogContent>
        </Dialog>
    );
}