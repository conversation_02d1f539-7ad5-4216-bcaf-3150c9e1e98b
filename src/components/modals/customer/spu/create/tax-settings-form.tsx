"use client"

import { Country, findCountries } from "@/api/common-api"
import { CountryStateTaxConfig } from "@/api/inventory/customer/customer-product-api"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card } from "@/components/ui/card"
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { zodResolver } from "@hookform/resolvers/zod"
import { useRequest } from "ahooks"
import { Plus, X } from "lucide-react"
import { useState } from "react"
import { useForm } from "react-hook-form"
import * as z from "zod"

export interface TaxRate {
    countryId: string
    countryName: string
    vat: number | null
    additionalTax: number | null
    stateTaxConfigs?: CountryStateTaxConfig[]
}

const stateTaxConfigSchema = z.object({
    enName: z.string(),
    cnName: z.string(),
    isoAlphaTwo: z.string(),
    additionalTax: z.number(),
})

const formSchema = z.object({
    countryId: z.string(),
    countryName: z.string(),
    vat: z.number().nullable(),
    additionalTax: z.number().nullable(),
    stateTaxConfigs: z.array(stateTaxConfigSchema).optional(),
})

type TaxSettingsFormProps = {
    onSubmit: (data: TaxRate) => void
    onCancel: () => void
    existingCountries?: string[]
}

export function TaxSettingsForm({ onSubmit, onCancel, existingCountries = [] }: TaxSettingsFormProps) {
    const [selectedCountry, setSelectedCountry] = useState<Country | null>(null)
    const [stateConfigs, setStateConfigs] = useState<CountryStateTaxConfig[]>([])

    const { data: countries } = useRequest(findCountries)

    const form = useForm<z.infer<typeof formSchema>>({
        resolver: zodResolver(formSchema),
        defaultValues: {
            countryId: "",
            countryName: "",
            vat: null,
            additionalTax: null,
            stateTaxConfigs: [],
        },
    })

    const availableCountries = countries?.filter(
        (country) => !existingCountries.includes(country.id)
    ) || []

    const handleCountryChange = (countryId: string) => {
        const country = countries?.find((c) => c.id === countryId)
        if (country) {
            setSelectedCountry(country)
            form.setValue("countryId", country.id)
            form.setValue("countryName", country.countryName)
            form.setValue("vat", 0)
            form.setValue("additionalTax", 0)

            // 如果国家有州/省配置，则初始化
            if (country.states && country.states.length > 0) {
                const initialStateConfigs: CountryStateTaxConfig[] = country.states.map(state => ({
                    enName: state.enName,
                    cnName: state.cnName,
                    isoAlphaTwo: state.isoAlphaTwo,
                    additionalTax: 0
                }))
                setStateConfigs(initialStateConfigs)
                form.setValue("stateTaxConfigs", initialStateConfigs)
            } else {
                setStateConfigs([])
                form.setValue("stateTaxConfigs", [])
            }
        }
    }

    const addStateConfig = () => {
        const newStateConfig: CountryStateTaxConfig = {
            enName: "",
            cnName: "",
            isoAlphaTwo: "",
            additionalTax: 0,
        }
        setStateConfigs([...stateConfigs, newStateConfig])
    }

    const removeStateConfig = (index: number) => {
        const newConfigs = [...stateConfigs]
        newConfigs.splice(index, 1)
        setStateConfigs(newConfigs)
        form.setValue("stateTaxConfigs", newConfigs)
    }

    const updateStateConfig = (index: number, field: keyof CountryStateTaxConfig, value: string | number) => {
        const newConfigs = [...stateConfigs]
        newConfigs[index] = {
            ...newConfigs[index],
            [field]: value,
        }
        setStateConfigs(newConfigs)
        form.setValue("stateTaxConfigs", newConfigs)
    }

    const onFormSubmit = (data: z.infer<typeof formSchema>) => {
        onSubmit({
            countryId: data.countryId,
            countryName: data.countryName,
            vat: data.vat,
            additionalTax: data.additionalTax,
            stateTaxConfigs: data.stateTaxConfigs,
        })
        form.reset()
        setSelectedCountry(null)
        setStateConfigs([])
    }

    return (
        <Form {...form}>
            <form onSubmit={form.handleSubmit(onFormSubmit)} className="space-y-4">
                <div className="grid grid-cols-3 gap-4">
                    <FormField
                        control={form.control}
                        name="countryId"
                        render={({ field }) => (
                            <FormItem>
                                <FormLabel>国家</FormLabel>
                                <Select
                                    value={field.value}
                                    onValueChange={(value) => handleCountryChange(value)}
                                >
                                    <FormControl>
                                        <SelectTrigger>
                                            <SelectValue placeholder="选择国家" />
                                        </SelectTrigger>
                                    </FormControl>
                                    <SelectContent>
                                        {availableCountries.map((country) => (
                                            <SelectItem key={country.id} value={country.id}>
                                                {country.countryName}
                                            </SelectItem>
                                        ))}
                                    </SelectContent>
                                </Select>
                                <FormMessage />
                            </FormItem>
                        )}
                    />

                    <FormField
                        control={form.control}
                        name="vat"
                        render={({ field }) => (
                            <FormItem>
                                <FormLabel>VAT税率</FormLabel>
                                <FormControl>
                                    <Input
                                        type="number"
                                        step="0.01"
                                        placeholder="输入VAT税率"
                                        {...field}
                                        value={field.value ?? 0}
                                        onChange={(e) =>
                                            field.onChange(e.target.value ? Number(e.target.value) : 0)
                                        }
                                    />
                                </FormControl>
                                <FormMessage />
                            </FormItem>
                        )}
                    />

                    <FormField
                        control={form.control}
                        name="additionalTax"
                        render={({ field }) => (
                            <FormItem>
                                <FormLabel>附加税率/费</FormLabel>
                                <FormControl>
                                    <Input
                                        type="number"
                                        step="0.01"
                                        placeholder="输入附加税率"
                                        {...field}
                                        value={field.value ?? 0}
                                        onChange={(e) =>
                                            field.onChange(e.target.value ? Number(e.target.value) : 0)
                                        }
                                    />
                                </FormControl>
                                <FormMessage />
                            </FormItem>
                        )}
                    />
                </div>

                {selectedCountry?.states && selectedCountry.states.length > 0 && (
                    <Card className="p-4">
                        <div className="flex justify-between items-center mb-4">
                            <h3 className="text-lg font-semibold">州/省税率配置</h3>
                            <Button type="button" variant="outline" size="sm" onClick={addStateConfig}>
                                <Plus className="w-4 h-4 mr-2" />
                                添加州/省税率
                            </Button>
                        </div>
                        <div className="space-y-4">
                            {stateConfigs.map((config, index) => (
                                <div key={index} className="grid grid-cols-4 gap-4 items-end">
                                    <FormItem>
                                        <FormLabel>州/省名称(英文)</FormLabel>
                                        <FormControl>
                                            <Input
                                                value={config.enName}
                                                onChange={(e) => updateStateConfig(index, "enName", e.target.value)}
                                                placeholder="例如: California"
                                            />
                                        </FormControl>
                                    </FormItem>
                                    <FormItem>
                                        <FormLabel>州/省名称(中文)</FormLabel>
                                        <FormControl>
                                            <Input
                                                value={config.cnName}
                                                onChange={(e) => updateStateConfig(index, "cnName", e.target.value)}
                                                placeholder="例如: 加利福尼亚州"
                                            />
                                        </FormControl>
                                    </FormItem>
                                    <FormItem>
                                        <FormLabel>州/省代码</FormLabel>
                                        <FormControl>
                                            <Input
                                                value={config.isoAlphaTwo}
                                                onChange={(e) => updateStateConfig(index, "isoAlphaTwo", e.target.value)}
                                                placeholder="例如: CA"
                                            />
                                        </FormControl>
                                    </FormItem>
                                    <FormItem>
                                        <FormLabel>附加税率/费</FormLabel>
                                        <div className="flex gap-2">
                                            <FormControl>
                                                <Input
                                                    type="number"
                                                    step="0.01"
                                                    value={config.additionalTax}
                                                    onChange={(e) => updateStateConfig(index, "additionalTax", Number(e.target.value))}
                                                    placeholder="输入税率"
                                                />
                                            </FormControl>
                                            <Button
                                                type="button"
                                                variant="destructive"
                                                size="icon"
                                                onClick={() => removeStateConfig(index)}
                                            >
                                                <X className="w-4 h-4" />
                                            </Button>
                                        </div>
                                    </FormItem>
                                </div>
                            ))}
                        </div>
                    </Card>
                )}

                <div className="flex justify-end gap-2">
                    <Button type="button" variant="outline" onClick={onCancel}>
                        取消
                    </Button>
                    <Button type="submit">添加税率</Button>
                </div>
            </form>
        </Form>
    )
}

