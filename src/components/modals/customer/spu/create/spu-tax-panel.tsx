"use client"

import {CountryStateTaxConfig} from "@/api/inventory/customer/customer-product-api"
import type {TaxRate} from "@/components/modals/customer/spu/create/tax"
import {Button} from "@/components/ui/button"
import {Input} from "@/components/ui/input"
import {Table, TableBody, TableCell, TableHead, TableHeader, TableRow} from "@/components/ui/table"
import {Edit, Trash2} from "lucide-react"
import React, {useState} from "react"

interface SPUTaxPanelProps<T extends { id: string }> {
    spu: T
    taxRates: TaxRate[]
    onDelete: (spuId: string, countryId: string) => void
    onUpdate: (spuId: string, updatedTaxRate: TaxRate) => void
    renderSpuTitle: (spu: T) => React.ReactNode
    loading?: boolean
}

export function SPUTaxPanel<T extends { id: string }>({spu, taxRates, onDelete, onUpdate, loading, renderSpuTitle}: SPUTaxPanelProps<T>) {
    const [editingRate, setEditingRate] = useState<TaxRate | null>(null)
    const [tempVat, setTempVat] = useState("")
    const [tempAdditionalTax, setTempAdditionalTax] = useState("")
    const [tempStateConfigs, setTempStateConfigs] = useState<CountryStateTaxConfig[]>([])

    const handleEdit = (rate: TaxRate) => {
        setEditingRate(rate)
        setTempVat(rate.vat?.toString() || "")
        setTempAdditionalTax(rate.additionalTax?.toString() || "")
        setTempStateConfigs(rate.stateTaxConfigs || [])
    }

    const handleSave = (rate: TaxRate) => {
        const updatedRate = {
            ...rate,
            vat: tempVat ? Number(tempVat) : null,
            additionalTax: tempAdditionalTax ? Number(tempAdditionalTax) : null,
            stateTaxConfigs: tempStateConfigs
        }
        onUpdate(spu.id, updatedRate)
        setEditingRate(null)
    }

    const handleCancel = () => {
        setEditingRate(null)
        setTempVat("")
        setTempAdditionalTax("")
        setTempStateConfigs([])
    }

    const updateStateConfig = (index: number, field: keyof CountryStateTaxConfig, value: string | number) => {
        const newConfigs = [...tempStateConfigs]
        newConfigs[index] = {
            ...newConfigs[index],
            [field]: value,
        }
        setTempStateConfigs(newConfigs)
    }

    return (
        <div className="w-full bg-white rounded-md border p-4">
            <div className="flex flex-col space-y-4">
                <div className="flex flex-col">
                    {renderSpuTitle(spu)}
                </div>

                <div className="border rounded-lg">
                    <Table>
                        <TableHeader className="bg-muted">
                            <TableRow>
                                <TableHead className="w-[35%]">国家</TableHead>
                                <TableHead className="w-[25%]">VAT</TableHead>
                                <TableHead className="w-[25%]">附加税|费</TableHead>
                                <TableHead className="w-[15%]">操作</TableHead>
                            </TableRow>
                        </TableHeader>
                        <TableBody>
                            {taxRates.length === 0 ? (
                                <TableRow>
                                    <TableCell colSpan={4} className="text-center text-muted-foreground">
                                        暂无税率配置
                                    </TableCell>
                                </TableRow>
                            ) : (
                                taxRates.map((rate, index) => (
                                    <>
                                        <TableRow key={index}>
                                            <TableCell>{rate.countryName}</TableCell>
                                            <TableCell>
                                                {editingRate?.countryId === rate.countryId ? (
                                                    <Input
                                                        type="number"
                                                        value={tempVat}
                                                        onChange={(e) => setTempVat(e.target.value)}
                                                        className="w-24"
                                                        step="0.01"
                                                        min="0"
                                                        disabled={loading}
                                                    />
                                                ) : (
                                                    rate.vat !== null ? rate.vat.toFixed(2) : "-"
                                                )}
                                            </TableCell>
                                            <TableCell>
                                                {editingRate?.countryId === rate.countryId ? (
                                                    <Input
                                                        type="number"
                                                        value={tempAdditionalTax}
                                                        onChange={(e) => setTempAdditionalTax(e.target.value)}
                                                        className="w-24"
                                                        step="0.01"
                                                        min="0"
                                                        disabled={loading}
                                                    />
                                                ) : (
                                                    rate.additionalTax !== null ? rate.additionalTax.toFixed(2) : "-"
                                                )}
                                            </TableCell>
                                            <TableCell>
                                                <div className="flex space-x-2">
                                                    {editingRate?.countryId === rate.countryId ? (
                                                        <>
                                                            <Button
                                                                variant="ghost"
                                                                size="icon"
                                                                onClick={() => handleSave(rate)}
                                                                disabled={loading}
                                                            >
                                                                ✓
                                                            </Button>
                                                            <Button
                                                                variant="ghost"
                                                                size="icon"
                                                                onClick={handleCancel}
                                                                disabled={loading}
                                                            >
                                                                ✕
                                                            </Button>
                                                        </>
                                                    ) : (
                                                        <>
                                                            <Button
                                                                variant="ghost"
                                                                size="icon"
                                                                onClick={() => handleEdit(rate)}
                                                                disabled={loading}
                                                            >
                                                                <Edit className="h-4 w-4" />
                                                            </Button>
                                                            <Button
                                                                variant="ghost"
                                                                size="icon"
                                                                onClick={() => onDelete(spu.id, rate.countryId)}
                                                                disabled={loading}
                                                            >
                                                                <Trash2 className="h-4 w-4 text-destructive" />
                                                            </Button>
                                                        </>
                                                    )}
                                                </div>
                                            </TableCell>
                                        </TableRow>
                                        {/* 州/省税率配置 */}
                                        {rate.stateTaxConfigs && rate.stateTaxConfigs.length > 0 && (
                                            <TableRow>
                                                <TableCell colSpan={4} className="bg-muted/5">
                                                    <div className="pl-8 py-2">
                                                        <h4 className="text-sm font-medium mb-2">州/省税率配置</h4>
                                                        <div className="space-y-2">
                                                            {(editingRate?.countryId === rate.countryId ? tempStateConfigs : rate.stateTaxConfigs).map((state, index) => (
                                                                <div key={index} className="grid grid-cols-4 gap-4 items-center">
                                                                    <div>
                                                                        <p className="font-medium">{state.cnName}</p>
                                                                        <p className="text-sm text-muted-foreground">
                                                                            {state.enName} ({state.isoAlphaTwo})
                                                                        </p>
                                                                    </div>
                                                                    <div className="col-span-2">
                                                                        {editingRate?.countryId === rate.countryId ? (
                                                                            <div className="flex items-center gap-2">
                                                                                <span className="text-sm text-muted-foreground">附加税/费:</span>
                                                                                <Input
                                                                                    type="number"
                                                                                    step="0.01"
                                                                                    value={state.additionalTax}
                                                                                    onChange={(e) => updateStateConfig(index, "additionalTax", Number(e.target.value))}
                                                                                    className="w-24"
                                                                                    disabled={loading}
                                                                                />
                                                                            </div>
                                                                        ) : (
                                                                            <div className="flex items-center gap-2">
                                                                                <span className="text-sm text-muted-foreground">附加税/费:</span>
                                                                                <span>{state.additionalTax}</span>
                                                                            </div>
                                                                        )}
                                                                    </div>
                                                                </div>
                                                            ))}
                                                        </div>
                                                    </div>
                                                </TableCell>
                                            </TableRow>
                                        )}
                                    </>
                                ))
                            )}
                        </TableBody>
                    </Table>
                </div>
            </div>
        </div>
    )
}

