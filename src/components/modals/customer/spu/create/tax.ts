import { CountryStateTaxConfig } from "@/api/inventory/customer/customer-product-api"

  export interface TaxRate {
    countryId: string
    countryName: string
    vat: number | null
    additionalTax: number | null
    stateTaxConfigs?: CountryStateTaxConfig[]
  }
  
  export interface SPU {
    id: string
    name: string
    systemSpuCode: string
  }
  
  export interface SPUTaxRates {
    spuId: string
    taxRates: TaxRate[]
  }
  
  