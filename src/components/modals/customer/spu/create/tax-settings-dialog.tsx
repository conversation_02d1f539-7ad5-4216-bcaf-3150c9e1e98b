"use client"

import {BatchCustomerSpuTaxRequest, CustomerSpuTax} from "@/api/inventory/customer/customer-product-api"
import type {SPUTaxRates, TaxRate} from "@/components/modals/customer/spu/create/tax"
import {Button} from "@/components/ui/button"
import {Dialog<PERSON>ontent, DialogHeader, DialogTitle} from "@/components/ui/dialog"
import {Plus} from "lucide-react"
import React, {useEffect, useState} from "react"
import {toast} from "sonner"
import {SPUTaxPanel} from "./spu-tax-panel"
import {TaxSettingsForm} from "./tax-settings-form"

interface TaxSettingsDialogProps<T extends { id: string }> {
    selectedSPUs: T[]
    renderSpuTitle: (spu: T) => React.ReactNode
    batchCreateCustomerSpuTax: (taxRate: BatchCustomerSpuTaxRequest) => Promise<void>
    batchUpdateCustomerSpuTax: (taxRate: BatchCustomerSpuTaxRequest) => Promise<void>
    deleteCustomerSpuTax: (spuId: string, countryId: string) => Promise<void>
    getCustomerSpuTax: (ids: string[]) => Promise<Record<string, CustomerSpuTax[]>>
}

export function TaxSettingsDialog<T extends { id: string }>(
  {
      selectedSPUs,
      renderSpuTitle,
      batchCreateCustomerSpuTax,
      batchUpdateCustomerSpuTax,
      deleteCustomerSpuTax,
      getCustomerSpuTax
  }: TaxSettingsDialogProps<T>) {
    const [spuTaxRates, setSpuTaxRates] = useState<SPUTaxRates[]>(
        selectedSPUs.map((spu) => ({ spuId: spu.id, taxRates: [] })),
    )
    const [isAddingNew, setIsAddingNew] = useState(false)
    const [loading, setLoading] = useState(false)

    useEffect(() => {
        const fetchTaxRates = async () => {
            try {
                setLoading(true)
                const spuIds = selectedSPUs.map(spu => spu.id)
                const taxData = await getCustomerSpuTax(spuIds)

                // 转换API返回的税率数据为组件所需格式
                const formattedTaxRates = selectedSPUs.map(spu => {
                    const spuTaxes = taxData[spu.id] || []
                    return {
                        spuId: spu.id,
                        taxRates: spuTaxes.map(tax => ({
                            countryId: tax.countryId,
                            countryName: tax.countryName, // 需要从Country API获取名称
                            vat: tax.vatTax,
                            additionalTax: tax.additionalTax,
                            stateTaxConfigs: tax.stateTaxConfigs || []
                        }))
                    }
                })
                setSpuTaxRates(formattedTaxRates)
            } catch (error: any) {
                toast.error('Failed to fetch tax rates: ' + error.message)
            } finally {
                setLoading(false)
            }
        }

        fetchTaxRates()
    }, [getCustomerSpuTax, selectedSPUs])

    const handleAddTaxRate = async (newTaxRate: TaxRate) => {
        try {
            setLoading(true)
            // 准备批量创建请求
            const request = {
                spuIds: selectedSPUs.map(spu => spu.id),
                customerSpuTaxReq: [{
                    countryId: newTaxRate.countryId,
                    countryName: newTaxRate.countryName,
                    vatTax: newTaxRate.vat || 0,
                    additionalTax: newTaxRate.additionalTax || 0,
                    stateTaxConfigs: newTaxRate.stateTaxConfigs || []
                }]
            }

            await batchCreateCustomerSpuTax(request)

            // 更新本地状态
            setSpuTaxRates((prev) =>
                prev.map((spuTax) => {
                    const hasCountry = spuTax.taxRates.some((tax) => tax.countryId === newTaxRate.countryId)
                    if (hasCountry) return spuTax
                    return {
                        ...spuTax,
                        taxRates: [...spuTax.taxRates, newTaxRate],
                    }
                }),
            )
            toast.success('税率添加成功')
        } catch (error: any) {
            toast.error('添加税率失败: ' + error.message)
        } finally {
            setLoading(false)
            setIsAddingNew(false)
        }
    }

    const handleDeleteTaxRate = async (spuId: string, countryId: string) => {
        try {
            setLoading(true)
            // 查找对应的税率记录ID
            const spuTax = spuTaxRates.find(tax => tax.spuId === spuId)
            if (!spuTax) return

            await deleteCustomerSpuTax(spuId, countryId)

            // 更新本地状态
            setSpuTaxRates((prev) =>
                prev.map((spuTax) => {
                    if (spuTax.spuId === spuId) {
                        return {
                            ...spuTax,
                            taxRates: spuTax.taxRates.filter((tax) => tax.countryId !== countryId),
                        }
                    }
                    return spuTax
                }),
            )
            toast.success('税率删除成功')
        } catch (error: any) {
            toast.error('删除税率失败: ' + error.message)
        } finally {
            setLoading(false)
        }
    }

    const handleUpdateTaxRate = async (spuId: string, updatedTaxRate: TaxRate) => {
        try {
            setLoading(true)
            // 确保stateTaxConfigs是一个有效的数组
            const stateTaxConfigs = updatedTaxRate.stateTaxConfigs?.map(state => ({
                enName: state.enName,
                cnName: state.cnName,
                isoAlphaTwo: state.isoAlphaTwo,
                additionalTax: state.additionalTax
            })) || [];

            const request = {
                spuIds: [spuId],
                customerSpuTaxReq: [{
                    countryId: updatedTaxRate.countryId,
                    countryName: updatedTaxRate.countryName,
                    vatTax: updatedTaxRate.vat || 0,
                    additionalTax: updatedTaxRate.additionalTax || 0,
                    stateTaxConfigs: stateTaxConfigs
                }]
            }

            await batchUpdateCustomerSpuTax(request)

            // 更新本地状态，确保包含州/省税率信息
            setSpuTaxRates(prev =>
                prev.map(spuTax => {
                    if (spuTax.spuId === spuId) {
                        return {
                            ...spuTax,
                            taxRates: spuTax.taxRates.map(tax =>
                                tax.countryId === updatedTaxRate.countryId
                                    ? {
                                        ...updatedTaxRate,
                                        stateTaxConfigs: stateTaxConfigs
                                    }
                                    : tax
                            )
                        }
                    }
                    return spuTax
                })
            )
            toast.success('税率更新成功')
        } catch (error: any) {
            toast.error('更新税率失败: ' + error.message)
        } finally {
            setLoading(false)
        }
    }

    const getAllUsedCountryIds = () => {
        const usedCountryIds = new Set<string>()
        spuTaxRates.forEach((spuTax) => {
            spuTax.taxRates.forEach((tax) => {
                usedCountryIds.add(tax.countryId)
            })
        })
        return Array.from(usedCountryIds)
    }

    return (
        <DialogContent className="max-w-screen-lg h-[85vh] overflow-y-auto">
            <DialogHeader className="sticky top-0 bg-white z-50 pb-2">
                <DialogTitle>税率设置</DialogTitle>
            </DialogHeader>

            <div className="py-2">
                {!isAddingNew && (
                    <Button onClick={() => setIsAddingNew(true)} disabled={loading}>
                        <Plus className="w-4 h-4 mr-2" />
                        添加税率
                    </Button>
                )}
            </div>

            {isAddingNew && (
                <div className="mb-4">
                    <TaxSettingsForm
                        onSubmit={handleAddTaxRate}
                        onCancel={() => setIsAddingNew(false)}
                        existingCountries={getAllUsedCountryIds()}
                    />
                </div>
            )}

            <div className="space-y-6 pb-6">
                {selectedSPUs.map((spu) => {
                    const spuTaxRate = spuTaxRates.find((tax) => tax.spuId === spu.id)
                    return (
                        <SPUTaxPanel
                            key={spu.id}
                            spu={spu}
                            taxRates={spuTaxRate?.taxRates || []}
                            onDelete={handleDeleteTaxRate}
                            onUpdate={handleUpdateTaxRate}
                            renderSpuTitle={renderSpuTitle}
                            loading={loading}
                        />
                    )
                })}
            </div>
        </DialogContent>
    )
}

