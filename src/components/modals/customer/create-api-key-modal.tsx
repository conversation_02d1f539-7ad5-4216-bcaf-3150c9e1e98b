import { createApi<PERSON><PERSON> } from "@/api/customer/customer-api";
import { But<PERSON> } from "@/components/ui/button";
import { Dialog, DialogClose, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { useRequest } from "ahooks";
import { toast } from "sonner";
import { useSetAtom } from "jotai";
import { refreshTableAtom } from "@/state/common";
import { useEffect, useState } from "react";
import { Copy } from "lucide-react";

interface CreateApiKeyModalProps {
    customerId: string
    openapiKey?: string | null
}


export function CreateApiKeyModal({ customerId, openapiKey }: CreateApiKeyModalProps) {

    const setRefreshTable = useSetAtom(refreshTableAtom)
    const [key, setKey] = useState<string | null>(openapiKey ?? '');
    const [open, setOpen] = useState(true);

    useEffect(() => {
        setKey(openapiKey ?? '');
    }, [openapiKey]);

    const { runAsync: runCreateApiKey, loading } = useRequest(createApiKey, {
        manual: true,
        onSuccess: (generatedKey) => {
            toast.success('API密钥创建成功')
            setRefreshTable(prev => prev + 1)
            setKey(generatedKey)
        },
        onError: (error) => {
            toast.error('API密钥创建失败:' + error.message)
        }
    })

    const handleCopy = () => {
        if (key) {
            navigator.clipboard.writeText(key);
            toast.success("API 密钥已复制到剪贴板");
        }
    };


    return (
        <Dialog open={open} onOpenChange={setOpen}>
            <DialogContent>
                {key ? (
                    <>
                        <DialogHeader>
                            <DialogTitle>{openapiKey ? "查看 API 密钥" : "API 密钥已创建"}</DialogTitle>
                            <DialogDescription>
                                {openapiKey
                                    ? "这是与客户关联的 API 密钥。"
                                    : "请复制并妥善保管以下 API 密钥。关闭此窗口后将无法再次查看。"}
                            </DialogDescription>
                        </DialogHeader>
                        <div className="my-4 p-3 border rounded bg-muted flex items-center justify-between">
                            <code className="break-all text-sm mr-2">{key}</code>
                            <Button variant="ghost" size="icon" onClick={handleCopy} aria-label="复制密钥">
                                <Copy className="h-4 w-4" />
                            </Button>
                        </div>
                        <DialogFooter>
                            <DialogClose asChild>
                                <Button>关闭</Button>
                            </DialogClose>
                        </DialogFooter>
                    </>
                ) : (
                    <>
                        <DialogHeader>
                            <DialogTitle>创建 API 密钥</DialogTitle>
                            <DialogDescription>
                                为该客户创建一个新的 API 密钥。请注意：密钥创建后只会显示一次，请妥善保管。
                            </DialogDescription>
                        </DialogHeader>
                        <DialogFooter>
                            <DialogClose asChild>
                                <Button variant="outline">取消</Button>
                            </DialogClose>
                            <Button onClick={() => runCreateApiKey(customerId)} disabled={loading}>
                                {loading ? "创建中..." : "创建"}
                            </Button>
                        </DialogFooter>
                    </>
                )}
            </DialogContent>
        </Dialog>
    )
}