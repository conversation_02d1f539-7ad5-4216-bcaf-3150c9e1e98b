import {
    <PERSON><PERSON>,
    <PERSON><PERSON><PERSON>ontent,
    <PERSON><PERSON><PERSON>eader,
    <PERSON><PERSON>Title,
} from "@/components/ui/dialog";
import { Badge } from "@/components/ui/badge";
import { ScrollArea } from "@/components/ui/scroll-area";
import type { RenderError } from '@react-pdf-viewer/core';
import { SpecialZoomLevel, Viewer, Worker } from '@react-pdf-viewer/core';
import { defaultLayoutPlugin } from '@react-pdf-viewer/default-layout';
import { printPlugin } from '@react-pdf-viewer/print';
import { Loader2 } from "lucide-react";
import React, { useCallback, useEffect, useState } from 'react';

import { exportPdf } from '@/api/common-api';
import { SamePackageOrder } from '@/api/supplier/supplier-order/supplier-order-package-api';
import '@react-pdf-viewer/core/lib/styles/index.css';
import '@react-pdf-viewer/default-layout/lib/styles/index.css';
import '@react-pdf-viewer/print/lib/styles/index.css';
import { toast } from 'sonner';

// 添加自定义样式
import './pdf-modal.css';

interface PDFViewerButtonProps {
    pdfUrl: string;
    title?: string;
    orderNos?: string[];
    packageContent?: SamePackageOrder;
}

const PDFViewerModal: React.FC<PDFViewerButtonProps> = ({ pdfUrl, title, orderNos, packageContent }) => {
    const [open, setOpen] = useState(true);
    const [pdfBlobUrl, setPdfBlobUrl] = useState<string | null>(null);
    const [isLoading] = useState(false);
    const [error] = useState<string | null>(null);

    // 配置打印插件
    const printPluginInstance = printPlugin();

    // 配置默认布局插件
    const defaultLayoutPluginInstance = defaultLayoutPlugin({
        sidebarTabs: () => [],
        toolbarPlugin: {
            fullScreenPlugin: {
                onEnterFullScreen: () => { },
                onExitFullScreen: () => { },
            },
        },
    });

    useEffect(() => {
        let blobUrl: string | null = null;

        async function fetchPdf() {
            try {
                const blob = await exportPdf(pdfUrl);
                blobUrl = URL.createObjectURL(blob.data);
                setPdfBlobUrl(blobUrl);
            } catch (error) {
                console.error('Error fetching PDF:', error);
                toast.error('加载PDF失败: ' + (error as Error).message);
            }
        }

        if (!pdfBlobUrl) {
            fetchPdf();
        }

        return () => {
            if (blobUrl) {
                URL.revokeObjectURL(blobUrl);
            }
        };
    }, [pdfUrl]);

    const renderError: RenderError = useCallback(
        (error) => {
            console.error('PDF viewer error:', error);
            return (
                <div className="flex items-center justify-center h-full text-red-500">
                    PDF加载失败: {error.message || '未知错误'}
                </div>
            );
        },
        []
    );

    return (
        <Dialog open={open} onOpenChange={setOpen}>
            <DialogContent className="max-w-[90vw] max-h-[90vh] w-full h-full">
                <DialogHeader className="border-b pb-4 mb-4">
                    <DialogTitle className="text-4xl font-bold text-gray-900 dark:text-gray-100">
                        {title ? title : 'PDF 预览'}
                    </DialogTitle>
                    {/* 显示订单信息 */}
                    {packageContent && (
                        <ScrollArea className="mt-4 max-h-[30vh]">
                            <div className="space-y-4 pr-2">
                                {/* 已完成订单 */}
                                {Object.keys(packageContent.completed).length > 0 && (
                                    <div>
                                        <p className="text-lg font-bold text-green-700 mb-3">
                                            ✅ 已扫描完成订单:
                                        </p>
                                        <div className="grid grid-cols-1 md:grid-cols-6 gap-3">
                                            {Object.entries(packageContent.completed).map(([orderNo, order]) => (
                                                <div
                                                    key={orderNo}
                                                    className="bg-green-50 border border-green-200 rounded-lg px-4 py-3"
                                                >
                                                    <div className="flex items-center justify-between mb-2">
                                                        <span className="font-bold text-sm text-green-800">
                                                            {orderNo} ({order.scanCount}/{order.count})
                                                        </span>
                                                        {order.onSet && (
                                                            <Badge className="bg-orange-500 text-white font-bold px-2 py-1 text-xs animate-pulse">
                                                                🎁 套装
                                                            </Badge>
                                                        )}
                                                    </div>
                                                    <div className="text-xs font-bold text-blue-700 bg-blue-50 px-3 py-2 rounded border-l-4 border-blue-500">
                                                        📦 {order.title}
                                                    </div>
                                                </div>
                                            ))}
                                        </div>
                                    </div>
                                )}

                                {/* 未完成订单 */}
                                {Object.keys(packageContent.nonCompleted).length > 0 && (
                                    <div>
                                        <p className="text-lg font-bold text-orange-700 mb-3">
                                            ⏳ 待扫描订单:
                                        </p>
                                        <div className="grid grid-cols-1 md:grid-cols-6 gap-3">
                                            {Object.entries(packageContent.nonCompleted).map(([orderNo, order]) => (
                                                <div
                                                    key={orderNo}
                                                    className="bg-orange-50 border border-orange-200 rounded-lg px-4 py-3"
                                                >
                                                    <div className="flex items-center justify-between mb-2">
                                                        <span className="font-bold text-xs text-orange-800">
                                                            {orderNo} ({order.scanCount}/{order.count})
                                                        </span>
                                                        {order.onSet && (
                                                            <Badge variant="outline" className="border-orange-500 text-orange-700 font-bold px-2 py-1 text-xs">
                                                                🎁 套装
                                                            </Badge>
                                                        )}
                                                    </div>
                                                    <div className="text-xs font-bold text-gray-700 bg-gray-100 px-3 py-2 rounded border-l-4 border-gray-400">
                                                        📦 {order.title}
                                                    </div>
                                                </div>
                                            ))}
                                        </div>
                                    </div>
                                )}
                            </div>
                        </ScrollArea>
                    )}

                    {/* 兼容旧的orderNos显示方式 */}
                    {!packageContent && orderNos && orderNos.length > 0 && (
                        <div className="mt-2 space-y-1">
                            <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                                订单号:
                            </p>
                            <ScrollArea className="max-h-[30vh] mt-2">
                                <div className="flex flex-wrap gap-2 pr-2">
                                    {orderNos.map((orderNo, index) => (
                                        <span
                                            key={index}
                                            className="inline-flex items-center text-2xl px-3 py-1 rounded-full font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200 border border-blue-200 dark:border-blue-700"
                                        >
                                            {orderNo}
                                        </span>
                                    ))}
                                </div>
                            </ScrollArea>
                        </div>
                    )}
                </DialogHeader>
                <div className="pdf-viewer-container">
                    {isLoading && (
                        <div className="flex items-center justify-center h-full">
                            <Loader2 className="h-8 w-8 animate-spin" />
                            <span className="ml-2">正在加载PDF...</span>
                        </div>
                    )}
                    {error && (
                        <div className="flex items-center justify-center h-full text-red-500">
                            {error}
                        </div>
                    )}
                    {pdfBlobUrl && !isLoading && !error && (
                        <Worker workerUrl="https://unpkg.com/pdfjs-dist@3.11.174/build/pdf.worker.min.js">
                            <div style={{ height: '100%' }}>
                                <Viewer
                                    fileUrl={pdfBlobUrl}
                                    plugins={[defaultLayoutPluginInstance, printPluginInstance]}
                                    defaultScale={SpecialZoomLevel.PageFit}
                                    renderError={renderError}
                                />
                            </div>
                        </Worker>
                    )}
                </div>
            </DialogContent>
        </Dialog>
    );
};

export default PDFViewerModal;