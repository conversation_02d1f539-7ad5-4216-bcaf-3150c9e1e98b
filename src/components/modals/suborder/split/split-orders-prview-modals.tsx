"use client";

import { <PERSON><PERSON>, <PERSON>alog<PERSON>ontent, Di<PERSON><PERSON>eader, DialogTitle } from "@/components/ui/dialog";
import { useRequest } from "ahooks";
import { fetchSplitOrder } from "@/api/order/sub-order/sub-orders";
import { Package, ShoppingBag, Box, Truck, Loader2 } from "lucide-react";
import { useState } from "react";
import { Badge } from "@/components/ui/badge";
import { cn } from "@/lib/utils";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion";
import { Button } from "@/components/ui/button";

export function SplitedOrdersPreviewModal({ orderId }: { orderId: string }) {
    const [open, setOpen] = useState(true);

    const { data: subOrderData, loading } = useRequest(fetchSplitOrder, {
        defaultParams: [orderId]
    });

    return (
        <Dialog open={open} onOpenChange={() => setOpen(false)}>
            <DialogContent className="max-w-screen-2xl h-[90vh] flex flex-col">
                <DialogHeader className="sticky top-0 z-30 bg-background pb-4 border-b">
                    <DialogTitle>
                        <div className="flex items-center gap-2">
                            <Package className="w-5 h-5" />
                            <div>拆分订单预览</div>
                        </div>
                    </DialogTitle>
                </DialogHeader>
                
                {loading ? (
                    <div className="flex-1 flex items-center justify-center">
                        <div className="flex flex-col items-center gap-2 text-muted-foreground">
                            <Loader2 className="w-8 h-8 animate-spin" />
                            <div>加载拆分订单数据...</div>
                        </div>
                    </div>
                ) : !subOrderData || subOrderData.length === 0 ? (
                    <div className="flex-1 flex items-center justify-center">
                        <div className="flex flex-col items-center gap-2 text-muted-foreground">
                            <Box className="w-8 h-8" />
                            <div>没有找到拆分订单数据</div>
                        </div>
                    </div>
                ) : (
                    <ScrollArea className="flex-1 px-1">
                        <div className="space-y-6 py-4">
                            {/* Original order summary */}
                            <Card>
                                <CardHeader className="pb-2">
                                    <CardTitle className="text-base flex items-center gap-2">
                                        <ShoppingBag className="w-4 h-4" />
                                        原始订单
                                    </CardTitle>
                                    <CardDescription>
                                        订单号: {orderId}
                                    </CardDescription>
                                </CardHeader>
                                <CardContent>
                                    <div className="text-sm">
                                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                            <div className="flex items-center gap-2">
                                                <span className="font-medium">总产品数:</span>
                                                <Badge variant="secondary">
                                                    {subOrderData.reduce((sum, item) => sum + item.product.qty, 0)}
                                                </Badge>
                                            </div>
                                            <div className="flex items-center gap-2">
                                                <span className="font-medium">总重量:</span>
                                                <Badge variant="outline" className="font-mono">
                                                    {subOrderData.reduce((sum, item) => sum + (item.product.weight * item.product.qty), 0)}g
                                                </Badge>
                                            </div>
                                        </div>
                                    </div>
                                </CardContent>
                            </Card>

                            {/* Split packages visualization */}
                            <div className="space-y-4">
                                <div className="flex items-center gap-2">
                                    <Truck className="w-5 h-5" />
                                    <h3 className="text-lg font-medium">拆分包裹</h3>
                                </div>
                                
                                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                    {/* Group orders by parent ID to simulate packages */}
                                    {subOrderData && Array.from(new Set(subOrderData.map(item => item.id))).map((id, packageIndex) => {
                                        const packageId = (packageIndex + 1).toString();
                                        const packageItems = subOrderData.filter(item => item.id === id);
                                        const totalWeight = packageItems.reduce((sum, item) => sum + (item.product.weight * item.product.qty), 0);
                                        
                                        return (
                                            <Card key={packageId} className={cn(
                                                "overflow-hidden border-l-4",
                                                packageId === "1" ? "border-l-blue-500" : "border-l-green-500"
                                            )}>
                                                <CardHeader className="bg-muted/40 pb-2">
                                                    <CardTitle className="text-base flex items-center justify-between">
                                                        <div className="flex items-center gap-2">
                                                            <Box className="w-4 h-4" />
                                                            包裹 {packageId}
                                                        </div>
                                                        <Badge variant="outline" className="font-mono">
                                                            {totalWeight}g
                                                        </Badge>
                                                    </CardTitle>
                                                </CardHeader>
                                                <CardContent className="p-0">
                                                    <Accordion type="single" collapsible defaultValue="items" className="w-full">
                                                        <AccordionItem value="items" className="border-0">
                                                            <AccordionTrigger className="px-6 py-2 text-sm hover:no-underline hover:bg-muted/30">
                                                                产品列表 ({packageItems.length})
                                                            </AccordionTrigger>
                                                            <AccordionContent>
                                                                <div className="px-2 pb-2">
                                                                    <table className="w-full border-collapse">
                                                                        <thead className="bg-muted/40 sticky top-0">
                                                                            <tr>
                                                                                <th className="text-left p-2 text-xs font-medium text-muted-foreground">产品</th>
                                                                                <th className="text-center p-2 text-xs font-medium text-muted-foreground">数量</th>
                                                                                <th className="text-right p-2 text-xs font-medium text-muted-foreground">重量</th>
                                                                            </tr>
                                                                        </thead>
                                                                        <tbody>
                                                                            {packageItems.map((item) => (
                                                                                <tr key={item.id} className="border-b border-border/40 hover:bg-muted/20">
                                                                                    <td className="p-2 text-sm">
                                                                                        <div className="flex flex-col gap-0.5">
                                                                                            <div className="font-medium truncate" title={item.product.name}>
                                                                                                {item.product.name}
                                                                                            </div>
                                                                                            <div className="text-xs text-muted-foreground">
                                                                                                ID: {item.id}
                                                                                            </div>
                                                                                        </div>
                                                                                    </td>
                                                                                    <td className="p-2 text-center">
                                                                                        <Badge variant="secondary" className="font-medium">
                                                                                            {item.product.qty}
                                                                                        </Badge>
                                                                                    </td>
                                                                                    <td className="p-2 text-right font-mono text-sm">
                                                                                        {item.product.weight * item.product.qty}g
                                                                                    </td>
                                                                                </tr>
                                                                            ))}
                                                                        </tbody>
                                                                    </table>
                                                                </div>
                                                            </AccordionContent>
                                                        </AccordionItem>
                                                    </Accordion>
                                                </CardContent>
                                            </Card>
                                        );
                                    })}
                                </div>
                            </div>
                        </div>
                    </ScrollArea>
                )}
                
                <div className="sticky bottom-0 pt-4 bg-background border-t flex justify-end">
                    <Button variant="ghost" onClick={() => setOpen(false)}>关闭</Button>
                </div>
            </DialogContent>
        </Dialog>
    );
}