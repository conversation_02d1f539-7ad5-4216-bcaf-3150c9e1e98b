"use client"

import { useState, useEffect, useCallback } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { X, Package } from "lucide-react"
import { SubOrder } from "@/api/order/sub-order/sub-orders"
import { Badge } from "@/components/ui/badge"
import { cn } from "@/lib/utils"

export interface PackageAllocation {
    [packageId: string]: {
        [productId: string]: number
    }
}

export interface OrderSplittingProps {
    data: SubOrder[]
    loading?: boolean
    onConfirm?: (packages: string[], allocations: PackageAllocation) => void
}

export default function OrderSplitting({ data, loading = false, onConfirm }: OrderSplittingProps) {
    const [packages, setPackages] = useState<string[]>(["1", "2"])
    const [allocations, setAllocations] = useState<PackageAllocation>({
        "1": {},
        "2": {},
    })

    // Calculate package weights
    const calculatePackageWeight = useCallback(
        (packageId: string) => {
            return Object.entries(allocations[packageId] || {}).reduce((total, [productId, quantity]) => {
                const product = data.find((p) => p.id === productId)
                return total + (product?.product.weight || 0) * quantity
            }, 0)
        },
        [allocations, data],
    )

    // Calculate main package quantities (package 1)
    const calculateMainPackageQuantities = useCallback(() => {
        const newMainPackage: { [productId: string]: number } = {}

        data.forEach((product) => {
            let allocatedInOtherPackages = 0

            // Sum quantities in other packages
            packages.slice(1).forEach((packageId) => {
                if (allocations[packageId]) {
                    allocatedInOtherPackages += allocations[packageId][product.id] || 0
                }
            })

            // Calculate remaining quantity for main package
            newMainPackage[product.id] = Math.max(0, product.product.qty - allocatedInOtherPackages)
        })

        return newMainPackage
    }, [allocations, packages, data])

    // Update main package when other packages change
    useEffect(() => {
        // Skip if we don't have any packages yet
        if (packages.length === 0) return

        const mainPackageId = "1"
        const newMainPackageQuantities = calculateMainPackageQuantities()

        // Only update if values have actually changed
        const currentMainPackage = allocations[mainPackageId] || {}
        let hasChanged = false

        for (const productId of Object.keys(newMainPackageQuantities)) {
            if (newMainPackageQuantities[productId] !== (currentMainPackage[productId] || 0)) {
                hasChanged = true
                break
            }
        }

        if (hasChanged) {
            setAllocations((prev) => ({
                ...prev,
                [mainPackageId]: newMainPackageQuantities,
            }))
        }
    }, [allocations, packages, calculateMainPackageQuantities])

    // Handle adding a new package
    const addPackage = useCallback(() => {
        // Check if the last package has at least one product
        const lastPackageId = packages[packages.length - 1]
        const lastPackage = allocations[lastPackageId] || {}
        const hasProduct = Object.values(lastPackage).some((quantity) => quantity > 0)

        if (!hasProduct) {
            alert("前一个包裹必须至少包含一个产品")
            return
        }

        const newPackageId = (packages.length + 1).toString()

        // Initialize new package with zero quantities
        const newPackageAllocation: { [productId: string]: number } = {}
        data.forEach((product) => {
            newPackageAllocation[product.id] = 0
        })

        setPackages((prev) => [...prev, newPackageId])
        setAllocations((prev) => ({
            ...prev,
            [newPackageId]: newPackageAllocation,
        }))
    }, [packages, allocations, data])

    // Handle removing a package
    const removePackage = useCallback((packageId: string) => {
        if (packageId === "1") return // Can't remove main package

        setPackages((prev) => prev.filter((id) => id !== packageId))
        setAllocations((prev) => {
            const newAllocations = { ...prev }
            delete newAllocations[packageId]
            return newAllocations
        })
    }, [])

    // Handle quantity change
    const handleQuantityChange = useCallback(
        (packageId: string, productId: string, value: string) => {
            if (packageId === "1") return // Can't manually change main package

            const numValue = Number.parseInt(value) || 0
            const product = data.find((p) => p.id === productId)

            if (!product) return

            // Ensure value doesn't exceed total quantity
            const maxAllowed = product.product.qty
            const validValue = Math.min(Math.max(0, numValue), maxAllowed)

            setAllocations((prev) => ({
                ...prev,
                [packageId]: {
                    ...prev[packageId],
                    [productId]: validValue,
                },
            }))
        },
        [data],
    )

    return (
        <div className="flex flex-col h-full overflow-y-auto overflow-x-auto w-full max-h-[80vh]">
            <div className="flex-1 ">
                <table className="w-full border-collapse">
                    <thead className="sticky top-0 bg-background z-10">
                        <tr className="border-b border-border/50">
                            <th className="p-3 text-left font-medium text-muted-foreground">订单号</th>
                            <th className="p-3 text-left font-medium text-muted-foreground">库存信息</th>
                            <th className="p-3 text-left font-medium text-muted-foreground">供应商</th>
                            <th className="p-3 text-center font-medium text-muted-foreground w-20">总数</th>
                            {packages.map((packageId) => (
                                <th key={packageId} className="p-3 text-center relative min-w-[120px]">
                                    <div className="flex items-center justify-center gap-2">
                                        <Badge
                                            variant={packageId === "1" ? "default" : "outline"}
                                            className={cn(
                                                "px-3 py-1 text-sm font-medium",
                                                packageId === "1" && "bg-primary"
                                            )}
                                        >
                                            包裹 {packageId}
                                        </Badge>
                                        {packageId !== "1" && (
                                            <button
                                                onClick={() => removePackage(packageId)}
                                                className="absolute -right-1 top-0 p-1 rounded-full bg-background border hover:bg-muted transition-colors z-10"
                                            >
                                                <X className="w-3 h-3 text-muted-foreground" />
                                            </button>
                                        )}
                                    </div>
                                </th>
                            ))}
                        </tr>
                    </thead>
                    <tbody>
                        {data.map((product) => (
                            <tr key={product.id} className="border-b border-border/50 hover:bg-muted/50 transition-colors">
                                <td className="p-3">
                                    <span className="font-medium">{product.orderNo}</span>
                                </td>
                                <td className="p-3">
                                    <div className="space-y-1 text-sm text-muted-foreground">
                                        <div className="flex items-center gap-2">
                                            <span className="font-medium">Title:</span>
                                            {product.product.title}
                                        </div>
                                        <div className="flex items-center gap-2">
                                            <span className="font-medium">SKU:</span>
                                            {product.product.spu}
                                        </div>
                                        <div className="flex items-center gap-2">
                                            <span className="font-medium">重量:</span>
                                            {product.product.weight}g
                                        </div>
                                        <div className="flex items-center gap-2">
                                            <span className="font-medium">当前数量:</span>
                                            {product.product.qty}
                                        </div>
                                    </div>
                                </td>
                                <td className="p-3">
                                    <Badge variant="secondary" className="font-semibold">
                                        {product.product.supplierName}
                                    </Badge>
                                </td>
                                <td className="p-3 text-center">
                                    <Badge variant="secondary" className="font-semibold">
                                        {product.product.qty}
                                    </Badge>
                                </td>
                                {packages.map((packageId) => (
                                    <td key={`${product.id}-${packageId}`} className="p-3 text-center">
                                        {packageId === "1" ? (
                                            <span className="text-muted-foreground font-medium">
                                                {allocations[packageId]?.[product.id] || 0}
                                            </span>
                                        ) : (
                                            <Input
                                                type="number"
                                                min="0"
                                                max={product.product.qty}
                                                value={allocations[packageId]?.[product.id] || 0}
                                                onChange={(e) => handleQuantityChange(packageId, product.id, e.target.value)}
                                                className="w-20 mx-auto text-center"
                                            />
                                        )}
                                    </td>
                                ))}
                            </tr>
                        ))}
                    </tbody>
                    <tfoot className="sticky bottom-12  bg-background z-10">
                        <tr className="border-t border-border">
                            <td colSpan={3} className="p-3 text-right font-medium text-muted-foreground">
                                包裹重量
                            </td>
                            {packages.map((packageId) => (
                                <td key={packageId} className="p-3 text-center">
                                    <Badge variant="outline" className="font-mono">
                                        {calculatePackageWeight(packageId)}g
                                    </Badge>
                                </td>
                            ))}
                        </tr>
                    </tfoot>
                </table>
            </div>

            <div className="sticky bottom-0 pt-4 bg-background border-t">
                <div className="flex justify-between">
                    <Button
                        variant="outline"
                        onClick={addPackage}
                        className="flex items-center gap-2"
                        disabled={loading}
                    >
                        <Package className="w-4 h-4" />
                        添加包裹
                    </Button>
                    <div className="flex items-center gap-2">
                        <Button
                            variant="default"
                            onClick={() => onConfirm && onConfirm(packages, allocations)}
                            disabled={loading}
                        >
                            {loading ? '拆分中...' : '确认拆分'}
                        </Button>
                    </div>
                </div>
            </div>
        </div>
    )
}
