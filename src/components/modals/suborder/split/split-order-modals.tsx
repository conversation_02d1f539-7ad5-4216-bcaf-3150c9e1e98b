import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>er, DialogTitle } from "@/components/ui/dialog";
import { useRequest } from "ahooks";
import { fetchMergedOrder, splitOrder } from "@/api/order/sub-order/sub-orders";
import OrderSplitting, { PackageAllocation } from "./order-splitting";
import { Package } from "lucide-react";
import { toast } from "sonner";
import { useState } from "react";

export function SplitOrderModal({ orderId }: { orderId: string }) {

    const [open, setOpen] = useState(true);

    const { data: subOrderData } = useRequest(fetchMergedOrder, {
        defaultParams: [orderId]
    });

    const { runAsync, loading: splitLoading } = useRequest(splitOrder, {
        manual: true,
        onSuccess: () => {
            toast.success("拆分成功");
            setOpen(false);
        },
        onError: (error) => {
            toast.error(error.message);
        }
    });

    const handleConfirm = async (packages: string[], allocations: PackageAllocation) => {
        const invalidPackages = packages.filter(packageId => {
            const packageAllocation = allocations[packageId] || {};
            const totalQuantity = Object.values(packageAllocation).reduce((sum, qty) => sum + (qty || 0), 0);
            return totalQuantity === 0;
        });

        if (invalidPackages.length > 0) {
            toast.error(`以下包裹的总数量为零，请确保每个包裹至少有一个产品: ${invalidPackages.join(', ')}`);
            return;
        }

        const splitOrderData = [];
        for (const packageId in allocations) {
            const allocation = allocations[packageId];
            splitOrderData.push(allocation)
        }
        await runAsync(splitOrderData)
    };

    return (
        <Dialog open={open} onOpenChange={() => setOpen(false)}>
            <DialogContent className="max-w-screen-2xl">
                <DialogHeader>
                    <DialogTitle>
                        <div className="flex items-center gap-2">
                            <Package className="w-5 h-5" />
                            <div>拆分订单</div>
                        </div>
                    </DialogTitle>
                </DialogHeader>
                <OrderSplitting
                    data={subOrderData || []}
                    loading={splitLoading}
                    onConfirm={handleConfirm}
                />
            </DialogContent>
        </Dialog>
    )
}