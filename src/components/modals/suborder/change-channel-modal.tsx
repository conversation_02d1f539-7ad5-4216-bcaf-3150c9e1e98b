// import { getChannels } from "@/api/channel/channel-api";
// import { Channel } from "@/api/channel/channel-model";
// import { changeSubOrdersChannel } from "@/api/order/sub-order/sub-orders";
// import { Button } from "@/components/ui/button";
// import { Command, CommandEmpty, CommandGroup, CommandInput, CommandItem, CommandList } from "@/components/ui/command";
// import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
// import { Label } from "@/components/ui/label";
// import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
// import { cn } from "@/lib/utils";
// import { refreshTableAtom } from "@/state/common";
// import { useSetAtom } from "jotai";
// import { Check, ChevronDown, Truck } from "lucide-react";
// import { useEffect, useState } from "react";
// import { toast } from "sonner";

// export default function ChangeSubOrderChannelModal({
//     subOrderIds,
// }: {
//     subOrderIds: string[]
// }) {

//     const [open, setOpen] = useState(true);
//     const [commandOpen, setCommandOpen] = useState(false);
//     const [channels, setChannels] = useState<Channel[]>([]);
//     const refresh = useSetAtom(refreshTableAtom);
//     const [selectedChannel, setSelectedChannel] = useState<string | null>(null);

//     useEffect(() => {
//         const fetchChannels = async () => {
//             try {
//                 const fetchedChannels = await getChannels();
//                 setChannels(fetchedChannels as Channel[]);
//             } catch (error) {
//                 console.error("Error fetching channels:", error);
//             }
//         };
//         fetchChannels();
//     }, []);

//     const handleConfirm = () => {
//         if (!selectedChannel) {
//             toast.error('请选择物流渠道');
//             return;
//         }
//         changeSubOrdersChannel(subOrderIds, selectedChannel)
//             .then(() => {
//                 toast.success('修改成功');
//                 refresh(prev => prev + 1);
//                 setOpen(false);
//             })
//             .catch((error) => {
//                 toast.error('修改失败:' + error.message);
//             })
//     }

//     return (
//         <Dialog open={open} onOpenChange={setOpen}>
//             <DialogContent className="sm:max-w-[600px]">
//                 <DialogHeader>
//                     <DialogTitle className="text-xl font-semibold">编辑运单信息</DialogTitle>
//                 </DialogHeader>
//                 <div className="space-y-2">
//                     <Label htmlFor="shipMethod" className="text-sm font-medium flex items-center gap-2">
//                         <Truck className="w-4 h-4" />
//                         物流渠道 <span className="text-red-500">*</span>
//                     </Label>
//                     <Popover open={commandOpen} onOpenChange={setCommandOpen}>
//                         <PopoverTrigger asChild>
//                             <Button
//                                 variant="outline"
//                                 role="combobox"
//                                 aria-expanded={commandOpen}
//                                 className="w-full justify-between bg-background px-3 font-normal"
//                             >
//                                 <span className={cn("truncate", !selectedChannel && "text-muted-foreground")}>
//                                     {selectedChannel
//                                         ? channels.find((channel) => channel.id === selectedChannel)?.displayName
//                                         : "选择物流渠道"}
//                                 </span>
//                                 <ChevronDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
//                             </Button>
//                         </PopoverTrigger>
//                         <PopoverContent className="w-full min-w-[var(--radix-popper-anchor-width)] p-0">
//                             <Command>
//                                 <CommandInput placeholder="搜索物流渠道..." />
//                                 <CommandList className="max-h-[300px] overflow-y-auto">
//                                     <CommandEmpty>未找到相关渠道</CommandEmpty>
//                                     <CommandGroup>
//                                         {channels.map((channel) => (
//                                             <CommandItem
//                                                 key={channel.id}
//                                                 value={channel.methodName}
//                                                 onSelect={() => {
//                                                     setSelectedChannel(channel.id);
//                                                     setCommandOpen(false);
//                                                 }}
//                                             >
//                                                 <div className="w-full flex items-center justify-between space-x-2">
//                                                     <span className="font-medium">{channel.displayName}</span>
//                                                     <div className="flex items-center space-x-2 text-gray-500">
//                                                         <span>|</span>
//                                                         <span className="text-sm">{channel.methodName}</span>
//                                                         <span>|</span>
//                                                         <span className="text-sm">{channel.methodCode}</span>
//                                                     </div>
//                                                 </div>
//                                                 {selectedChannel === channel.id && (
//                                                     <Check className="ml-auto h-4 w-4" />
//                                                 )}
//                                             </CommandItem>
//                                         ))}
//                                     </CommandGroup>
//                                 </CommandList>
//                             </Command>
//                         </PopoverContent>
//                     </Popover>
//                 </div>
//                 <div className="flex justify-end">
//                     <Button onClick={handleConfirm}>确定</Button>
//                 </div>
//             </DialogContent>
//         </Dialog>
//     )
// }