import {SubOrderStatus, updateSubOrder} from '@/api/order/sub-order/sub-orders';
import {Button} from '@/components/ui/button';
import {<PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle} from '@/components/ui/dialog';
import {Form, FormControl, FormField, FormItem, FormLabel} from '@/components/ui/form';
import {Input} from '@/components/ui/input';
import {useForm} from 'react-hook-form';
import {zodResolver} from '@hookform/resolvers/zod';
import * as z from 'zod';
import {toast} from 'sonner';
import {useState} from 'react';

const editFormSchema = z.object({
  designUrl: z.string().url().optional().or(z.literal('')),
  effectUrl: z.string().url().optional().or(z.literal('')),
  designImgSearchFiled: z.string().optional().or(z.literal('')),
  effectImgSearchFiled: z.string().optional().or(z.literal('')),
  color: z.string().optional().or(z.literal('')),
  size: z.string().optional().or(z.literal('')),
});

type EditFormValues = z.infer<typeof editFormSchema>;

interface EditSubOrderModalProps {
  subOrder: {
    id: string;
    designUrl: string | null;
    effectUrl: string | null;
    designImgSearchFiled: string | null;
    effectImgSearchFiled: string | null;
    status: SubOrderStatus,
    color: string | null;
    size: string | null;
  };
  onSuccess?: () => void;
  onClose: () => void;
}

export default function EditSubOrderModal({subOrder, onSuccess}: EditSubOrderModalProps) {
  const [open, setOpen] = useState(true);

  const canEditProductInfo = subOrder.status === "FAILED"

  const form = useForm<EditFormValues>({
    resolver: zodResolver(editFormSchema),
    defaultValues: {
      designUrl: subOrder.designUrl || '',
      effectUrl: subOrder.effectUrl || '',
      designImgSearchFiled: subOrder.designImgSearchFiled || '',
      effectImgSearchFiled: subOrder.effectImgSearchFiled || '',
      color: subOrder.color || '',
      size: subOrder.size || '',
    },
  });

  const onSubmit = async (values: EditFormValues) => {
    try {
      // 更新基本信息
      const request = {
        designUrl: values.designUrl || null,
        effectUrl: values.effectUrl || null,
        designImgSearchFiled: values.designImgSearchFiled || null,
        effectImgSearchFiled: values.effectImgSearchFiled || null,
        color: values.color || null,
        size: values.size || null,
      };
      await updateSubOrder(subOrder.id, request);

      toast.success('更新成功');
      onSuccess?.();
      setOpen(false);
    } catch (error: any) {
      toast.error('更新失败: ' + error.message);
    }
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>编辑子订单</DialogTitle>
        </DialogHeader>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <FormField
              control={form.control}
              name="designUrl"
              render={({field}) => (
                <FormItem>
                  <FormLabel>设计图链接</FormLabel>
                  <FormControl>
                    <Input {...field} />
                  </FormControl>
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="effectUrl"
              render={({field}) => (
                <FormItem>
                  <FormLabel>效果图链接</FormLabel>
                  <FormControl>
                    <Input {...field} />
                  </FormControl>
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="color"
              render={({field}) => (
                <FormItem>
                  <FormLabel>颜色</FormLabel>
                  <FormControl>
                    <Input {...field} disabled={!canEditProductInfo}/>
                  </FormControl>
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="size"
              render={({field}) => (
                <FormItem>
                  <FormLabel>尺寸</FormLabel>
                  <FormControl>
                    <Input {...field} disabled={!canEditProductInfo}/>
                  </FormControl>
                </FormItem>
              )}
            />
            <div className="flex justify-end space-x-2">
              <Button variant="outline" type="button" onClick={() => setOpen(false)}>
                取消
              </Button>
              <Button type="submit">
                保存
              </Button>
            </div>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}