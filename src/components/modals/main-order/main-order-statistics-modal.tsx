import { useState, useEffect, useCallback } from 'react';
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { mainOrderApi, MainOrderStatisticsResponse, MainOrderStatus, MainOrderType } from "@/api/order/mainorder/main-order-api";
import { BarChart3, Package, CheckCircle, Clock, FileText, Image, ShoppingCart, Truck, AlertCircle, XCircle } from "lucide-react";
import { toast } from "sonner";
import { useNavigate } from "react-router-dom";

interface MainOrderStatisticsModalProps {
    mainOrderId: string;
    onClose?: () => void;
}

// 定义所有可能的子订单状态
const SubOrderStatusMap: Record<string, { label: string; color: string; icon: any }> = {
    'CREATED': { label: '已创建', color: 'text-yellow-600', icon: Clock },
    'CANCELLED': { label: '已取消', color: 'text-gray-600', icon: XCircle },
    'COMPLETED': { label: '已完成', color: 'text-green-600', icon: CheckCircle },
    'SPLIT': { label: '已拆分', color: 'text-blue-600', icon: Package },
    'SUPPLIER_MATCHED': { label: '供应商已匹配', color: 'text-green-600', icon: CheckCircle },
    'FAILED': { label: '失败', color: 'text-red-600', icon: AlertCircle },
    // 轨迹相关状态
    'TRACKING_NOT_FOUND': { label: '未找到轨迹', color: 'text-gray-500', icon: Clock },
    'TRACKING_PRE_ADVICE_RECEIVED': { label: '电子预报', color: 'text-blue-500', icon: Clock },
    'TRACKING_PICKED_UP': { label: '已揽收', color: 'text-blue-600', icon: Package },
    'TRACKING_IN_TRANSIT': { label: '运输中', color: 'text-blue-600', icon: Truck },
    'TRACKING_ARRIVED_DESTINATION_COUNTRY': { label: '到达目的国', color: 'text-purple-600', icon: Truck },
    'TRACKING_IN_CUSTOMS': { label: '海关清关中', color: 'text-orange-600', icon: Clock },
    'TRACKING_CUSTOMS_CLEARED': { label: '清关完成', color: 'text-green-500', icon: CheckCircle },
    'TRACKING_ARRIVED_FOR_PICKUP': { label: '到达待取', color: 'text-blue-500', icon: Package },
    'TRACKING_OUT_FOR_DELIVERY': { label: '派送中', color: 'text-blue-600', icon: Truck },
    'TRACKING_DELIVERY_FAILED': { label: '派送失败', color: 'text-red-500', icon: AlertCircle },
    'TRACKING_DELIVERED': { label: '已签收', color: 'text-green-600', icon: CheckCircle },
    'TRACKING_EXCEPTION': { label: '异常', color: 'text-red-600', icon: AlertCircle },
    'TRACKING_RETURNED': { label: '已退回', color: 'text-orange-600', icon: XCircle },
    'TRACKING_CANCELLED': { label: '已取消', color: 'text-gray-600', icon: XCircle },
    'TRACKING_UNKNOWN': { label: '未知状态', color: 'text-gray-500', icon: Clock },
};

export function MainOrderStatisticsModal({ mainOrderId, onClose }: MainOrderStatisticsModalProps) {
    const [isOpen, setIsOpen] = useState(true);
    const [statistics, setStatistics] = useState<MainOrderStatisticsResponse | null>(null);
    const [loading, setLoading] = useState(false);
    const navigate = useNavigate();

    const handleClose = () => {
        setIsOpen(false);
        onClose?.();
    };

    const fetchStatistics = useCallback(async () => {
        setLoading(true);
        try {
            const data = await mainOrderApi.getStatistics(mainOrderId);
            setStatistics(data);
        } catch (error: any) {
            toast.error('获取统计信息失败: ' + error.message);
        } finally {
            setLoading(false);
        }
    }, [mainOrderId]);

    // 处理状态卡片点击
    const handleStatusCardClick = useCallback((status: string) => {
        // 构建跳转URL - 跳转到子订单页面并筛选该主订单下的指定状态
        const url = `/dashboard/order/sub-order?parentId=${mainOrderId}&status=${status}`;

        // 关闭弹窗并跳转
        handleClose();
        navigate(url);
    }, [mainOrderId, navigate, handleClose]);

    // 初始加载数据
    useEffect(() => {
        fetchStatistics();
    }, [fetchStatistics]);

    // 状态显示映射
    const statusDisplayMap: Record<string, { label: string; color: string; icon: any }> = {
        [MainOrderStatus.CREATED]: { label: '已创建', color: 'text-yellow-600', icon: Clock },
        [MainOrderStatus.COMPLETED]: { label: '已完成', color: 'text-green-600', icon: CheckCircle },
        [MainOrderStatus.MATCHING]: { label: '匹配供应商中', color: 'text-blue-600', icon: Clock },
        [MainOrderStatus.PARTIALLY_MATCHED]: { label: '部分匹配供应商', color: 'text-orange-600', icon: Clock },
        [MainOrderStatus.MATCHED]: { label: '全部匹配供应商', color: 'text-green-600', icon: CheckCircle },
        [MainOrderStatus.CANCELD]: { label: '已取消', color: 'text-gray-600', icon: Clock },
        [MainOrderStatus.FAILED]: { label: '失败', color: 'text-red-600', icon: Clock },
    };

    // 类型显示映射
    const typeDisplayMap: Record<string, { label: string; color: string; icon: any }> = {
        [MainOrderType.IMAGE]: { label: '图片订单', color: 'text-blue-600', icon: Image },
        [MainOrderType.ORDER]: { label: '普通订单', color: 'text-purple-600', icon: ShoppingCart },
    };

    return (
        <Dialog open={isOpen} onOpenChange={setIsOpen}>
            <DialogContent className="max-w-7xl max-h-[95vh] overflow-y-auto">
                <DialogHeader className="pb-6">
                    <DialogTitle className="flex items-center gap-3 text-2xl">
                        <BarChart3 className="h-7 w-7 text-blue-600" />
                        主订单统计信息
                    </DialogTitle>
                </DialogHeader>

                <div className="space-y-8">
                    {loading && (
                        <div className="text-center text-sm text-muted-foreground">
                            加载中...
                        </div>
                    )}

                    {/* 总体统计 */}
                    {statistics && (
                        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                            <Card className="border border-blue-100 shadow-md bg-blue-50/50">
                                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                                    <CardTitle className="text-sm font-medium text-gray-700">总子订单数</CardTitle>
                                    <Package className="h-4 w-4 text-blue-500" />
                                </CardHeader>
                                <CardContent className="pt-1">
                                    <div className="text-2xl font-bold text-gray-900">{statistics.totalSubOrders || 0}</div>
                                    <p className="text-xs text-muted-foreground mt-1">
                                        总计子订单数量
                                    </p>
                                </CardContent>
                            </Card>

                            <Card className="border border-green-100 shadow-md bg-green-50/50">
                                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                                    <CardTitle className="text-sm font-medium text-gray-700">已完成子订单</CardTitle>
                                    <CheckCircle className="h-4 w-4 text-green-500" />
                                </CardHeader>
                                <CardContent className="pt-1">
                                    <div className="text-2xl font-bold text-green-600">{statistics.completedSubOrders || 0}</div>
                                    <p className="text-xs text-muted-foreground mt-1">
                                        完成率 {statistics.totalSubOrders > 0 ? ((statistics.completedSubOrders / statistics.totalSubOrders) * 100).toFixed(1) : '0'}%
                                    </p>
                                </CardContent>
                            </Card>

                            <Card className="border border-purple-100 shadow-md bg-purple-50/50">
                                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                                    <CardTitle className="text-sm font-medium text-gray-700">主订单状态</CardTitle>
                                    <FileText className="h-4 w-4 text-purple-500" />
                                </CardHeader>
                                <CardContent className="pt-1">
                                    <div className="text-xl font-bold text-purple-600">{statistics.mainOrderStatus}</div>
                                    <p className="text-xs text-muted-foreground mt-1">
                                        {statusDisplayMap[statistics.mainOrderStatus]?.label || statistics.mainOrderStatus}
                                    </p>
                                </CardContent>
                            </Card>
                        </div>
                    )}

                    <Separator />

                    {/* 子订单状态分布 */}
                    <div className="space-y-4">
                        <h3 className="text-lg font-semibold text-gray-800">子订单状态分布</h3>
                        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 2xl:grid-cols-6 gap-3">
                            {statistics?.subOrderStatusCounts && Object.entries(statistics.subOrderStatusCounts).map(([statusCode, count]) => {
                                const percentage = statistics && statistics.totalSubOrders > 0
                                    ? ((count / statistics.totalSubOrders) * 100).toFixed(1)
                                    : '0';
                                const statusInfo = SubOrderStatusMap[statusCode];
                                const IconComponent = statusInfo?.icon || FileText;

                                return (
                                    <Card
                                        key={statusCode}
                                        className="p-3 cursor-pointer hover:shadow-lg transition-all duration-200 hover:bg-white border border-gray-200 shadow-md bg-gray-50/80"
                                        onClick={() => handleStatusCardClick(statusCode)}
                                    >
                                        <div className="flex flex-col gap-2">
                                            <div className="flex items-center justify-between">
                                                <div className="p-1.5 rounded-lg bg-gray-100">
                                                    <IconComponent className={`h-4 w-4 ${statusInfo?.color || 'text-gray-500'}`} />
                                                </div>
                                                <div className="text-right">
                                                    <div className="text-lg font-bold text-gray-900">{count}</div>
                                                    <div className="text-xs text-muted-foreground">
                                                        {percentage}%
                                                    </div>
                                                </div>
                                            </div>
                                            <div className="space-y-1">
                                                <Badge
                                                    variant={count > 0 ? "default" : "outline"}
                                                    className="text-xs w-fit h-5"
                                                >
                                                    {statusInfo?.label || statusCode}
                                                </Badge>
                                                <div className="text-xs text-muted-foreground font-mono truncate" title={statusCode}>
                                                    {statusCode}
                                                </div>
                                            </div>
                                        </div>
                                    </Card>
                                );
                            })}
                        </div>
                    </div>

                </div>

                <div className="flex justify-end pt-8 border-t">
                    <Button onClick={handleClose} size="lg" className="px-8">
                        关闭
                    </Button>
                </div>
            </DialogContent>
        </Dialog>
    );
}
