import {But<PERSON>} from "@/components/ui/button"
import {ReactNode, useState} from "react"
import {Dialog, DialogContent, DialogDescription, DialogTitle} from "../ui/dialog"

interface DoubleCheckModalProps {
    onConfirm: () => void
    onCancel: () => void
    title: string
    description: string
    children?: ReactNode
}

export function DoubleCheckModal({ onConfirm, onCancel, title, description, children }: DoubleCheckModalProps) {

    const [isOpen, setIsOpen] = useState(true)

    return (
        <Dialog open={isOpen} onOpenChange={setIsOpen}>
            <DialogContent className="sm:max-w-[425px] transition-all duration-300 ease-in-out">
                <DialogTitle>{title}</DialogTitle>
                <DialogDescription>{description}</DialogDescription>
                {children && (
                    <div className="mt-4 max-h-[calc(100vh-300px)] overflow-y-auto">
                        {children}
                    </div>
                )}
                <div className="mt-6 flex justify-end space-x-2">
                    <Button onClick={() => {
                        setIsOpen(false)
                        onCancel()
                    }} variant="secondary">取消</Button>
                    <Button onClick={() => {
                        setIsOpen(false)
                        onConfirm()
                    }} variant="default">确认</Button>
                </div>
            </DialogContent>
        </Dialog >
    )
}