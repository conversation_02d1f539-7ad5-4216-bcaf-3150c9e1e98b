import { useState } from 'react'
import { <PERSON><PERSON>, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogFooter } from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Separator } from "@/components/ui/separator"
import { InfoCircledIcon } from '@radix-ui/react-icons'
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"
import { createBusiness } from '@/api/user/business-api'
import { toast } from 'sonner'
import { DefaultErrorNotification, DefaultSuccessNotification } from '../toast/default-notification'

export function CreateInitialBusinessModal() {

    const [isOpen, setIsOpen] = useState(true)
    const [formData, setFormData] = useState({
        businessName: '',
        userName: '',
        accountName: '',
        accountPassword: '',
        bucketName: '',
        // region: '',
        // secretId: '',
        // secretKey: ''
    })

    const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const { name, value } = e.target
        setFormData(prev => ({ ...prev, [name]: value }))
    }

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault()
        console.log('Form submitted:', formData)
        createBusiness(formData).then(() => {
            toast.custom(id => <DefaultSuccessNotification id={id} message='创建成功' needLink={false} />)
            setIsOpen(false)
        }).catch((error) => {
            toast.custom(id => <DefaultErrorNotification id={id} message={`创建失败: ${error.message}`} needLink={false} />)
        })
    }

    return (
        <Dialog open={isOpen} onOpenChange={setIsOpen}>
            <DialogContent className="sm:max-w-[600px]">
                <DialogHeader>
                    <DialogTitle className="text-2xl font-semibold">创建初始数据</DialogTitle>
                    <DialogDescription>
                        请填写以下信息以创建初始数据。所有字段都是必填的。
                    </DialogDescription>
                </DialogHeader>
                <form onSubmit={handleSubmit}>
                    <div className="grid grid-cols-2 gap-4 py-4">
                        <div className="space-y-1 col-span-2">
                            <Label htmlFor="businessName" className="text-sm font-medium">
                                业务名称
                            </Label>
                            <Input
                                id="businessName"
                                name="businessName"
                                value={formData.businessName}
                                onChange={handleInputChange}
                                className="col-span-2"
                                required
                            />
                        </div>
                        <Separator className="col-span-2" />
                        <div className="space-y-1">
                            <Label htmlFor="username" className="text-sm font-medium">
                                用户名
                            </Label>
                            <Input
                                id="userName"
                                name="userName"
                                value={formData.userName}
                                onChange={handleInputChange}
                                className="col-span-2"
                                required
                            />
                        </div>
                        <div className="space-y-1">
                            <Label htmlFor="accountName" className="text-sm font-medium">
                                账户名称
                            </Label>
                            <Input
                                id="accountName"
                                name="accountName"
                                value={formData.accountName}
                                onChange={handleInputChange}
                                className="col-span-2"
                                required
                            />
                        </div>
                        <div className="space-y-1">
                            <Label htmlFor="accountPassword" className="text-sm font-medium">
                                账户密码
                            </Label>
                            <Input
                                id="accountPassword"
                                name="accountPassword"
                                type="password"
                                value={formData.accountPassword}
                                onChange={handleInputChange}
                                className="col-span-2"
                                required
                            />
                        </div>
                        <Separator className="col-span-2" />
                        <div className="space-y-1 col-span-2">
                            <div className="flex items-center justify-between">
                                <Label htmlFor="bucketName" className="text-sm font-medium">
                                    腾讯云存储桶信息
                                </Label>
                                <TooltipProvider>
                                    <Tooltip>
                                        <TooltipTrigger asChild>
                                            <InfoCircledIcon className="h-4 w-4 text-muted-foreground" />
                                        </TooltipTrigger>
                                        <TooltipContent>
                                            <p>请填写您的腾讯云存储桶信息</p>
                                        </TooltipContent>
                                    </Tooltip>
                                </TooltipProvider>
                            </div>
                            <Input
                                id="bucketName"
                                name="bucketName"
                                placeholder="存储桶名称"
                                value={formData.bucketName}
                                onChange={handleInputChange}
                                className="col-span-2"
                                required
                            />
                        </div>
                        {/* <div className="space-y-1">
                            <Input
                                id="region"
                                name="region"
                                placeholder="地域"
                                value={formData.region}
                                onChange={handleInputChange}
                                className="col-span-2"
                                required
                            />
                        </div>
                        <div className="space-y-1">
                            <Input
                                id="secretId"
                                name="secretId"
                                placeholder="SecretId"
                                value={formData.secretId}
                                onChange={handleInputChange}
                                className="col-span-2"
                                required
                            />
                        </div>
                        <div className="space-y-1">
                            <Input
                                id="secretKey"
                                name="secretKey"
                                placeholder="SecretKey"
                                type="password"
                                value={formData.secretKey}
                                onChange={handleInputChange}
                                className="col-span-2"
                                required
                            /> */}
                        {/* </div> */}
                    </div>
                    <DialogFooter className="col-span-2 flex justify-end">
                        <Button type="button" variant="outline" onClick={() => setIsOpen(false)}>
                            取消
                        </Button>
                        <Button type="submit">创建</Button>
                    </DialogFooter>
                </form>
            </DialogContent>
        </Dialog>
    )
}

