import { Dialog, DialogContent, DialogTitle } from "@/components/ui/dialog";
import * as clipboard from "clipboard-polyfill";
import { Copy, Info } from "lucide-react";
import { useState } from 'react';
import { toast } from "sonner";


export const ImageGalleryModal = ({ imageUrls }: { imageUrls: string[] }) => {
    const [open, setOpen] = useState(true)
    const handleCopy = async (url: string) => {
        // try {
        //     await navigator.clipboard?.writeText(url);
        //     toast.success("已复制图片链接");
        // } catch (error) {
        //     // 如果clipboard API不可用,使用传统方法
        //     const textarea = document.createElement('textarea');
        //     textarea.value = url;
        //     document.body.appendChild(textarea);
        //     textarea.select();
        //     try {
        //         document.execCommand('copy');
        //         toast.success("已复制图片链接");
        //     } catch (err) {
        //         toast.error("复制失败,请手动复制");
        //     }
        //     document.body.removeChild(textarea);
        // }
        try {
            await clipboard.writeText(url);
            toast.success("已复制图片链接");
        } catch (err) {
            toast.error("复制失败，请手动复制");
        }
    }

    return (
        <Dialog open={open} onOpenChange={setOpen}>
            <DialogContent className="sm:max-w-[80vw] sm:max-h-[80vh] overflow-y-auto">
                <DialogTitle>
                    <div className="flex items-center gap-2">
                        商品图片
                    </div>
                    <div className="flex items-center mt-2 space-x-1">
                        <Info className="h-4 w-4 text-gray-500" />
                        <p className="text-xs text-gray-500">
                            如果图片的太大会导致无法显示, 点击图片复制链接
                        </p>
                    </div>


                </DialogTitle>
                <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4 mt-4">
                    {imageUrls.map((url, index) => (
                        <div key={index} className="relative group">
                            <img
                                src={url + "?imageView2/1/w/400/h/300"}
                                alt={`Product image ${index + 1}`}
                                className="w-full h-48 object-cover rounded-md shadow-md"
                            />
                            <div
                                className="absolute inset-0 flex items-center justify-center bg-black/50 opacity-0 group-hover:opacity-100 transition-opacity cursor-pointer"
                                onClick={() => handleCopy(url)}
                            >
                                <Copy className="h-6 w-6 text-white" />
                            </div>
                        </div>
                    ))}
                </div>
            </DialogContent>
        </Dialog>
    );
};