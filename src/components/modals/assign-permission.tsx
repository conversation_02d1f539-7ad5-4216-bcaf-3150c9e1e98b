import { useState } from "react";
import { Dialog, DialogContent, DialogTitle } from "../ui/dialog";
import { Info } from "lucide-react";
import PermissionView from "../check/permission-view";



export default function AssignPermissionModal() {

    const [open, setOpen] = useState(true);

    return (
        <Dialog open={open} onOpenChange={setOpen}>
            <DialogContent className="sm:max-w-[40vw] sm:max-h-[90vh] overflow-y-auto">
                <DialogTitle>
                    <div className="flex items-center gap-2">
                        分配权限
                    </div>
                    <div className="flex items-center mt-2 space-x-1">
                        <Info className="h-4 w-4 text-gray-500" />
                        <p className="text-xs text-gray-500">
                            请选择需要分配的权限
                        </p>
                    </div>

                </DialogTitle>
                <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4 mt-4">
                   <PermissionView />
                </div>
            </DialogContent>
        </Dialog>
    )
}