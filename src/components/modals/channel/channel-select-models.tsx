import { DialogContent, DialogTitle, DialogHeader } from "@/components/ui/dialog";
import { ChannelSelect, ChannelSelectProps } from "./channel-select";


export default function ChannelSelectModels({ onConfirm, company, route }: ChannelSelectProps) {

    return (
        <DialogContent>
            <DialogHeader>
                <DialogTitle>物流选择</DialogTitle>
            </DialogHeader>
            <ChannelSelect onConfirm={onConfirm} company={company} route={route} />
        </DialogContent>
    )
}