"use client"

import { getChannels } from "@/api/channel/channel-api"
import { Channel } from "@/api/channel/channel-model"
import { Button } from "@/components/ui/button"
import { Label } from "@/components/ui/label"
import { cn } from "@/lib/utils"
import { CheckIcon, ChevronLeftIcon } from "@radix-ui/react-icons"
import { useEffect, useRef, useState } from "react"
import { toast } from "sonner"
import { popAllModals } from ".."

interface Company {
    name: string
    displayName: string
    routes: Route[]
}

interface Route {
    methodCode: string
    methodName: string
}

const processChannelsData = (data: Channel[]): Company[] => {
    const companyMap = new Map<string, Company>()

    data.forEach((channel) => {
        if (!channel.enabled) return // 跳过未启用的渠道

        if (!companyMap.has(channel.name)) {
            companyMap.set(channel.name, {
                name: channel.name,
                displayName: channel.displayName,
                routes: [],
            })
        }

        const company = companyMap.get(channel.name)
        if (company) {
            company.routes.push({
                methodCode: channel.methodCode,
                methodName: channel.methodName,
            })
        }
    })
    return Array.from(companyMap.values())
}

export interface ChannelSelectProps {
    onConfirm: (company: string, route: string) => void
    company?: string
    route?: string
}

export function ChannelSelect({ onConfirm, company, route }: ChannelSelectProps) {
    const [open, setOpen] = useState(false)
    const [selectedCompany, setSelectedCompany] = useState<string | null>(company || null)
    const [selectedRoute, setSelectedRoute] = useState<string | null>(route || null)
    const [selectionMode, setSelectionMode] = useState<"company" | "route">(company ? "route" : "company")
    const [availableRoutes, setAvailableRoutes] = useState<Route[]>([])
    const [displayValue, setDisplayValue] = useState("选择物流公司...")
    const selectRef = useRef<HTMLButtonElement>(null)

    const [companiesData, setCompaniesData] = useState<Company[]>([])

    useEffect(() => {
        const fetchChannels = async () => {
            try {
                const fetchedChannels = await getChannels();
                const processedData = processChannelsData(fetchedChannels as Channel[]);
                setCompaniesData(processedData);

                // 如果有默认公司，设置对应的路线
                if (company) {
                    const selectedCompanyData = processedData.find(c => c.name === company);
                    if (selectedCompanyData) {
                        setAvailableRoutes(selectedCompanyData.routes);

                        // 如果同时有默认路线，更新显示值
                        if (route) {
                            const selectedRouteData = selectedCompanyData.routes.find(r => r.methodCode === route);
                            if (selectedRouteData) {
                                setDisplayValue(`${selectedCompanyData.displayName} - ${selectedRouteData.methodName}`);
                            } else {
                                setDisplayValue(selectedCompanyData.displayName);
                            }
                        } else {
                            setDisplayValue(`${selectedCompanyData.displayName} - 选择配送路线...`);
                        }
                    }
                }
            } catch (error) {
                console.error("Error fetching channels:", error);
            }
        };
        fetchChannels();
    }, [company, route]);

    // 当选择公司时更新可用路线
    useEffect(() => {
        if (selectedCompany) {
            const company = companiesData.find((c) => c.name === selectedCompany)
            if (company) {
                setAvailableRoutes(company.routes)
                setSelectionMode("route")
                setDisplayValue(company.displayName)
            }
        } else {
            setSelectionMode("company")
            setDisplayValue("选择物流公司...")
        }
    }, [selectedCompany])

    // 当选择路线时更新显示值
    useEffect(() => {
        if (selectedCompany && selectedRoute) {
            const company = companiesData.find((c) => c.name === selectedCompany)
            const route = availableRoutes.find((r) => r.methodCode === selectedRoute)
            if (company && route) {
                setDisplayValue(`${company.displayName} - ${route.methodName}`)
                setOpen(false)
            }
        } else if (selectedCompany) {
            const company = companiesData.find((c) => c.name === selectedCompany)
            if (company) {
                setDisplayValue(`${company.displayName} - 选择配送路线...`)
            }
        }
    }, [selectedRoute, selectedCompany, availableRoutes])

    // 返回到公司选择
    const handleBackToCompanies = () => {
        setSelectionMode("company")
        // 保留已选择的公司，但清除路线选择
        setSelectedRoute(null)
    }

    // 清除所有选择
    const handleClearSelection = () => {
        setSelectedCompany(null)
        setSelectedRoute(null)
        setSelectionMode("company")
        setOpen(false)
    }

    // 处理公司选择
    const handleCompanySelect = (value: string) => {
        setSelectedCompany(value === selectedCompany ? null : value)
    }

    // 处理路线选择
    const handleRouteSelect = (value: string) => {
        setSelectedRoute(value === selectedRoute ? null : value)
    }

    const handleConfirm = (e: React.MouseEvent<HTMLButtonElement>) => {
        e.preventDefault()
        if (!selectedCompany) {
            toast.error('请选择物流公司')
            return
        }
        if (!selectedRoute) {
            toast.error('请选择配送路线')
            return
        }
        popAllModals()
        onConfirm(selectedCompany, selectedRoute)
    }

    return (
        <div className="space-y-6">
            <div className="space-y-2">
                <Label htmlFor="logistics-select">物流选择</Label>
                <div className="relative">
                    <button
                        id="logistics-select"
                        ref={selectRef}
                        onClick={(e) => {
                            e.preventDefault()
                            setOpen(!open)
                        }}
                        className="flex h-9 w-full items-center justify-between gap-2 rounded-lg border border-input bg-background px-3 py-2 text-start text-sm text-foreground shadow-sm shadow-black/5 focus:border-ring focus:outline-none focus:ring-[3px] focus:ring-ring/20"
                    >
                        <span className="truncate">{displayValue}</span>
                        <svg
                            xmlns="http://www.w3.org/2000/svg"
                            width="16"
                            height="16"
                            viewBox="0 0 24 24"
                            fill="none"
                            stroke="currentColor"
                            strokeWidth="2"
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            className="shrink-0 text-muted-foreground/80"
                        >
                            <path d="m6 9 6 6 6-6" />
                        </svg>
                    </button>

                    {open && (
                        <div
                            className="absolute z-50 mt-1 w-full min-w-[8rem] overflow-hidden rounded-lg border border-input bg-popover text-popover-foreground shadow-lg shadow-black/5 animate-in fade-in-0 zoom-in-95 data-[side=bottom]:slide-in-from-top-2"
                            style={{ width: selectRef.current?.offsetWidth }}
                        >
                            <div className="max-h-[min(24rem,var(--radix-select-content-available-height))]">
                                {selectionMode === "route" && (
                                    <div className="border-b px-2 py-1.5 sticky top-0 bg-popover z-20">
                                        <button
                                            className="flex w-full items-center text-sm text-muted-foreground py-1.5 px-2 rounded-md hover:bg-accent hover:text-accent-foreground"
                                            onClick={handleBackToCompanies}
                                        >
                                            <ChevronLeftIcon className="mr-2 h-4 w-4" />
                                            返回物流公司列表
                                        </button>
                                    </div>
                                )}

                                <div className="p-1">
                                    {selectionMode === "company" ? (
                                        <div role="group" className="py-1 relative">
                                            <div className={cn(
                                                "py-1.5 pe-2 ps-8 text-xs font-medium text-muted-foreground sticky bg-popover z-10",
                                                "top-0"
                                            )}>物流公司</div>
                                            <div className="max-h-[300px] overflow-y-auto">
                                                {companiesData.map((company) => (
                                                    <div
                                                        key={company.name}
                                                        className={cn(
                                                            "relative flex w-full cursor-default select-none items-center rounded-md py-1.5 pe-2 ps-8 text-sm outline-none hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground",
                                                            selectedCompany === company.name && "bg-accent text-accent-foreground",
                                                        )}
                                                        onClick={() => handleCompanySelect(company.name)}
                                                        role="option"
                                                        aria-selected={selectedCompany === company.name}
                                                        tabIndex={0}
                                                    >
                                                        <span className="absolute start-2 flex size-3.5 items-center justify-center">
                                                            {selectedCompany === company.name && <CheckIcon strokeWidth={2} />}
                                                        </span>
                                                        <span>{company.displayName}</span>
                                                    </div>
                                                ))}
                                            </div>
                                        </div>
                                    ) : (
                                        <div role="group" className="py-1 relative">
                                            <div className={cn(
                                                "py-1.5 pe-2 ps-8 text-xs font-medium text-muted-foreground sticky bg-popover z-10",
                                                "top-0"
                                            )}>
                                                {companiesData.find((c) => c.name === selectedCompany)?.displayName} 配送路线
                                            </div>
                                            <div className="max-h-[300px] overflow-y-auto">
                                                {availableRoutes.map((route) => (
                                                    <div
                                                        key={route.methodCode}
                                                        className={cn(
                                                            "relative flex w-full cursor-default select-none items-center rounded-md py-1.5 pe-2 ps-8 text-sm outline-none hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground",
                                                            selectedRoute === route.methodCode && "bg-accent text-accent-foreground",
                                                        )}
                                                        onClick={() => handleRouteSelect(route.methodCode)}
                                                        role="option"
                                                        aria-selected={selectedRoute === route.methodCode}
                                                        tabIndex={0}
                                                    >
                                                        <span className="absolute start-2 flex size-3.5 items-center justify-center">
                                                            {selectedRoute === route.methodCode && <CheckIcon strokeWidth={2} />}
                                                        </span>
                                                        <span>{route.methodName}</span>
                                                    </div>
                                                ))}
                                            </div>
                                        </div>
                                    )}
                                </div>
                            </div>
                        </div>
                    )}
                </div>
            </div>
            <div className="flex justify-end items-center gap-2">
                <Button variant="outline" size="sm" onClick={handleClearSelection}>
                    重新选择
                </Button>
                <Button className="" size={'sm'} onClick={handleConfirm}>
                    确定
                </Button>
            </div>
        </div>
    )
}

