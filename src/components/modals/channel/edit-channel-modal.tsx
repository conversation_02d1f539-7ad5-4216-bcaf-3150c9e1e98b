"use client"

import { Channel } from '@/api/channel/channel-model';
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { refreshTableAtom } from '@/state/common';
import { useSetAtom } from 'jotai';
import {
    Building2,
    Code,
    CreditCard,
    Package,
    Save,
    Settings,
    Tag,
    X
} from "lucide-react";
import React, { useState } from 'react';

interface EditChannelModalProps {
    channel: Channel;
    onSave: (updatedChannel: any) => Promise<void>;
    onClose: () => void;
}

const EditChannelModal: React.FC<EditChannelModalProps> = ({ channel, onSave, onClose }) => {
    const [open, setOpen] = useState(true);
    const setRefresh = useSetAtom(refreshTable<PERSON>tom);
    const [editedChannel, setEditedChannel] = useState({
        displayName: channel.displayName,
        methodCode: channel.methodCode,
        methodName: channel.methodName,
        enabled: channel.enabled,
        iossNumber: channel.iossNumber || '',
    });

    const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const { name, value } = e.target;
        setEditedChannel(prev => ({
            ...prev,
            [name]: value
        }));
    };

    const handleSwitchChange = (checked: boolean) => {
        setEditedChannel(prev => ({
            ...prev,
            enabled: checked
        }));
    };

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();
        await onSave(editedChannel);
        setOpen(false);
    };

    const onSelfClose = () => {
        setOpen(false);
        onClose();
    };

    return (
        <Dialog open={open} onOpenChange={setOpen}>
            <DialogContent className="sm:max-w-[500px]">
                <DialogHeader>
                    <DialogTitle className="text-xl font-semibold">编辑渠道信息</DialogTitle>
                </DialogHeader>
                <form onSubmit={handleSubmit} className="space-y-6 py-4">
                    <div className="space-y-4">
                        <div className="space-y-2">
                            <Label htmlFor="displayName" className="text-sm font-medium flex items-center gap-2">
                                <Package className="w-4 h-4" />
                                渠道名称 <span className="text-red-500">*</span>
                            </Label>
                            <Input
                                id="displayName"
                                name="displayName"
                                value={editedChannel.displayName}
                                onChange={handleChange}
                                className="w-full"
                                required
                            />
                        </div>

                        <div className="space-y-2">
                            <Label htmlFor="methodCode" className="text-sm font-medium flex items-center gap-2">
                                <Code className="w-4 h-4" />
                                方法代码 <span className="text-red-500">*</span>
                            </Label>
                            <Input
                                id="methodCode"
                                name="methodCode"
                                value={editedChannel.methodCode}
                                onChange={handleChange}
                                className="w-full"
                                required
                            />
                        </div>

                        <div className="space-y-2">
                            <Label htmlFor="methodName" className="text-sm font-medium flex items-center gap-2">
                                <Tag className="w-4 h-4" />
                                方法名称
                            </Label>
                            <Input
                                id="methodName"
                                name="methodName"
                                value={editedChannel.methodName}
                                onChange={handleChange}
                                className="w-full"
                            />
                        </div>

                        <div className="space-y-2">
                            <Label htmlFor="iossNumber" className="text-sm font-medium flex items-center gap-2">
                                <CreditCard className="w-4 h-4" />
                                IOSS税号
                            </Label>
                            <Input
                                id="iossNumber"
                                name="iossNumber"
                                value={editedChannel.iossNumber}
                                onChange={handleChange}
                                className="w-full"
                                placeholder="请输入IOSS税号（用于欧盟VAT处理）"
                            />
                        </div>

                        <div className="flex items-center space-x-2">
                            <Switch
                                id="enabled"
                                checked={editedChannel.enabled}
                                onCheckedChange={handleSwitchChange}
                            />
                            <Label htmlFor="enabled" className="text-sm font-medium flex items-center gap-2">
                                <Settings className="w-4 h-4" />
                                启用状态
                            </Label>
                        </div>
                    </div>

                    <div className="flex justify-end space-x-3 pt-4 border-t">
                        <Button
                            type="button"
                            variant="outline"
                            onClick={onSelfClose}
                            className="px-6 flex items-center gap-2"
                        >
                            <X className="w-4 h-4" />
                            取消
                        </Button>
                        <Button
                            type="submit"
                            className="px-6 bg-blue-600 hover:bg-blue-700 flex items-center gap-2"
                        >
                            <Save className="w-4 h-4" />
                            保存
                        </Button>
                    </div>
                </form>
            </DialogContent>
        </Dialog>
    );
};

export default EditChannelModal;
