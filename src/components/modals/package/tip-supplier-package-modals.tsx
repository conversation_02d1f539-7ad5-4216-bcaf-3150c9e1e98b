import { DialogContent, Di<PERSON>Header, DialogTitle } from "@/components/ui/dialog"
import { Badge } from "@/components/ui/badge"
import { SamePackageOrder } from "@/api/supplier/supplier-order/supplier-order-package-api"

export const TipSupplierPackageModals = ({ content, packageContent }: { content: string, packageContent?: SamePackageOrder }) => {
    return (
        <DialogContent className="max-w-3xl">
            <DialogHeader>
                <DialogTitle>
                    提示
                </DialogTitle>
            </DialogHeader>

            <div className="text-center font-bold text-lg text-red-500">
                {content}
            </div>

            {packageContent && (
                <div className="mt-4">
                    <div className="space-y-4">
                        <div className="space-y-2">
                            <div className="text-sm font-medium">已扫描订单:</div>
                            <div className="grid grid-cols-1 gap-3">
                                {Object.entries(packageContent.completed).map(([orderNo, order]) => (
                                    <div
                                        key={orderNo}
                                        className="bg-primary/10 text-primary rounded-lg px-4 py-3 border border-primary/20"
                                    >
                                        <div className="flex items-center justify-between mb-2">
                                            <div className="font-bold text-lg">
                                                {orderNo} ({order.scanCount}/{order.count})
                                            </div>
                                            {order.onSet && (
                                                <Badge className="bg-orange-500 text-white font-extrabold px-4 py-1.5 text-base rounded-full shadow-lg ring-2 ring-orange-300 animate-pulse">
                                                    🎁 套装订单
                                                </Badge>
                                            )}
                                        </div>
                                        <div className="text-base font-bold text-blue-700 bg-blue-50 px-3 py-2 rounded border-l-4 border-blue-500">
                                            📦 {order.title}
                                        </div>
                                    </div>
                                ))}
                            </div>
                        </div>

                        {/* 未完成订单 */}
                        {Object.keys(packageContent.nonCompleted).length > 0 && (
                            <div className="space-y-2">
                                <div className="text-sm font-medium text-muted-foreground">
                                    待扫描订单:
                                </div>
                                <div className="grid grid-cols-1 gap-3">
                                    {Object.entries(packageContent.nonCompleted).map(([orderNo, order]) => (
                                        <div
                                            key={orderNo}
                                            className="border-2 border-dashed border-muted-foreground/50 rounded-lg px-4 py-3 text-muted-foreground bg-gray-50"
                                        >
                                            <div className="flex items-center justify-between mb-2">
                                                <div className="font-bold text-lg">
                                                    {orderNo} ({order.scanCount}/{order.count})
                                                </div>
                                                {order.onSet && (
                                                    <Badge variant="outline" className="border-orange-500 text-orange-700 font-extrabold px-4 py-1.5 text-base rounded-full shadow-sm ring-1 ring-orange-200 bg-orange-50">
                                                        🎁 套装订单
                                                    </Badge>
                                                )}
                                            </div>
                                            <div className="text-base font-bold text-gray-700 bg-gray-100 px-3 py-2 rounded border-l-4 border-gray-400">
                                                📦 {order.title}
                                            </div>
                                        </div>
                                    ))}
                                </div>
                            </div>
                        )}
                    </div>
                </div>
            )}
        </DialogContent>
    )
}
