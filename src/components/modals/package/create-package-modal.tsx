import { supplierOrderPackageApi } from "@/api/supplier/supplier-order/supplier-order-package-api"
import { But<PERSON> } from "@/components/ui/button"
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import { useState } from "react"
import { toast } from "sonner"




export const CreatePackageModal = ({ onSuccess }: { onSuccess: () => void }) => {

    const [open, setOpen] = useState(true)

    const [name, setName] = useState('')

    const [error, setError] = useState<string | null>(null)

    return (
        <Dialog open={open} onOpenChange={setOpen}>
            <DialogContent>
                <DialogHeader>
                    <DialogTitle>
                        创建包裹
                    </DialogTitle>
                </DialogHeader>

                <Input placeholder="请输入包裹名称" value={name} onChange={(e) => setName(e.target.value)} />

                {error && <div className="text-red-500">{error}</div>}

                <div className="flex justify-end">
                    <Button onClick={() => {
                        if (name.trim() === '') {
                            setError('请输入包裹名称')
                            return
                        }
                        setError(null)
                        setOpen(false)

                        toast.promise(supplierOrderPackageApi.createSupplierOrderPackage(name), {
                            loading: '创建中...',
                            success: () => {
                                onSuccess()
                                return '创建成功'
                            },
                            error: (e) => "创建失败: " + e.message
                        })
                    }}>创建</Button>
                </div>
            </DialogContent>
        </Dialog>
    )
}
