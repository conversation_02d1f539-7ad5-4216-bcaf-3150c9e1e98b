import { SupplierOrderPackage, supplierOrderPackageApi } from "@/api/supplier/supplier-order/supplier-order-package-api"
import { Button } from "@/components/ui/button"
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import { useState } from "react"
import { toast } from "sonner"




export const EditPackageModal = ({ onSuccess, pkg }: { onSuccess: () => void, pkg: SupplierOrderPackage }) => {

    const [open, setOpen] = useState(true)

    const [name, setName] = useState(pkg.packageNo)

    const [error, setError] = useState<string | null>(null)

    return (
        <Dialog open={open} onOpenChange={setOpen}>
            <DialogContent>
                <DialogHeader>
                    <DialogTitle>
                        编辑包裹
                    </DialogTitle>
                </DialogHeader>

                <Input placeholder="请输入包裹名称" value={name} onChange={(e) => setName(e.target.value)} />

                {error && <div className="text-red-500">{error}</div>}

                <div className="flex justify-end">
                    <Button onClick={() => {
                        if (name.trim() === '') {
                            setError('请输入包裹名称')
                            return
                        }
                        setError(null)
                        setOpen(false)

                        toast.promise(supplierOrderPackageApi.editSupplierOrderPackage(pkg.id, name), {
                            loading: '编辑中...',
                            success: () => {
                                onSuccess()
                                return '编辑成功'
                            },
                            error: (e) => "编辑失败: " + e.message
                        })
                    }}>编辑</Button>
                </div>
            </DialogContent>
        </Dialog>
    )
}
