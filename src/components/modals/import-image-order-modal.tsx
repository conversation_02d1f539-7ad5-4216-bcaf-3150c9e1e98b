import { queryDownloadTemplate, TemplateType } from "@/api/order/templete/download-templete"
import { uploadImgDownloadExcel } from "@/api/order/upload-excel-api"
import { Button } from "@/components/ui/button"
import { <PERSON><PERSON>, <PERSON>alogContent, <PERSON><PERSON>Footer, <PERSON>alog<PERSON>eader, DialogTitle } from "@/components/ui/dialog"
import { Label } from "@/components/ui/label"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Switch } from "@/components/ui/switch"
import { refreshTableAtom, userInfoAtom } from "@/state/common"
import { useRequest } from "ahooks"
import { AnimatePresence, motion } from 'framer-motion'
import { useAtomValue, useSetAtom } from "jotai"
import { FileText, FileUp, Key, ShoppingBag, User, X, Zap, <PERSON>clip, Trash2 } from 'lucide-react'
import { useEffect, useState } from 'react'
import { toast } from "sonner"
import { DefaultErrorNotification } from "../toast/default-notification"
import { CustomerSelected } from "./customer/customer-selected"

export default function ImportImageOrderModal() {
    const [open, setOpen] = useState(true)
    const [file, setFile] = useState<File | null>(null)
    const [attachments, setAttachments] = useState<File[]>([])
    const [action, setAction] = useState('compress')
    const [needAuthorization, setNeedAuthorization] = useState(false)
    const [selectedUser, setSelectedUser] = useState('')
    const [selectedTemplate, setSelectedTemplate] = useState('')
    const [namingType, setNamingType] = useState('default')
    const [urlSplitToken, setUrlSplitToken] = useState('0')
    const [needDownloadImage, setNeedDownloadImage] = useState(true)
    const flush = useSetAtom(refreshTableAtom)

    const user = useAtomValue(userInfoAtom)

    const { data: templates, run: fetchTemplates } = useRequest(queryDownloadTemplate, {
        manual: true
    })

    useEffect(() => {
        if (open) {
            const type = action === 'order' ? TemplateType.ORDER : TemplateType.IMAGE
            fetchTemplates(undefined, type)
        }
    }, [open])

    useEffect(() => {
        if (templates) {
            setSelectedTemplate(templates.filter(template => template.onDefault)[0]?.id ?? templates[0]?.id ?? '')
        }
    }, [templates])

    const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
        if (event.target.files) {
            setFile(event.target.files[0])
        }
    }

    const handleAttachmentChange = (event: React.ChangeEvent<HTMLInputElement>) => {
        if (event.target.files) {
            const newAttachments = Array.from(event.target.files)
            setAttachments(prev => [...prev, ...newAttachments])
        }
    }

    const removeAttachment = (index: number) => {
        setAttachments(prev => prev.filter((_, i) => i !== index))
    }


    const handleConfirm = async () => {
        setOpen(false)
        if (file) {
            toast.promise(uploadImgDownloadExcel(file, selectedTemplate, selectedUser, action, needAuthorization, namingType, urlSplitToken, needDownloadImage, attachments)
                .then(() => {
                    flush(prev => prev + 1)
                }), {
                loading: '上传中...',
                success: '上传成功',
                error: (e) => {
                    return (
                        <DefaultErrorNotification id={e.id} message={e.message} needLink={false} />
                    )
                }
            })
        }
    }

    const handleActionChange = (value: string) => {
        setAction(value)
        setSelectedTemplate('')
        const type = value === 'order' ? TemplateType.ORDER : TemplateType.IMAGE
        fetchTemplates(undefined, type)
    }

    const isCompressMode = action === 'compress'
    const scrollHeight = isCompressMode ? 'h-[calc(60vh-140px)]' : 'h-[calc(85vh-140px)]'

    return (
        <Dialog open={open} onOpenChange={setOpen}>
            <DialogContent className="max-w-5xl max-h-[90vh] overflow-y-auto">
                <DialogHeader className="flex-shrink-0">
                    <DialogTitle className="text-lg font-semibold text-center text-primary">选择您的操作</DialogTitle>
                </DialogHeader>

                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 py-4">
                    {/* 左列 */}
                    <div className="space-y-5">
                        {/* 文件上传 */}
                        <div className="space-y-2">
                            <Label htmlFor="file" className="text-sm font-medium">上传文件</Label>
                            <motion.div
                                whileHover={{ scale: 1.02 }}
                                whileTap={{ scale: 0.98 }}
                            >
                                <input
                                    id="file"
                                    type="file"
                                    onChange={handleFileChange}
                                    className="hidden"
                                />
                                <Button onClick={() => document.getElementById('file')?.click()} variant="outline" className="w-full rounded-xl h-12">
                                    <FileUp className="mr-2 h-4 w-4" />
                                    {file ? file.name : '选择文件'}
                                </Button>
                            </motion.div>
                        </div>

                        {/* 操作选择 */}
                        <div className="space-y-3">
                            <Label className="text-sm font-medium">选择操作</Label>
                            <RadioGroup value={action} onValueChange={handleActionChange} className="grid grid-cols-2 gap-3">
                                <div>
                                    <RadioGroupItem value="compress" id="compress" className="peer sr-only" />
                                    <Label
                                        htmlFor="compress"
                                        className="flex flex-col items-center justify-center rounded-xl border-2 border-muted bg-popover p-4 hover:bg-accent hover:text-accent-foreground peer-data-[state=checked]:border-[#22C55E] [&:has([data-state=checked])]:border-primary h-20"
                                    >
                                        <Zap className="mb-2 h-5 w-5 text-blue-500" />
                                        <span className="text-sm font-medium">仅压缩</span>
                                    </Label>
                                </div>
                                {(user?.supplierId === null || user?.supplierId === undefined) && (
                                    <div>
                                        <RadioGroupItem value="order" id="order" className="peer sr-only" />
                                        <Label
                                            htmlFor="order"
                                            className="flex flex-col items-center justify-center rounded-xl border-2 border-muted bg-popover p-4 hover:bg-accent hover:text-accent-foreground peer-data-[state=checked]:border-[#22C55E] [&:has([data-state=checked])]:border-primary h-20"
                                        >
                                            <ShoppingBag className="mb-2 h-5 w-5 text-green-500" />
                                            <span className="text-sm font-medium">进入订单</span>
                                        </Label>
                                    </div>
                                )}
                            </RadioGroup>
                        </div>

                        {/* 模板选择 */}
                        <div className="space-y-3">
                            <Label className="text-sm font-medium">选择解析模板</Label>
                            <Select
                                value={selectedTemplate}
                                onValueChange={setSelectedTemplate}
                            >
                                <SelectTrigger className="w-full rounded-xl h-12">
                                    <SelectValue placeholder="选择模板" />
                                </SelectTrigger>
                                <SelectContent>
                                    {templates?.map(template => (
                                        <SelectItem key={template.id} value={template.id}>
                                            <div className="flex items-center">
                                                <FileText className="mr-2 h-4 w-4 text-blue-500" />
                                                {template.name}
                                            </div>
                                        </SelectItem>
                                    ))}
                                </SelectContent>
                            </Select>
                            {action === 'order' && (
                                <div className="flex items-center space-x-2 px-1">
                                    <Switch
                                        id="authorization"
                                        checked={needAuthorization}
                                        onCheckedChange={setNeedAuthorization}
                                    />
                                    <Label htmlFor="authorization" className="text-sm text-muted-foreground cursor-pointer flex items-center">
                                        <Key className="mr-2 h-4 w-4 text-yellow-500" />
                                        模板下载需要授权
                                    </Label>
                                </div>
                            )}
                        </div>

                        {/* 客户选择 */}
                        {(action === 'order' || needAuthorization) && (
                            <AnimatePresence>
                                <motion.div
                                    initial={{ opacity: 0, height: 0 }}
                                    animate={{ opacity: 1, height: 'auto' }}
                                    exit={{ opacity: 0, height: 0 }}
                                    transition={{ duration: 0.2 }}
                                    className="space-y-2"
                                >
                                    <Label htmlFor="authorization" className="text-sm font-medium flex items-center">
                                        <User className="mr-2 h-4 w-4 text-yellow-500" />
                                        选择订单来源客户
                                    </Label>
                                    <CustomerSelected select={setSelectedUser} />
                                </motion.div>
                            </AnimatePresence>
                        )}
                    </div>

                    {/* 右列 */}
                    <div className="space-y-5">
                        {/* 附件上传 */}
                        {action === 'order' && (
                            <AnimatePresence>
                                <motion.div
                                    initial={{ opacity: 0, height: 0 }}
                                    animate={{ opacity: 1, height: 'auto' }}
                                    exit={{ opacity: 0, height: 0 }}
                                    transition={{ duration: 0.2 }}
                                    className="space-y-2"
                                >
                                    <Label htmlFor="attachments" className="text-sm font-medium">上传附件（可选）</Label>
                                    <motion.div
                                        whileHover={{ scale: 1.02 }}
                                        whileTap={{ scale: 0.98 }}
                                    >
                                        <input
                                            id="attachments"
                                            type="file"
                                            multiple
                                            onChange={handleAttachmentChange}
                                            className="hidden"
                                            accept=".zip,.rar,.7z,.tar,.gz,.xlsx,.xls,.csv,.pdf,.doc,.docx,.jpg,.jpeg,.png,.gif,.bmp,.webp,.txt,.json,.xml"
                                        />
                                        <Button onClick={() => document.getElementById('attachments')?.click()} variant="outline" className="w-full rounded-xl h-12">
                                            <Paperclip className="mr-2 h-4 w-4" />
                                            选择附件文件
                                        </Button>
                                    </motion.div>
                                    {attachments.length > 0 && (
                                        <div className="space-y-2 mt-2 max-h-32 overflow-y-auto">
                                            <Label className="text-xs text-muted-foreground">已选择的附件：</Label>
                                            {attachments.map((attachment, index) => (
                                                <div key={index} className="flex items-center justify-between bg-muted p-2 rounded-lg">
                                                    <div className="flex items-center space-x-2 flex-1 min-w-0">
                                                        <Paperclip className="h-4 w-4 text-blue-500 flex-shrink-0" />
                                                        <span className="text-sm truncate">{attachment.name}</span>
                                                        <span className="text-xs text-muted-foreground flex-shrink-0">
                                                            ({(attachment.size / 1024 / 1024).toFixed(2)} MB)
                                                        </span>
                                                    </div>
                                                    <Button
                                                        variant="ghost"
                                                        size="sm"
                                                        onClick={() => removeAttachment(index)}
                                                        className="h-6 w-6 p-0 flex-shrink-0"
                                                    >
                                                        <Trash2 className="h-3 w-3" />
                                                    </Button>
                                                </div>
                                            ))}
                                        </div>
                                    )}
                                    <div className="text-xs text-muted-foreground">
                                        <p>支持格式：压缩包(.zip, .rar, .7z等)、Excel文件(.xlsx, .xls)、文档(.pdf, .doc)、图片等</p>
                                    </div>
                                </motion.div>
                            </AnimatePresence>
                        )}

                        {/* 下载地址分割符 */}
                        <div className="space-y-2">
                            <Label className="text-sm font-medium">下载地址分割符</Label>
                            <Select
                                value={urlSplitToken}
                                onValueChange={setUrlSplitToken}
                            >
                                <SelectTrigger className="w-full rounded-xl h-12">
                                    <SelectValue placeholder="选择分割符" />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem value="0">| 分割</SelectItem>
                                    <SelectItem value="1">; 分割</SelectItem>
                                    <SelectItem value='2'>换行 分割</SelectItem>
                                </SelectContent>
                            </Select>
                        </div>

                        {/* 命名方式 */}
                        <div className="space-y-3">
                            <Label className="text-sm font-medium">命名方式</Label>
                            <div className="space-y-3 bg-muted/30 p-4 rounded-xl">
                                <div className="flex items-center space-x-2">
                                    <Switch
                                        id="naming-type"
                                        checked={namingType === 'simple'}
                                        onCheckedChange={(checked) => setNamingType(checked ? 'simple' : 'default')}
                                    />
                                    <Label htmlFor="naming-type" className="text-sm text-muted-foreground cursor-pointer flex items-center">
                                        <FileText className="mr-2 h-4 w-4 text-blue-500" />
                                        使用简单命名
                                    </Label>
                                </div>
                                <div className="text-xs text-muted-foreground">
                                    <p>当前格式：{namingType === 'default' ? '订单号-原图片名称.扩展名' : '订单号-Excel列名-序号.扩展名'}</p>
                                </div>
                            </div>
                        </div>

                        {/* 下载图片设置 */}
                        {action === 'order' && (
                            <div className="space-y-3">
                                <Label className="text-sm font-medium">下载图片</Label>
                                <div className="bg-muted/30 p-4 rounded-xl space-y-3">
                                    <div className="flex items-center space-x-2">
                                        <Switch
                                            id="download-image"
                                            checked={needDownloadImage}
                                            onCheckedChange={setNeedDownloadImage}
                                        />
                                        <Label htmlFor="download-image" className="text-sm text-muted-foreground cursor-pointer flex items-center">
                                            <FileText className="mr-2 h-4 w-4 text-blue-500" />
                                            下载订单图片
                                        </Label>
                                    </div>
                                    <div className="text-xs text-muted-foreground">
                                        <p>默认下载图片，关闭后将只生成订单数据</p>
                                    </div>
                                </div>
                            </div>
                        )}
                    </div>
                </div>

                <DialogFooter className="sm:justify-between flex-shrink-0 mt-6 pt-4 border-t">
                    <Button variant="outline" onClick={() => setOpen(false)} className="rounded-xl">
                        <X className="mr-2 h-4 w-4" />
                        取消
                    </Button>
                    <Button onClick={handleConfirm} className="bg-primary text-primary-foreground hover:bg-primary/90 rounded-xl">
                        <Zap className="mr-2 h-4 w-4" />
                        确定
                    </Button>
                </DialogFooter>
            </DialogContent>
        </Dialog>
    )
}