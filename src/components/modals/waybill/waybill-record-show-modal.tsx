import { WaybillR<PERSON><PERSON>, waybill<PERSON><PERSON>ord<PERSON><PERSON> } from "@/api/order/waybill/waybill-record-api";
import { DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { useRequest } from "ahooks";
import {
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableHeader,
    TableRow,
} from "@/components/ui/table";
import { format } from "date-fns";
import { Skeleton } from "@/components/ui/skeleton";
import PDFViewerButton from "@/components/buttons/pdf-view";

export function WaybillRecordShowModal({ waybillId }: { waybillId: string }) {

    const { data, loading } = useRequest<WaybillRecord[], any>(waybillRecordApi.list, {
        defaultParams: [waybillId],
    });

    return (
        <DialogContent className="max-w-4xl">
            <DialogHeader>
                <DialogTitle>运单记录</DialogTitle>
            </DialogHeader>
            <div className="space-y-4">
                {loading ? (
                    <div className="space-y-2">
                        <Skeleton className="h-12 w-full" />
                        <Skeleton className="h-12 w-full" />
                        <Skeleton className="h-12 w-full" />
                    </div>
                ) : (
                    <Table>
                        <TableHeader>
                            <TableRow>
                                <TableHead>运单号</TableHead>
                                <TableHead>渠道</TableHead>
                                <TableHead>配送方式</TableHead>
                                <TableHead>创建时间</TableHead>
                                <TableHead>运单标签</TableHead>
                            </TableRow>
                        </TableHeader>
                        <TableBody>
                            {data?.map((record) => (
                                <TableRow key={record.id}>
                                    <TableCell>{record.waybillNo}</TableCell>
                                    <TableCell>{record.channel || '-'}</TableCell>
                                    <TableCell>{record.shipMethod || '-'}</TableCell>
                                    <TableCell>
                                        {format(new Date(record.createdAt), 'yyyy-MM-dd HH:mm:ss')}
                                    </TableCell>
                                    <TableCell>
                                        {record.waybillLabelUrl && (
                                            <PDFViewerButton
                                                pdfUrl={record.waybillLabelUrl}
                                            />
                                        )}
                                    </TableCell>
                                </TableRow>
                            ))}
                        </TableBody>
                    </Table>
                )}
            </div>
        </DialogContent>
    );
}