import { Button } from "@/components/ui/button";
import { <PERSON><PERSON><PERSON>onte<PERSON>, Di<PERSON>Header, DialogTitle } from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import { useRequest } from "ahooks";
import { popAllModals } from "..";
import { toast } from "sonner";
import { cancelStopWaybillPrint } from "@/api/order/waybill/waybill-api";


export function CancelStopWaybillPrintModal({ waybillIds }: { waybillIds: string[] }) {



    const { run, loading } = useRequest(cancelStopWaybillPrint, {
        manual: true,
        onSuccess: () => {
            toast.success('取消成功')
            popAllModals()
        },
        onError: (error) => {
            toast.error('取消失败: ' + error.message)
        }
    })

    const handleStopPrint = () => {
        run({
            ids: waybillIds,
        })
    }

    return (
        <DialogContent>
            <DialogHeader>
                <DialogTitle>停止打印设置</DialogTitle>
            </DialogHeader>
            <div className="flex flex-col gap-4">
                <div className="flex flex-col gap-2">
                    <Label>提示</Label>
                    <Button onClick={handleStopPrint} disabled={loading}>{loading ? '取消中...' : '取消'}</Button>
                </div>
            </div>
        </DialogContent>
    )
}