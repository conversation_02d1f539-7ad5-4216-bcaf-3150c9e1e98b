import { Button } from "@/components/ui/button";
import { <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>eader, DialogTitle } from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import { useRequest } from "ahooks";
import { useState } from "react";
import { popAllModals } from "..";
import { toast } from "sonner";
import { stopWaybillPrint } from "@/api/order/waybill/waybill-api";
import { Textarea } from "@/components/ui/textarea";


export function StopWaybillPrintModal({ waybillIds }: { waybillIds: string[] }) {


    const [warning, setWarning] = useState('')

    const { run, loading } = useRequest(stopWaybillPrint, {
        manual: true,
        onSuccess: () => {
            toast.success('设置成功')
            popAllModals()
        },
        onError: (error) => {
            toast.error('设置失败: ' + error.message)
        }
    })

    const handleStopPrint = () => {
        run({
            ids: waybillIds,
            warning,
        })
    }

    return (
        <DialogContent>
            <DialogHeader>
                <DialogTitle>停止打印设置</DialogTitle>
            </DialogHeader>
            <div className="flex flex-col gap-4">
                <div className="flex flex-col gap-2">
                    <Label>提示</Label>
                    <Textarea value={warning} onChange={(e) => setWarning(e.target.value)} />
                </div>
                <Button onClick={handleStopPrint} disabled={loading}>{loading ? '设置中...' : '设置'}</Button>
            </div>
        </DialogContent>
    )
}