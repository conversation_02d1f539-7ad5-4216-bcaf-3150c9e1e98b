import { Di<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>Header, DialogTitle } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { useState } from "react";
import { useRequest } from "ahooks";
import { exportWaybillExcel } from "@/api/order/waybill/waybill-api";
import { toast } from "sonner";
import { useExcelDownload } from "@/hooks/use-export-file";





export function WaybillExcelExportModels({ waybillIds }: { waybillIds: string[] }) {

    const [fileName, setFileName] = useState('')

    const [open, setOpen] = useState(true)


    const { runAsync: runExportWaybillExcel } = useRequest(exportWaybillExcel, {
        manual: true,
        onSuccess: () => {
            setOpen(false)
            toast.success('导出成功')
        },
        onError: (e) => {
            toast.error('导出失败:' + e.message)
        }
    })

    const { downloadExcel, isLoading } = useExcelDownload(runExportWaybillExcel)

    return (
        <Dialog open={open} onOpenChange={setOpen}>
            <DialogContent>
                <DialogHeader>
                    <DialogTitle>导出运单excel</DialogTitle>
                </DialogHeader>
                <div className="flex flex-col gap-4">
                    <Input type="text" placeholder="请输入文件名" value={fileName} onChange={(e) => setFileName(e.target.value)} />
                    <Button onClick={() => downloadExcel(waybillIds, fileName)} disabled={isLoading}>{isLoading ? '导出中...' : '导出'}</Button>
                </div>
            </DialogContent>
        </Dialog>
    )
}