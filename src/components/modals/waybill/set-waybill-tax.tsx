import { setWaybillTax } from "@/api/order/waybill/waybill-api";
import { Button } from "@/components/ui/button";
import { DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { useRequest } from "ahooks";
import { useState } from "react";
import { popAllModals } from "..";
import { toast } from "sonner";





export function SetWaybillTaxModal({ waybillIds }: { waybillIds: string[] }) {


    const [tax, setTax] = useState(0)

    const { run: updateTax, loading } = useRequest(setWaybillTax, {
        manual: true,
        onSuccess: () => {
            toast.success('设置成功')
            popAllModals()
        },
        onError: (error) => {
            toast.error('设置失败: ' + error.message)
        }
    })

    const handleUpdateTax = () => {
        updateTax({
            waybillIds,
            tax,
        })
    }

    return (
        <DialogContent>
            <DialogHeader>
                <DialogTitle>设置快速通道税费</DialogTitle>
            </DialogHeader>
            <div className="flex flex-col gap-4">
                <div className="flex flex-col gap-2">
                    <Label>税费</Label>
                    <Input type="number" value={tax} onChange={(e) => setTax(Number(e.target.value))} />
                </div>
                <Button onClick={handleUpdateTax} disabled={loading}>{loading ? '设置中...' : '设置'}</Button>
            </div>
        </DialogContent>
    )
}