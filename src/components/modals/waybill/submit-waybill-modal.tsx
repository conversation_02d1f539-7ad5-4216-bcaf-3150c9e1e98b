import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { useState } from "react"
import { Dialog, DialogContent, DialogDescription, DialogTitle } from "@/components/ui/dialog"
import { toast } from "sonner"
import { submitWaybill } from "@/api/order/waybill/waybill-api"

interface SubmitWaybillModalProps {
    waybillId: string
    onSuccess?: () => void
}

export function SubmitWaybillModal({ waybillId, onSuccess }: SubmitWaybillModalProps) {
    const [isOpen, setIsOpen] = useState(true)
    const [waybillNo, setWaybillNo] = useState("")
    const [isSubmitting, setIsSubmitting] = useState(false)

    const handleSubmit = async () => {
        if (!waybillNo.trim()) {
            toast.error("请输入运单号")
            return
        }

        setIsSubmitting(true)
        try {
            await submitWaybill(waybillId, waybillNo)
            toast.success("运单提交成功")
            setIsOpen(false)
            onSuccess?.()
        } catch (error: any) {
            toast.error("运单提交失败: " + (error.message || "未知错误"))
        } finally {
            setIsSubmitting(false)
        }
    }

    return (
        <Dialog open={isOpen} onOpenChange={setIsOpen}>
            <DialogContent className="sm:max-w-[425px] transition-all duration-300 ease-in-out">
                <DialogTitle>提交运单</DialogTitle>
                <DialogDescription>
                    请输入运单号以提交运单，系统将自动获取对应的面单信息。
                </DialogDescription>
                <div className="mt-4 space-y-4">
                    <div className="space-y-2">
                        <Label htmlFor="waybillNo">运单号</Label>
                        <Input
                            id="waybillNo"
                            placeholder="请输入运单号"
                            value={waybillNo}
                            onChange={(e) => setWaybillNo(e.target.value)}
                            onKeyDown={(e) => {
                                if (e.key === "Enter" && !isSubmitting) {
                                    handleSubmit()
                                }
                            }}
                            disabled={isSubmitting}
                        />
                    </div>
                </div>
                <div className="mt-6 flex justify-end space-x-2">
                    <Button 
                        onClick={() => setIsOpen(false)} 
                        variant="secondary"
                        disabled={isSubmitting}
                    >
                        取消
                    </Button>
                    <Button 
                        onClick={handleSubmit} 
                        variant="default"
                        disabled={isSubmitting || !waybillNo.trim()}
                    >
                        {isSubmitting ? "提交中..." : "提交"}
                    </Button>
                </div>
            </DialogContent>
        </Dialog>
    )
}