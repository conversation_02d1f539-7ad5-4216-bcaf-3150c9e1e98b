"use client"

import type React from "react"

import { useState } from "react"
import { Check, Info } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import {
    <PERSON><PERSON>,
    DialogContent,
    DialogHeader,
    <PERSON>alogTitle,
    <PERSON>alogTrigger,
    <PERSON><PERSON>Footer,
} from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Switch } from "@/components/ui/switch"
import { Separator } from "@/components/ui/separator"
import { cn } from "@/lib/utils"
import { useRequest } from "ahooks"
import { extendWaybill } from "@/api/order/waybill/waybill-api"
import { toast } from "sonner"

export default function ExtendWaybillModals({ id, originalOrderNumber }: { id: string, originalOrderNumber: string }) {
    const [open, setOpen] = useState(true)
    const [manualEntry, setManualEntry] = useState(false)
    const [orderNumber, setOrderNumber] = useState<string | null>(null)

    const { runAsync: extendWaybillRequest, loading } = useRequest(extendWaybill, {
        manual: true,
        onSuccess: (result) => {
            toast.success("订单号已扩展")
            setOpen(false)
            window.open(`/dashboard/order/waybill?orderNo=${result}`, '_blank')
        },
        onError: (error) => {
            toast.error("扩展订单号失败: " + error.message)
        }
    })

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault()
        // 处理表单提交
        // 如果 manualEntry 为 true，使用 orderNumber
        // 否则使用系统生成的订单号
        await extendWaybillRequest(id, orderNumber || undefined)
    }

    return (
        <Dialog open={open} onOpenChange={setOpen}>
            <DialogTrigger asChild>
                <Button variant="outline">扩展运单</Button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-[575px]" autoFocus={false}>
                <DialogHeader>
                    <DialogTitle>扩展运单</DialogTitle>
                </DialogHeader>

                <Separator className="" />

                <form onSubmit={handleSubmit}>
                    <div className="rounded-lg border border-amber-100 p-4 mb-4 bg-[#FFFACD] text-amber-800 text-sm leading-relaxed">
                        <p className="mb-2">请确认您的运单信息：</p>
                        <ul className="list-disc pl-5 space-y-1">
                            <li>这个操作只会生成一个新的运单</li>
                            <li>不会生成新的子订单</li>
                            <li>新生成的运单不会推送给工厂</li>
                        </ul>
                        <p className="mt-2">如需手动设置订单号，请开启下方开关。</p>
                    </div>
                    <div className="grid gap-4 py-2">
                        <div className="flex items-center justify-between">
                            <div className="space-y-0.5">
                                <Label htmlFor="manual-entry">手动输入订单号</Label>
                                <p className="text-sm text-muted-foreground">默认情况下系统会自动生成订单号</p>
                            </div>
                            <Switch id="manual-entry" checked={manualEntry} onCheckedChange={setManualEntry} />
                        </div>

                        {manualEntry && (
                            <div className="flex items-start gap-2 p-3 rounded-lg bg-blue-50 border border-blue-100">
                                <Info className="h-5 w-5 text-blue-500 mt-0.5 flex-shrink-0" />
                                <div className="text-sm text-blue-700">
                                    <p className="font-medium">您正在创建子订单</p>
                                    <p className="mt-1">
                                        原始订单号: <span className="font-mono font-medium">{originalOrderNumber}</span>
                                    </p>
                                    <p className="mt-1">建议您在手动输入的订单号中包含对原始订单的引用。</p>
                                </div>
                            </div>
                        )}

                        <div
                            className={cn(
                                "grid gap-2 transition-all duration-300",
                                manualEntry ? "grid-rows-[1fr] opacity-100" : "grid-rows-[0fr] opacity-0",
                            )}
                        >
                            <div className="overflow-hidden p-px">
                                <Label htmlFor="order-number">订单号</Label>
                                <Input
                                    id="order-number"
                                    value={orderNumber || ""}
                                    onChange={(e) => setOrderNumber(e.target.value)}
                                    placeholder={`请输入订单号（例如：${originalOrderNumber}-TJ）`}
                                    className="w-full"
                                    disabled={!manualEntry}
                                />
                            </div>
                        </div>

                        <div className="rounded-lg border p-3 bg-muted/50">
                            <div className="flex items-center gap-2 text-sm">
                                <Check className="h-4 w-4 text-green-500" />
                                <span>{manualEntry ? "您正在使用手动输入的订单号" : "系统将为您自动生成订单号"}</span>
                            </div>
                        </div>
                    </div>

                    <Separator className="my-4" />

                    <DialogFooter>
                        <Button type="button" variant="outline" onClick={() => setOpen(false)}>
                            取消
                        </Button>
                        <Button type="submit" disabled={loading}>{loading ? "提交中..." : "提交"}</Button>
                    </DialogFooter>
                </form>
            </DialogContent>
        </Dialog>
    )
}
