import { WaybillPageResponse } from '@/api/order/waybill/waybill-model';
import { But<PERSON> } from "@/components/ui/button";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { refreshTableAtom } from '@/state/common';
import { useSetAtom } from 'jotai';
import {
    Building2,
    Code,
    CreditCard,
    DollarSign,
    FileText,
    Globe,
    Mail,
    MapPin,
    MapPinned,
    Package,
    Phone,
    Save,
    User,
    Weight,
    X,
    AlertTriangle
} from "lucide-react";
import React, { useState, useMemo } from 'react';

interface EditWaybillModalProps {
    waybill: WaybillPageResponse;
    onSave: (updatedWaybill: Partial<WaybillPageResponse>) => void;
    onClose: () => void;
}

const EditWaybillModal: React.FC<EditWaybillModalProps> = ({ waybill, onSave, onClose }) => {
    const [open, setOpen] = useState(true);
    const setRefresh = useSetAtom(refreshTableAtom);
    const [editedWaybill, setEditedWaybill] = useState({
        receiverName: waybill.receiverName,
        country: waybill.country,
        state: waybill.state,
        city: waybill.city,
        street: waybill.street,
        postcode: waybill.postcode,
        phone: waybill.phone,
        channel: waybill.channel,
        shipMethod: waybill.shipMethod,
        taxNumber: waybill.taxNumber,
        hsCode: waybill.hsCode,
        material: waybill.material,
        street2: waybill.street2,
        name: waybill.name,
        totalPrice: waybill.totalPrice,
        weight: waybill.product.weight,
        iossNumber: waybill.iossNumber,
    });

    // 检查是否是昌联渠道，如果是则禁用编辑
    const isChanglianChannel = useMemo(() => {
        return waybill.channel === 'CHANGLIAN';
    }, [waybill.channel]);

    const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const { name, value } = e.target;
        setEditedWaybill(prev => ({ ...prev, [name]: value }));
    };

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        onSave(editedWaybill);
        setRefresh(prev => prev + 1);
        onSelfClose();
    };

    const onSelfClose = () => {
        setOpen(false);
        onClose();
    }

    return (
        <Dialog open={open} onOpenChange={setOpen}>
            <DialogContent className="sm:max-w-[800px]">
                <DialogHeader>
                    <DialogTitle className="text-xl font-semibold">编辑运单信息</DialogTitle>
                </DialogHeader>
                <ScrollArea className="max-h-[80vh] px-1">
                    {isChanglianChannel && (
                        <Alert className="mb-4 border-red-200 bg-red-50">
                            <AlertDescription className="text-red-700 ml-1">
                                当前运单使用常联渠道，不支持编辑参数。如需更改参数，请联系物流部门进行沟通处理。
                            </AlertDescription>
                        </Alert>
                    )}
                    <form onSubmit={handleSubmit} className="space-y-6 py-4">
                        <div className="grid grid-cols-3 gap-x-4 gap-y-6">
                            <div className="space-y-2">
                                <Label htmlFor="receiverName" className="text-sm font-medium flex items-center gap-2">
                                    <User className="w-4 h-4" />
                                    收件人 <span className="text-red-500">*</span>
                                </Label>
                                <Input
                                    id="receiverName"
                                    name="receiverName"
                                    value={editedWaybill.receiverName}
                                    onChange={handleChange}
                                    className="w-full !ring-0 focus:outline-none"
                                    disabled={isChanglianChannel}
                                    required
                                />
                            </div>

                            <div className="space-y-2">
                                <Label htmlFor="phone" className="text-sm font-medium flex items-center gap-2">
                                    <Phone className="w-4 h-4" />
                                    电话 <span className="text-red-500">*</span>
                                </Label>
                                <Input
                                    id="phone"
                                    name="phone"
                                    value={editedWaybill.phone}
                                    onChange={handleChange}
                                    className="w-full !ring-0 focus:outline-none"
                                    disabled={isChanglianChannel}
                                    required
                                />
                            </div>

                            <div className="space-y-2">
                                <Label htmlFor="country" className="text-sm font-medium flex items-center gap-2">
                                    <Globe className="w-4 h-4" />
                                    国家 <span className="text-red-500">*</span>
                                </Label>
                                <Input
                                    id="country"
                                    name="country"
                                    value={editedWaybill.country}
                                    onChange={handleChange}
                                    className="w-full !ring-0 focus:outline-none"
                                    disabled={isChanglianChannel}
                                    required
                                />
                            </div>

                            <div className="space-y-2">
                                <Label htmlFor="state" className="text-sm font-medium flex items-center gap-2">
                                    <MapPin className="w-4 h-4" />
                                    州/省
                                </Label>
                                <Input
                                    id="state"
                                    name="state"
                                    value={editedWaybill.state}
                                    onChange={handleChange}
                                    className="w-full !ring-0 focus:outline-none"
                                    disabled={isChanglianChannel}
                                />
                            </div>

                            <div className="space-y-2">
                                <Label htmlFor="city" className="text-sm font-medium flex items-center gap-2">
                                    <Building2 className="w-4 h-4" />
                                    城市 <span className="text-red-500">*</span>
                                </Label>
                                <Input
                                    id="city"
                                    name="city"
                                    value={editedWaybill.city}
                                    onChange={handleChange}
                                    className="w-full !ring-0 focus:outline-none"
                                    disabled={isChanglianChannel}
                                    required
                                />
                            </div>

                            <div className="space-y-2">
                                <Label htmlFor="postcode" className="text-sm font-medium flex items-center gap-2">
                                    <Mail className="w-4 h-4" />
                                    邮编 <span className="text-red-500">*</span>
                                </Label>
                                <Input
                                    id="postcode"
                                    name="postcode"
                                    value={editedWaybill.postcode}
                                    onChange={handleChange}
                                    className="w-full !ring-0 focus:outline-none"
                                    disabled={isChanglianChannel}
                                    required
                                />
                            </div>

                            <div className="space-y-2">
                                <Label htmlFor="taxNumber" className="text-sm font-medium flex items-center gap-2">
                                    <CreditCard className="w-4 h-4" />
                                    税号
                                </Label>
                                <Input
                                    id="taxNumber"
                                    name="taxNumber"
                                    value={editedWaybill.taxNumber}
                                    onChange={handleChange}
                                    className="w-full !ring-0 focus:outline-none"
                                    disabled={isChanglianChannel}
                                />
                            </div>
                            <div className="space-y-2">
                                <Label htmlFor="iossNumber" className="text-sm font-medium flex items-center gap-2">
                                    <CreditCard className="w-4 h-4" />
                                    IOSS税号
                                </Label>
                                <Input
                                    id="iossNumber"
                                    name="iossNumber"
                                    value={editedWaybill.iossNumber}
                                    onChange={handleChange}
                                    className="w-full !ring-0 focus:outline-none"
                                    disabled={isChanglianChannel}
                                />
                            </div>
                            <div className="space-y-2">
                                <Label htmlFor="hsCode" className="text-sm font-medium flex items-center gap-2">
                                    <Code className="w-4 h-4" />
                                    海关编码 HS Code
                                </Label>
                                <Input
                                    id="hsCode"
                                    name="hsCode"
                                    value={editedWaybill.hsCode}
                                    onChange={handleChange}
                                    className="w-full !ring-0 focus:outline-none"
                                    disabled={isChanglianChannel}
                                />
                            </div>
                            <div className="space-y-2">
                                <Label htmlFor="material" className="text-sm font-medium flex items-center gap-2">
                                    <Package className="w-4 h-4" />
                                    材质
                                </Label>
                                <Input
                                    id="material"
                                    name="material"
                                    value={editedWaybill.material}
                                    onChange={handleChange}
                                    disabled={isChanglianChannel}
                                />
                            </div>
                            <div className="space-y-2">
                                <Label htmlFor="name" className="text-sm font-medium flex items-center gap-2">
                                    <FileText className="w-4 h-4" />
                                    物品英文名
                                </Label>
                                <Input
                                    id="name"
                                    name="name"
                                    value={editedWaybill.name}
                                    onChange={handleChange}
                                    disabled={isChanglianChannel}
                                />
                            </div>
                            <div className="space-y-2">
                                <Label htmlFor="totalPrice" className="text-sm font-medium flex items-center gap-2">
                                    <DollarSign className="w-4 h-4" />
                                    总价
                                </Label>
                                <Input
                                    id="totalPrice"
                                    name="totalPrice"
                                    value={editedWaybill.totalPrice}
                                    onChange={handleChange}
                                    disabled={isChanglianChannel}
                                />
                            </div>
                            <div className="space-y-2">
                                <Label htmlFor="weight" className="text-sm font-medium flex items-center gap-2">
                                    <Weight className="w-4 h-4" />
                                    重量
                                </Label>
                                <Input
                                    id="weight"
                                    name="weight"
                                    value={editedWaybill.weight}
                                    onChange={handleChange}
                                    disabled={isChanglianChannel}
                                />
                            </div>
                        </div>

                        <div className="space-y-2">
                            <Label htmlFor="street" className="text-sm font-medium flex items-center gap-2">
                                <MapPinned className="w-4 h-4" />
                                街道地址1 <span className="text-red-500">*</span>
                            </Label>
                            <Input
                                id="street"
                                name="street"
                                value={editedWaybill.street}
                                onChange={handleChange}
                                className="w-full !ring-0 focus:outline-none"
                                disabled={isChanglianChannel}
                                required
                            />
                        </div>

                        <div className="space-y-2">
                            <Label htmlFor="street2" className="text-sm font-medium flex items-center gap-2">
                                <MapPinned className="w-4 h-4" />
                                街道地址2
                            </Label>
                            <Input
                                id="street2"
                                name="street2"
                                value={editedWaybill.street2}
                                onChange={handleChange}
                                className="w-full !ring-0 focus:outline-none"
                                disabled={isChanglianChannel}
                            />
                        </div>
                    </form>
                </ScrollArea>
                <div className="flex justify-end space-x-3 mt-6 pt-4 border-t">
                    <Button
                        type="button"
                        variant="outline"
                        onClick={onSelfClose}
                        className="px-6 flex items-center gap-2"
                    >
                        <X className="w-4 h-4" />
                        取消
                    </Button>
                    <Button
                        onClick={handleSubmit}
                        className="px-6 bg-blue-600 hover:bg-blue-700 flex items-center gap-2"
                        disabled={isChanglianChannel}
                    >
                        <Save className="w-4 h-4" />
                        保存
                    </Button>
                </div>
            </DialogContent>
        </Dialog>
    );
};

export default EditWaybillModal;