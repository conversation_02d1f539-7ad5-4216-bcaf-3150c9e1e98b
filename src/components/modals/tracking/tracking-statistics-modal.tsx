import { useState, useEffect, useCallback } from 'react';
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { DateRangePicker } from "@/components/ui/date-range-picker";
import { getTrackingStatistics } from "@/api/tracking/tracking-api";
import { TrackingStatisticsResponse, TrackingStatus } from "@/api/tracking/tracking-model";
import { BarChart3, Package, CheckCircle, Clock, Truck, Calendar } from "lucide-react";
import { toast } from "sonner";
import { format, subDays } from "date-fns";
import { useNavigate } from "react-router-dom";

interface TrackingStatisticsModalProps {
    onClose?: () => void;
}

export function TrackingStatisticsModal({ onClose }: TrackingStatisticsModalProps) {
    const [isOpen, setIsOpen] = useState(true);
    const [statistics, setStatistics] = useState<TrackingStatisticsResponse | null>(null);
    const [loading, setLoading] = useState(false);
    const navigate = useNavigate();

    // 初始化默认时间范围（最近30天）
    const getInitialDateRange = () => {
        const endDate = new Date();
        const startDate = subDays(endDate, 29); // 最近30天
        return { from: startDate, to: endDate };
    };

    const [dateRange, setDateRange] = useState(getInitialDateRange());

    const handleClose = () => {
        setIsOpen(false);
        onClose?.();
    };

    const fetchStatistics = useCallback(async (startDate?: string, endDate?: string) => {
        setLoading(true);
        try {
            const data = await getTrackingStatistics(startDate, endDate);
            setStatistics(data);
        } catch (error: any) {
            toast.error('获取统计信息失败: ' + error.message);
        } finally {
            setLoading(false);
        }
    }, []);

    // 处理日期范围变化
    const handleDateRangeChange = useCallback(({ range }: { range: { from: Date; to: Date | undefined } }) => {
        // 只有当 to 不为 undefined 时才更新状态
        if (range.to) {
            setDateRange({ from: range.from, to: range.to });
            const startDate = format(range.from, 'yyyy-MM-dd');
            const endDate = format(range.to, 'yyyy-MM-dd');
            fetchStatistics(startDate, endDate);
        }
    }, [fetchStatistics]);

    // 处理状态卡片点击
    const handleStatusCardClick = useCallback((statusCode: string) => {
        if (!dateRange.from || !dateRange.to) return;

        const startDate = format(dateRange.from, 'yyyy-MM-dd');
        const endDate = format(dateRange.to, 'yyyy-MM-dd');

        // 构建跳转URL
        const url = `/dashboard/order/tracking?createdAtFrom=${startDate}&createdAtTo=${endDate}&status=${statusCode}`;

        // 关闭弹窗并跳转
        handleClose();
        navigate(url);
    }, [dateRange, navigate, handleClose]);

    // 初始加载数据
    useEffect(() => {
        if (dateRange.from && dateRange.to) {
            const startDate = format(dateRange.from, 'yyyy-MM-dd');
            const endDate = format(dateRange.to, 'yyyy-MM-dd');
            fetchStatistics(startDate, endDate);
        }
    }, []); // 只在组件挂载时执行一次

    const completionRate = statistics && statistics.totalTracking > 0
        ? ((statistics.completedTracking / statistics.totalTracking) * 100).toFixed(1)
        : '0';

    return (
        <Dialog open={isOpen} onOpenChange={setIsOpen}>
            <DialogContent className="max-w-6xl max-h-[95vh] overflow-y-auto">
                <DialogHeader>
                    <DialogTitle className="flex items-center gap-2">
                        <BarChart3 className="h-5 w-5" />
                        轨迹统计信息
                    </DialogTitle>
                </DialogHeader>

                <div className="space-y-6">
                    {/* 时间范围选择 */}
                    <Card>
                        <CardHeader>
                            <CardTitle className="flex items-center gap-2">
                                <Calendar className="h-4 w-4" />
                                时间范围
                            </CardTitle>
                        </CardHeader>
                        <CardContent>
                            <div className="flex items-center gap-4">
                                <DateRangePicker
                                    initialDateFrom={dateRange.from}
                                    initialDateTo={dateRange.to}
                                    onUpdate={handleDateRangeChange}
                                    showCompare={false}
                                    align="start"
                                    locale="zh-CN"
                                />
                                {loading && (
                                    <div className="text-sm text-muted-foreground">
                                        加载中...
                                    </div>
                                )}
                            </div>
                        </CardContent>
                    </Card>

                    <Separator />

                    {/* 总体统计 - 改为3列网格 */}
                    {statistics && (
                        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                            <Card>
                                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                                    <CardTitle className="text-sm font-medium">总轨迹数</CardTitle>
                                    <Package className="h-4 w-4 text-muted-foreground" />
                                </CardHeader>
                                <CardContent>
                                    <div className="text-2xl font-bold">{statistics.totalTracking}</div>
                                </CardContent>
                            </Card>

                            <Card>
                                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                                    <CardTitle className="text-sm font-medium">已完成</CardTitle>
                                    <CheckCircle className="h-4 w-4 text-green-500" />
                                </CardHeader>
                                <CardContent>
                                    <div className="text-2xl font-bold text-green-600">{statistics.completedTracking}</div>
                                    <p className="text-xs text-muted-foreground">
                                        完成率 {completionRate}%
                                    </p>
                                </CardContent>
                            </Card>

                            <Card>
                                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                                    <CardTitle className="text-sm font-medium">进行中</CardTitle>
                                    <Clock className="h-4 w-4 text-blue-500" />
                                </CardHeader>
                                <CardContent>
                                    <div className="text-2xl font-bold text-blue-600">{statistics.pendingTracking}</div>
                                </CardContent>
                            </Card>
                        </div>
                    )}

                    <Separator />

                    {/* 状态分布 */}
                    <div className="space-y-4">
                        <h3 className="text-lg font-semibold">状态分布</h3>
                        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
                            {Object.entries(TrackingStatus).map(([key, statusInfo]) => {
                                const count = statistics?.statusCounts[statusInfo.display] || 0;
                                const percentage = statistics && statistics.totalTracking > 0
                                    ? ((count / statistics.totalTracking) * 100).toFixed(1)
                                    : '0';

                                return (
                                    <Card
                                        key={key}
                                        className="p-4 cursor-pointer hover:shadow-md transition-shadow duration-200 hover:bg-gray-50"
                                        onClick={() => handleStatusCardClick(statusInfo.code)}
                                    >
                                        <div className="flex items-center justify-between">
                                            <div className="flex flex-col gap-1">
                                                <Badge
                                                    variant={count > 0 ? "default" : "outline"}
                                                    className="text-xs w-fit"
                                                >
                                                    {statusInfo.display}
                                                </Badge>
                                                <span className="text-xs text-muted-foreground">
                                                    {statusInfo.code}
                                                </span>
                                            </div>
                                            <div className="text-right">
                                                <div className="text-xl font-bold">{count}</div>
                                                <div className="text-xs text-muted-foreground">
                                                    {percentage}%
                                                </div>
                                            </div>
                                        </div>
                                    </Card>
                                );
                            })}
                        </div>
                    </div>

                    <Separator />

                    {/* 渠道分布 */}
                    {statistics && (
                        <div className="space-y-4">
                            <h3 className="text-lg font-semibold flex items-center gap-2">
                                <Truck className="h-5 w-5" />
                                渠道分布
                            </h3>
                            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
                                {Object.entries(statistics.channelCounts).map(([channel, count]) => {
                                    const percentage = statistics.totalTracking > 0
                                        ? ((count / statistics.totalTracking) * 100).toFixed(1)
                                        : '0';

                                    return (
                                        <Card key={channel} className="p-4">
                                            <div className="flex items-center justify-between">
                                                <div className="flex flex-col gap-1">
                                                    <Badge variant="secondary" className="text-xs w-fit">
                                                        {channel}
                                                    </Badge>
                                                </div>
                                                <div className="text-right">
                                                    <div className="text-xl font-bold">{count}</div>
                                                    <div className="text-xs text-muted-foreground">
                                                        {percentage}%
                                                    </div>
                                                </div>
                                            </div>
                                        </Card>
                                    );
                                })}
                            </div>
                        </div>
                    )}

                    {/* 进度条显示完成率 */}
                    {statistics && (
                        <div className="space-y-2">
                            <div className="flex justify-between text-sm">
                                <span>整体完成进度</span>
                                <span>{completionRate}%</span>
                            </div>
                            <div className="w-full bg-gray-200 rounded-full h-2">
                                <div
                                    className="bg-green-600 h-2 rounded-full transition-all duration-300"
                                    style={{ width: `${completionRate}%` }}
                                />
                            </div>
                        </div>
                    )}
                </div>

                <div className="flex justify-end pt-4">
                    <Button onClick={handleClose}>
                        关闭
                    </Button>
                </div>
            </DialogContent>
        </Dialog>
    );
}
