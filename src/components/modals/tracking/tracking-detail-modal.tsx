import { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>eader, <PERSON>alogTitle } from "@/components/ui/dialog";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Separator } from "@/components/ui/separator";
import { TrackingInfoResponse } from "@/api/tracking/tracking-model";
import {
    Package,
    Truck,
    MapPin,
    Clock,
    Phone,
    Globe,
    ExternalLink,
    CheckCircle,
    Circle,
    AlertCircle
} from "lucide-react";

interface TrackingDetailModalProps {
    trackingInfo: TrackingInfoResponse;
    onClose?: () => void;
}

export function TrackingDetailModal({ trackingInfo, onClose }: TrackingDetailModalProps) {
    const [isOpen, setIsOpen] = useState(true);

    const handleClose = () => {
        setIsOpen(false);
        onClose?.();
    };

    const getStatusIcon = (status: string, isCompleted: boolean) => {
        if (status === 'EXCEPTION') {
            return <AlertCircle className="h-4 w-4 text-red-500" />;
        }
        return isCompleted ?
            <CheckCircle className="h-4 w-4 text-green-500" /> :
            <Circle className="h-4 w-4 text-gray-400" />;
    };

    const getStatusColor = (status: string) => {
        switch (status) {
            case 'DELIVERED': return 'bg-green-100 text-green-800';
            case 'IN_TRANSIT': return 'bg-blue-100 text-blue-800';
            case 'OUT_FOR_DELIVERY': return 'bg-yellow-100 text-yellow-800';
            case 'EXCEPTION': return 'bg-red-100 text-red-800';
            case 'RETURNED': return 'bg-gray-100 text-gray-800';
            default: return 'bg-gray-100 text-gray-800';
        }
    };

    return (
        <Dialog open={isOpen} onOpenChange={setIsOpen}>
            <DialogContent className="max-w-4xl max-h-[90vh] p-0">
                <DialogHeader className="p-6 pb-0">
                    <DialogTitle className="flex items-center gap-2">
                        <Package className="h-5 w-5" />
                        轨迹详情 - {trackingInfo.waybillNo}
                    </DialogTitle>
                </DialogHeader>

                <ScrollArea className="max-h-[calc(90vh-120px)]">
                    <div className="p-6 space-y-6">
                        {/* 基本信息 */}
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div className="space-y-4">
                                <h3 className="text-lg font-semibold">基本信息</h3>
                                <div className="space-y-3">
                                    <div className="flex items-center gap-2">
                                        <Package className="h-4 w-4 text-blue-500" />
                                        <span className="text-sm text-muted-foreground">运单号:</span>
                                        <div className="flex items-center gap-2">
                                            <span className="text-sm text-muted-foreground">
                                                {trackingInfo.waybillNo}
                                            </span>
                                        </div>
                                    </div>
                                    {trackingInfo.trackingNumber && (
                                        <div className="flex items-center gap-2">
                                            <Truck className="h-4 w-4 text-green-500" />
                                            <span className="text-sm text-muted-foreground">跟踪号:</span>
                                            <div className="flex items-center gap-2">
                                                <div className="text-sm text-muted-foreground">
                                                    {trackingInfo.trackingNumber}
                                                </div>
                                            </div>
                                        </div>
                                    )}
                                    <div className="flex items-center gap-2">
                                        <span className="text-sm text-muted-foreground">渠道:</span>
                                        <Badge variant="outline">{trackingInfo.channel}</Badge>
                                    </div>
                                    <div className="flex items-center gap-2">
                                        <span className="text-sm text-muted-foreground">当前状态:</span>
                                        <Badge className={getStatusColor(trackingInfo.currentStatus)}>
                                            {trackingInfo.currentStatusDisplay}
                                        </Badge>
                                    </div>
                                    {trackingInfo.deliveryDays && (
                                        <div className="flex items-center gap-2">
                                            <Clock className="h-4 w-4 text-orange-500" />
                                            <span className="text-sm text-muted-foreground">运输天数:</span>
                                            <span>{trackingInfo.deliveryDays} 天</span>
                                        </div>
                                    )}
                                </div>
                            </div>

                            <div className="space-y-4">
                                <h3 className="text-lg font-semibold">地址信息</h3>
                                <div className="space-y-3">
                                    {trackingInfo.originCountry && (
                                        <div className="flex items-center gap-2">
                                            <MapPin className="h-4 w-4 text-blue-500" />
                                            <span className="text-sm text-muted-foreground">起始国家:</span>
                                            <span>{trackingInfo.originCountry}</span>
                                        </div>
                                    )}
                                    {trackingInfo.destinationCountry && (
                                        <div className="flex items-center gap-2">
                                            <MapPin className="h-4 w-4 text-red-500" />
                                            <span className="text-sm text-muted-foreground">目的国家:</span>
                                            <span>{trackingInfo.destinationCountry}</span>
                                        </div>
                                    )}
                                </div>

                                {/* 末端服务商信息 */}
                                {trackingInfo.lastMileProvider && (
                                    <div className="space-y-3">
                                        <h4 className="font-medium">末端服务商</h4>
                                        <div className="space-y-2">
                                            <div className="flex items-center gap-2">
                                                <span className="text-sm text-muted-foreground">名称:</span>
                                                <span>{trackingInfo.lastMileProvider.name}</span>
                                            </div>
                                            {trackingInfo.lastMileProvider.telephone && (
                                                <div className="flex items-center gap-2">
                                                    <Phone className="h-4 w-4 text-green-500" />
                                                    <span className="text-sm text-muted-foreground">电话:</span>
                                                    <span>{trackingInfo.lastMileProvider.telephone}</span>
                                                </div>
                                            )}
                                            {trackingInfo.lastMileProvider.website && (
                                                <div className="flex items-center gap-2">
                                                    <Globe className="h-4 w-4 text-blue-500" />
                                                    <span className="text-sm text-muted-foreground">网站:</span>
                                                    <a
                                                        href={trackingInfo.lastMileProvider.website}
                                                        target="_blank"
                                                        rel="noopener noreferrer"
                                                        className="text-blue-600 hover:underline flex items-center gap-1"
                                                    >
                                                        访问网站 <ExternalLink className="h-3 w-3" />
                                                    </a>
                                                </div>
                                            )}
                                        </div>
                                    </div>
                                )}
                            </div>
                        </div>

                        {/* 关联订单 */}
                        <div className="space-y-3">
                            <h3 className="text-lg font-semibold">关联订单</h3>
                            <div className="flex flex-wrap gap-2">
                                {trackingInfo.orderNos.map((orderNo, index) => (
                                    <div key={index} className="flex items-center gap-2">
                                        <span className="text-sm text-muted-foreground">
                                            {orderNo}
                                        </span>
                                    </div>
                                ))}
                            </div>
                        </div>

                        {/* POD链接 */}
                        {trackingInfo.podLinks.length > 0 && (
                            <div className="space-y-3">
                                <h3 className="text-lg font-semibold">POD链接</h3>
                                <div className="space-y-2">
                                    {trackingInfo.podLinks.map((link, index) => (
                                        <a
                                            key={index}
                                            href={link}
                                            target="_blank"
                                            rel="noopener noreferrer"
                                            className="text-blue-600 hover:underline flex items-center gap-1 text-sm"
                                        >
                                            查看POD {index + 1} <ExternalLink className="h-3 w-3" />
                                        </a>
                                    ))}
                                </div>
                            </div>
                        )}

                        <Separator />

                        {/* 轨迹事件时间线 */}
                        <div className="space-y-4">
                            <h3 className="text-lg font-semibold">轨迹事件</h3>
                            <div className="space-y-4">
                                {trackingInfo.trackingEvents.map((event, index) => {
                                    const isCompleted = index < trackingInfo.trackingEvents.length - 1 ||
                                        event.status === 'DELIVERED';
                                    return (
                                        <div key={index} className="flex gap-4">
                                            <div className="flex flex-col items-center">
                                                {getStatusIcon(event.status, isCompleted)}
                                                {index < trackingInfo.trackingEvents.length - 1 && (
                                                    <div className="w-px h-8 bg-gray-200 mt-2" />
                                                )}
                                            </div>
                                            <div className="flex-1 space-y-1 pb-4">
                                                <div className="flex items-center gap-2">
                                                    <Badge
                                                        variant={event.isLastMileEvent ? "default" : "outline"}
                                                        className="text-xs"
                                                    >
                                                        {event.statusDisplay}
                                                    </Badge>
                                                    <span className="text-sm text-muted-foreground">{event.eventTime}</span>
                                                </div>
                                                <p className="text-sm">{event.description}</p>
                                                {event.location && (
                                                    <div className="flex items-center gap-1 text-xs text-muted-foreground">
                                                        <MapPin className="h-3 w-3" />
                                                        {event.location}
                                                    </div>
                                                )}
                                            </div>
                                        </div>
                                    );
                                })}
                            </div>
                        </div>
                    </div>
                </ScrollArea>

                <div className="p-6 pt-0">
                    <Button onClick={handleClose} className="w-full">
                        关闭
                    </Button>
                </div>
            </DialogContent>
        </Dialog>
    );
}
