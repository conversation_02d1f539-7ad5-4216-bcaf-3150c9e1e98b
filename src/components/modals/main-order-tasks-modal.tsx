import { mainOrderApi } from "@/api/order/mainorder/main-order-api"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { useRequest } from "ahooks"
import { BarChart2, ChevronDown, ChevronRight, Info, RefreshCw } from 'lucide-react'
import { useState } from "react"
import { toast } from "sonner"
import MultiStateProgress from "../progress/multi-state-progress"
import { Badge } from "../ui/badge"
import { Button } from "../ui/button"
import { ScrollArea } from "../ui/scroll-area"
import * as clipboard from "clipboard-polyfill";


interface OrderTaskModalProps {
    id: string
    orderNumber: string
}

export default function EnhancedOrderTaskModal({ id, orderNumber }: OrderTaskModalProps) {

    const [open, setOpen] = useState(true)
    const [expandedGroups, setExpandedGroups] = useState<Set<string>>(new Set())

    const { data, run } = useRequest(mainOrderApi.downloadTaskOverview, {
        defaultParams: [id],
    })

    const queuedTasks = data?.statusMap?.CREATED ?? 0
    const processingTasks = data?.statusMap?.PROCESSING ?? 0
    const completedTasks = data?.statusMap?.COMPLETED ?? 0
    const failedTasks = data?.statusMap?.FAILED ?? 0

    const toggleGroup = (errorMessage: string) => {
        console.log(errorMessage)
        setExpandedGroups(prev => {
            const newSet = new Set(prev)
            if (newSet.has(errorMessage)) {
                newSet.delete(errorMessage)
            } else {
                newSet.add(errorMessage)
            }
            return newSet
        })
    }

    return (
        <Dialog open={open} onOpenChange={setOpen}>
            <DialogContent className="sm:max-w-[1200px] max-h-[90vh] flex flex-col">
                <DialogHeader>
                    <DialogTitle className="text-3xl font-bold text-blue-800 flex items-center justify-between">
                        <span>订单 #{orderNumber} 任务</span>
                        <Button
                            variant="ghost"
                            size="icon"
                            onClick={() => run(id)}
                            className="hover:bg-blue-100"
                        >
                            <RefreshCw className="h-5 w-5 text-blue-600" />
                        </Button>
                    </DialogTitle>
                </DialogHeader>
                <div className="mt-2 space-y-6 flex-1 overflow-hidden">
                    <div className="bg-white rounded-lg p-4">
                        <h3 className="text-xl font-semibold text-purple-700 mb-3 flex items-center">
                            <BarChart2 className="mr-2" size={24} />
                            任务概览 ({queuedTasks + processingTasks + completedTasks + failedTasks})
                        </h3>
                        <div className="grid grid-cols-2 gap-4 text-center sm:grid-cols-4">
                            <div className="bg-blue-100 p-3 rounded-md">
                                <p className="text-2xl font-bold text-blue-800">{queuedTasks}</p>
                                <p className="text-sm text-blue-600">待处理</p>
                            </div>
                            <div className="bg-yellow-100 p-3 rounded-md">
                                <p className="text-2xl font-bold text-yellow-800">{processingTasks}</p>
                                <p className="text-sm text-yellow-600">处理中</p>
                            </div>
                            <div className="bg-green-100 p-3 rounded-md">
                                <p className="text-2xl font-bold text-green-800">{completedTasks}</p>
                                <p className="text-sm text-green-600">成功</p>
                            </div>
                            <div className="bg-red-100 p-3 rounded-md">
                                <p className="text-2xl font-bold text-red-800">{failedTasks}</p>
                                <p className="text-sm text-red-600">失败</p>
                            </div>
                        </div>
                        <div className="mt-4">
                            <p className="text-sm text-gray-600 mb-1">整体进度</p>
                            <MultiStateProgress states={{
                                success: completedTasks,
                                failed: failedTasks,
                                processing: processingTasks,
                                queued: queuedTasks,
                            }} />
                        </div>
                    </div>
                    <div className="flex flex-row justify-between">
                        <div className="flex flex-col gap-2">
                            <h3 className="text-xl font-semibold text-purple-700 flex items-center">
                                <BarChart2 className="mr-2" size={24} />
                                失败的任务
                            </h3>
                            <div className="flex items-center gap-1">
                                <Info className="text-gray-500 italic" size={16} />
                                <div className="text-sm text-gray-500  italic">
                                    标题是错误信息，点击标题可以展开和折叠，展开后显示失败的订单号,多个订单号用逗号分隔,点击订单号可以复制
                                </div>
                            </div>
                        </div>
                        <Button
                            className="text-green-600 bg-green-50 hover:bg-green-100 group relative overflow-hidden transition-all duration-300 hover:pr-7"
                            size="sm"
                            onClick={() => {
                                toast.promise(mainOrderApi.retryMainOrderFailedDownloadTask(id), {
                                    success: '重试成功',
                                    error: (error) => {
                                        return '重试失败: ' + error.message;
                                    },
                                })
                                setOpen(false)
                            }}
                        >
                            <span className="flex items-center">
                                重试
                                <RefreshCw className="w-2 h-2 absolute right-2 transform translate-x-full opacity-0 group-hover:translate-x-0 group-hover:opacity-100 transition-all duration-300 animate-spin" />
                            </span>
                        </Button>
                    </div>


                    <ScrollArea className="h-[400px] w-full pr-4">
                        {failedTasks > 0 ? (
                            data?.failedOverView?.map((item, index) => (
                                <div key={index} className="mb-4">
                                    <button
                                        onClick={() => toggleGroup(item.errorMessage)}
                                        className="w-full text-left p-2 bg-red-50 hover:bg-red-100 rounded-md flex items-center justify-between"
                                    >
                                        <span className="font-medium text-red-800 truncate flex-grow mr-2">{item.errorMessage}</span>
                                        <div className="flex items-center">
                                            <Badge variant="outline" className="mr-2">{item.orderNos.split(',').length}</Badge>
                                            {expandedGroups.has(item.errorMessage) ? <ChevronDown size={20} /> : <ChevronRight size={20} />}
                                        </div>
                                    </button>
                                    {expandedGroups.has(item.errorMessage) && (
                                        <div className="mt-2 ml-4">
                                            <div
                                                className="p-2 bg-white border border-red-200 rounded-md text-xs relative group cursor-pointer hover:bg-gray-50"
                                                onClick={() => {
                                                    clipboard.writeText(item.orderNos).then(() => {
                                                        toast.success("订单号已复制到剪贴板")
                                                    })
                                                }}
                                            >
                                                {item.orderNos}
                                                <span className="absolute right-2 top-1/2 -translate-y-1/2 opacity-0 group-hover:opacity-100 transition-opacity text-gray-500 z-10 text-xs">
                                                    点击复制
                                                </span>
                                            </div>
                                        </div>
                                    )}
                                </div>
                            ))
                        ) : (
                            <div className="text-center text-gray-500 py-4">
                                暂无失败任务
                            </div>
                        )}
                    </ScrollArea>
                </div>
            </DialogContent>
        </Dialog>
    )
}