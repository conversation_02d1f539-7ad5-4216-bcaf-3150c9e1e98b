import {
    Toolt<PERSON>,
    Toolt<PERSON>Content,
    <PERSON><PERSON><PERSON><PERSON>rovider,
    TooltipTrigger,
} from "@/components/ui/tooltip";
import React from 'react';

interface TruncatedUrlProps {
    url: string | undefined | null;
    maxLength?: number;
}

const truncateUrl = (url: string, maxLength: number = 30) => {
    if (url.length <= maxLength) return url;
    const lastPart = url.slice(-15);
    return url.substring(0, maxLength - 18) + '...' + lastPart;
};

export const TruncatedUrl: React.FC<TruncatedUrlProps> = ({ url, maxLength = 30 }) => {
    if (!url) return <div className={'min-w-[100px]'}></div>;
    return (
        <TooltipProvider>
            <Tooltip>
                <TooltipTrigger asChild>
                    <a
                        href={url}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-blue-600 hover:underline truncate block max-w-[250px]"
                    >
                        {truncateUrl(url, maxLength)}
                    </a>
                </TooltipTrigger>
                <TooltipContent>
                    <p>{url}</p>
                </TooltipContent>
            </Tooltip>
        </TooltipProvider>
    );
};