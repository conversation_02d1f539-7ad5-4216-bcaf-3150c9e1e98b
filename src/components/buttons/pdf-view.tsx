import React, { useState, useEffect, useCallback } from 'react';
import { Worker, Viewer, SpecialZoomLevel } from '@react-pdf-viewer/core';
import { defaultLayoutPlugin } from '@react-pdf-viewer/default-layout';
import type { RenderError } from '@react-pdf-viewer/core';
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
    <PERSON>alog,
    DialogContent,
    DialogHeader,
    DialogTitle,
    DialogTrigger,
} from "@/components/ui/dialog";
import { Loader2 } from "lucide-react";

import '@react-pdf-viewer/core/lib/styles/index.css';
import '@react-pdf-viewer/default-layout/lib/styles/index.css';
import { Eye } from "lucide-react";
import { exportPdf } from '@/api/common-api';

interface PDFViewerButtonProps {
    pdfUrl: string;
}

const PDFViewerButton: React.FC<PDFViewerButtonProps> = ({ pdfUrl }) => {
    const [open, setOpen] = useState(false);
    const [pdfBlobUrl, setPdfBlobUrl] = useState<string | null>(null);
    const [isLoading] = useState(false);
    const [error] = useState<string | null>(null);
    const defaultLayoutPluginInstance = defaultLayoutPlugin();

    const handleOpenPdf = useCallback(async () => {
        if (!pdfBlobUrl) {
            try {
                const blob = await exportPdf(pdfUrl);
                const url = URL.createObjectURL(blob.data);
                setPdfBlobUrl(url);
            } catch (error) {
                console.error('Error fetching PDF:', error);
                // 这里可以添加错误处理，比如显示一个错误消息
            }
        }
        setOpen(true);
    }, [pdfUrl, pdfBlobUrl]);

    useEffect(() => {
        return () => {
            if (pdfBlobUrl) {
                URL.revokeObjectURL(pdfBlobUrl);
            }
        };
    }, [pdfBlobUrl]);

    const renderError: RenderError = useCallback(
        (error) => {
            console.error('PDF viewer error:', error);
            return (
                <div className="flex items-center justify-center h-full text-red-500">
                    PDF加载失败: {error.message || '未知错误'}
                </div>
            );
        },
        []
    );

    return (
        <Dialog open={open} onOpenChange={setOpen}>
            <DialogTrigger asChild>
                <Button
                    variant="outline"
                    size="sm"
                    onClick={handleOpenPdf}
                    disabled={isLoading}
                    className="hover:bg-blue-50 text-blue-600 border-blue-200 hover:border-blue-300 transition-colors"
                >
                    {isLoading ? (
                        <Loader2 className="mr-1.5 h-3.5 w-3.5 animate-spin" />
                    ) : (
                        <Eye className="mr-1.5 h-3.5 w-3.5" />
                    )}
                    <span className="text-xs">{isLoading ? '加载中...' : '查看运单'}</span>
                </Button>
            </DialogTrigger>
            <DialogContent className="max-w-[90vw] max-h-[90vh] w-full h-full">
                <DialogHeader>
                    <DialogTitle>PDF 预览</DialogTitle>
                </DialogHeader>
                <div className="h-[calc(90vh-100px)]">
                    {isLoading && (
                        <div className="flex items-center justify-center h-full">
                            <Loader2 className="h-8 w-8 animate-spin" />
                            <span className="ml-2">正在加载PDF...</span>
                        </div>
                    )}
                    {error && (
                        <div className="flex items-center justify-center h-full text-red-500">
                            {error}
                        </div>
                    )}
                    {pdfBlobUrl && !isLoading && !error && (
                        <Worker workerUrl="https://unpkg.com/pdfjs-dist@3.11.174/build/pdf.worker.min.js">
                            <Viewer
                                fileUrl={pdfBlobUrl}
                                plugins={[defaultLayoutPluginInstance]}
                                defaultScale={SpecialZoomLevel.PageFit}
                                renderError={renderError}
                            />
                        </Worker>
                    )}
                </div>
            </DialogContent>
        </Dialog>
    );
};

export default PDFViewerButton;