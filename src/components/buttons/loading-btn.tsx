import { useRequest } from "ahooks";
import { But<PERSON> } from "../ui/button";
import { Loader2 } from "lucide-react";

export default function RequestButton({
    params,
    request,
    children,
    onError,
    onSuccess,
    size = "sm",
    variant = "default",
}: {
    params: [any],
    children: React.ReactNode,
    request: (params: any) => Promise<any>,
    size: "sm" | "icon" | "lg" | "default",
    variant: "default" | "outline" | "ghost" | "link",
    onSuccess?: (res: any) => void,
    onError?: (err: any) => void,
}) {
    const { loading, runAsync } = useRequest(request, {
        manual: true,
        debounceWait: 400,
        onSuccess,
        onError
    });

    return (
        <Button onClick={() => runAsync(params)} size={size} variant={variant} disabled={loading}>
            {loading ? (
                <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    加载中...
                </>
            ) : (
                children
            )}
        </Button>
    );
}