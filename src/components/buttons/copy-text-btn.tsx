import { cn } from '@/lib/utils';
import { Check, Copy } from 'lucide-react';
import React, { useCallback, useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';

export interface CopyIconProps {
    value: string;
    delay?: number;
    className?: string;
    url?: string;
}

const CopyIcon: React.FC<CopyIconProps> = ({ value, delay = 2000, className }) => {
    const [copied, setCopied] = useState(false);

    useEffect(() => {
        if (copied) {
            const timer = setTimeout(() => setCopied(false), delay);
            return () => clearTimeout(timer);
        }
    }, [copied, delay]);


    const handleCopy = useCallback(async () => {
        if (typeof window !== 'undefined') {
            console.log('window', value)
            try {
                if (navigator.clipboard) {
                    await navigator.clipboard.writeText(value);
                } else {
                    // 回退方法
                    const textArea = document.createElement('textarea');
                    textArea.value = value;
                    document.body.appendChild(textArea);
                    textArea.select();
                    document.execCommand('copy');
                    document.body.removeChild(textArea);
                }
                setCopied(true);
            } catch (err) {
                console.error('Failed to copy text: ', err);
            }
        }
        console.log('copy', value)
    }, [value]);

    return (
        <>
            {
                copied ? (
                    <Check width={12} height={12} className="h-4 w-4 text-green-500" onClick={() => setCopied(false)} />
                ) : (
                    <Copy width={12} height={12} className={`${className} cursor-pointer`} onClick={handleCopy} />
                )
            }
        </>
        // </Button>
    );
};


export const CopyText: React.FC<CopyIconProps> = ({ value, delay = 2000, className, url }) => {

    const navigate = useNavigate()

    return (
        <div className={`flex flex-row space-x-1 items-center ${className}`}>
            <span className={`text-xs ${url ? 'cursor-pointer hover:underline' : ''}`} onClick={() => url && navigate(url)}>{value}</span>
            <CopyIcon value={value} delay={delay} className={cn(className)} />
        </div>
    );
}
