import {ReactNode} from "react";
import {pushModal} from "../modals";
import {Button, ButtonProps} from "../ui/button";


export interface DoubleCheckBtnProps {
    buttonText: string | ReactNode
    onConfirm: () => void
    onCancel: () => void
    title: string
    size?: ButtonProps['size']
    description: string
    children?: ReactNode
    variant?: ButtonProps['variant']
    className?: string
    disabled?: boolean
}


export function DoubleCheckBtn({title, size, description, children, onConfirm, onCancel, buttonText, variant, disabled, className}: DoubleCheckBtnProps) {
    return <Button variant={variant} size={size} className={className} disabled={disabled} onClick={() => pushModal('DoubleCheckModal', {title, description, children, onConfirm, onCancel})}>{buttonText}</Button>
}
