"use client"

import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import {
    Tooltip,
    TooltipContent,
    TooltipProvider,
    TooltipTrigger,
} from "@/components/ui/tooltip"
import { cn } from "@/lib/utils"
import { Copy, Edit, LucideIcon, MoreHorizontal, Plus, Trash2 } from 'lucide-react'
import * as React from "react"

// Root component
const Dock = React.forwardRef<
    HTMLDivElement,
    React.HTMLAttributes<HTMLDivElement>
>(({ className, children, ...props }, ref) => (
    <div
        ref={ref}
        className="pointer-events-none fixed inset-x-0 bottom-0 z-50 flex items-end justify-center"
        {...props}
    >
        <div className={cn(
            "pointer-events-auto mb-12 flex items-center gap-4 rounded-lg bg-black/70 p-2 shadow-sm backdrop-blur-sm  px-4",
            className
        )}>
            {children}
        </div>
    </div>
))
Dock.displayName = "Dock"

// Item component
interface DockItemProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
    icon: LucideIcon
    label: string
}

const DockItem = React.forwardRef<HTMLButtonElement, DockItemProps>(
    ({ icon: Icon, label, className, ...props }, ref) => (
        <TooltipProvider>
            <Tooltip>
                <TooltipTrigger asChild>
                    <button
                        ref={ref}
                        className={cn(
                            "group relative flex h-8 w-8 items-center justify-center rounded-lg bg-background/90 transition-all hover:bg-background",
                            className
                        )}
                        {...props}
                    >
                        <Icon className="h-4 w-4 text-foreground/70 transition-colors group-hover:text-foreground" />
                    </button>
                </TooltipTrigger>
                <TooltipContent side="top" className="mb-2">
                    {label}
                </TooltipContent>
            </Tooltip>
        </TooltipProvider>
    )
)
DockItem.displayName = "DockItem"

// Menu component
interface DockMenuProps extends React.ComponentProps<typeof DropdownMenu> {
    icon?: LucideIcon
    children: React.ReactNode
}

const DockMenu = React.forwardRef<HTMLButtonElement, DockMenuProps>(
    ({ icon: Icon = MoreHorizontal, children, ...props }, ref) => (
        <DropdownMenu {...props}>
            <DropdownMenuTrigger asChild>
                <button
                    ref={ref}
                    className="flex h-10 w-10 items-center justify-center rounded-lg bg-background/50 transition-all hover:bg-background/90"
                >
                    <Icon className="h-5 w-5 text-foreground/70" />
                </button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
                {children}
            </DropdownMenuContent>
        </DropdownMenu>
    )
)
DockMenu.displayName = "DockMenu"

// Example usage
export function DockExample() {
    const handleAdd = () => console.log('Add new item')
    const handleDelete = () => console.log('Delete selected item')
    const handleCopy = () => console.log('Copy selected item')
    const handleEdit = () => console.log('Edit selected item')

    return (
        <Dock>
            <DockItem icon={Plus} label="Add New" onClick={handleAdd} />
            <DockItem icon={Trash2} label="Delete" onClick={handleDelete} />
            <DockItem icon={Copy} label="Duplicate" onClick={handleCopy} />
            <DockItem icon={Edit} label="Edit" onClick={handleEdit} />
            <DockMenu>
                <DropdownMenuItem>Select All</DropdownMenuItem>
                <DropdownMenuItem>Clear Selection</DropdownMenuItem>
                <DropdownMenuItem>Export</DropdownMenuItem>
            </DockMenu>
        </Dock>
    )
}

export { Dock, DockItem, DockMenu }

