import React, {ChangeEvent, DragEvent, useEffect, useRef, useState} from 'react';
import {Maximize2, Upload, X} from 'lucide-react';
import {Button} from "@/components/ui/button";
import {Card, CardContent} from "@/components/ui/card";
import {Progress} from "@/components/ui/progress";
import {cn} from "@/lib/utils";
import {Dialog, DialogContent} from "@/components/ui/dialog";

interface ImageUploadProps {
    maxSizeMB?: number;
    allowedTypes?: string[];
    initialImage?: string;
    onImageUpload?: (file: File) => void;
    onImageRemove?: () => void;
    width?: string;
    height?: string;
    imageContainerHeight?: string;
    className?: string;
    cardClassName?: string;
}

const ImageUpload: React.FC<ImageUploadProps> = ({
                                                     maxSizeMB = 5,
                                                     allowedTypes = ['image/jpeg', 'image/png', 'image/gif'],
                                                     initialImage,
                                                     onImageUpload,
                                                     onImageRemove,
                                                     width = '250px',
                                                     height = '150px',
                                                     imageContainerHeight = '130px',
                                                     className = '',
                                                     cardClassName = '',
                                                 }) => {
    type UploadState = 'idle' | 'uploading' | 'completed';

    const [uploadState, setUploadState] = useState<UploadState>(initialImage ? 'completed' : 'idle');
    const [progress, setProgress] = useState<number>(0);
    const [image, setImage] = useState<string | null>(initialImage || null);
    const fileInputRef = useRef<HTMLInputElement>(null);
    const [isDialogOpen, setIsDialogOpen] = useState(false);

    useEffect(() => {
        if (initialImage) {
            setImage(initialImage);
            setUploadState('completed');
        }
    }, [initialImage]);

    const validateFile = (file: File): boolean => {
        if (!allowedTypes.includes(file.type)) {
            alert("Invalid file type. Please upload an image.");
            return false;
        }
        if (file.size > maxSizeMB * 1024 * 1024) {
            alert(`File size should not exceed ${maxSizeMB}MB.`);
            return false;
        }
        return true;
    };

    const simulateUpload = (file: File): void => {
        setUploadState('uploading');
        setProgress(0);
        const interval = setInterval(() => {
            setProgress((prevProgress) => {
                if (prevProgress >= 100) {
                    clearInterval(interval);
                    setUploadState('completed');
                    const reader = new FileReader();
                    reader.onloadend = () => {
                        setImage(reader.result as string);
                        onImageUpload && onImageUpload(file);
                    };
                    reader.readAsDataURL(file);
                    return 100;
                }
                return prevProgress + 10;
            });
        }, 100);
    };

    const handleFileChange = (event: ChangeEvent<HTMLInputElement>): void => {
        const file = event.target.files?.[0];
        if (file && validateFile(file)) {
            simulateUpload(file);
        }
    };

    const handleUpload = (): void => {
        fileInputRef.current?.click();
    };

    const handleDrop = (event: DragEvent<HTMLDivElement>): void => {
        event.preventDefault();
        const file = event.dataTransfer.files[0];
        if (file && validateFile(file)) {
            simulateUpload(file);
        }
    };

    const handleDragOver = (event: DragEvent<HTMLDivElement>): void => {
        event.preventDefault();
    };

    const handleRemove = (): void => {
        setUploadState('idle');
        setProgress(0);
        setImage(null);
        onImageRemove && onImageRemove();
    };


    const handleImageClick = (): void => {
        setIsDialogOpen(true);
    };

    return (
        <div>
            <Card className={cn("", cardClassName)} style={{width, height}}>
                <CardContent className={cn("p-2", className)}>
                    {uploadState === 'idle' && (
                        <div
                            className="border-2 border-dashed border-gray-300 rounded-lg p-4 text-center cursor-pointer h-full flex flex-col items-center justify-center"
                            onClick={handleUpload}
                            onDrop={handleDrop}
                            onDragOver={handleDragOver}
                        >
                            <Upload className="h-8 w-8 text-gray-400"/>
                            <p className="mt-2 text-xs text-gray-900">Drag an image</p>
                            <p className="mt-1 text-xs text-gray-500">Select a image or drag here to upload directly</p>
                            <input
                                type="file"
                                ref={fileInputRef}
                                onChange={handleFileChange}
                                accept={allowedTypes.join(',')}
                                className="hidden"
                            />
                        </div>
                    )}
                    {uploadState === 'uploading' && (
                        <div className="text-center">
                            <Progress value={progress} className="w-full mt-2"/>
                            <p className="mt-2 text-xs font-semibold text-gray-900">Uploading Picture</p>
                            <p className="mt-1 text-xs text-gray-500">Do not refresh or perform any other action while
                                the
                                picture is being uploaded</p>
                        </div>
                    )}
                    {uploadState === 'completed' && image && (
                        <div className="relative">
                            <div
                                className="border border-gray-200 rounded-lg p-4 mb-4 flex items-center justify-center overflow-hidden cursor-pointer group"
                                style={{height: imageContainerHeight}}
                                onClick={handleImageClick}
                            >
                                <img src={image} alt="Uploaded" className="max-w-full max-h-full object-contain"/>
                                <div
                                    className="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity">
                                    <Maximize2 className="text-white h-8 w-8"/>
                                </div>
                            </div>
                            <Button
                                variant="ghost"
                                size="icon"
                                className="absolute top-2 right-2 bg-white hover:bg-gray-100"
                                onClick={handleRemove}
                            >
                                <X className="h-4 w-4"/>
                            </Button>
                        </div>
                    )}
                </CardContent>
            </Card>
            <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
                <DialogContent className="max-w-4xl max-h-[90vh] flex items-center justify-center p-0">
                    <img src={image || undefined} alt="Enlarged" className="max-w-full max-h-full object-contain" />
                </DialogContent>
            </Dialog>
        </div>
    );
};

export default ImageUpload;