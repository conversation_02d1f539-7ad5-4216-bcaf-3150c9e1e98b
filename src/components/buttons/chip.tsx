import { cn } from '@/lib/utils';
import React from 'react';

type ChipVariant = 'default' | 'blue' | 'green' | 'yellow' | 'red' | 'purple' | 'pink' | 'indigo' | 'teal';

interface ChipProps {
    label: string;
    variant?: ChipVariant;
    outlined?: boolean;
    showIcon?: boolean;
    icon?: React.ReactNode;
}

export const Chip: React.FC<ChipProps> = ({ label, variant = 'default', outlined = false, showIcon = true, icon }) => {
    const getVariantStyles = (): string => {
        const baseStyles = 'text-xs font-medium px-2 py-1 rounded-sm transition-all inline-flex items-center whitespace-nowrap';
        const variants: Record<ChipVariant, string> = {
            default: 'bg-gray-100 text-gray-800 hover:bg-gray-200',
            blue: 'bg-blue-100 text-blue-800 hover:bg-blue-200',
            green: 'bg-green-100 text-green-800 hover:bg-green-200',
            yellow: 'bg-yellow-100 text-yellow-800 hover:bg-yellow-200',
            red: 'bg-red-100 text-red-800 hover:bg-red-200',
            purple: 'bg-purple-100 text-purple-800 hover:bg-purple-200',
            pink: 'bg-pink-100 text-pink-800 hover:bg-pink-200',
            indigo: 'bg-indigo-100 text-indigo-800 hover:bg-indigo-200',
            teal: 'bg-teal-100 text-teal-800 hover:bg-teal-200',
        };

        const outlinedStyles: Record<ChipVariant, string> = {
            default: 'border border-gray-300 bg-transparent text-gray-800 hover:bg-gray-100',
            blue: 'border border-blue-300 bg-transparent text-blue-800 hover:bg-blue-100',
            green: 'border border-green-300 bg-transparent text-green-800 hover:bg-green-100',
            yellow: 'border border-yellow-300 bg-transparent text-yellow-800 hover:bg-yellow-100',
            red: 'border border-red-300 bg-transparent text-red-800 hover:bg-red-100',
            purple: 'border border-purple-300 bg-transparent text-purple-800 hover:bg-purple-100',
            pink: 'border border-pink-300 bg-transparent text-pink-800 hover:bg-pink-100',
            indigo: 'border border-indigo-300 bg-transparent text-indigo-800 hover:bg-indigo-100',
            teal: 'border border-teal-300 bg-transparent text-teal-800 hover:bg-teal-100',
        };

        return cn(baseStyles, outlined ? outlinedStyles[variant] : variants[variant]);
    };

    const getDotColor = (): string => {
        const dotColors: Record<ChipVariant, string> = {
            default: 'bg-gray-500',
            blue: 'bg-blue-500',
            green: 'bg-green-500',
            yellow: 'bg-yellow-500',
            red: 'bg-red-500',
            purple: 'bg-purple-500',
            pink: 'bg-pink-500',
            indigo: 'bg-indigo-500',
            teal: 'bg-teal-500',
        };

        return dotColors[variant];
    };

    return (
        <span className={getVariantStyles()}>
            {showIcon && <span className={`w-1.5 h-1.5 rounded-full mr-1.5 ${getDotColor()}`}></span>}
            {icon && <span className="mr-1.5">{icon}</span>}
            {label}
        </span>
    );
};