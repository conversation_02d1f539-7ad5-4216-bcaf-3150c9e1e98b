import React from 'react';
import {FormControl, FormField, FormItem, FormLabel, FormMessage} from "@/components/ui/form";
import ImageUpload from "@/components/buttons/image-upload";

interface MultiImageUploadFieldProps {
    form: any;  // You might want to use a more specific type from react-hook-form
    name: string;
    label: string;
    maxImages: number;
    uploadImage: (file: File) => Promise<string>;  // Function to handle image upload
}

const MultiImageUploadField: React.FC<MultiImageUploadFieldProps> = ({
                                                                         form,
                                                                         name,
                                                                         label,
                                                                         maxImages,
                                                                         uploadImage
                                                                     }) => {
    return (
        <FormField
            control={form.control}
            name={name}
            render={({field}) => (
                <FormItem>
                    <FormLabel>{label}</FormLabel>
                    <FormControl>
                        <div className="flex flex-row flex-wrap gap-4">
                            {field.value.map((imageUrl: string, index: number) => (
                                <div key={`image-${imageUrl}-${index}`} className="relative">
                                    <ImageUpload
                                        key={`upload-${imageUrl}-${index}`}
                                        onImageUpload={async (file) => {
                                            const uploadedUrl = await uploadImage(file);
                                            const newImages = [...field.value];
                                            newImages[index] = uploadedUrl;
                                            field.onChange(newImages);
                                        }}
                                        onImageRemove={() => {
                                            const newImages = field.value.filter((_: any, i: number) => i !== index);
                                            field.onChange(newImages);
                                        }}
                                        initialImage={imageUrl}
                                    />
                                </div>
                            ))}
                            {field.value.length < maxImages && (
                                <ImageUpload
                                    key={`new-upload-${field.value.length}`}
                                    onImageUpload={async (file) => {
                                        const uploadedUrl = await uploadImage(file);
                                        field.onChange([...field.value, uploadedUrl]);
                                    }}
                                    onImageRemove={() => {}}
                                />
                            )}
                        </div>
                    </FormControl>
                    <FormMessage/>
                </FormItem>
            )}
        />
    );
};

export default MultiImageUploadField;