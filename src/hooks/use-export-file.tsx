import { useState } from 'react';

type DownloadFunction = (...params: any[]) => Promise<any>;

export const useExcelDownload = (downloadFn: DownloadFunction) => {
  const [isLoading, setIsLoading] = useState(false);

  const downloadExcel = async (...params: any[]) => {
    setIsLoading(true);
    try {
      const response = await downloadFn(...params);
      const data = response.data;
      if (!(data instanceof Blob)) {
        throw new Error('响应不是Blob类型');
      }

      // 从 Content-Disposition 头中获取文件名
      const contentDisposition = response.headers['content-disposition'];
      let fileName = 'download.xlsx';
      if (contentDisposition) {
        const fileNameMatch = contentDisposition.match(/filename\*?=['"]?([^;\n]+)['"]?/i);
        if (fileNameMatch && fileNameMatch[1]) {
          fileName = decodeURIComponent(fileNameMatch[1].replace(/\+/g, ' '));
        }
      }
      console.log(fileName);
      const blobUrl = window.URL.createObjectURL(data);
      const link = document.createElement('a');
      link.href = blobUrl;
      link.download = fileName;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(blobUrl);

    } catch (error) {
    } finally {
      setIsLoading(false);
    }
  };

  return { downloadExcel, isLoading };
};