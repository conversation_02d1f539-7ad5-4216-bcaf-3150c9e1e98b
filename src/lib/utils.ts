import { clsx, type ClassValue } from "clsx";
import { twMerge } from "tailwind-merge";

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}


export function getDateFromString(dateString: string) {
  // 首先尝试使用 Number 转换
  const timestamp = Number(dateString);
  if (!isNaN(timestamp)) {
    return new Date(timestamp);
  }

  // 如果 Number 转换失败,则尝试使用 BigInt
  const bigIntTimestamp = BigInt(dateString);
  if (bigIntTimestamp) {
    return new Date(Number(bigIntTimestamp));
  }

  // 如果上述方法都失败,则尝试使用 new Date() 构造函数
  const date = new Date(dateString);
  if (!isNaN(date.getTime())) {
    return date;
  }

  // 如果所有方法都失败,则返回 null
  return null;
}