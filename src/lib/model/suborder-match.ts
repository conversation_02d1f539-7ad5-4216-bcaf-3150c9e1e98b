import { match } from "ts-pattern";

export const waybillStatusInfoMatch = (status: string) => match(status)
    .with("CREATED", () => ({
        label: "已创建",
        variant: "yellow" as const,
    }))
    .with("PENDING", () => ({
        label: "待处理",
        variant: "yellow" as const,
    }))
    .with("PROCESSING", () => ({
        label: "处理中",
        variant: "blue" as const,
    }))
    .with("COMPLETED", () => ({
        label: "已完成",
        variant: "green" as const,
    }))
    .with("FAILED", () => ({
        label: "失败",
        variant: "red" as const,
    }))
    .otherwise(() => ({
        label: "等待创建",
        variant: "default" as const,
    }));

export const supplierOrderStatusInfoMatch = (status: string) => match(status)
    .with("CREATED", () => ({
        label: "已创建",
        variant: "yellow" as const,
    }))
    .with("PROCESSING", () => ({
        label: "处理中",
        variant: "blue" as const,
    }))
    .with("COMPLETED", () => ({
        label: "已完成",
        variant: "green" as const,
    }))
    .with("FAILED", () => ({
        label: "失败",
        variant: "red" as const,
    }))
    .otherwise(() => ({
        label: "等待创建",
        variant: "purple" as const,
    }));


export const subOrderStatusInfoMatch = (status: string) => match(status)
    .with("CREATED", () => ({
        label: "已创建",
        variant: "yellow" as const,
    }))
    .with("COMPLETED", () => ({
        label: "已完成",
        variant: "green" as const,
    }))
    .with("CANCELLED", () => ({
        label: "已取消",
        variant: "red" as const,
    }))
    .with("FAILED", () => ({
        label: "失败",
        variant: "red" as const,
    }))
    .with("SPLIT", () => ({
        label: "已拆分",
        variant: "blue" as const,
    }))
    .with("SUPPLIER_MATCHED", () => ({
        label: "已匹配供应商",
        variant: "green" as const,
    }))
    // 轨迹相关状态
    .with("TRACKING_NOT_FOUND", () => ({
        label: "未找到轨迹",
        variant: "gray" as const,
    }))
    .with("TRACKING_PRE_ADVICE_RECEIVED", () => ({
        label: "电子预报",
        variant: "blue" as const,
    }))
    .with("TRACKING_PICKED_UP", () => ({
        label: "已揽收",
        variant: "blue" as const,
    }))
    .with("TRACKING_IN_TRANSIT", () => ({
        label: "运输途中",
        variant: "blue" as const,
    }))
    .with("TRACKING_ARRIVED_DESTINATION_COUNTRY", () => ({
        label: "到达目的地国家",
        variant: "blue" as const,
    }))
    .with("TRACKING_IN_CUSTOMS", () => ({
        label: "清关中",
        variant: "blue" as const,
    }))
    .with("TRACKING_CUSTOMS_CLEARED", () => ({
        label: "清关完成",
        variant: "blue" as const,
    }))
    .with("TRACKING_ARRIVED_FOR_PICKUP", () => ({
        label: "到达待取",
        variant: "blue" as const,
    }))
    .with("TRACKING_OUT_FOR_DELIVERY", () => ({
        label: "派送中",
        variant: "blue" as const,
    }))
    .with("TRACKING_DELIVERY_FAILED", () => ({
        label: "投递失败",
        variant: "red" as const,
    }))
    .with("TRACKING_DELIVERED", () => ({
        label: "已签收",
        variant: "green" as const,
    }))
    .with("TRACKING_EXCEPTION", () => ({
        label: "异常",
        variant: "red" as const,
    }))
    .with("TRACKING_RETURNED", () => ({
        label: "已退回",
        variant: "red" as const,
    }))
    .with("TRACKING_CANCELLED", () => ({
        label: "已取消",
        variant: "red" as const,
    }))
    .with("TRACKING_UNKNOWN", () => ({
        label: "未知状态",
        variant: "gray" as const,
    }))
    .otherwise(() => ({
        label: status,
        variant: "default" as const,
    }));