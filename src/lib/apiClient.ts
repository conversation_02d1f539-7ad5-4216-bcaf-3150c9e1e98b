import axios, {AxiosError, AxiosRequestConfig, AxiosResponse} from 'axios';

// 创建 axios 实例
const axiosInstance = axios.create({
    timeout: 500000,
    baseURL: import.meta.env.VITE_API_URL,
    headers: { 'Content-Type': 'application/json;charset=utf-8' },
});

interface Result<T = any> {
    code: string;
    msg: string;
    data: T | null;
}

// 请求拦截
axiosInstance.interceptors.request.use(
    (config) => {
        if (config.url === '/api/v1/users/login' || config.url === '/api/v1/users/logout') {
            return config;
        }
        let token = localStorage.getItem('token');
        if (!token) {
            //to /login
            window.location.href = '/login'
            return config;
        }
        config.headers.Authorization = `Bearer ${removeQuotesBoth(token)} `;
        return config;
    },
    (error) => {
        // 请求错误时做些什么
        return Promise.reject(error);
    },
);

// 响应拦截
axiosInstance.interceptors.response.use((res) => {
    if (
      (res.headers as any).hasContentType("application/json")
    ) {
      if (res.data instanceof Blob) {
        return res.data.text().then(text => {
          res.data = JSON.parse(text);
          res.config.responseType = 'json';
        })
      }
      if (res.data instanceof ArrayBuffer) {
        const decoder = new TextDecoder('utf-8');
        const text = decoder.decode(res.data)
        res.data = JSON.parse(text);
        res.config.responseType = 'json';
      }
    }
    if (res.data instanceof Blob) {
        return res;
    }
    // 检查响应的内容类型
    const contentType = res.headers['content-type'];
    if (contentType && contentType.includes('application/octet-stream')) {
        // 如果是二进制流数据，直接返回整个响应对象
        return res;
    }
    // 对于其他类型的响应，返回数据部分
    return res.data;
},
    (error: AxiosError<Result>) => {
        if (error.response?.status === 401) {
            console.log('登录过期，请重新登录')
            localStorage.removeItem('token')
            window.location.href = '/login'
            return Promise.reject(new Error('登录过期，请重新登录'));
        }
        const { response, message } = error || {};

      if (error.response) {
        if (
          (error.response.headers as any).hasContentType("application/json")
        ) {
          if (error.response.data instanceof Blob) {
            return error.response.data.text().then(text => {
              error.response!.data = JSON.parse(text);
              error.response!.config.responseType = 'json';
              return Promise.reject(new Error(error.response!.data.msg || message))
            })
          }
          if (error.response.data instanceof ArrayBuffer) {
            const decoder = new TextDecoder('utf-8');
            const text = decoder.decode(error.response.data)
            error.response.data = JSON.parse(text);
            error.response.config.responseType = 'json';
            return Promise.reject(new Error(error.response.data.msg || message))
          }
        }
      }

      console.log('API Error:', error)
        let errMsg = '';
        try {
            errMsg = response?.data?.msg || message;
        } catch (error) {
            throw new Error(error as unknown as string);
        }
        if (errMsg === '') {
            errMsg = "system error";
        }
        // console.error(errMsg);
        return Promise.reject(new Error(errMsg));
    },
);

class APIClient {
    get<T = any>(config: AxiosRequestConfig): Promise<T> {
        if (typeof window === 'undefined') return Promise.resolve({} as T);
        return this.request({ ...config, method: 'GET' });
    }

    post<T = any>(config: AxiosRequestConfig): Promise<T> {
        if (typeof window === 'undefined') return Promise.resolve({} as T);
        return this.request({ ...config, method: 'POST' });
    }

    put<T = any>(config: AxiosRequestConfig): Promise<T> {
        if (typeof window === 'undefined') return Promise.resolve({} as T);
        return this.request({ ...config, method: 'PUT' });
    }

    delete<T = any>(config: AxiosRequestConfig): Promise<T> {
        if (typeof window === 'undefined') return Promise.resolve({} as T);
        return this.request({ ...config, method: 'DELETE' });
    }

    request<T = any>(config: AxiosRequestConfig): Promise<T> {
        if (typeof window === 'undefined') return Promise.resolve({} as T);
        return new Promise((resolve, reject) => {
            axiosInstance
                .request<any, AxiosResponse<Result>>(config)
                .then((res: AxiosResponse<Result>) => {
                    resolve(res as unknown as Promise<T>);
                })
                .catch((e: Error | AxiosError) => {
                    reject(e);
                });
        });
    }
}

function removeQuotesBoth(str: string): string {
    return str.replace(/^['"]|['"]$/g, '');
}


export default new APIClient();
