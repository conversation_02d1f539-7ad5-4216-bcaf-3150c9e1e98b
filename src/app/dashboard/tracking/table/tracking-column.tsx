import { ColumnDef } from "@tanstack/table-core";
import { Package, MapPin, Clock, Truck, Eye, RefreshCw } from "lucide-react";
import { TrackingInfoResponse } from "@/api/tracking/tracking-model";
import { Chip } from "@/components/buttons/chip";
import { CopyText } from "@/components/buttons/copy-text-btn";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { pushModal } from "@/components/modals";
import { updateTrackingByWaybillNo } from "@/api/tracking/tracking-api";
import { toast } from "sonner";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";

// 状态颜色映射函数 - 根据displayName匹配
const getTrackingStatusVariant = (statusDisplay: string): 'default' | 'blue' | 'green' | 'yellow' | 'red' | 'purple' | 'pink' | 'indigo' | 'teal' => {
    switch (statusDisplay) {
        case '未找到':
        case '未知状态':
            return 'default';
        case '电子预报':
            return 'blue';
        case '已揽收':
        case '运输途中':
        case '到达目的地国家':
        case '派送中':
            return 'blue';
        case '清关中':
            return 'yellow';
        case '清关完成':
        case '到达待取':
            return 'teal';
        case '已签收':
            return 'green';
        case '投递失败':
        case '异常':
        case '已退回':
        case '已取消':
            return 'red';
        default:
            return 'default';
    }
};

export const TrackingColumns: ColumnDef<TrackingInfoResponse>[] = [
    {
        id: "waybillNo",
        accessorKey: "waybillNo",
        header: '运单信息',
        cell: ({ row }) => {
            const tracking = row.original;
            return (
                <div className="space-y-2 min-w-[200px]">
                    <div className="flex items-center gap-1">
                        <Package className="h-3 w-3 text-blue-500" />
                        <CopyText value={tracking.waybillNo} />
                    </div>
                    {tracking.trackingNumber && (
                        <div className="flex items-center gap-1">
                            <Truck className="h-3 w-3 text-green-500" />
                            <CopyText value={tracking.trackingNumber} />
                        </div>
                    )}
                    <div className="flex items-center gap-1">
                        <Badge variant="outline" className="text-xs">
                            {tracking.channel}
                        </Badge>
                    </div>
                </div>
            );
        },
        meta: {
            title: '运单信息',
        },
    },
    {
        id: "orderNos",
        accessorKey: "orderNos",
        header: '订单号',
        cell: ({ row }) => {
            const orderNos = row.original.orderNos;
            return (
                <div className="space-y-1 min-w-[150px]">
                    {orderNos.slice(0, 3).map((orderNo, index) => (
                        <div key={index} className="flex items-center gap-1">
                            <CopyText value={orderNo} className="text-xs" />
                        </div>
                    ))}
                    {orderNos.length > 3 && (
                        <div className="text-xs text-muted-foreground">
                            +{orderNos.length - 3} 更多
                        </div>
                    )}
                </div>
            );
        },
        meta: {
            title: '订单号',
        },
    },
    {
        id: "currentStatus",
        accessorKey: "currentStatus",
        header: '当前状态',
        cell: ({ row }) => {
            const tracking = row.original;

            return (
                <div className="space-y-2">
                    <Chip
                        label={tracking.currentStatusDisplay}
                        variant={getTrackingStatusVariant(tracking.currentStatusDisplay)}
                        outlined={false}
                    />
                    {tracking.deliveryDays && (
                        <div className="flex items-center gap-1 text-xs text-muted-foreground">
                            <Clock className="h-3 w-3" />
                            {tracking.deliveryDays} 天
                        </div>
                    )}
                </div>
            );
        },
        meta: {
            title: '当前状态',
        },
    },
    {
        id: "destinationCountry",
        accessorKey: "destinationCountry",
        header: '目的地',
        cell: ({ row }) => {
            const tracking = row.original;
            return (
                <div className="space-y-1">
                    {tracking.destinationCountry && (
                        <div className="flex items-center gap-1">
                            <MapPin className="h-3 w-3 text-red-500" />
                            <span className="text-sm">{tracking.destinationCountry}</span>
                        </div>
                    )}
                    {tracking.originCountry && (
                        <div className="text-xs text-muted-foreground">
                            从 {tracking.originCountry}
                        </div>
                    )}
                </div>
            );
        },
        meta: {
            title: '目的地',
        },
    },
    {
        id: "lastUpdatedAt",
        accessorKey: "lastUpdatedAt",
        header: '最后更新',
        cell: ({ row }) => {
            const tracking = row.original;
            return (
                <div className="space-y-1">
                    <div className="text-sm">{tracking.lastUpdatedAt}</div>
                    {tracking.lastEventTime && (
                        <div className="text-xs text-muted-foreground">
                            事件: {tracking.lastEventTime}
                        </div>
                    )}
                </div>
            );
        },
        meta: {
            title: '最后更新',
        },
    },
    {
        id: "actions",
        header: '操作',
        cell: ({ row }) => {
            const tracking = row.original;

            const handleViewDetails = () => {
                pushModal('TrackingDetailModal', {
                    trackingInfo: tracking
                });
            };

            const handleUpdate = async () => {
                try {
                    await updateTrackingByWaybillNo(tracking.waybillNo);
                    toast.success('轨迹更新成功');
                    // 刷新页面
                    window.location.reload();
                } catch (error: any) {
                    toast.error('轨迹更新失败: ' + error.message);
                }
            };

            return (
                <div className="flex items-center gap-2">
                    <TooltipProvider>
                        <Tooltip>
                            <TooltipTrigger asChild>
                                <Button
                                    variant="ghost"
                                    size="sm"
                                    onClick={handleViewDetails}
                                >
                                    <Eye className="h-4 w-4" />
                                </Button>
                            </TooltipTrigger>
                            <TooltipContent>查看详情</TooltipContent>
                        </Tooltip>
                    </TooltipProvider>

                    <TooltipProvider>
                        <Tooltip>
                            <TooltipTrigger asChild>
                                <Button
                                    variant="ghost"
                                    size="sm"
                                    onClick={handleUpdate}
                                >
                                    <RefreshCw className="h-4 w-4" />
                                </Button>
                            </TooltipTrigger>
                            <TooltipContent>更新轨迹</TooltipContent>
                        </Tooltip>
                    </TooltipProvider>
                </div>
            );
        },
        meta: {
            title: '操作',
        },
    },
    {
        id: "waybillNos",
    },
    {
        id: "trackingNumbers",
    },
];
