import { cancelWaybill, countWaybillSupplierOrderScanned, retryWaybill, updateWaybillStatus } from "@/api/order/waybill/waybill-api";
import { WaybillPageResponse } from "@/api/order/waybill/waybill-model";
import { refreshTrackingByWaybillId, getTrackingByWaybillId } from "@/api/tracking/tracking-api";
import { Chip } from "@/components/buttons/chip";
import { CopyText } from "@/components/buttons/copy-text-btn";
import { DoubleCheckBtn } from "@/components/buttons/double-check-btn";
import { FormattedTime } from "@/components/common/formatted-time";
import { pushModal } from "@/components/modals";
import { Button } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";
import { refreshTableAtom } from "@/state/common";
import { ColumnDef, Row } from "@tanstack/react-table";
import { useRequest } from "ahooks";
import { useSet<PERSON><PERSON> } from "jotai";
import { AlertCircle, Boxes, CircleDollarSign, Eye, FileText, Hash, MapPinned, MoreHorizontal, Package, Phone, ShoppingBagIcon, Tags, User } from "lucide-react";
import React from "react";
import { toast } from "sonner";
import { match } from "ts-pattern";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu";

interface WaybillActionsProps {
    row: Row<WaybillPageResponse>;
}

const WaybillActions: React.FC<WaybillActionsProps> = ({ row }) => {
    const refresh = useSetAtom(refreshTableAtom);

    const { runAsync: updateWaybill } = useRequest(updateWaybillStatus, {
        manual: true,
    })

    const { runAsync: retry } = useRequest(retryWaybill, {
        manual: true,
    })

    const { runAsync: refreshTracking } = useRequest(refreshTrackingByWaybillId, {
        manual: true,
    })

    const { runAsync: getTracking } = useRequest(getTrackingByWaybillId, {
        manual: true,
    })

    const handleEdit = () => {
        pushModal('EditWaybillModal', {
            waybill: row.original,
            onSave: async (updatedWaybill) => {
                toast.promise(
                    updateWaybill(row.original.id, {
                        ...updatedWaybill,
                        channelId: updatedWaybill.channelId
                    }),
                    {
                        loading: '更新中...',
                        success: () => '更新成功',
                        error: (error) => {
                            return '更新失败: ' + error.message;
                        },
                    }
                );
            },
            onClose: () => { },
        });
    };

    const handleRetry = async () => {
        const doRetry = () => {
            toast.promise(
                retry(row.original.id),
                {
                    loading: '重试中...',
                    success: () => {
                        refresh(prev => prev + 1);
                        return '重试成功';
                    },
                    error: (error) => {
                        return '重试失败: ' + error.message;
                    },
                }
            )
        }

        const r = await countWaybillSupplierOrderScanned([row.original.id])

        const scannedResult = r[row.original.id]
        if (scannedResult && scannedResult.total != 0 && scannedResult.scanned == scannedResult.total) {
            const element = <div className="flex flex-col">
                {row.original.orderNos.split(',').map(orderNo => <span>{orderNo}</span>)}
            </div>

            pushModal('DoubleCheckModal', {
                title: '运单已经出库',
                description: "运单已出库,请确认以继续重试",
                children: element,
                onConfirm: () => {
                    doRetry()
                },
                onCancel: () => {
                }
            })
        } else {
            doRetry()
        }
    }

    const handleCancel = async () => {
        toast.promise(
            cancelWaybill(row.original.id, ""),
            {
                loading: '取消中...',
                success: () => {
                    refresh(prev => prev + 1);
                    return '取消成功';
                },
                error: (error) => {
                    return '取消失败: ' + error.message;
                },
            }
        );
    }

    const handleRefreshTracking = () => {
        toast.promise(
            refreshTracking(row.original.id),
            {
                loading: '刷新轨迹中...',
                success: (result) => {
                    refresh(prev => prev + 1);
                    return result.success ? '轨迹刷新成功' : `轨迹刷新完成: ${result.message}`;
                },
                error: (error) => {
                    return '轨迹刷新失败: ' + error.message;
                },
            }
        );
    };

    const handleViewTracking = async () => {
        try {
            const trackingInfo = await getTracking(row.original.id);
            pushModal('TrackingDetailModal', {
                trackingInfo: trackingInfo,
                onClose: () => { }
            });
        } catch (error: any) {
            if (error.message === 'Request failed with status code 404') {
                // 运单还没有生成轨迹信息，提供友好的提示和操作建议
                pushModal('DoubleCheckModal', {
                    title: '轨迹信息不存在',
                    description: '该运单还没有生成轨迹信息。这可能是因为运单刚创建或者还未开始物流跟踪。您可以尝试刷新轨迹信息来获取最新状态。',
                    confirmText: '刷新轨迹',
                    cancelText: '取消',
                    onConfirm: () => {
                        handleRefreshTracking();
                    },
                    onCancel: () => { }
                });
            } else {
                toast.error('获取轨迹信息失败: ' + error.message);
            }
        }
    };

    return (
        <div className='flex items-center gap-2'>
            <Button
                variant="link"
                className="h-auto p-0 text-blue-600 text-xs"
                onClick={handleEdit}
            >
                编辑
            </Button>
            <Separator orientation='vertical' className='h-4' />
            <DoubleCheckBtn
                className='h-auto p-0 text-orange-600 text-xs'
                variant='link'
                title='重试运单'
                description='确定要重试该运单吗？'
                onConfirm={handleRetry}
                buttonText='重试'
                onCancel={() => { }}
            />
            <DropdownMenu>
                <DropdownMenuTrigger asChild>
                    <Button variant="ghost" className="h-8 w-8 p-0">
                        <span className="sr-only">Open menu</span>
                        <MoreHorizontal className="h-4 w-4" />
                    </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                    <DropdownMenuItem
                        className="text-xs"
                        onClick={() => pushModal('WaybillRecordShowModal', { waybillId: row.original.id })}
                    >
                        记录
                    </DropdownMenuItem>
                    <DropdownMenuItem
                        className="text-xs"
                        onClick={() => pushModal('ExtendWaybillModals', { id: row.original.id, originalOrderNumber: row.original.orderNo })}
                    >
                        扩展
                    </DropdownMenuItem>
                    <DropdownMenuItem
                        className="text-xs"
                        onClick={handleViewTracking}
                    >
                        查看轨迹
                    </DropdownMenuItem>
                    <DropdownMenuItem
                        className="text-xs"
                        onClick={handleRefreshTracking}
                    >
                        刷新轨迹
                    </DropdownMenuItem>
                    {row.original.status === 'FAILED' && (
                        <DropdownMenuItem
                            className='text-xs'
                            onSelect={(e) => {
                                e.preventDefault();
                                pushModal('DoubleCheckModal', {
                                    title: '取消运单',
                                    description: '确定要取消该运单吗？',
                                    onConfirm: handleCancel,
                                    onCancel: () => { }
                                })
                            }}
                        >
                            取消
                        </DropdownMenuItem>
                    )}
                </DropdownMenuContent>
            </DropdownMenu>
        </div>
    );
};

// 状态匹配函数
const matchWaybillStatus = (status: string) => {
    return match(status)
        .with("CREATED", () => <Chip variant="yellow" label="已创建" />)
        .with("COMPLETED", () => <Chip variant="green" label="已完成" />)
        .with("CANCELLED", () => <Chip variant="red" label="已取消" />)
        .with("FAILED", () => <Chip variant="red" label="失败" />)
        .otherwise(() => <Chip variant="default" label={status} />);
};

export const ErrorDisplay: React.FC<{ errorMsg: string }> = ({ errorMsg }) => {
    const errors = errorMsg.split(/[;\n]/).filter(error => error.trim());

    return (
        <div className="space-y-1.5" style={{ maxWidth: '200px' }}>
            {errors.map((error, index) => (
                <div
                    key={index}
                    className="flex items-start gap-1.5 bg-red-50 text-red-600 px-2 py-1.5 rounded text-xs"
                >
                    <AlertCircle className="h-3 w-3 flex-shrink-0 mt-0.5" />
                    <span className="break-all">{error.trim()}</span>
                </div>
            ))}
        </div>
    );
};

export const OrderNumbers: React.FC<{ orderNos: string }> = ({ orderNos }) => {
    const orders = orderNos.split(',').filter(Boolean);
    const hasMultipleOrders = orders.length > 1;

    return (
        <div className={`relative ${hasMultipleOrders ? 'pl-3' : ''}`}>
            {/* 关联线 */}
            {hasMultipleOrders && (
                <div className="absolute left-0 top-2 bottom-2 w-0.5 bg-blue-100 rounded-full" />
            )}

            <div className="space-y-2">
                {orders.map((orderNo, index) => (
                    <div key={index} className="relative">
                        {/* 横向连接线 */}
                        {hasMultipleOrders && (
                            <div className="absolute -left-3 top-1/2 w-3 h-px bg-blue-100" />
                        )}
                        {index === 0 && hasMultipleOrders && (
                            <div className="mb-1 text-xs">
                                <Chip
                                    variant="blue"
                                    label={`${orders.length}个关联订单`}
                                />
                            </div>
                        )}
                        <div className="flex items-center gap-2 bg-gray-50/50 rounded px-2 py-1.5">
                            <Hash className="h-3 w-3 text-gray-500" />
                            <CopyText value={orderNo.trim()} />
                            {hasMultipleOrders && (
                                <span className="text-xs text-gray-400">#{index + 1}</span>
                            )}
                        </div>
                    </div>
                ))}
            </div>
        </div>
    );
};

export const WaybillColumns: ColumnDef<WaybillPageResponse>[] = [
    {
        accessorKey: "orderInfo",
        header: '订单信息',
        cell: ({ row }) => (
            <div className="space-y-3">
                {/* 订单号码部分 */}
                <OrderNumbers orderNos={row.original.orderNos} />

                {/* 状态显示 */}
                <div className="flex items-center gap-2">
                    {matchWaybillStatus(row.original.status)}
                </div>
            </div>
        ),
        meta: {
            title: '订单信息',
        },
    },

    {
        accessorKey: "waybillInfo",
        header: '运单信息',
        cell: ({ row }) => (
            <div className="bg-gray-50/70 rounded-md p-2 space-y-1.5">
                <div className="flex items-center gap-2">
                    <FileText className="h-4 w-4 text-blue-500" />
                    <span className="text-xs text-gray-500 font-medium">运单信息</span>
                </div>
                <div className="space-y-1">
                    <div className="flex items-center gap-2">
                        <span className="text-xs font-medium text-gray-600 min-w-[50px]">运单号:</span>
                        {row.original.waybillNo ? (
                            <CopyText value={row.original.waybillNo} />
                        ) : (
                            <span className="text-xs text-gray-400">暂无</span>
                        )}
                    </div>
                    <div className="flex items-center gap-2">
                        <span className="text-xs font-medium text-gray-600 min-w-[50px]">尾程号:</span>
                        {row.original.trackingNumber ? (
                            <CopyText value={row.original.trackingNumber} />
                        ) : (
                            <span className="text-xs text-gray-400">无</span>
                        )}
                    </div>
                </div>
            </div>
        ),
        meta: {
            title: '运单信息',
        },
    },

    {
        accessorKey: "errorInfo",
        header: '错误信息',
        size: 300,
        cell: ({ row }) => row.original.errorMsg ? (
            <ErrorDisplay errorMsg={row.original.errorMsg} />
        ) : (
            <span className="text-gray-400 text-xs">-</span>
        ),
        meta: {
            title: '错误信息',
        },
    },

    {
        accessorKey: "receiverInfo",
        header: '收货信息',
        cell: ({ row }) => {
            const { receiverName, phone, country, state, city, street, street2, postcode } = row.original;
            return (
                <Popover>
                    <PopoverTrigger asChild>
                        <div className="cursor-pointer hover:bg-gray-50/50 p-1 rounded-sm transition-colors">
                            <div className="flex flex-col justify-start items-start gap-2 text-sm">
                                <div className="flex items-center gap-2">
                                    <User className="h-4 w-4 text-gray-500" />
                                    <span className="font-medium max-w-[120px] text-xs">{receiverName}</span>
                                </div>
                                <div className="flex items-center gap-2">
                                    <MapPinned className="h-4 w-4 text-gray-500" />
                                    <span className="text-xs font-medium">{country + "-" + state}</span>
                                </div>
                            </div>
                        </div>
                    </PopoverTrigger>
                    <PopoverContent className="w-80 p-4" align="start">
                        <div className="space-y-3">
                            <div className="font-medium text-gray-900 border-b pb-2">收货信息详情</div>
                            <div className="space-y-2">
                                <div className="flex items-center gap-2">
                                    <User className="h-4 w-4 text-gray-500" />
                                    <span className="text-sm font-medium">收件人:</span>
                                    <span className="text-sm">{receiverName}</span>
                                </div>

                                <div className="flex items-center gap-2">
                                    <Phone className="h-4 w-4 text-gray-500" />
                                    <span className="text-sm font-medium">联系电话:</span>
                                    <span className="text-sm">{phone}</span>
                                </div>
                            </div>

                            <div className="border-t pt-3">
                                <div className="text-sm font-medium text-gray-700 mb-2">收货地址</div>
                                <div className="space-y-1.5 text-sm text-gray-600">
                                    <div><span className="font-medium">国家:</span> {country}</div>
                                    <div><span className="font-medium">州/省:</span> {state}</div>
                                    <div><span className="font-medium">城市:</span> {city}</div>
                                    <div><span className="font-medium">地址1:</span> {street}</div>
                                    {street2 && <div><span className="font-medium">地址2:</span> {street2}</div>}
                                    <div><span className="font-medium">邮编:</span> {postcode}</div>
                                </div>
                            </div>

                            {(row.original.taxNumber || row.original.iossNumber) && (
                                <div className="border-t pt-3">
                                    <div className="text-sm font-medium text-gray-700 mb-2">税号信息</div>
                                    <div className="space-y-1.5 text-sm text-gray-600">
                                        {row.original.taxNumber && (
                                            <div><span className="font-medium">税号:</span> {row.original.taxNumber}</div>
                                        )}
                                        {row.original.iossNumber && (
                                            <div><span className="font-medium">IOSS税号:</span> {row.original.iossNumber}</div>
                                        )}
                                    </div>
                                </div>
                            )}
                        </div>
                    </PopoverContent>
                </Popover>
            );
        },
        meta: {
            title: '收货信息',
        },
    },
    {
        accessorKey: "product",
        header: '产品信息',
        cell: ({ row }) => {
            const product = row.original.product;
            return product.spu && (
                <Popover>
                    <PopoverTrigger asChild>
                        <div className="cursor-pointer hover:bg-gray-50/50 p-1 rounded-sm transition-colors">
                            <div className="flex flex-col justify-start items-start gap-2 text-sm">
                                <div className="flex items-center gap-2">
                                    <ShoppingBagIcon className="h-4 w-4 text-blue-500" />
                                    <span className="font-medium max-w-[120px] text-xs truncate">{product.title}</span>
                                </div>
                                <div className="flex items-center gap-2">
                                    <Tags className="h-4 w-4 text-orange-500" />
                                    <span className="text-xs font-medium">{product.spu}</span>
                                </div>
                            </div>
                        </div>
                    </PopoverTrigger>
                    <PopoverContent className="w-80 p-4" align="start">
                        <div className="space-y-3">
                            <div className="font-medium text-gray-900 border-b pb-2">产品信息详情</div>

                            <div className="space-y-2">
                                <div className="flex items-center gap-2">
                                    <ShoppingBagIcon className="h-4 w-4 text-blue-500" />
                                    <span className="text-sm font-medium">产品标题:</span>
                                    <span className="text-sm">{product.title}</span>
                                </div>

                                <div className="flex items-center gap-2">
                                    <Package className="h-4 w-4 text-gray-500" />
                                    <span className="text-sm font-medium">产品名称:</span>
                                    <span className="text-sm">{product.name} | {product.cnName}</span>
                                </div>

                                <div className="flex items-center gap-2">
                                    <Tags className="h-4 w-4 text-orange-500" />
                                    <span className="text-sm font-medium">SPU:</span>
                                    <span className="text-sm">{product.spu}</span>
                                </div>
                            </div>

                            <div className="border-t pt-3">
                                <div className="text-sm font-medium text-gray-700 mb-2">规格信息</div>
                                <div className="space-y-1.5 text-sm text-gray-600">
                                    <div className="flex items-center gap-2">
                                        <Boxes className="h-4 w-4 text-purple-500" />
                                        <span className="font-medium">规格:</span>
                                        <span>{product.color} - {product.size}</span>
                                    </div>
                                    <div className="flex items-center gap-2">
                                        <CircleDollarSign className="h-4 w-4 text-green-500" />
                                        <span className="font-medium">数量/价格:</span>
                                        <span>{product.qty}件 / {product.price} {product.currency}</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </PopoverContent>
                </Popover>
            );
        },
        meta: {
            title: '产品信息',
        },
    },

    {
        accessorKey: "shippingInfo",
        header: '配送信息',
        cell: ({ row }) => {
            return (
                <div className="space-y-2 max-w-[150px]">
                    {/* 配送方式 */}
                    {/* <div className="bg-orange-50/50 rounded-md p-2">
                        <div className="flex items-center gap-2 mb-1">
                            <Package className="h-4 w-4 text-orange-500" />
                            <span className="text-xs font-medium text-orange-700">配送方式</span>
                        </div>
                        <div className="text-sm font-medium text-gray-800">{row.original.shipMethod}</div>
                    </div> */}

                    {/* 渠道信息 */}
                    <div className="space-y-1">
                        <div className="flex items-center gap-2">
                            <span className="text-xs font-medium text-gray-600 min-w-[40px]">渠道:</span>
                            <span className="text-xs text-gray-800 font-medium">{row.original.channel}</span>
                        </div>
                        <div className="flex items-center gap-2">
                            <span className="text-xs font-medium text-gray-600 min-w-[40px]">运输:</span>
                            <span className="text-xs text-gray-600">{row.original.shipMethodAlias}</span>
                        </div>
                    </div>

                    {/* 运单标签 */}
                    {row.original.waybillLabelUrl && (
                        <div className="pt-1 border-t border-gray-100">
                            <Button
                                variant="outline"
                                size="sm"
                                onClick={() => {
                                    pushModal('PDFViewerModal', {
                                        pdfUrl: row.original.waybillLabelUrl,
                                    });
                                }}
                                className="w-full hover:bg-blue-50 text-blue-600 border-blue-200 hover:border-blue-300 transition-colors"
                            >
                                <Eye className="mr-1.5 h-3.5 w-3.5" />
                                <span className="text-xs">查看运单标签</span>
                            </Button>
                        </div>
                    )}
                </div>
            )
        },
        meta: {
            title: '配送信息',
        },
    },
    {
        accessorKey: "totalPrice",
        header: '申报价值',
        cell: ({ row }) => {
            const totalPrice = row.original.totalPrice;
            return (
                <div className="bg-gray-50/50 rounded-md p-2 space-y-1">
                    <div className="flex items-center gap-2" >
                        <CircleDollarSign className="h-4 w-4 text-gray-500" />
                        {
                            totalPrice !== null && totalPrice !== undefined ? (
                                <div className="text-sm font-medium text-gray-800">
                                    ${totalPrice.toLocaleString('zh-CN', {
                                        minimumFractionDigits: 2,
                                        maximumFractionDigits: 2
                                    })}
                                </div>
                            ) : (
                                <div className="text-xs text-gray-400 italic">-</div>
                            )
                        }
                    </div>
                </div >
            );
        },
        meta: {
            title: '申报价值',
        },
    },
    {
        accessorKey: "timeInfo",
        header: () => {
            return (
                <div className="w-[150px]">
                    <span className="">时间信息</span>
                </div>
            )
        },
        cell: ({ row }) => (
            <div className="bg-gray-50/50 rounded-md p-2 space-y-2">
                <div className="flex items-center gap-2">
                    <div className="h-2 w-2 bg-green-500 rounded-full"></div>
                    <span className="text-xs font-medium text-gray-700">创建时间</span>
                </div>
                <div className="space-y-1">
                    <div className="text-xs text-gray-800 font-mono">
                        <FormattedTime timestamp={row.original.createdAt} formatter="YYYY-MM-DD" />
                    </div>
                    <div className="text-xs text-gray-600 font-mono">
                        <FormattedTime timestamp={row.original.createdAt} formatter="HH:mm" />
                    </div>
                    <div className="flex items-center gap-1 pt-1 border-t border-gray-100">
                        <User className="h-3 w-3 text-gray-400" />
                        <span className="text-xs text-gray-500">创建人: {row.original.createdByName}</span>
                    </div>
                </div>
            </div>
        ),
        meta: {
            title: '时间信息',
        },
    },
    {
        accessorKey: "actions",
        header: "操作",
        cell: ({ row }) => <WaybillActions row={row} />,
        meta: {
            title: '操作',
        },
    },
] as const;
