import { ColumnDef } from "@tanstack/table-core";
import { Boxes, Calendar, CircleDollarSign, Hash, MapPinned, Package, Phone, ShoppingBagIcon, Tags, User } from "lucide-react";
import { cancelSubOrder, deleteSubOrder, getSubOrderImages, retryMatchSupplier, SubOrder } from "@/api/order/sub-order/sub-orders";
import { Chip } from "@/components/buttons/chip";
import { FormattedTime } from "@/components/common/formatted-time";
import { CopyText } from "@/components/buttons/copy-text-btn";
import { refreshTableAtom } from "@/state/common";
import { useSetAtom } from "jotai";
import { Button } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";
import { toast } from "sonner";
import { pushModal } from "@/components/modals";
import { ErrorDisplay } from "../../waybill/table/waybill-column";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { subOrderStatusInfoMatch, waybillStatusInfoMatch } from "@/lib/model/suborder-match";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuSeparator, DropdownMenuTrigger } from "@/components/ui/dropdown-menu";
import { MoreHorizontal } from "lucide-react";
import { Row } from "@tanstack/react-table";

export const SubOrderColumns: ColumnDef<SubOrder>[] = [
    {
        accessorKey: "orderNo",
        header: '订单号',
        cell: ({ row }) => (
            <div className="space-y-2">
                <div className="flex items-center gap-1">
                    <Hash className="h-3 w-3 text-blue-500" />
                    <CopyText value={row.getValue("orderNo")} />
                </div>
                <div className="flex items-center gap-1">
                    <Hash className="h-3 w-3 text-orange-500" />
                    <CopyText value={row.original.customerOrderNo} />
                </div>
            </div>
        ),
        meta: {
            title: '订单号',
        },
    },
    {
        id: "orderNos",
    },
    {
        id: "customerOrderNos"
    },
    {
        accessorKey: "status",
        header: '状态',
        cell: ({ row }) => {
            const status = row.original.status;
            const statusInfo = subOrderStatusInfoMatch(status)

            return (
                <div className="space-y-1 max-w-[150px]">
                    <div className="flex items-center space-x-2">
                        <Chip variant={statusInfo.variant} label={statusInfo.label} />
                    </div>
                    <div className="flex items-center space-x-2 text-sm text-gray-500">
                        <Calendar className="h-4 w-4" />
                        <FormattedTime timestamp={row.original.updatedAt} formatter='YYYY-MM-DD HH:mm' />
                    </div>
                </div>
            );
        },
        filterFn: (row, id, value) => {
            return value.includes(row.getValue(id))
        },
        meta: {
            title: '状态',
        },
    },

    {
        accessorKey: "receiverInfo",
        header: '收货信息',
        cell: ({ row }) => {
            const { receiverName, phone, country, state, city, address1, address2, postcode } = row.original.recipient;
            return (
                <Popover>
                    <PopoverTrigger asChild>
                        <div className="cursor-pointer hover:bg-gray-50/50 p-1 rounded-sm transition-colors">
                            <div className="flex flex-col justify-start items-start gap-2 text-sm">
                                <div className="flex items-center gap-2">
                                    <User className="h-4 w-4 text-gray-500" />
                                    <span className="font-medium max-w-[120px] text-xs">{receiverName}</span>
                                </div>
                                <div className="flex items-center gap-2">
                                    <MapPinned className="h-4 w-4 text-gray-500" />
                                    <span className="text-xs font-medium">{country + "-" + state}</span>
                                </div>
                            </div>
                        </div>
                    </PopoverTrigger>
                    <PopoverContent className="w-80 p-4" align="start">
                        <div className="space-y-3">
                            <div className="font-medium text-gray-900 border-b pb-2">收货信息详情</div>
                            <div className="space-y-2">
                                <div className="flex items-center gap-2">
                                    <User className="h-4 w-4 text-gray-500" />
                                    <span className="text-sm font-medium">收件人:</span>
                                    <span className="text-sm">{receiverName}</span>
                                </div>

                                <div className="flex items-center gap-2">
                                    <Phone className="h-4 w-4 text-gray-500" />
                                    <span className="text-sm font-medium">联系电话:</span>
                                    <span className="text-sm">{phone}</span>
                                </div>
                            </div>

                            <div className="border-t pt-3">
                                <div className="text-sm font-medium text-gray-700 mb-2">收货地址</div>
                                <div className="space-y-1.5 text-sm text-gray-600">
                                    <div><span className="font-medium">国家:</span> {country}</div>
                                    <div><span className="font-medium">州/省:</span> {state}</div>
                                    <div><span className="font-medium">城市:</span> {city}</div>
                                    <div><span className="font-medium">地址1:</span> {address1}</div>
                                    {address2 && <div><span className="font-medium">地址2:</span> {address2}</div>}
                                    <div><span className="font-medium">邮编:</span> {postcode}</div>
                                </div>
                            </div>
                        </div>
                    </PopoverContent>
                </Popover>
            );
        },
        meta: {
            title: '收货信息',
        },
    },
    {
        accessorKey: "product",
        header: '产品信息',
        cell: ({ row }) => {
            const product = row.original.product;
            return product.spu && (
                <Popover>
                    <PopoverTrigger asChild>
                        <div className="cursor-pointer hover:bg-gray-50/50 p-1 rounded-sm transition-colors">
                            <div className="flex flex-col justify-start items-start gap-2 text-sm">
                                <div className="flex items-center gap-2">
                                    <ShoppingBagIcon className="h-4 w-4 text-blue-500" />
                                    <span className="font-medium max-w-[120px] text-xs truncate">{product.title}</span>
                                </div>
                                <div className="flex items-center gap-2">
                                    <Tags className="h-4 w-4 text-orange-500" />
                                    <span className="text-xs font-medium">{product.spu}</span>
                                </div>
                            </div>
                        </div>
                    </PopoverTrigger>
                    <PopoverContent className="w-80 p-4" align="start">
                        <div className="space-y-3">
                            <div className="font-medium text-gray-900 border-b pb-2">产品信息详情</div>

                            <div className="space-y-2">
                                <div className="flex items-center gap-2">
                                    <ShoppingBagIcon className="h-4 w-4 text-blue-500" />
                                    <span className="text-sm font-medium">产品标题:</span>
                                    <span className="text-sm">{product.title}</span>
                                </div>

                                <div className="flex items-center gap-2">
                                    <Package className="h-4 w-4 text-gray-500" />
                                    <span className="text-sm font-medium">产品名称:</span>
                                    <span className="text-sm">{product.name} | {product.cnName}</span>
                                </div>

                                <div className="flex items-center gap-2">
                                    <Tags className="h-4 w-4 text-orange-500" />
                                    <span className="text-sm font-medium">SPU:</span>
                                    <span className="text-sm">{product.spu}</span>
                                </div>
                            </div>

                            <div className="border-t pt-3">
                                <div className="text-sm font-medium text-gray-700 mb-2">规格信息</div>
                                <div className="space-y-1.5 text-sm text-gray-600">
                                    <div className="flex items-center gap-2">
                                        <Boxes className="h-4 w-4 text-purple-500" />
                                        <span className="font-medium">规格:</span>
                                        <span>{product.color} - {product.size}</span>
                                    </div>
                                    <div className="flex items-center gap-2">
                                        <CircleDollarSign className="h-4 w-4 text-green-500" />
                                        <span className="font-medium">数量/价格:</span>
                                        <span>{product.qty}件 / {product.price} {product.currency}</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </PopoverContent>
                </Popover>
            );
        },
        meta: {
            title: '产品信息',
        },
    },
    {
        accessorKey: "shipping",
        header: '配送信息',
        cell: ({ row }) => {
            const shipping = row.original.shipping;
            return (
                <div className="space-y-2">
                    {/* 渠道信息 */}
                    <div className="space-y-1">
                        <div className="flex items-center gap-2">
                            <span className="text-xs font-medium text-gray-600 min-w-[40px]">渠道:</span>
                            <span className="text-xs text-gray-800 font-medium">{shipping.channel}</span>
                        </div>
                        <div className="flex items-center gap-2">
                            <span className="text-xs font-medium text-gray-600 min-w-[40px]">运输:</span>
                            <span className="text-xs text-gray-600">{shipping.shipMethod}</span>
                        </div>
                        <div className="flex items-center gap-2">
                            <span className="text-xs font-medium text-gray-600 min-w-[40px]">类型:</span>
                            <span className="text-xs text-gray-600">{shipping.deliveryMethod}</span>
                        </div>
                    </div>
                </div>
            );
        },
        meta: {
            title: '配送信息',
        },
    },
    {
        accessorKey: "waybillStatus",
        header: '业务状态',
        cell: ({ row }) => {
            const waybillStatusInfo = waybillStatusInfoMatch(row.original.waybillStatus);
            // const supplierOrderStatusInfo = supplierOrderStatusInfoMatch(row.original.supplierOrderStatus);
            return (
                <div className="flex flex-col items-center gap-2">
                    <div className="flex items-center">
                        <span className="text-xs font-medium text-gray-600 min-w-[40px]">运单:</span>
                        <Chip variant={waybillStatusInfo.variant} label={waybillStatusInfo.label} />
                    </div>
                    {/* <div className="flex items-center gap-2">
                        <span className="text-xs font-medium text-gray-600 min-w-[40px]">供应商:</span>
                        <Chip variant={supplierOrderStatusInfo.variant} label={supplierOrderStatusInfo.label} />
                    </div> */}
                </div>
            );
        },
        filterFn: (row, id, value) => {
            return value.includes(row.getValue(id))
        },
        meta: {
            title: '业务状态',
        },
    },
    {
        accessorKey: "supplier",
        header: '供应商',
        cell: ({ row }) => {
            const product = row.original.product;
            return <div className="text-xs text-gray-500">{product.supplierName}</div>
        },
        meta: {
            title: '供应商',
        },
    },
    {
        accessorKey: "errorInfo",
        header: () => {
            return (
                <div className="max-w-[100px]">
                    <span className="">错误信息</span>
                </div>
            )
        },
        cell: ({ row }) => row.original.failedReason ? (
            <ErrorDisplay errorMsg={row.original.failedReason} />
        ) : (
            <span className="text-gray-400 text-xs">-</span>
        ),
        meta: {
            title: '错误信息',
        },
    },
    {
        accessorKey: 'actions',
        header: '操作',
        cell: ({ row }) => <SubOrderActions row={row} />,
        meta: {
            title: '操作',
        },
    }
] as const;

const SubOrderActions = ({ row }: { row: Row<SubOrder> }) => {
    const setRefreshTable = useSetAtom(refreshTableAtom);

    return (
        <div className="flex items-center gap-2">
            <Button
                variant="link"
                className="h-auto p-0 text-blue-600 text-xs"
                onClick={() => {
                    pushModal('EditSubOrderModal', {
                        subOrder: {
                            id: row.original.id,
                            designUrl: row.original.designUrl,
                            effectUrl: row.original.effectUrl,
                            designImgSearchFiled: row.original.designImgSearchFiled,
                            effectImgSearchFiled: row.original.effectImgSearchFiled,
                            status: row.original.status,
                            color: row.original.product.color,
                            size: row.original.product.size,
                        },
                        onSuccess: () => setRefreshTable(prev => prev + 1),
                        onClose: () => { }
                    });
                }}>
                编辑
            </Button>
            <Separator orientation='vertical' className='h-4' />
            <Button
                variant="link"
                className="h-auto p-0 text-blue-600 text-xs"
                onClick={() => pushModal('SplitOrderModal', { orderId: row.original.id })}>
                拆分
            </Button>
            <Separator orientation='vertical' className='h-4' />
            <Button
                variant="link"
                className="h-auto p-0 text-red-600 text-xs"
                onClick={() => {
                    toast.promise(
                        retryMatchSupplier(row.original.id).then(() => {
                            setRefreshTable(prev => prev + 1);
                        }),
                        {
                            loading: '重试匹配供应商中...',
                            success: '重试匹配供应商成功',
                            error: (error) => `重试匹配供应商失败: ${error.message}`,
                        }
                    )
                }}>
                重试
            </Button>
            <DropdownMenu>
                <DropdownMenuTrigger asChild>
                    <Button variant="ghost" className="h-8 w-8 p-0">
                        <span className="sr-only">Open menu</span>
                        <MoreHorizontal className="h-4 w-4" />
                    </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                    <DropdownMenuItem
                        className="text-xs"
                        onClick={() => window.open(`/dashboard/order/waybill?orderNo=${row.original.orderNo}`, '_blank')}>
                        查看运单
                    </DropdownMenuItem>
                    <DropdownMenuItem
                        className="text-xs"
                        onClick={async () => {
                            const images = await getSubOrderImages(row.original.orderNo);
                            pushModal('ImageGalleryModal', { imageUrls: images.flatMap(url => url.split(',')) })
                        }}>
                        查看图片
                    </DropdownMenuItem>
                    {(row.original.status === "FAILED" || (row.original.status === "SUPPLIER_MATCHED" && row.original.waybillStatus === "FAILED")) && (
                        <DropdownMenuItem
                            className="text-xs"
                            onSelect={(e) => {
                                e.preventDefault();
                                pushModal('DoubleCheckModal', {
                                    title: '取消订单',
                                    description: '取消订单将会放弃该订单的后续处理，请谨慎操作',
                                    onConfirm: () => {
                                        toast.promise(
                                            cancelSubOrder(row.original.id).then(() => {
                                                setRefreshTable(prev => prev + 1);
                                            }),
                                            {
                                                loading: '取消中...',
                                                success: '取消成功',
                                                error: '取消失败',
                                            }
                                        );
                                    },
                                    onCancel: () => { }
                                })
                            }}>
                            取消订单
                        </DropdownMenuItem>
                    )}
                    <DropdownMenuSeparator />
                    <DropdownMenuItem
                        className="text-red-600 text-xs focus:text-red-600 focus:bg-red-50"
                        onSelect={(e) => {
                            e.preventDefault();
                            pushModal('DoubleCheckModal', {
                                title: '删除订单',
                                description: '删除订单将删除所有相关数据，请谨慎操作',
                                onConfirm: () => {
                                    toast.promise(
                                        deleteSubOrder(row.original.id).then(() => {
                                            setRefreshTable(prev => prev + 1);
                                        }),
                                        {
                                            loading: '删除中...',
                                            success: '删除成功',
                                            error: '删除失败',
                                        }
                                    );
                                },
                                onCancel: () => { }
                            })
                        }}>
                        删除
                    </DropdownMenuItem>
                </DropdownMenuContent>
            </DropdownMenu>
        </div>
    );
};