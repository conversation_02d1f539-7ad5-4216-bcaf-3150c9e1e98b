import { batchDeleteSubOrder, changeSubOrdersChannel, mergeSubOrders, pageQuerySubOrder } from "@/api/order/sub-order/sub-orders";
import useSearchParamsManager from "@/hooks/use-url-param";
import { pushModal } from "@/components/modals";
import { DataTableUrlFilter } from "@/components/select/data-table-column-filter";
import { DataTable } from "@/components/table/base-table";
import { DataTableToolbar, ToolbarItem, ToolbarLeft, ToolbarRight } from "@/components/table/toolbar/data-table-toolbar";
import { DataTableViewOptions } from "@/components/table/toolbar/data-table-view-options";
import { DateRangePicker } from "@/components/ui/date-range-picker";
import { ExpandableTabs } from "@/components/ui/expandable-tabs";
import { SelectWithInput } from "@/components/ui/select-with-input";
import { Textarea } from "@/components/ui/textarea";
import { refreshTable<PERSON><PERSON> } from "@/state/common";
import dayjs from "dayjs";
import { useSet<PERSON>tom } from "jotai";
import { Merge, Trash2, Truck } from "lucide-react";
import { toast } from "sonner";
import { SubOrderColumns } from "./table/sub-order-column";
import { countryList } from "@/lib/country";

export default function SubOrderPage() {

    const { addParam, getParam } = useSearchParamsManager();

    const refresh = useSetAtom(refreshTableAtom);

    const handleChangeChannel = (subOrderIds: string[]) => {
        pushModal('ChannelSelectModels', {
            onConfirm: (company: string, route: string) => {
                changeSubOrdersChannel(subOrderIds, company, route)
                    .then(() => {
                        toast.success('修改成功');
                        refresh(prev => prev + 1);
                    })
                    .catch((error) => {
                        toast.error('修改失败:' + error.message);
                    })
            }
        })
    }


    const handleDelete = (subOrderIds: string[]) => {
        pushModal('DoubleCheckModal', {
            title: '删除子订单',
            description: '确定要删除这些子订单吗？',
            onConfirm: () => {
                batchDeleteSubOrder(subOrderIds).then(() => {
                    toast.success('删除成功');
                    refresh(prev => prev + 1);
                }).catch((error) => {
                    toast.error('删除失败:' + error.message);
                })
            },
            onCancel: () => {
                console.log('取消删除子订单');
            }
        })
    }

    const handleMerge = (subOrderIds: string[]) => {
        pushModal('DoubleCheckModal', {
            title: '合并子订单',
            description: '确定要合并这些子订单吗？',
            onConfirm: () => {
                mergeSubOrders(subOrderIds).then(() => {
                    toast.success('合并成功');
                    refresh(prev => prev + 1);
                }).catch((error) => {
                    toast.error('合并失败:' + error.message);
                })
            },
            onCancel: () => {
            }
        })
    }

    return (
        <div className="flex flex-col gap-4">
            <div className="flex items-center justify-between">
                <h1 className="text-2xl font-bold">子订单管理</h1>
            </div>

            <div className="rounded-lg border shadow-sm p-4">
                <DataTable
                    columns={SubOrderColumns}
                    onFetch={param => pageQuerySubOrder(param)}
                    isFixedHeader={false}
                    isNeedSelect={true}
                    containerHeight="620px"
                    toolbar={(table, tableId) => (
                        <DataTableToolbar table={table} tableId={tableId}>
                            <ToolbarLeft>
                                <div className="flex flex-col gap-2">
                                    <div className="flex items-center gap-2">
                                        <ToolbarItem>
                                            <SelectWithInput
                                                options={[
                                                    { label: "订单号", value: "orderNo" },
                                                    { label: "主订单编号", value: "orderId" },
                                                    { label: "上传文件名", value: "fileName" },
                                                    { label: "客户订单号", value: "customerOrderNo" }
                                                ]}
                                                inputPlaceholder="请输入搜索内容"
                                            />
                                        </ToolbarItem>
                                        <ToolbarItem>
                                            <DataTableUrlFilter
                                                paramKey="hasAssignedChannel"
                                                title="是否分配渠道"
                                                options={[
                                                    { label: '已分配', value: 'true' },
                                                    { label: '未分配', value: 'false' },
                                                ]}
                                            />
                                        </ToolbarItem>
                                        <ToolbarItem>
                                            <DataTableUrlFilter
                                                paramKey="status"
                                                title="任务状态"
                                                options={[
                                                    { label: '已创建', value: 'CREATED' },
                                                    { label: '已完成', value: 'COMPLETED' },
                                                    { label: '已取消', value: 'CANCELLED' },
                                                    { label: '已拆分', value: 'SPLIT' },
                                                    { label: '供应商匹配', value: 'SUPPLIER_MATCHED' },
                                                    { label: '失败', value: 'FAILED' },
                                                    // 轨迹相关状态
                                                    { label: '未找到轨迹', value: 'TRACKING_NOT_FOUND' },
                                                    { label: '电子预报', value: 'TRACKING_PRE_ADVICE_RECEIVED' },
                                                    { label: '已揽收', value: 'TRACKING_PICKED_UP' },
                                                    { label: '运输途中', value: 'TRACKING_IN_TRANSIT' },
                                                    { label: '到达目的地国家', value: 'TRACKING_ARRIVED_DESTINATION_COUNTRY' },
                                                    { label: '清关中', value: 'TRACKING_IN_CUSTOMS' },
                                                    { label: '清关完成', value: 'TRACKING_CUSTOMS_CLEARED' },
                                                    { label: '到达待取', value: 'TRACKING_ARRIVED_FOR_PICKUP' },
                                                    { label: '派送中', value: 'TRACKING_OUT_FOR_DELIVERY' },
                                                    { label: '投递失败', value: 'TRACKING_DELIVERY_FAILED' },
                                                    { label: '已签收', value: 'TRACKING_DELIVERED' },
                                                    { label: '异常', value: 'TRACKING_EXCEPTION' },
                                                    { label: '已退回', value: 'TRACKING_RETURNED' },
                                                    { label: '已取消(轨迹)', value: 'TRACKING_CANCELLED' },
                                                    { label: '未知状态', value: 'TRACKING_UNKNOWN' },
                                                ]}
                                            />
                                        </ToolbarItem>
                                        <ToolbarItem>
                                            <DataTableUrlFilter
                                                paramKey="waybillStatus"
                                                title="运单状态"
                                                options={
                                                    [
                                                        { label: '等待创建', value: 'PENDING' },
                                                        { label: '已创建', value: 'CREATED' },
                                                        { label: '已取消', value: 'CANCELLED' },
                                                        { label: '失败', value: 'FAILED' },
                                                    ]
                                                }
                                            />
                                        </ToolbarItem>
                                        <ToolbarItem>
                                            <DataTableUrlFilter
                                                paramKey="supplierOrderStatus"
                                                title="供应商订单状态"
                                                options={
                                                    [
                                                        { label: '等待创建', value: 'PENDING' },
                                                        { label: '已创建', value: 'CREATED' },
                                                        { label: '已取消', value: 'CANCELLED' },
                                                        { label: '失败', value: 'FAILED' },
                                                    ]
                                                }
                                            />
                                        </ToolbarItem>
                                        <ToolbarItem>
                                            <DataTableUrlFilter
                                                paramKey="country"
                                                title="国家"
                                                options={countryList}
                                            />
                                        </ToolbarItem>
                                        <ToolbarItem>
                                            <DateRangePicker
                                                onUpdate={(values) => {
                                                    addParam('createdAtFrom', dayjs(values.range.from).format('YYYY-MM-DD'))
                                                    addParam('createdAtTo', dayjs(values.range.to).format('YYYY-MM-DD'))
                                                }}
                                                initialDateFrom={getParam('createdAtFrom') ? dayjs(getParam('createdAtFrom')).toDate() : dayjs().subtract(15, 'day').toDate()}
                                                initialDateTo={getParam('createdAtTo') ? dayjs(getParam('createdAtTo')).toDate() : dayjs().toDate()}
                                                align="start"
                                                locale="zh-CN"
                                                showCompare={false}
                                            />
                                        </ToolbarItem>
                                    </div>
                                    <div className="flex items-center space-x-4">
                                        <ToolbarItem>
                                            <Textarea
                                                placeholder="搜索订单,多个订单用空行隔开..."
                                                className="text-xs w-96 min-h-[60px]"
                                                defaultValue={table.getColumn('orderNos')?.getFilterValue() as string | undefined || ''}
                                                onChange={(e) => {
                                                    table.getColumn('orderNos')?.setFilterValue(e.target.value);
                                                }}
                                            />
                                        </ToolbarItem>
                                        <ToolbarItem>
                                            <Textarea
                                                placeholder="搜索客户订单号,多个订单用空行隔开..."
                                                className="text-xs w-96 min-h-[60px]"
                                                defaultValue={table.getColumn('customerOrderNos')?.getFilterValue() as string | undefined || ''}
                                                onChange={(e) => {
                                                    table.getColumn('customerOrderNos')?.setFilterValue(e.target.value);
                                                }}
                                            />
                                        </ToolbarItem>
                                    </div>
                                </div>
                            </ToolbarLeft>
                            <ToolbarRight>
                                {table.getSelectedRowModel().rows.length > 0 && (
                                    // <Dock>
                                    //     <DockItem icon={Truck} label="选择渠道" onClick={() => handleChangeChannel(table.getSelectedRowModel().rows.map((row: any) => row.original.id))} />
                                    //     <DockItem icon={Merge} label="合并" onClick={() => handleMerge(table.getSelectedRowModel().rows.map((row: any) => row.original.id))} />
                                    //     <DockItem icon={Trash2} label="删除" onClick={() => handleDelete(table.getSelectedRowModel().rows.map((row: any) => row.original.id))} />
                                    // </Dock>
                                    <ExpandableTabs tabs={[{
                                        title: '选择渠道',
                                        icon: Truck,
                                        onClick: () => handleChangeChannel(table.getSelectedRowModel().rows.map(row => row.original.id))
                                    }, {
                                        title: '合并',
                                        icon: Merge,
                                        onClick: () => handleMerge(table.getSelectedRowModel().rows.map(row => row.original.id))
                                    },
                                    {
                                        type: 'separator'
                                    },
                                    {
                                        title: '删除',
                                        icon: Trash2,
                                        onClick: () => handleDelete(table.getSelectedRowModel().rows.map(row => row.original.id))
                                    }]}
                                    />
                                )}
                                <ToolbarItem>
                                    <DataTableViewOptions table={table} tableId={tableId} />
                                </ToolbarItem>
                            </ToolbarRight>
                        </DataTableToolbar>
                    )}
                    dependencies={[]}
                />
            </div>
        </div>
    )
}