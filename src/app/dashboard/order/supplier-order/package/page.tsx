import useSearchParamsManager from "@/hooks/use-url-param";
import { DataTable } from "@/components/table/base-table";
import {
    DataTableToolbar,
    ToolbarItem,
    ToolbarLeft,
    ToolbarRight
} from "@/components/table/toolbar/data-table-toolbar";
import { SelectWithInput } from "@/components/ui/select-with-input";
import { DataTableUrlFilter } from "@/components/select/data-table-column-filter";
import { DateRangePicker } from "@/components/ui/date-range-picker";
import dayjs from "dayjs";
import { DataTableViewOptions } from "@/components/table/toolbar/data-table-view-options";

import { SupplierOrderPackageColumns } from "./table/supplier-order-package-column";
import { supplierOrderPackageApi } from "@/api/supplier/supplier-order/supplier-order-package-api";
import { Button } from "@/components/ui/button";
import { PlusIcon } from "@radix-ui/react-icons";
import { pushModal } from "@/components/modals";
import { useSet<PERSON>tom } from "jotai";
import { refreshTableAtom } from "@/state/common";

export default function SupplierOrderPackagePage() {

    const { addParam, getParam } = useSearchParamsManager();

    const refresh = useSetAtom(refreshTableAtom)

    const handleCreatePackage = () => {
        pushModal('CreatePackageModal', {
            onSuccess: () => {
                refresh(prev => prev + 1)
            }
        })
    }

    return (
        <div className="flex flex-col gap-4">
            <div className="flex items-center justify-between">
                <h1 className="text-2xl font-bold">包裹管理</h1>
            </div>
            <div className="rounded-lg border shadow-sm p-4">
                <DataTable
                    columns={SupplierOrderPackageColumns}
                    onFetch={param => supplierOrderPackageApi.pageQuerySupplierOrderPackage(param)}
                    isFixedHeader={false}
                    containerHeight="620px"
                    toolbar={(table, tableId) => (
                        <DataTableToolbar table={table} tableId={tableId}>
                            <ToolbarLeft>
                                <ToolbarItem>
                                    <SelectWithInput
                                        options={[
                                            { label: "订单号", value: "orderNo" },
                                        ]}
                                        inputPlaceholder="请输入搜索内容"
                                    />
                                </ToolbarItem>
                                <ToolbarItem>
                                    <DataTableUrlFilter
                                        paramKey="status"
                                        title="任务状态"
                                        options={[
                                            { label: '已创建', value: 'CREATED' },
                                            { label: '处理中', value: 'PROCESSING' },
                                            { label: '已完成', value: 'COMPLETED' },
                                        ]}
                                    />
                                </ToolbarItem>
                                <ToolbarItem>
                                    <DateRangePicker
                                        onUpdate={(values) => {
                                            addParam('createdAtFrom', dayjs(values.range.from).format('YYYY-MM-DD'))
                                            addParam('createdAtTo', dayjs(values.range.to).format('YYYY-MM-DD'))
                                        }}
                                        initialDateFrom={getParam('createdAtFrom') ? dayjs(getParam('createdAtFrom')).toDate() : dayjs().subtract(365, 'day').toDate()}
                                        initialDateTo={getParam('createdAtTo') ? dayjs(getParam('createdAtTo')).toDate() : dayjs().toDate()}
                                        align="start"
                                        locale="zh-CN"
                                        showCompare={false}
                                    />
                                </ToolbarItem>
                            </ToolbarLeft>

                            <ToolbarRight>
                                <ToolbarItem>
                                    <Button className="h-7 text-sm" onClick={handleCreatePackage}>
                                        <PlusIcon className="h-4 w-4" />
                                        <span className="text-xs">创建包裹</span>
                                    </Button>
                                </ToolbarItem>
                                <ToolbarItem>
                                    <DataTableViewOptions table={table} tableId={tableId} />
                                </ToolbarItem>
                            </ToolbarRight>

                        </DataTableToolbar>
                    )}
                    dependencies={[]}
                />
            </div>
        </div>
    )
}