import { SupplierOrderPackage } from "@/api/supplier/supplier-order/supplier-order-package-api";
import { CopyText } from "@/components/buttons/copy-text-btn";
import { Hash } from "lucide-react";
import { ColumnDef } from "@tanstack/react-table";
import { FormattedTime } from "@/components/common/formatted-time";
import { Chip } from "@/components/buttons/chip";
import { match } from "ts-pattern";
import { Button } from "@/components/ui/button";
import { pushModal } from "@/components/modals";
import { refreshTableAtom } from "@/state/common";
import { useSetAtom } from "jotai";

const matchSupplierOrderPackageStatus = (status: string) => {
    return match(status)
        .with("CREATED", () => <Chip variant="yellow" label="已创建" />)
        .with("PROCESSING", () => <Chip variant="purple" label="处理中" />)
        .with("COMPLETED", () => <Chip variant="green" label="已完成" />)
        .otherwise(() => <Chip variant="default" label={status} />);
};


export const SupplierOrderPackageColumns: ColumnDef<SupplierOrderPackage>[] = [
    {
        accessorKey: 'packageNo',
        header: '包裹号',
        cell: ({ row }) => (
            <div className="flex items-center space-x-2">
                <Hash className="h-4 w-4 text-gray-500" />
                <CopyText value={row.original.packageNo} url={`/dashboard/order/supplier-order/package/detail/${row.original.id}`} />
            </div>
        ),
        meta: {
            title: '包裹号',
        },
    },
    {
        accessorKey: 'scanCount',
        header: '已扫描数量',
        cell: ({ row }) => (
            <div className="flex items-center space-x-2">
                <span className="text-sm text-gray-600">{row.original.scanCount}</span>
            </div>
        ),
        meta: {
            title: '已扫描数量',
        },
    },
    {
        accessorKey: 'status',
        header: '状态',
        cell: ({ row }) => (
            <div className="flex items-center space-x-2">
                {matchSupplierOrderPackageStatus(row.original.status)}
            </div>
        ),
        meta: {
            title: '状态',
        },
    },
    {
        accessorKey: "timeInfo",
        header: '时间信息',
        cell: ({ row }) => (
            <div className="space-y-1">
                <div className="text-sm">
                    <div className="text-gray-600">
                        创建: <FormattedTime timestamp={row.original.createdAt} formatter="YYYY-MM-DD HH:mm:ss" />
                    </div>
                    <div className="text-gray-500 text-xs">by {row.original.createdByName}</div>
                </div>
                <div className="text-sm">
                    <div className="text-gray-600">
                        更新: <FormattedTime timestamp={row.original.updatedAt} formatter="YYYY-MM-DD HH:mm:ss" />
                    </div>
                    <div className="text-gray-500 text-xs">by {row.original.updatedByName}</div>
                </div>
            </div>
        ),
        meta: {
            title: '时间信息',
        },
    },
    {
        accessorKey: 'actions',
        header: '操作',
        cell: ({ row }) => {
            const refresh = useSetAtom(refreshTableAtom)
            return (
                <div className="flex items-center space-x-2">
                    <Button variant="link" className="h-auto p-0 text-blue-600 text-xs" onClick={() => {
                        pushModal('EditPackageModal', {
                            pkg: row.original,
                            onSuccess: () => {
                                refresh(prev => prev + 1)
                            }
                        })
                    }}>编辑</Button>
                </div>
            )
        },
    }
]