"use client"

import { PackageStatus, SupplierOrderPackage, supplierOrderPackageApi } from "@/api/supplier/supplier-order/supplier-order-package-api"
import { pushModal } from "@/components/modals"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { ScrollArea } from "@/components/ui/scroll-area"
import { AlertCircle, ArrowLeft, FileText, CheckCircle } from 'lucide-react'
import { useEffect, useRef, useState } from "react"
import { Link, useParams } from "react-router-dom"

export default function SupplierOrderPackageDetailPage() {
    const { id } = useParams()
    const [scanInput, setScanInput] = useState("")
    const [packageData, setPackageData] = useState<SupplierOrderPackage | null>(null)
    const [error, setError] = useState("")
    const inputRef = useRef<HTMLInputElement>(null)

    // 获取包裹详情
    useEffect(() => {
        if (id) {
            loadPackageDetail()
        }
    }, [id])

    // 保持输入框焦点
    useEffect(() => {
        inputRef.current?.focus()
    }, [])

    // 处理扫描枪输入
    const handleKeyDown = async (e: React.KeyboardEvent<HTMLInputElement>) => {
        if (e.key === 'Enter') {
            e.preventDefault()
            const orderNo = scanInput.trim().toUpperCase()

            if (!orderNo || !packageData) return

            try {
                const response = await supplierOrderPackageApi.scanSupplierOrderPackage(orderNo, id!!)
                if (response.waybillUrl) {
                    if (response.canPrintWayBillPdf) {
                        const packageContent = response.packageContent
                        pushModal('PDFViewerModal', { pdfUrl: response.waybillUrl, title: `箱子号: ${response.index}`, packageContent: packageContent })
                    } else {
                        pushModal('TipSupplierPackageModals', { content: `请不要打印面单: ${response.stopPrintWayBillWarning}`, packageContent: response.packageContent })
                    }
                } else {
                    pushModal('TipSupplierPackageModals', { content: `请将货物放入箱子号: ${response.index} 中`, packageContent: response.packageContent })
                }
                await loadPackageDetail()
                setScanInput("")
                setError("")
                inputRef.current?.focus()
            } catch (err: any) {
                setError(err.message || "扫描失败")
                setScanInput("")
                inputRef.current?.focus()
            }
        }
    }

    const loadPackageDetail = async () => {
        try {
            const data = await supplierOrderPackageApi.getSupplierOrderPackageDetail(id!)
            setPackageData(data)
        } catch (err: any) {
            setError("加载包裹详情失败: " + err.message)
        }
    }

    const handleScan = async (e: React.FormEvent) => {
        e.preventDefault()
        const orderNo = scanInput.trim().toUpperCase()

        if (!orderNo || !packageData) return

        try {
            const response = await supplierOrderPackageApi.scanSupplierOrderPackage(orderNo, id!!)
            if (response.waybillUrl) {
                if (response.canPrintWayBillPdf) {
                    const packageContent = response.packageContent
                    pushModal('PDFViewerModal', { pdfUrl: response.waybillUrl, title: `箱子号: ${response.index}`, packageContent: packageContent })
                } else {
                    pushModal('TipSupplierPackageModals', { content: `请不要打印面单: ${response.stopPrintWayBillWarning}`, packageContent: response.packageContent })
                }
            } else {
                pushModal('TipSupplierPackageModals', { content: `请将货物放入箱子号: ${response.index} 中`, packageContent: response.packageContent })
            }
            await loadPackageDetail() // 重新加载数据
            setScanInput("")
            setError("")
        } catch (err: any) {
            setError(err.message || "扫描失败")
            setScanInput("")
        }
    }

    const handleComplete = async () => {
        if (!packageData) return

        try {
            await supplierOrderPackageApi.completeSupplierOrderPackage(id!!)
            await loadPackageDetail()
        } catch (err: any) {
            setError(err.message || "完成包裹失败")
        }
    }

    const handleForceCompleteBox = (wayBillRelation: string, boxIndex: number) => {
        if (!packageData) return

        pushModal('DoubleCheckModal', {
            title: '强制完成箱子确认',
            description: `确定要强制完成箱子号 ${boxIndex} 吗？\n\n此操作将标记该箱子中所有未完成的订单为已完成状态，无法撤销。`,
            onConfirm: async () => {
                try {
                    await supplierOrderPackageApi.forceCompleteBox(id!!, wayBillRelation)
                    await loadPackageDetail()
                    setError("")
                } catch (err: any) {
                    setError(err.message || "强制完成箱子失败")
                }
            },
            onCancel: () => {
                // 取消操作，不需要做任何事情
            }
        })
    }

    if (!packageData) {
        return <div className="flex justify-center items-center h-[600px]">加载中...</div>
    }

    return (
        <div className="container mx-auto p-4 max-w-screen-2xl">
            <Button
                variant="ghost"
                size="icon"
                className="mb-4"
                asChild
            >
                <Link to='/dashboard/order/supplier-order/package'>
                    <ArrowLeft className="h-4 w-4" />
                </Link>
            </Button>

            <div className="flex justify-between items-center mb-6">
                <div>
                    <h2 className="text-2xl font-bold">包裹详情 #{packageData.packageNo}</h2>
                    <p className="text-muted-foreground">
                        总数量: {packageData.scanCount} | 状态: {packageData.status}
                    </p>
                </div>
                {packageData.status !== PackageStatus.COMPLETED && (
                    <Button onClick={handleComplete}>完成包裹</Button>
                )}
            </div>

            <form onSubmit={handleScan} className="mb-6">
                <div className="flex gap-2">
                    <Input
                        ref={inputRef}
                        type="text"
                        value={scanInput}
                        onChange={(e) => setScanInput(e.target.value)}
                        onKeyDown={handleKeyDown}
                        placeholder="扫描订单号..."
                        className="text-lg"
                        autoComplete="off"
                        autoFocus
                    />
                    <Button
                        type="submit"
                        disabled={packageData.status === PackageStatus.COMPLETED}
                    >
                        确认
                    </Button>
                </div>
            </form>

            {error && (
                <Alert variant="destructive" className="mb-4">
                    <AlertCircle className="h-4 w-4" />
                    <AlertTitle>错误</AlertTitle>
                    <AlertDescription>{error}</AlertDescription>
                </Alert>
            )}

            <ScrollArea className="h-[600px] rounded-md border p-4">
                <div className="grid grid-cols-4 md:grid-cols-5 gap-4">
                    {Object.entries(packageData.packageContent)
                        .sort(([, a], [, b]) => a.index - b.index)
                        .map(([wayBillRelation, samePackageOrder]) => (
                            <Card
                                key={wayBillRelation}
                                className={`relative border-2 ${samePackageOrder.onComplete
                                    ? "border-green-500 "
                                    : "border-red-500 "
                                    }`}
                            >
                                <CardContent className="p-4">
                                    <div className="flex justify-between items-center mb-4">
                                        <h3 className="text-lg font-bold">箱子号: {samePackageOrder.index}</h3>
                                        <Badge variant={samePackageOrder.onComplete ? "default" : "destructive"}>
                                            {samePackageOrder.onComplete ? "完成" : "未完成"}
                                        </Badge>
                                    </div>

                                    <div className="mb-4 text-sm text-muted-foreground">
                                        <div>渠道: {samePackageOrder.channel}</div>
                                        <div>运输方式: {samePackageOrder.shipMethod}</div>
                                    </div>

                                    {samePackageOrder.wayBillUrl && samePackageOrder.canPrintWayBillPdf && (
                                        <div className="mb-4">
                                            <Button
                                                variant="outline"
                                                size="sm"
                                                className="w-full"
                                                onClick={() => pushModal('PDFViewerModal', { pdfUrl: samePackageOrder.wayBillUrl, title: `箱子号: ${samePackageOrder.index}`, packageContent: samePackageOrder })}
                                            >
                                                <FileText className="h-4 w-4 mr-2" />
                                                查看运单
                                            </Button>
                                        </div>
                                    )}
                                    {(!samePackageOrder.canPrintWayBillPdf && samePackageOrder.stopPrintWayBillWarning) && (
                                        <div className="mb-4">
                                            <Alert variant="destructive">
                                                <AlertCircle className="h-4 w-4" />
                                                <AlertTitle>运单已停止打印</AlertTitle>
                                                <AlertDescription>{samePackageOrder.stopPrintWayBillWarning}</AlertDescription>
                                            </Alert>
                                        </div>
                                    )}

                                    <div className="space-y-4">
                                        <div className="space-y-2">
                                            <div className="text-sm font-medium">已扫描订单:</div>
                                            <div className="grid grid-cols-1 gap-2">
                                                {Object.entries(samePackageOrder.completed).map(([orderNo, order]) => (
                                                    <div
                                                        key={orderNo}
                                                        className="bg-primary/10 text-primary rounded px-2 py-1"
                                                    >
                                                        <div>
                                                            {orderNo} ({order.scanCount}/{order.count})
                                                        </div>
                                                        <div className="text-xs font-bold text-muted-foreground">{order.title}</div>
                                                    </div>
                                                ))}
                                            </div>
                                        </div>

                                        {/* 未完成订单 */}
                                        {Object.keys(samePackageOrder.nonCompleted).length > 0 && (
                                            <div className="space-y-2">
                                                <div className="text-sm font-medium text-muted-foreground">
                                                    待扫描订单:
                                                </div>
                                                <div className="grid grid-cols-1 gap-2">
                                                    {Object.entries(samePackageOrder.nonCompleted).map(([orderNo, order]) => (
                                                        <div
                                                            key={orderNo}
                                                            className="border-2 border-dashed border-muted-foreground/50 rounded px-2 py-1 text-muted-foreground"
                                                        >
                                                            <div>
                                                                {orderNo} ({order.scanCount}/{order.count})
                                                            </div>
                                                            <div className="text-xs font-bold text-muted-foreground">{order.title}</div>
                                                        </div>
                                                    ))}
                                                </div>
                                            </div>
                                        )}

                                        {/* 强制完成按钮 - 只在箱子未完成且包裹未完成时显示 */}
                                        {!samePackageOrder.onComplete && packageData.status !== PackageStatus.COMPLETED && (
                                            <div className="pt-4 border-t">
                                                <Button
                                                    variant="outline"
                                                    size="sm"
                                                    className="w-full text-orange-600 border-orange-600 hover:bg-orange-50"
                                                    onClick={() => handleForceCompleteBox(wayBillRelation, samePackageOrder.index)}
                                                >
                                                    <CheckCircle className="h-4 w-4 mr-2" />
                                                    强制完成箱子
                                                </Button>
                                            </div>
                                        )}
                                    </div>
                                </CardContent>
                            </Card>
                        ))}
                </div>
            </ScrollArea>
        </div>
    )
}

