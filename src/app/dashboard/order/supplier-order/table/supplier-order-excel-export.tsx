import React, { useState } from 'react';
import { Button } from "@/components/ui/button";
import {
    Dialog,
    DialogContent,
    DialogDescription,
    DialogFooter,
    DialogHeader,
    DialogTitle,
    DialogTrigger,
} from "@/components/ui/dialog";
import { downloadSupplierOrder } from '@/api/supplier/supplier-order/supplier-order-api';
import { toast } from 'sonner';

interface SupplierOrderExcelExportProps {
    selectedRows: any[];
}

export const SupplierOrderExcelExport: React.FC<SupplierOrderExcelExportProps> = ({ selectedRows }) => {

    const [isDialogOpen, setIsDialogOpen] = useState(false);

    const [fileName, setFileName] = useState('');

    const selectedIds = selectedRows.map(row => row.original.id);

    const handleExport = async () => {
        toast.promise(downloadSupplierOrder({ ids: selectedIds, fileName: fileName }), {
            loading: '导出中...',
            success: '导出成功',
            error: '导出失败'
        });
        setIsDialogOpen(false);
    };

    if (selectedRows.length === 0) return null;

    return (
        <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
            <DialogTrigger asChild>
                <Button className='h-8'>导出Excel</Button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-[425px]">
                <DialogHeader>
                    <DialogTitle>确认导出</DialogTitle>
                    <DialogDescription>
                        您确定要导出选中的 {selectedRows.length} 条记录吗？
                    </DialogDescription>
                </DialogHeader>
                <div className="grid gap-4 py-4">
                    <div className="grid grid-cols-4 items-center gap-4">
                        <label htmlFor="fileName" className="text-right">
                            文件名
                        </label>
                        <input
                            id="fileName"
                            value={fileName}
                            onChange={(e) => setFileName(e.target.value)}
                            className="col-span-3 px-3 py-2 border rounded-md"
                            placeholder="请输入文件名"
                        />
                    </div>
                </div>
                <DialogFooter>
                    <Button variant="outline" onClick={() => setIsDialogOpen(false)}>
                        取消
                    </Button>
                    <Button onClick={handleExport}>确认</Button>
                </DialogFooter>
            </DialogContent>
        </Dialog>
    );
};
