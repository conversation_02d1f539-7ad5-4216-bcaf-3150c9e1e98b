import { SupplierOrderPageResponse } from "@/api/supplier/supplier-order/supplier-order-model";
import { ColumnDef } from "@tanstack/react-table";
import { Chip } from "@/components/buttons/chip";
import { FormattedTime } from "@/components/common/formatted-time";
import { match } from "ts-pattern";
import { Package, Hash, FileText, Phone, Building, AlertCircle } from "lucide-react";
import { CopyText } from "@/components/buttons/copy-text-btn";
import { Row } from "@tanstack/react-table";
import { useRequest } from "ahooks";
import { cancelSupplierOrder, completeSupplierOrder, deliverySupplierOrder, handleSupplierOrderException } from "@/api/supplier/supplier-order/supplier-order-api";
import { toast } from "sonner";
import { useSetAtom } from "jotai";
import { refreshTableAtom } from "@/state/common";
import React from "react";
import { Separator } from "@/components/ui/separator";
import { DoubleCheckBtn } from "@/components/buttons/double-check-btn";
import { InfoItem, AddressInfo } from "@/components/common/info-item";
import PdfView from "@/components/buttons/pdf-view";
import { ErrorDisplay } from "../../waybill/table/waybill-column";

interface SupplierOrderActionsProps {
    row: Row<SupplierOrderPageResponse>;
}

const SupplierOrderActions: React.FC<SupplierOrderActionsProps> = ({ row }) => {
    const refresh = useSetAtom(refreshTableAtom);

    const { runAsync: retry } = useRequest(handleSupplierOrderException, {
        manual: true,
    })


    const handlerCancelSupplierOrder = async () => {
        await cancelSupplierOrder(row.original.id).then(() => {
            toast.success("取消成功")
            refresh((prev) => prev + 1)
        }).catch((e) => {
            toast.error("取消失败: " + e.message)
        });
    }

    const handleCompleteSupplierOrder = async () => {
        await completeSupplierOrder(row.original.id).then(() => {
            toast.success("制作完成")
            refresh((prev) => prev + 1)
        }).catch((e) => {
            toast.error("制作失败: " + e.message)
        });
    }

    const handleDeliver = async () => {
        await deliverySupplierOrder(row.original.id).then(() => {
            toast.success("发货成功")
            refresh((prev) => prev + 1)
        }).catch((e) => {
            toast.error("发货失败: " + e.message)
        });
    }

    const handleRetry = async () => {
        toast.promise(
            retry(row.original.id),
            {
                loading: '重试中...',
                success: () => {
                    refresh((prev) => prev + 1);
                    return '重试成功';
                },
                error: (error) => {
                    return '重试失败: ' + error.message;
                },
            }
        );
    }

    return (
        <div className='flex items-center gap-2'>
            <DoubleCheckBtn
                className='h-auto p-0 text-blue-600 text-xs'
                variant='link'
                title='制作完成'
                description='确定要制作完成该订单吗？'
                onConfirm={handleCompleteSupplierOrder}
                buttonText='制作完成'
                onCancel={() => { }}
            />
            <Separator orientation='vertical' className='h-4' />
            <DoubleCheckBtn
                className='h-auto p-0 text-blue-600 text-xs'
                variant='link'
                title='发货'
                description='确定要发货该订单吗？'
                onConfirm={handleDeliver}
                buttonText='发货'
                onCancel={() => { }}
            />
            <Separator orientation='vertical' className='h-4' />
            <DoubleCheckBtn
                className='h-auto p-0 text-orange-600 text-xs'
                variant='link'
                title='重试订单'
                description='确定要重试该订单吗？'
                onConfirm={handleRetry}
                buttonText='重试'
                onCancel={() => { }}
            />
            <Separator orientation='vertical' className='h-4' />
            <DoubleCheckBtn
                className='h-auto p-0 text-orange-600 text-xs'
                variant='link'
                title='取消订单'
                description='确定要取消该订单吗？'
                onConfirm={handlerCancelSupplierOrder}
                buttonText='取消'
                onCancel={() => { }}
            />
        </div>
    );
};

// 状态匹配函数
const matchSupplierOrderStatus = (status: string) => {
    return match(status)
        .with("CREATED", () => <Chip variant="yellow" label="已创建" />)
        .with("MATCHED_SUPPLY", () => <Chip variant="blue" label="已匹配供应商" />)
        .with("PRODUCING", () => <Chip variant="purple" label="生产中" />)
        .with("PRODUCT_COMPLETED", () => <Chip variant="green" label="生产完成" />)
        .with("SHIPPED", () => <Chip variant="green" label="已发货" />)
        .with("FAILED", () => <Chip variant="red" label="失败" />)
        .with("CANCELLED", () => <Chip variant="red" label="已取消" />)
        .otherwise(() => <Chip variant="default" label={status} />);
};

export const SupplierOrderColumns: ColumnDef<SupplierOrderPageResponse>[] = [
    {
        accessorKey: "orderInfo",
        header: '订单信息',
        cell: ({ row }) => (
            <div className="space-y-1">
                <div className="flex items-center space-x-2">
                    <Hash className="h-4 w-4 text-gray-500" />
                    <CopyText value={row.original.orderNo} />
                </div>
                {row.original.supplierName && (
                    <div className="flex items-center space-x-2">
                        <Building className="h-4 w-4 text-gray-500" />
                        <span className="text-sm text-gray-600">{row.original.supplierName}</span>
                    </div>
                )}
                {row.original.title && (
                    <div className="flex items-center space-x-2">
                        <Package className="h-4 w-4 text-gray-500" />
                        <span className="text-sm text-gray-600">{row.original.title}</span>
                    </div>
                )}
                <div className="flex items-center space-x-2">
                    <FileText className="h-4 w-4 text-gray-500" />
                    <span className="text-sm text-gray-600">SKU: {row.original.skuCode}</span>
                </div>
                <div>
                    {matchSupplierOrderStatus(row.original.status)}
                </div>
            </div>
        ),
        meta: {
            title: '订单信息',
        },
    },
    {
        id: "orderNos",
    },
    {
        accessorKey: "supplierName",
        header: '供应商',
        cell: ({ row }) => (
            <div className="text-sm text-gray-600">{row.original.supplierName}</div>
        ),
        meta: {
            title: '供应商',
        },
    },
    {
        accessorKey: "errorInfo",
        header: '错误信息',
        cell: ({ row }) => row.original.errorMsg ? (
            <ErrorDisplay errorMsg={row.original.errorMsg} />
        ) : (
            <span className="text-gray-400 text-xs">-</span>
        ),
        meta: {
            title: '错误信息',
        },
    },
    {
        accessorKey: "addressInfo",
        header: '地址信息',
        cell: ({ row }) => {
            const recipient = {
                address1: row.original.address1,
                address2: row.original.address2,
                city: row.original.city,
                state: row.original.state,
                country: row.original.country,
                postcode: row.original.postcode
            };

            return (
                <div className="space-y-1">
                    <InfoItem
                        icon={Phone}
                        text={row.original.phone}
                        tooltip="联系电话"
                    />
                    <AddressInfo recipient={recipient} />
                </div>
            );
        },
        meta: {
            title: '地址信息',
        },
    },
    {
        accessorKey: "shippingInfo",
        header: '配送信息',
        cell: ({ row }) => (
            <div className="space-y-1">
                <div className="flex items-center space-x-2">
                    <Package className="h-4 w-4 text-orange-500" />
                    <span className="font-medium">{row.original.shipMethod}</span>
                </div>
                {row.original.canPrintWayBillPdf && (
                    <div className="text-sm text-gray-600">
                        <div>渠道: {row.original.channel}</div>
                        <div>配送方式: {row.original.shipMethodAlias}</div>
                    </div>
                )}
                {row.original.waybillLabelUrl && row.original.canPrintWayBillPdf && (
                    <div className="mt-2 flex items-center gap-2">
                        <div className="h-5 w-0.5 bg-gray-200 rounded-full" />
                        <PdfView pdfUrl={row.original.waybillLabelUrl} />
                    </div>
                )}
                {!row.original.canPrintWayBillPdf && (
                    <div className="mt-2 flex flex-col gap-2">
                        <div className="flex items-center gap-2">
                            <AlertCircle className="h-4 w-4 text-red-500" />
                            <span className="text-sm text-red-500 font-bold">运单已停止打印: </span>
                        </div>
                        <span className="text-sm text-red-500">{row.original.stopPrintWayBillWarning}</span>
                    </div>
                )}
            </div>
        ),
        meta: {
            title: '配送信息',
        },
    },
    {
        accessorKey: "timeInfo",
        header: () => {
            return (
                <div className="flex items-center space-x-2 min-w-[180px]">
                    <span className="">时间信息</span>
                </div>
            )
        },
        cell: ({ row }) => (
            <div className="space-y-1">
                <div className="text-sm">
                    <div className="text-gray-600">
                        创建: <FormattedTime timestamp={row.original.createdAt} formatter="YYYY-MM-DD HH:mm:ss" />
                    </div>
                    <div className="text-gray-500 text-xs">by {row.original.createdByName}</div>
                </div>
                <div className="text-sm">
                    <div className="text-gray-600">
                        更新: <FormattedTime timestamp={row.original.updatedAt} formatter="YYYY-MM-DD HH:mm:ss" />
                    </div>
                    <div className="text-gray-500 text-xs">by {row.original.updatedByName}</div>
                </div>
                {row.original.scanAt && (
                    <div className="text-sm">
                        <div className="text-gray-600">
                            扫描: {row.original.scanAt}
                        </div>
                    </div>
                )}
            </div>
        ),
        meta: {
            title: '时间信息',
        },
    },
    {
        accessorKey: "actions",
        header: "操作",
        cell: ({ row }) => <SupplierOrderActions row={row} />,
        meta: {
            title: '操作',
        },
    },
] as const;
