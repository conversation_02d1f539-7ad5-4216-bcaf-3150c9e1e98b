import useSearchParamsManager from "@/hooks/use-url-param";
import {DataTable} from "@/components/table/base-table";
import {DataTableToolbar, ToolbarItem, ToolbarLeft, ToolbarRight} from "@/components/table/toolbar/data-table-toolbar";
import {SupplierOrderColumns} from "./table/supplier-order-column";
import {batchSupplierOrderExceptionHandler, pageSupplierOrder} from "@/api/supplier/supplier-order/supplier-order-api";
import {SelectWithInput} from "@/components/ui/select-with-input";
import {DataTableUrlFilter} from "@/components/select/data-table-column-filter";
import {DateRangePicker} from "@/components/ui/date-range-picker";
import dayjs from "dayjs";
import {DataTableViewOptions} from "@/components/table/toolbar/data-table-view-options";
import {SupplierOrderExcelExport} from "./table/supplier-order-excel-export";
import {SupplierOrderLabelExport} from "./table/supplier-order-label-export";
import {selectSupplier} from "@/api/supplier/supplier-api";
import {useRequest} from "ahooks";
import {ExpandableTabs} from "@/components/ui/expandable-tabs";
import {RotateCcw} from "lucide-react";
import {toast} from "sonner";
import {useSetAtom} from "jotai";
import {refreshTableAtom} from "@/state/common";
import {Textarea} from "@/components/ui/textarea";
import { CountrySelect } from "@/components/select/country-select";

export default function SupplierOrderPage() {

    const { addParam, getParam } = useSearchParamsManager();

    const { data: suppliers } = useRequest(selectSupplier, {
        manual: false,
    })


    const refresh = useSetAtom(refreshTableAtom)

    return (
        <div className="flex flex-col gap-4">
            <div className="flex items-center justify-between">
                <h1 className="text-2xl font-bold">供应商订单管理</h1>
            </div>
            <div className="rounded-lg border shadow-sm p-4">
                <DataTable
                    columns={SupplierOrderColumns}
                    onFetch={param => pageSupplierOrder(param)}
                    isFixedHeader={false}
                    isNeedSelect={true}
                    containerHeight="620px"
                    toolbar={(table, tableId) => (
                        <DataTableToolbar table={table} tableId={tableId}>
                            <ToolbarLeft>
                                <div className="flex flex-col gap-2">
                                    <div className="flex flex-row gap-2">
                                        <ToolbarItem>
                                            <SelectWithInput
                                                options={[
                                                    { label: "订单号", value: "orderNo" },
                                                    { label: "SPU", value: "spu" },
                                                    { label: "上传文件名", value: "fileName" },
                                                    { label: "主任务编号", value: "mainOrderId" },
                                                ]}
                                                inputPlaceholder="请输入搜索内容"
                                            />
                                        </ToolbarItem>
                                        <ToolbarItem>
                                            <DataTableUrlFilter
                                                paramKey="status"
                                                title="任务状态"
                                                options={[
                                                    { label: '已创建', value: 'CREATED' },
                                                    { label: '已匹配供应商', value: 'MATCHED_SUPPLY' },
                                                    { label: '生产中', value: 'PRODUCING' },
                                                    { label: '生产完成', value: 'PRODUCT_COMPLETED' },
                                                    { label: '已发货', value: 'SHIPPED' },
                                                    { label: '失败', value: 'FAILED' },
                                                    { label: '已取消', value: 'CANCELLED' }
                                                ]}
                                            />
                                        </ToolbarItem>
                                        <ToolbarItem>
                                            <DataTableUrlFilter
                                                paramKey="supplierId"
                                                title="供应商"
                                                options={suppliers?.map(supplier => ({ label: supplier.name, value: supplier.id })) || []}
                                            />
                                        </ToolbarItem>
                                        <ToolbarItem>
                                            <CountrySelect
                                                value={getParam('country') || ''}
                                                onValueChange={(value, code) => {
                                                    addParam('country', value);
                                                    addParam('countryCode', code);
                                                }}
                                                placeholder="选择国家"
                                                className="w-48"
                                            />
                                        </ToolbarItem>
                                        <ToolbarItem>
                                            <DateRangePicker
                                                onUpdate={(values) => {
                                                    addParam('createdAtFrom', dayjs(values.range.from).format('YYYY-MM-DD'))
                                                    addParam('createdAtTo', dayjs(values.range.to).format('YYYY-MM-DD'))
                                                }}
                                                initialDateFrom={getParam('createdAtFrom') ? dayjs(getParam('createdAtFrom')).toDate() : dayjs().subtract(365, 'day').toDate()}
                                                initialDateTo={getParam('createdAtTo') ? dayjs(getParam('createdAtTo')).toDate() : dayjs().toDate()}
                                                align="start"
                                                locale="zh-CN"
                                                showCompare={false}
                                            />
                                        </ToolbarItem>
                                    </div>
                                    <div>
                                        <ToolbarItem>
                                            <div className="flex items-center">
                                                <ToolbarItem>
                                                    <Textarea
                                                        placeholder="搜索订单,多个订单用空行隔开..."
                                                        className="text-xs w-96 min-h-[60px]"
                                                        defaultValue={table.getColumn('orderNos')?.getFilterValue() as string | undefined || ''}
                                                        onChange={(e) => {
                                                            table.getColumn('orderNos')?.setFilterValue(e.target.value);
                                                        }}
                                                    />
                                                </ToolbarItem>
                                            </div>
                                        </ToolbarItem>
                                    </div>
                                </div>
                            </ToolbarLeft>

                            <ToolbarRight>
                                <ToolbarItem>
                                    <SupplierOrderExcelExport selectedRows={table.getSelectedRowModel().rows} />
                                </ToolbarItem>
                                <ToolbarItem>
                                    <SupplierOrderLabelExport selectedRows={table.getSelectedRowModel().rows} />
                                </ToolbarItem>
                                <ToolbarItem>
                                    <DataTableViewOptions table={table} tableId={tableId} />
                                </ToolbarItem>
                            </ToolbarRight>

                            {table.getSelectedRowModel().rows.length > 0 && (
                                // <Dock>
                                //     <DockItem icon={Truck} label="选择渠道" onClick={() => handleChangeChannel(table.getSelectedRowModel().rows.map((row: any) => row.original.id))} />
                                //     <DockItem icon={Merge} label="合并" onClick={() => handleMerge(table.getSelectedRowModel().rows.map((row: any) => row.original.id))} />
                                //     <DockItem icon={Trash2} label="删除" onClick={() => handleDelete(table.getSelectedRowModel().rows.map((row: any) => row.original.id))} />
                                // </Dock>
                                <ExpandableTabs tabs={[
                                    {
                                        title: '重试',
                                        icon: RotateCcw,
                                        onClick: async () => {
                                            toast.promise(
                                              batchSupplierOrderExceptionHandler(table.getSelectedRowModel().rows.map(row => row.original.id)),
                                                {
                                                    loading: '重试中...',
                                                  success: () => {
                                                        refresh(prev => prev + 1);
                                                        table.setRowSelection({});
                                                        return '重试成功';
                                                    },
                                                    error: (error) => {
                                                        return '重试失败:' + error.message;
                                                    }
                                                }
                                            )
                                        }
                                    },
                                ]}
                                />
                            )}

                        </DataTableToolbar>
                    )}
                    dependencies={[]}
                />
            </div>
        </div>
    )
}