import { Supplier<PERSON>ainOrder, supplierMainOrderApi } from "@/api/order/supplier-main-order/supplier-main-order";
import { selectSupplier } from "@/api/supplier/supplier-api";
import { Chip } from "@/components/buttons/chip";
import { FormattedTime } from "@/components/common/formatted-time";
import useSearchParamsManager from "@/hooks/use-url-param";
import { pushModal } from "@/components/modals";
import { DataTableUrlFilter } from "@/components/select/data-table-column-filter";
import { DataTable } from "@/components/table/base-table";
import { DataTableToolbar, ToolbarItem, ToolbarLeft, ToolbarRight } from "@/components/table/toolbar/data-table-toolbar";
import { DataTableViewOptions } from "@/components/table/toolbar/data-table-view-options";
import { Button } from "@/components/ui/button";
import { DateRangePicker } from "@/components/ui/date-range-picker";
import { ExpandableTabs } from "@/components/ui/expandable-tabs";
import { SelectWithInput } from "@/components/ui/select-with-input";
import { refreshTableAtom, userInfoAtom } from "@/state/common";
import { ColumnDef } from "@tanstack/react-table";
import { useRequest } from "ahooks";
import dayjs from "dayjs";
import { useAtomValue, useSetAtom } from "jotai";
import { toast } from "sonner";
import { Link } from "react-router-dom";
import { ChevronDown, ChevronUp, ExternalLink, Factory, FileDown, Paperclip, Upload } from "lucide-react";
import { useState } from "react";
import { Separator } from "@/components/ui/separator";
import { userOnNotSupplier } from "@/api/user/user-api";

const columns: ColumnDef<SupplierMainOrder>[] = [
    {
        accessorKey: "mainOrderId",
        header: "主订单ID",
        cell: ({ row }) => {
            return (
                <Link to={`/dashboard/order/supplier-order?mainOrderId=${row.original.mainOrderId}&supplierId=${row.original.supplierId}`} className="text-blue-600 hover:underline">
                    {row.original.mainOrderId}
                </Link>
            )
        },
        meta: {
            title: "主订单ID",
        }
    },
    {
        accessorKey: "supplierName",
        header: "供应商",
        cell: ({ row }) => {
            return <div className="text-sm text-gray-600">{row.original.supplierName}</div>
        },
    },
    {
        accessorKey: "fileName",
        header: "文件名",
        meta: {
            title: "文件名",
        }
    },
    {
        accessorKey: "attachments",
        header: "附件",
        cell: ({ row }) => {
            const { attachmentUrls } = row.original;
            const [expanded, setExpanded] = useState(false);

            if (!attachmentUrls || attachmentUrls.length === 0) {
                return <span className="text-gray-400 text-xs">无附件</span>;
            }

            const displayUrls = expanded ? attachmentUrls : attachmentUrls.slice(0, 2);

            return (
                <div className="space-y-1">
                    {displayUrls.map((url, index) => {
                        const fileName = url.split('/').pop() || `附件${index + 1}`;
                        return (
                            <div key={index} className="flex items-center space-x-1">
                                <Paperclip className="h-3 w-3 text-blue-500" />
                                <a
                                    href={url}
                                    target="_blank"
                                    rel="noopener noreferrer"
                                    className="text-xs text-blue-600 hover:text-blue-800 hover:underline truncate max-w-[100px]"
                                    title={fileName}
                                >
                                    {fileName}
                                </a>
                                <ExternalLink className="h-3 w-3 text-gray-400" />
                            </div>
                        );
                    })}
                    {attachmentUrls.length > 2 && (
                        <button
                            onClick={() => setExpanded(!expanded)}
                            className="flex items-center text-xs text-blue-600 hover:text-blue-800 mt-1 cursor-pointer"
                        >
                            {expanded ? (
                                <>
                                    <ChevronUp className="h-3 w-3 mr-1" />
                                    收起
                                </>
                            ) : (
                                <>
                                    <ChevronDown className="h-3 w-3 mr-1" />
                                    查看全部 ({attachmentUrls.length})
                                </>
                            )}
                        </button>
                    )}
                </div>
            );
        },
        meta: {
            title: "附件",
        },
    },
    {
        accessorKey: "accepted",
        header: "是否接受",
        cell: ({ row }) => {
            return <Chip variant={row.original.accepted ? "green" : "red"} label={row.original.accepted ? "是" : "否"} />
        },
    },
    {
        accessorKey: "createdAt",
        header: "创建时间",
        meta: {
            title: "创建时间",
        },
        cell: ({ row }) => {
            return <FormattedTime timestamp={row.getValue('createdAt')} formatter='DD/MM/YY HH:mm' className='text-gray-500' />
        }
    },
    {
        accessorKey: "updatedAt",
        header: "更新时间",
        cell: ({ row }) => {
            return <FormattedTime timestamp={row.getValue('updatedAt')} formatter='DD/MM/YY HH:mm' className='text-gray-500' />
        },
        meta: {
            title: "更新时间",
        }
    },
    {
        accessorKey: 'actions',
        header: '操作',
        cell: ({ row }) => {
            const setRefreshTable = useSetAtom(refreshTableAtom);
            const user = useAtomValue(userInfoAtom);
            return (
                <div className='flex items-center gap-2'>
                    <Button
                        variant="link"
                        className="h-auto p-0 text-blue-600 text-xs" onClick={async () => {
                            try {
                                await supplierMainOrderApi.acceptSupplierMainOrder(row.original.id)
                                toast.success('接受订单成功')
                                setRefreshTable(prev => prev + 1)
                            } catch (error) {
                                toast.error('接受订单失败:' + error)
                            }
                        }}>接受订单</Button>
                    { }
                    {userOnNotSupplier(user) && (
                        <>
                            <Separator orientation="vertical" className="h-4" />
                            <Button
                                variant="link"
                                className="h-auto p-0 text-blue-600 text-xs"
                                onClick={() => {
                                    pushModal('UploadAttachmentModal', {
                                        orderId: row.original.id,
                                        onSuccess: () => {
                                            setRefreshTable(prev => prev + 1);
                                        },
                                        onClose: () => {
                                            setRefreshTable(prev => prev + 1);
                                        },
                                        isOpen: true,
                                    });
                                }}
                            >
                                上传附件
                            </Button>
                        </>
                    )}

                </div>
            )
        },
        meta: {
            title: '操作',
        },
    }
];

export default function SupplierMainOrderPage() {
    const { addParam, getParam } = useSearchParamsManager();

    const { data: suppliers } = useRequest(selectSupplier, {
        manual: false,
    })

    const handleExportBill2 = async (ids: string[]) => {
        if (ids.length === 0) {
            toast.error('请先选择订单')
            return
        }
        toast.promise(supplierMainOrderApi.exportBill2({ supplierMainOrderIds: ids }), {
            loading: '正在创建导出账单任务...',
            success: '创建导出任务成功',
            error: (error) => `导出账单失败: ${error.message || '未知错误'}`,
        })
    }

    return (
        <div className="flex flex-col gap-4">
            <div className="flex items-center justify-between">
                <h1 className="text-2xl font-bold">供应商主订单管理</h1>
            </div>
            <div className="rounded-lg border shadow-sm p-4">
                <DataTable
                    columns={columns}
                    isNeedSelect={true}
                    onFetch={param => supplierMainOrderApi.pageSupplierMainOrder(param)}
                    isFixedHeader={false}
                    containerHeight="620px"
                    toolbar={(table, tableId) => (
                        <DataTableToolbar table={table} tableId={tableId}>
                            <ToolbarLeft>
                                <ToolbarItem>
                                    <SelectWithInput
                                        options={[
                                            { label: "主订单ID", value: "mainOrderId" },
                                            { label: "文件名", value: "fileName" },
                                        ]}
                                        inputPlaceholder="请输入搜索内容"
                                    />
                                </ToolbarItem>
                                <ToolbarItem>
                                    <DataTableUrlFilter
                                        paramKey="supplierIds"
                                        title="供应商"
                                        options={suppliers?.map(supplier => ({ label: supplier.name, value: supplier.id })) || []}
                                    />
                                </ToolbarItem>
                                <ToolbarItem>
                                    <DateRangePicker
                                        onUpdate={(values) => {
                                            addParam('createdAtFrom', dayjs(values.range.from).format('YYYY-MM-DD'))
                                            addParam('createdAtTo', dayjs(values.range.to).format('YYYY-MM-DD'))
                                        }}
                                        initialDateFrom={getParam('createdAtFrom') ? dayjs(getParam('createdAtFrom')).toDate() : dayjs().subtract(365, 'day').toDate()}
                                        initialDateTo={getParam('createdAtTo') ? dayjs(getParam('createdAtTo')).toDate() : dayjs().toDate()}
                                        align="start"
                                        locale="zh-CN"
                                        showCompare={false}
                                    />
                                </ToolbarItem>
                            </ToolbarLeft>

                            <ToolbarRight>
                                {table.getSelectedRowModel().rows.length > 0 && (
                                    <ExpandableTabs tabs={[{
                                        title: '切换工厂',
                                        icon: Factory,
                                        onClick: () => {
                                            console.log(table.getSelectedRowModel().rows.map(row => row.original.id))
                                            pushModal('ChangeSupplierOrderSupplierModals', {
                                                orderId: table.getSelectedRowModel().rows.map(row => row.original.id)[0]
                                            })
                                        }
                                    }, {
                                        title: '导出账单',
                                        icon: FileDown,
                                        onClick: () => {
                                            pushModal('DoubleCheckModal', {
                                                title: '导出账单',
                                                description: '是否确认导出这些账单？',
                                                children: '已选择: ' + table.getSelectedRowModel().rows.length + ' 个订单',
                                                onConfirm: () => handleExportBill2(table.getSelectedRowModel().rows.map(row => row.original.id)),
                                                onCancel: () => {
                                                }
                                            })
                                        }
                                    }]}
                                    />
                                )}
                                <ToolbarItem>
                                    <DataTableViewOptions table={table} tableId={tableId} />
                                </ToolbarItem>
                            </ToolbarRight>
                        </DataTableToolbar>
                    )}
                    dependencies={[]}
                />
            </div>
        </div>
    )
}
