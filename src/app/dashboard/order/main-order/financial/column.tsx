"use client"

import { WaybillPageResponse } from "@/api/order/waybill/waybill-model";
import { Di<PERSON>, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { ColumnDef } from "@tanstack/react-table";
import { OrderNumbers } from "../../waybill/table/waybill-column";

export const columns: ColumnDef<WaybillPageResponse>[] = [
    {
        accessorKey: "orderNos",
        header: "订单号",
        cell: ({ row }) => {
            return (
                <div className="space-y-2">
                    <OrderNumbers orderNos={row.original.orderNos} />
                </div>
            )
        },
    },
    {
        accessorKey: "quickChannelTax",
        header: "快速通道费",
        cell: ({ row }) => (
            <div className="font-medium">
                ¥{row.original.quickChannelTax.toLocaleString()}
            </div>
        ),
    },
    {
        accessorKey: "customerNeedPayCost",
        header: "客户支付费用",
        cell: ({ row }) => (
            <div className="font-medium">
                ¥{row.original.customerNeedPayCost.toLocaleString()}
            </div>
        ),
    },
    {
        accessorKey: "customerNeedPayTax",
        header: "客户支付税费",
        cell: ({ row }) => (
            <div className="font-medium">
                ¥{row.original.customerNeedPayTax.toLocaleString()}
            </div>
        ),
    },
    {
        accessorKey: "calculateCustomerNeedPayCostFlow",
        header: "费用计算流程",
        cell: ({ row }) => {
            const flow = row.original.calculateCustomerNeedPayCostFlow;
            if (!flow) return <div>-</div>;

            return (
                <Dialog>
                    <DialogTrigger asChild>
                        <div className="max-w-[400px] truncate cursor-pointer hover:text-primary">
                            {flow}
                        </div>
                    </DialogTrigger>
                    <DialogContent className="max-h-[80vh]">
                        <DialogHeader>
                            <DialogTitle>费用计算流程详情</DialogTitle>
                        </DialogHeader>
                        <div className="mt-6 whitespace-pre-wrap overflow-y-auto max-h-[calc(80vh-100px)]">
                            {flow}
                        </div>
                    </DialogContent>
                </Dialog>
            );
        },
    },
] as const;

