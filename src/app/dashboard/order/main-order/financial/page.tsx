import {correctWaybillCost, exportWaybillFinance, pageWaybill, staticWaybill} from "@/api/order/waybill/waybill-api"
import {Badge} from "@/components/ui/badge"
import {Button} from "@/components/ui/button"
import {Card, CardContent, CardHeader, CardTitle} from "@/components/ui/card"
import {SelectWithInput} from "@/components/ui/select-with-input"
import useSearchParamsManager from "@/hooks/use-url-param"
import {useRequest} from "ahooks"
import {ArrowLeft, Banknote, Calculator, FileText, Receipt} from "lucide-react"
import {useNavigate, useParams, useSearchParams} from "react-router-dom"
import {columns} from "./column"
import {DataTable} from "./data-table"
import {useEffect} from "react"
import {AxiosError} from "axios";
import {toast} from "sonner";
import {pushModal} from "@/components/modals";

export default function OrderFinancePage() {
    const { id } = useParams<{ id: string }>()
    const [searchParams] = useSearchParams()
    const navigate = useNavigate()
    const title = searchParams.get('title')
    const { getParam } = useSearchParamsManager()

  const {data: statisc, refreshAsync: refreshStatisticAsync} = useRequest(staticWaybill, {
        defaultParams: [id ?? '']
    })

    // 获取所有可能的搜索参数
    const orderNos = getParam('orderNos')
    const spu = getParam('spu')
    const fileName = getParam('fileName')
    const mainOrderId = getParam('mainOrderId') || id

    const { data: waybillData, run: fetchWaybillData } = useRequest(pageWaybill, {
        defaultParams: [{
            filters: {},
            sorting: [],
            pagination: {
                pageIndex: 0,
                pageSize: 100
            },
            searchParams: {
                mainOrderId,
                orderNos,
                spu,
                fileName,
            }
        }],
        debounceWait: 100,
        manual: true
    })

    useEffect(() => {
        fetchWaybillData({
            filters: {},
            sorting: [],
            pagination: {
                pageIndex: 0,
                pageSize: 100
            },
            searchParams: {
                mainOrderId,
                orderNos,
                spu,
                fileName,
            }
        })
    }, [fetchWaybillData, fileName, mainOrderId, orderNos, spu]);

    const handleExportWaybillFinance = () => {
        exportWaybillFinance(id!).then(res => {
            const blob = new Blob([res], {type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'})
            const url = window.URL.createObjectURL(blob)
            const a = document.createElement('a')
            a.href = url
            a.download = `财务数据-${id}.xlsx`
            document.body.appendChild(a)
            a.click()
            document.body.removeChild(a)
            window.URL.revokeObjectURL(url)
            toast.success("导出财务数据成功")
        }).catch(err => {
            console.error("导出财务数据失败:", err)
            console.log(typeof err,err.response)
            if (err instanceof AxiosError) {
                toast.error("导出财务数据失败: " + (err.response?.data?.msg || err.message || "未知错误"))
            } else {
                toast.error("导出财务数据失败: " + (err instanceof Error ? err.message : "未知错误"))
            }
        })
    }

  const handleCorrectClick = () => {
    pushModal("SimpleNumberInputModal", {
      title: "修正费用",
      decimalPlaces: 2,
      onConfirm: (value) => {
        if (value === undefined) {
          return;
        }
        correctWaybillCost(id!, value.toString()).then(() => {
          toast.success("修正费用完毕")
          return refreshStatisticAsync()
        }).catch(err => {
          if (err instanceof AxiosError) {
            toast.error("修正费用失败: " + (err.response?.data?.msg || err.message || "未知错误"))
          } else {
            toast.error("修正费用失败: " + (err instanceof Error ? err.message : "未知错误"))
          }
        })
      }
    })
  }

    // 使用API返回的统计数据
    const totals = {
        expressCharge: statisc?.totalQuickChannelTax ?? 0,
        customerPayment: statisc?.totalCustomerNeedPayCost ?? 0,
        tax: statisc?.totalCustomerNeedPayTax ?? 0,
      correct: statisc?.correctWaybillCost ?? 0,
        total: (statisc?.totalQuickChannelTax ?? 0) +
            (statisc?.totalCustomerNeedPayCost ?? 0) +
          (statisc?.totalCustomerNeedPayTax ?? 0) +
          (statisc?.correctWaybillCost ?? 0)
    }

    return (
        <div className="min-h-screen bg-gradient-to-b from-muted/50 to-background">
            <div className="container mx-auto py-8 space-y-8">
                {/* 订单信息 */}
                <div className="flex flex-col space-y-4 bg-background p-6 rounded-lg border shadow-sm">
                    <div className="flex items-center justify-between">
                        <div className="flex items-center gap-4">
                            <Button
                                variant="ghost"
                                size="icon"
                                onClick={() => navigate(-1)}
                                className="h-8 w-8"
                            >
                                <ArrowLeft className="h-4 w-4" />
                            </Button>
                            <div className="space-y-1">
                                <h1 className="text-3xl font-bold tracking-tight">订单财务详情</h1>
                                <p className="text-muted-foreground">查看和管理订单相关的所有财务信息</p>
                            </div>
                        </div>
                        <Badge variant="secondary" className="h-7 px-3 flex items-center gap-2">
                            <FileText className="h-4 w-4" />
                            {title}
                        </Badge>
                    </div>
                    <div className="flex items-center gap-2 text-sm">
                        <Badge variant="outline" className="h-6">
                            订单号: {id}
                        </Badge>
                        <Badge variant="outline" className="h-6">
                            运单数量: {waybillData?.total ?? 0}
                        </Badge>
                    </div>
                </div>

                {/* 统计卡片 */}
              <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-5">
                    <Card className="overflow-hidden">
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">累计快速通道费</CardTitle>
                            <div className="rounded-full p-2 bg-primary/10">
                                <Banknote className="h-4 w-4 text-primary" />
                            </div>
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">¥{totals.expressCharge.toLocaleString()}</div>
                        </CardContent>
                    </Card>
                    <Card className="overflow-hidden">
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">累计客户支付费用</CardTitle>
                            <div className="rounded-full p-2 bg-primary/10">
                                <Receipt className="h-4 w-4 text-primary" />
                            </div>
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">¥{totals.customerPayment.toLocaleString()}</div>
                        </CardContent>
                    </Card>
                    <Card className="overflow-hidden">
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">累计税费</CardTitle>
                            <div className="rounded-full p-2 bg-primary/10">
                                <Calculator className="h-4 w-4 text-primary" />
                            </div>
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">¥{totals.tax.toLocaleString()}</div>
                        </CardContent>
                    </Card>
                <Card className="overflow-hidden">
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">修正费用</CardTitle>
                    <div className="rounded-full p-2 bg-primary/10">
                      <Calculator className="h-4 w-4 text-primary"/>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold underline cursor-pointer" onClick={handleCorrectClick}>¥{totals.correct.toLocaleString()}</div>
                  </CardContent>
                </Card>
                    <Card className="overflow-hidden bg-primary">
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2 border-b border-primary-foreground/10">
                            <CardTitle className="text-sm font-medium text-primary-foreground">费用总计</CardTitle>
                            <div className="rounded-full p-2 bg-primary-foreground/10">
                                <Banknote className="h-4 w-4 text-primary-foreground" />
                            </div>
                        </CardHeader>
                        <CardContent className="mt-2">
                            <div className="text-2xl font-bold text-primary-foreground">¥{totals.total.toLocaleString()}</div>
                        </CardContent>
                    </Card>
                </div>

                {/* 数据表格 */}
                <div className="bg-background rounded-xl border shadow-sm">
                    <div className="p-4 border-b flex flex-row items-center justify-between">
                        <div>
                            <SelectWithInput
                              options={[
                                  {label: "订单号", value: "orderNos"},
                                  {label: "SPU", value: "spu"},
                                  {label: "上传文件名", value: "fileName"},
                                  {label: "主任务编号", value: "mainOrderId"},
                              ]}
                              inputPlaceholder="请输入搜索内容"
                            />
                        </div>
                        <div>
                            <Button variant="outline" size="sm" onClick={handleExportWaybillFinance}>
                                <FileText className="h-4 w-4 mr-2"/>
                                导出数据
                            </Button>
                        </div>
                    </div>
                    <DataTable columns={columns} data={waybillData?.content ?? []} />
                </div>
            </div>
        </div>
    )
}

