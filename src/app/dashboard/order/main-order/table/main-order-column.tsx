import { MainOrder, mainOrderApi } from '@/api/order/mainorder/main-order-api';
import { Chip } from '@/components/buttons/chip';
import { CopyText } from '@/components/buttons/copy-text-btn';
import { DoubleCheckBtn } from '@/components/buttons/double-check-btn';
import { FormattedTime } from '@/components/common/formatted-time';
import { pushModal } from '@/components/modals';
import SimpleMultiStateProgress from '@/components/progress/simple-multi-state-progress';
import { DefaultSuccessNotification } from '@/components/toast/default-notification';
import { Button } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuSeparator, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { refreshTable<PERSON>tom } from '@/state/common';
import { ColumnDef, Row } from '@tanstack/react-table';
import * as clipboard from "clipboard-polyfill";
import { useSetAtom } from 'jotai';
import { AlertCircleIcon, Calendar, CheckCircle2Icon, CopyIcon, FileIcon, Hash, HelpCircleIcon, LoaderIcon, UserIcon, WrenchIcon, ZapIcon, Paperclip, ExternalLink, BarChart3, MoreHorizontal, ChevronDown, ChevronUp } from 'lucide-react';
import { Link } from 'react-router-dom';
import { toast } from 'sonner';
import { match } from 'ts-pattern';
import { cn } from "@/lib/utils.ts";
import { useState } from 'react';
// import {MainOrderStatisticsModal} from "@/components/modals/main-order/main-order-statistics-modal";

export enum MainOrderType {
    IMAGE = 'IMAGE',
    ORDER = 'ORDER',
}

export const MainOrderColumns: ColumnDef<MainOrder>[] = [
    {
        id: 'basicInfo',
        accessorKey: 'id',
        header: () => <div className='flex items-center'>
            <div>基本信息</div>
        </div>,
        size: 280,
        cell: ({ row }) => {
            const { id, type, fileName, customerName } = row.original;
            return (
                <div className="space-y-2">
                    <div className="flex items-center space-x-2">
                        <Hash className="h-4 w-4 text-gray-500" />
                        <CopyText value={id} />
                        {match(type)
                            .with(MainOrderType.IMAGE, () => <Chip variant={'blue'} label='图片订单' showIcon={false} />)
                            .with(MainOrderType.ORDER, () => <Chip variant={'purple'} label='普通订单' showIcon={false} />)
                            .otherwise(() => null)}
                    </div>
                    <div className="flex items-center space-x-2">
                        <FileIcon className="h-4 w-4 text-gray-500" />
                        <span className="text-sm text-gray-600">{fileName}</span>
                    </div>
                    <div className="flex items-center space-x-2">
                        <UserIcon className="h-4 w-4 text-gray-500" />
                        <span className="text-sm text-gray-600">{customerName}</span>
                    </div>
                </div>
            )
        },
        meta: {
            title: '基本信息',
        },
    },
    {
        id: 'status',
        accessorKey: 'status',
        header: () => <div className=''>
            状态信息
        </div>,
        size: 200,
        cell: ({ row }) => {
            const { status, type, wayBillPushed, supplierPushed } = row.original;
            return (
                <div className="space-y-2">
                    <div>
                        {match(status)
                            .with('CREATED', () => <Chip variant={'yellow'} label='已创建' />)
                            .with('COMPLETED', () => <Chip variant={'green'} label='已完成' />)
                            .with('CANCELD', () => <Chip variant={'pink'} label='已取消' />)
                            .with('FAILED', () => <Chip variant={'pink'} label='失败' />)
                            .with('MATCHING', () => <Chip variant={'yellow'} label='匹配供应商中' />)
                            .with('PARTIALLY_MATCHED', () => <Chip variant={'red'} label='部分匹配供应商' />)
                            .with('MATCHED', () => <Chip variant={'green'} label='全部匹配供应商' />)
                            .otherwise(() => null)}
                    </div>
                    {type === MainOrderType.ORDER && (
                        <div className="flex gap-2">
                            {wayBillPushed ?
                                <Chip variant={'green'} label='已推送运单' /> :
                                <Chip variant={'default'} label='未推送运单' />
                            }
                            {supplierPushed ?
                                <Chip variant={'green'} label='已推送工厂' /> :
                                <Chip variant={'default'} label='未推送工厂' />
                            }
                        </div>
                    )}
                </div>
            )
        },
        meta: {
            title: '状态信息',
        },
    },
    {
        id: 'downloadInfo',
        accessorKey: 'downloadTask',
        header: () => <div className=''>
            <TooltipProvider>
                <Tooltip>
                    <TooltipTrigger asChild>
                        <div className='flex items-center gap-1 cursor-help'>
                            下载信息
                            <HelpCircleIcon className="w-4 h-4 text-gray-400" />
                        </div>
                    </TooltipTrigger>
                    <TooltipContent className="w-[300px] p-3">
                        <div className="space-y-2">
                            <div className="flex items-start gap-2">
                                <CheckCircle2Icon className="w-4 h-4 text-green-500 mt-0.5" />
                                <p className="text-xs">图片全部下载成功后，系统会自动生成压缩包</p>
                            </div>
                            <div className="flex items-start gap-2">
                                <AlertCircleIcon className="w-4 h-4 text-yellow-500 mt-0.5" />
                                <p className="text-xs">如果长时间未生成，请检查：
                                    <ol className="list-decimal ml-4 mt-1 space-y-1">
                                        <li>点击"查看"按钮检查是否有下载失败的图片</li>
                                        <li>对失败的图片进行重试下载</li>
                                    </ol>
                                </p>
                            </div>
                            <div className="flex items-start gap-2">
                                <WrenchIcon className="w-4 h-4 text-blue-500 mt-0.5" />
                                <p className="text-xs">如果重试后仍有问题，可以点击"压缩"按钮手动压缩已下载的图片</p>
                            </div>
                        </div>
                    </TooltipContent>
                </Tooltip>
            </TooltipProvider>
        </div>,
        size: 200,
        cell: ({ row }) => {
            const { downloadTask, imgDownloadStatus, imgZipDownloadUrl } = row.original;

            return (
                <div className="space-y-2">
                    <div>
                        <SimpleMultiStateProgress states={{
                            success: downloadTask.completed,
                            failed: downloadTask.failed,
                            processing: downloadTask.processing,
                            queued: downloadTask.created,
                        }} />
                    </div>
                    <div>
                        {match(imgDownloadStatus)
                            .with('IMAGE_DOWNING', () => <Chip variant={'yellow'} label='图片下载中' />)
                            .with('IMAGE_DOWNED', () => <Chip variant={'blue'} label='等待压缩' />)
                            .with('ZIP_DOING', () => <Chip variant={'yellow'} label='压缩中' />)
                            .with('ZIP_DONE', () => <Chip variant={'green'} label='压缩完成' />)
                            .otherwise(() => null)}
                    </div>
                    <div>
                        {imgZipDownloadUrl ? (
                            <div className="flex items-center gap-2">
                                <TooltipProvider>
                                    <Tooltip>
                                        <TooltipTrigger asChild>
                                            <Button
                                                variant={'default'}
                                                className="bg-green-100 text-green-600 hover:bg-green-200 h-6 text-xs"
                                                onClick={() => window.open(imgZipDownloadUrl, '_blank')}
                                            >
                                                <ZapIcon className="w-3 h-3 mr-1" />
                                                下载
                                            </Button>
                                        </TooltipTrigger>
                                        <TooltipContent side="top" className="flex items-center gap-2 max-w-[400px]">
                                            <span className="text-xs truncate">{imgZipDownloadUrl}</span>
                                            <Button
                                                variant="ghost"
                                                size="icon"
                                                className="h-4 w-4 hover:bg-transparent hover:text-green-600"
                                                onClick={() => {
                                                    clipboard.writeText(imgZipDownloadUrl).then(() => {
                                                        toast.custom((id) => <DefaultSuccessNotification id={id} message='链接已复制' />);
                                                    });
                                                }}
                                            >
                                                <CopyIcon className="h-3 w-3" />
                                            </Button>
                                        </TooltipContent>
                                    </Tooltip>
                                </TooltipProvider>
                            </div>
                        ) : (
                            <div className='flex items-center gap-1.5 px-2 py-1 text-gray-500 bg-gray-50 rounded-md text-xs w-fit'>
                                <LoaderIcon className="w-3 h-3 [animation:spin_3s_linear_infinite]" />
                                等待生成
                            </div>
                        )}
                    </div>
                </div>
            )
        },
        meta: {
            title: '下载信息',
        },
    },
    {
        accessorKey: "attachments",
        header: "附件",
        cell: ({ row }) => {
            const { attachmentUrls } = row.original;
            const [expanded, setExpanded] = useState(false);

            if (!attachmentUrls || attachmentUrls.length === 0) {
                return <span className="text-gray-400 text-xs">无附件</span>;
            }

            const displayUrls = expanded ? attachmentUrls : attachmentUrls.slice(0, 2);

            return (
                <div className="space-y-1">
                    {displayUrls.map((url, index) => {
                        const fileName = url.split('/').pop() || `附件${index + 1}`;
                        return (
                            <div key={index} className="flex items-center space-x-1">
                                <Paperclip className="h-3 w-3 text-blue-500" />
                                <a
                                    href={url}
                                    target="_blank"
                                    rel="noopener noreferrer"
                                    className="text-xs text-blue-600 hover:text-blue-800 hover:underline truncate max-w-[100px]"
                                    title={fileName}
                                >
                                    {fileName}
                                </a>
                                <ExternalLink className="h-3 w-3 text-gray-400" />
                            </div>
                        );
                    })}
                    {attachmentUrls.length > 2 && (
                        <button
                            onClick={() => setExpanded(!expanded)}
                            className="flex items-center text-xs text-blue-600 hover:text-blue-800 mt-1 cursor-pointer"
                        >
                            {expanded ? (
                                <>
                                    <ChevronUp className="h-3 w-3 mr-1" />
                                    收起
                                </>
                            ) : (
                                <>
                                    <ChevronDown className="h-3 w-3 mr-1" />
                                    查看全部 ({attachmentUrls.length})
                                </>
                            )}
                        </button>
                    )}
                </div>
            );
        },
        meta: {
            title: '附件',
        },
    },
    {
        accessorKey: "timeInfo",
        header: "时间信息",
        cell: ({ row }) => {
            const { createdAt, createdByName } = row.original;
            return (
                <div className="space-y-2">
                    <div className="flex items-center space-x-2 text-sm">
                        <Calendar className="h-4 w-4 text-gray-500" />
                        <div>
                            <div className="text-gray-600">创建: <FormattedTime timestamp={createdAt} formatter='DD/MM/YY HH:mm' /></div>
                            <div className="text-gray-500 text-xs">由 {createdByName}</div>
                        </div>
                    </div>
                </div>
            )
        },
        meta: {
            title: '时间信息',
        },
    },
    {
        accessorKey: 'actions',
        header: '操作',
        cell: ({ row }) => <MainOrderActions row={row} />,
        meta: {
            title: '操作',
        },
    }
];

const MainOrderActions = ({ row }: { row: Row<MainOrder> }) => {
    const setRefreshTable = useSetAtom(refreshTableAtom);
    const { id, type, wayBillPushed, supplierPushed, supplierNotified } = row.original;

    return (
        <div className="flex items-center gap-2">
            {type === MainOrderType.ORDER && !supplierPushed && (
                <>
                    <Button
                        variant="link"
                        className="h-auto p-0 text-blue-600 text-xs"
                        onClick={() => {
                            pushModal('DoubleCheckModal', {
                                title: '推送工厂',
                                description: '推送工厂将推送订单到工厂，请谨慎操作',
                                onConfirm: () => {
                                    toast.promise(
                                        mainOrderApi.supplyPush(id),
                                        {
                                            loading: '推送中...',
                                            success: () => {
                                                setRefreshTable(prev => prev + 1);
                                                return '推送成功';
                                            },
                                            error: (r) => {
                                                return r.message ?? '推送失败'
                                            }
                                        }
                                    );
                                },
                                onCancel: () => { }
                            });
                        }}>
                        推送工厂
                    </Button>
                    <Separator orientation='vertical' className='h-4' />
                </>
            )}

            {type === MainOrderType.ORDER && !wayBillPushed && (
                <>
                    <Button
                        variant="link"
                        className="h-auto p-0 text-blue-600 text-xs"
                        onClick={() => {
                            pushModal('DoubleCheckModal', {
                                title: '推送运单',
                                description: '推送运单将推送订单到运单，请谨慎操作',
                                onConfirm: () => {
                                    toast.promise(
                                        mainOrderApi.wayBillPush(id),
                                        {
                                            loading: '推送中...',
                                            success: () => {
                                                setRefreshTable(prev => prev + 1);
                                                return '推送成功';
                                            },
                                            error: (e) => {
                                                return e.message ?? '推送失败'
                                            },
                                        }
                                    );
                                },
                                onCancel: () => { }
                            });
                        }}>
                        推送运单
                    </Button>
                    <Separator orientation='vertical' className='h-4' />
                </>
            )}

            {type === MainOrderType.ORDER && (
                <>
                    <Button
                        variant="link"
                        className={cn("h-auto p-0 text-xs", supplierNotified ? "text-blue-900 font-semibold" : 'text-blue-600')}
                        onClick={() => {
                            pushModal('DoubleCheckModal', {
                                title: "通知采购",
                                description: '确定通知采购吗',
                                onConfirm: () => {
                                    toast.promise(
                                        mainOrderApi.supplyNtfy(id),
                                        {
                                            loading: '通知中...',
                                            success: () => {
                                                setRefreshTable(prev => prev + 1);
                                                return '通知成功';
                                            },
                                            error: (r) => {
                                                return r.message ?? '通知失败'
                                            },
                                        }
                                    );
                                },
                                onCancel: () => { }
                            });
                        }}>
                        {supplierNotified ? '再次通知采购' : '通知采购'}
                    </Button>
                    <Separator orientation='vertical' className='h-4' />
                </>
            )
            }
            <Button
                variant="link"
                className="h-auto p-0 text-blue-600 text-xs"
                onClick={() => {
                    pushModal('EnhancedOrderTaskModal', {
                        orderNumber: id,
                        id: id,
                    })
                }}>
                查看下载
            </Button>
            {type === MainOrderType.ORDER && (
                <>
                    <Separator orientation='vertical' className='h-4' />
                    <Button
                        variant="link"
                        className="h-auto p-0 text-blue-600 text-xs"
                        onClick={() => window.open(`/dashboard/order/sub-order?orderId=${id}`, '_blank')}>
                        查看子订单
                    </Button>
                </>
            )}
            <DropdownMenu>
                <DropdownMenuTrigger asChild>
                    <Button variant="ghost" className="h-8 w-8 p-0">
                        <span className="sr-only">Open menu</span>
                        <MoreHorizontal className="h-4 w-4" />
                    </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                    <DropdownMenuItem
                        className="text-xs"
                        asChild>
                        <Link to={`/dashboard/order/main-order/financial/${id}?title=${row.original.fileName}`}>
                            财务
                        </Link>
                    </DropdownMenuItem>
                    <DropdownMenuItem
                        className="text-xs"
                        onClick={() => {
                            pushModal("MainOrderStatisticsModal", {
                                mainOrderId: id,
                                onClose: () => {
                                    setRefreshTable(prev => prev + 1);
                                }
                            });
                        }}>
                        统计数据
                    </DropdownMenuItem>
                    <DropdownMenuItem
                        className="text-xs"
                        onClick={() => {
                            toast.promise(
                                mainOrderApi.compressMainOrder(id).then(() => {
                                    setRefreshTable(prev => prev + 1);
                                }),
                                {
                                    loading: '压缩中...',
                                    success: '压缩成功',
                                    error: '压缩失败',
                                }
                            );
                        }}>
                        压缩
                    </DropdownMenuItem>

                    <DropdownMenuItem
                        className="text-xs"
                        onClick={() => window.open(`/dashboard/order/waybill?mainOrderId=${id}`, '_blank')}>
                        查看运单
                    </DropdownMenuItem>
                    <DropdownMenuItem
                        className="text-xs"
                        onClick={() => window.open(`/dashboard/order/supplier-order?mainOrderId=${id}`, '_blank')}>
                        供应商订单
                    </DropdownMenuItem>
                    <DropdownMenuSeparator />
                    <DropdownMenuItem
                        className="text-red-600 text-xs focus:text-red-600 focus:bg-red-50"
                        onSelect={(e) => {
                            e.preventDefault();
                            pushModal('DoubleCheckModal', {
                                title: '删除订单',
                                description: '删除订单将删除所有相关数据，请谨慎操作',
                                onConfirm: () => {
                                    mainOrderApi.deleteMainOrder(id).then(() => {
                                        setRefreshTable(prev => prev + 1);
                                        toast.custom((id) => <DefaultSuccessNotification id={id} message='删除成功' needLink={true} />, {
                                            duration: 100000,
                                        });
                                    }).catch(() => {
                                        // toast.custom(<DefaultErrorNotification />);
                                    })
                                },
                                onCancel: () => { }
                            });
                        }}>
                        删除
                    </DropdownMenuItem>
                </DropdownMenuContent>
            </DropdownMenu>
        </div>
    );
};
