import { mainOrderApi } from "@/api/order/mainorder/main-order-api";
import useSearchParamsManager from "@/hooks/use-url-param";
import { pushModal } from "@/components/modals";
import { DataTable } from "@/components/table/base-table";
import {
    DataTableToolbar,
    ToolbarItem,
    ToolbarLeft,
    ToolbarRight
} from "@/components/table/toolbar/data-table-toolbar";
import { DataTableViewOptions } from "@/components/table/toolbar/data-table-view-options";
import { Button } from "@/components/ui/button";
import { FileUp } from "lucide-react";
import { MainOrderColumns } from "./table/main-order-column";
import { DateRangePicker } from "@/components/ui/date-range-picker";
import dayjs from "dayjs";
import { SelectWithInput } from "@/components/ui/select-with-input";
import { DataTableUrlFilter } from "@/components/select/data-table-column-filter";

export default function MainOrderPage() {

    const { addParam, getParam } = useSearchParamsManager();

    return (
        <div className="flex flex-col gap-4">
            <div className="flex items-center justify-between">
                <h1 className="text-2xl font-bold">主订单列表</h1>
            </div>
            <div className="rounded-lg border shadow-sm p-4">
                <DataTable
                    columns={MainOrderColumns}
                    onFetch={param => mainOrderApi.findAll(param)}
                    isFixedHeader={false}
                    containerHeight="620px"
                    toolbar={(table, tableId) => (
                        <DataTableToolbar table={table} tableId={tableId}>
                            <ToolbarLeft>
                                <ToolbarItem>
                                    <SelectWithInput
                                        options={[
                                            { label: "上传文件名", value: "fileName" },
                                            { label: "订单号", value: "id" },
                                        ]}
                                        inputPlaceholder="请输入搜索内容"
                                    />
                                </ToolbarItem>
                                <ToolbarItem>
                                    <DataTableUrlFilter
                                        paramKey="status"
                                        title="任务状态"
                                        options={
                                            [
                                                { label: '已创建', value: 'CREATED' },
                                                { label: '已完成', value: 'COMPLETED' },
                                                { label: '已取消', value: 'CANCELED' },
                                                { label: '失败', value: 'FAILED' },
                                            ]
                                        }
                                    />
                                </ToolbarItem>
                                <ToolbarItem>
                                    <DataTableUrlFilter
                                        paramKey="wayBillPushed"
                                        title="运单推送"
                                        options={[
                                            { label: '已推送', value: 'true' },
                                            { label: '未推送', value: 'false' },
                                        ]}
                                    />
                                </ToolbarItem>
                                <ToolbarItem>
                                    <DataTableUrlFilter
                                        paramKey="supplierPushed"
                                        title="工厂推送"
                                        options={[
                                            { label: '已推送', value: 'true' },
                                            { label: '未推送', value: 'false' },
                                        ]}
                                    />
                                </ToolbarItem>
                                <ToolbarItem>
                                    <DataTableUrlFilter
                                        paramKey="type"
                                        title="订单类型"
                                        options={[
                                            { label: '图片订单', value: 'IMAGE' },
                                            { label: '普通订单', value: 'ORDER' },
                                        ]}
                                    />
                                </ToolbarItem>
                                <ToolbarItem>
                                    <DateRangePicker
                                        onUpdate={(values) => {
                                            addParam('createdAtFrom', dayjs(values.range.from).format('YYYY-MM-DD'))
                                            addParam('createdAtTo', dayjs(values.range.to).format('YYYY-MM-DD'))
                                        }}
                                        initialDateFrom={getParam('createdAtFrom') ? dayjs(getParam('createdAtFrom')).toDate() : dayjs().subtract(365, 'day').toDate()}
                                        initialDateTo={getParam('createdAtTo') ? dayjs(getParam('createdAtTo')).toDate() : dayjs().toDate()}
                                        align="start"
                                        locale="zh-CN"
                                        showCompare={false}
                                    />
                                </ToolbarItem>
                            </ToolbarLeft>


                            <ToolbarRight>
                                <ToolbarItem>
                                    <DataTableViewOptions table={table} tableId={tableId} />
                                </ToolbarItem>
                                <ToolbarItem>
                                    <Button className="h-8 text-green-600 bg-green-50 hover:bg-green-100" onClick={() => pushModal('ImportImageOrderModal')}>
                                        <FileUp className="mr-2 h-4 w-4" />
                                        导入任务
                                    </Button>
                                </ToolbarItem>
                            </ToolbarRight>
                        </DataTableToolbar>
                    )}
                    dependencies={[]}
                />
            </div>
        </div>
    )
}