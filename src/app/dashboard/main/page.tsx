import { queryStatistic, QueryStatisticResponse } from "@/api/statistic-api"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { DateRangePicker } from "@/components/ui/date-range-picker"
import {
    <PERSON><PERSON><PERSON>,
    <PERSON><PERSON>ip<PERSON>ontent,
    <PERSON>ltipProvider,
    <PERSON>ltipTrigger,
} from "@/components/ui/tooltip"
import dayjs from "dayjs"
import "dayjs/locale/zh-cn"
import { Calendar, Loader2, Package, Truck } from "lucide-react"
import React from "react"
import { useNavigate } from "react-router-dom"
dayjs.locale("zh-cn")

export default function DashboardPage() {
    const navigate = useNavigate()
    const [statistics, setStatistics] = React.useState<QueryStatisticResponse | null>(null)
    const [loading, setLoading] = React.useState(true)
    const [dateRange, setDateRange] = React.useState({
        from: dayjs().subtract(30, 'day').toDate(),
        to: dayjs().toDate()
    })




    const fetchStatistics = React.useCallback(async (from: Date, to: Date) => {
        try {
            setLoading(true)
            const data = await queryStatistic(
                dayjs(from).format('YYYY-MM-DD'),
                dayjs(to).format('YYYY-MM-DD')
            )
            setStatistics(data)
        } catch (error) {
            console.error('Failed to fetch statistics:', error)
        } finally {
            setLoading(false)
        }
    }, [])

    React.useEffect(() => {
        fetchStatistics(dateRange.from, dateRange.to)
    }, [fetchStatistics, dateRange])

    const calculatePercentage = (failed: number, total: number) => {
        if (total === 0) return 0
        return ((failed / total) * 100).toFixed(1)
    }

    const navigateWithDateRange = (path: string) => {
        const params = new URLSearchParams({
            createdAtFrom: dayjs(dateRange.from).format('YYYY-MM-DD'),
            createdAtTo: dayjs(dateRange.to).format('YYYY-MM-DD'),
            status: 'FAILED'
        })
        navigate(`${path}?${params.toString()}`)
    }

    return (
        <div className="min-h-screen bg-white p-6">
            <div className="max-w-7xl mx-auto">
                {/* 头部区域 */}
                <div className="space-y-6 mb-8">
                    <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
                        <div className="space-y-2">
                            <h1 className="text-4xl font-bold tracking-tight text-gray-900">
                                订单统计概览
                            </h1>
                            <p className="text-gray-500">
                                实时监控运单和供应商订单状态
                            </p>
                        </div>
                        <div className="flex items-center gap-2 p-2 rounded-lg border border-gray-200 shadow-sm bg-white">
                            <Calendar className="h-4 w-4 text-gray-500" />
                            <DateRangePicker
                                onUpdate={({ range }) => {
                                    if (range.from && range.to) {
                                        setDateRange({ from: range.from, to: range.to })
                                    }
                                }}
                                initialDateFrom={dateRange.from}
                                initialDateTo={dateRange.to}
                                align="start"
                                locale="zh-CN"
                                showCompare={false}
                            />
                        </div>
                    </div>
                </div>

                {/* 统计卡片区域 */}
                {loading ? (
                    <div className="flex h-[400px] items-center justify-center">
                        <div className="space-y-4 text-center">
                            <Loader2 className="h-8 w-8 animate-spin text-blue-600 mx-auto" />
                            <p className="text-sm text-gray-500">加载统计数据中...</p>
                        </div>
                    </div>
                ) : (
                    <div className="grid gap-6 md:grid-cols-2">
                        <TooltipProvider>
                            <Tooltip>
                                <TooltipTrigger asChild>
                                    <Card
                                        className="overflow-hidden hover:shadow-xl transition-all duration-300 cursor-pointer group border border-gray-200"
                                        onClick={() => navigateWithDateRange('/dashboard/order/waybill')}
                                    >
                                        <CardHeader className="flex flex-row items-center justify-between pb-2 border-b border-gray-100 bg-gray-50">
                                            <CardTitle className="text-lg font-medium flex items-center gap-2 text-gray-900">
                                                <Truck className="h-5 w-5 text-blue-600 transition-transform group-hover:scale-110" />
                                                运单状态
                                            </CardTitle>
                                            <span className="text-sm text-gray-600 bg-white px-3 py-1 rounded-full border border-gray-200">
                                                {dayjs(dateRange.from).format('MM/DD')} - {dayjs(dateRange.to).format('MM/DD')}
                                            </span>
                                        </CardHeader>
                                        <CardContent className="pt-6 bg-white">
                                            <div className="space-y-6">
                                                <div>
                                                    <div className="flex items-center justify-between mb-4">
                                                        <div>
                                                            <div className="text-4xl font-bold text-gray-900">{statistics?.waybillAll || 0}</div>
                                                            <div className="text-sm text-gray-500 mt-1">总运单数</div>
                                                        </div>
                                                        <div className="text-right">
                                                            <div className="text-2xl font-semibold text-red-600">{statistics?.waybillFailed || 0}</div>
                                                            <div className="text-sm text-gray-500 mt-1">失败数量</div>
                                                        </div>
                                                    </div>
                                                    <div className="h-3 w-full bg-gray-100 rounded-full overflow-hidden">
                                                        <div
                                                            className="h-full bg-gradient-to-r from-red-600 to-red-500 shadow-sm transition-all duration-500 ease-out"
                                                            style={{
                                                                width: `${calculatePercentage(statistics?.waybillFailed || 0, statistics?.waybillAll || 1)}%`
                                                            }}
                                                        />
                                                    </div>
                                                    <div className="flex justify-end mt-2">
                                                        <span className="text-sm font-medium text-gray-600">
                                                            失败率: {calculatePercentage(statistics?.waybillFailed || 0, statistics?.waybillAll || 0)}%
                                                        </span>
                                                    </div>
                                                </div>
                                            </div>
                                        </CardContent>
                                    </Card>
                                </TooltipTrigger>
                                <TooltipContent side="top" className="bg-gray-900 text-white px-3 py-2">
                                    <p>点击查看失败运单详情</p>
                                </TooltipContent>
                            </Tooltip>
                        </TooltipProvider>

                        <TooltipProvider>
                            <Tooltip>
                                <TooltipTrigger asChild>
                                    <Card
                                        className="overflow-hidden hover:shadow-xl transition-all duration-300 cursor-pointer group border border-gray-200"
                                        onClick={() => navigateWithDateRange('/dashboard/order/supplier-order')}
                                    >
                                        <CardHeader className="flex flex-row items-center justify-between pb-2 border-b border-gray-100 bg-gray-50">
                                            <CardTitle className="text-lg font-medium flex items-center gap-2 text-gray-900">
                                                <Package className="h-5 w-5 text-blue-600 transition-transform group-hover:scale-110" />
                                                供应商订单状态
                                            </CardTitle>
                                            <span className="text-sm text-gray-600 bg-white px-3 py-1 rounded-full border border-gray-200">
                                                {dayjs(dateRange.from).format('MM/DD')} - {dayjs(dateRange.to).format('MM/DD')}
                                            </span>
                                        </CardHeader>
                                        <CardContent className="pt-6 bg-white">
                                            <div className="space-y-6">
                                                <div>
                                                    <div className="flex items-center justify-between mb-4">
                                                        <div>
                                                            <div className="text-4xl font-bold text-gray-900">{statistics?.supplierOrderAll || 0}</div>
                                                            <div className="text-sm text-gray-500 mt-1">总订单数</div>
                                                        </div>
                                                        <div className="text-right">
                                                            <div className="text-2xl font-semibold text-red-600">{statistics?.supplierOrderFailed || 0}</div>
                                                            <div className="text-sm text-gray-500 mt-1">失败数量</div>
                                                        </div>
                                                    </div>
                                                    <div className="h-3 w-full bg-gray-100 rounded-full overflow-hidden">
                                                        <div
                                                            className="h-full bg-gradient-to-r from-red-600 to-red-500 shadow-sm transition-all duration-500 ease-out"
                                                            style={{
                                                                width: `${calculatePercentage(statistics?.supplierOrderFailed || 0, statistics?.supplierOrderAll || 1)}%`
                                                            }}
                                                        />
                                                    </div>
                                                    <div className="flex justify-end mt-2">
                                                        <span className="text-sm font-medium text-gray-600">
                                                            失败率: {calculatePercentage(statistics?.supplierOrderFailed || 0, statistics?.supplierOrderAll || 0)}%
                                                        </span>
                                                    </div>
                                                </div>
                                            </div>
                                        </CardContent>
                                    </Card>
                                </TooltipTrigger>
                                <TooltipContent side="top" className="bg-gray-900 text-white px-3 py-2">
                                    <p>点击查看失败的供应商订单详情</p>
                                </TooltipContent>
                            </Tooltip>
                        </TooltipProvider>
                    </div>
                )}
            </div>
        </div>
    )
}
