import useSearchParamsManager from "@/hooks/use-url-param";
import { pushModal } from "@/components/modals";
import { DataTable } from "@/components/table/base-table";
import {
    DataTableToolbar,
    ToolbarItem,
    ToolbarLeft,
    ToolbarRight
} from "@/components/table/toolbar/data-table-toolbar";
import { DataTableViewOptions } from "@/components/table/toolbar/data-table-view-options";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Plus, Search } from "lucide-react";
import { pageQueryCustomerPlatformAccounts } from "@/api/customer/customer-platform-account-api";
import { CustomerPlatformAccountColumns } from "./table/customer-platform-account-columns";

export default function CustomerPlatformAccountManagementPage() {

    const { addParam, getParam } = useSearchParamsManager();

    const handleCreateAccount = () => {
        pushModal('CreateCustomerPlatformAccountModal', {
            customerId: getParam('customerId') || undefined
        });
    };

    return (
        <div className="flex flex-col gap-4">
            <div className="flex items-center justify-between">
                <h1 className="text-2xl font-bold">客户平台账户管理</h1>
            </div>
            <DataTable
                columns={CustomerPlatformAccountColumns}
                onFetch={pageQueryCustomerPlatformAccounts}
                toolbar={(table) => (
                    <DataTableToolbar table={table}>
                        <ToolbarLeft>
                            <ToolbarItem>
                                <div className="flex items-center space-x-2">
                                    <Search className="h-4 w-4 text-muted-foreground" />
                                    <Input
                                        placeholder="搜索邮箱..."
                                        value={getParam('email') || ''}
                                        onChange={(e) => addParam('email', e.target.value)}
                                        className="w-64"
                                    />
                                </div>
                            </ToolbarItem>
                            <ToolbarItem>
                                <div className="flex items-center space-x-2">
                                    <Input
                                        placeholder="搜索账户名..."
                                        value={getParam('accountName') || ''}
                                        onChange={(e) => addParam('accountName', e.target.value)}
                                        className="w-64"
                                    />
                                </div>
                            </ToolbarItem>
                        </ToolbarLeft>
                        <ToolbarRight>
                            <ToolbarItem>
                                <Button
                                    onClick={handleCreateAccount}
                                    size="sm"
                                    className="h-8 gap-1"
                                >
                                    <Plus className="h-4 w-4" />
                                    创建账户
                                </Button>
                            </ToolbarItem>
                            <DataTableViewOptions table={table} tableId={"customer-platform-accounts"} />
                        </ToolbarRight>
                    </DataTableToolbar>
                )}
            />
        </div>
    );
}
