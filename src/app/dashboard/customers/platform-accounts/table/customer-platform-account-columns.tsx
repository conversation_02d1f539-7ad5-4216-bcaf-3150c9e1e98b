import {
    CustomerPlatformAccountInfo,
    deleteCustomerPlatformAccount,
    changeCustomerPlatformAccountStatus
} from '@/api/customer/customer-platform-account-api';
import { refreshTableAtom } from '@/state/common';
import { ColumnDef } from '@tanstack/react-table';
import { useSetAtom } from 'jotai';
import { ClockIcon, HashIcon, MailIcon, UserIcon, Settings } from 'lucide-react';
import { toast } from 'sonner';
import { FormattedTime } from '@/components/common/formatted-time';
import { Chip } from '@/components/buttons/chip';
import { Separator } from '@/components/ui/separator';
import { Button } from '@/components/ui/button';
import { pushModal } from '@/components/modals';
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";

export const CustomerPlatformAccountColumns: ColumnDef<CustomerPlatformAccountInfo>[] = [
    {
        id: "id",
        accessorKey: "id",
        header: "ID",
        cell: ({ row }) => {
            const id = row.getValue("id") as string;
            return (
                <div className="flex items-center gap-2">
                    <HashIcon className="h-4 w-4 text-muted-foreground" />
                    <span className="font-mono text-sm">{id}</span>
                </div>
            );
        },
    },
    {
        id: "customerName",
        accessorKey: "customerName",
        header: "客户名称",
        cell: ({ row }) => {
            const customerName = row.getValue("customerName") as string;
            return (
                <div className="flex items-center gap-2">
                    <UserIcon className="h-4 w-4 text-muted-foreground" />
                    <span className="font-medium">{customerName}</span>
                </div>
            );
        },
    },
    {
        id: "email",
        accessorKey: "email",
        header: "邮箱",
        cell: ({ row }) => {
            const email = row.getValue("email") as string;
            return (
                <div className="flex items-center gap-2">
                    <MailIcon className="h-4 w-4 text-muted-foreground" />
                    <span className="font-mono text-sm">{email}</span>
                </div>
            );
        },
    },
    {
        id: "accountName",
        accessorKey: "accountName",
        header: "账户名称",
        cell: ({ row }) => {
            const accountName = row.getValue("accountName") as string;
            return (
                <div className="flex items-center gap-2">
                    <span className="font-medium">{accountName}</span>
                </div>
            );
        },
    },
    {
        id: "description",
        accessorKey: "description",
        header: "描述",
        cell: ({ row }) => {
            const description = row.getValue("description") as string;
            return (
                <div className="max-w-[200px] truncate">
                    <span className="text-sm text-muted-foreground">{description || '-'}</span>
                </div>
            );
        },
    },
    {
        id: "status",
        accessorKey: "status",
        header: "状态",
        cell: ({ row }) => {
            const status = row.getValue("status") as string;
            return (
                <Chip
                    variant={status === 'ENABLED' ? 'green' : 'red'}
                    label={status === 'ENABLED' ? '启用' : '禁用'}
                />
            );
        },
    },
    {
        id: "lastLoginAt",
        accessorKey: "lastLoginAt",
        header: "最后登录",
        cell: ({ row }) => {
            const lastLoginAt = row.getValue("lastLoginAt") as number | null;
            return (
                <div className="flex items-center gap-2">
                    <ClockIcon className="h-4 w-4 text-muted-foreground" />
                    {lastLoginAt ? (
                        <FormattedTime timestamp={lastLoginAt} />
                    ) : (
                        <span className="text-muted-foreground text-sm">从未登录</span>
                    )}
                </div>
            );
        },
    },
    {
        id: "createdAt",
        accessorKey: "createdAt",
        header: "创建时间",
        cell: ({ row }) => {
            const createdAt = row.getValue("createdAt") as number;
            return (
                <div className="flex items-center gap-2">
                    <ClockIcon className="h-4 w-4 text-muted-foreground" />
                    <FormattedTime timestamp={createdAt} />
                </div>
            );
        },
    },
    {
        id: "actions",
        header: "操作",
        cell: ({ row }) => {
            const account = row.original;
            const refresh = useSetAtom(refreshTableAtom);

            const handleEdit = () => {
                pushModal('EditCustomerPlatformAccountModal', {
                    accountId: account.id,
                    onClose: () => { }
                });
            };

            const handleDelete = () => {
                toast.promise(deleteCustomerPlatformAccount(account.id), {
                    loading: "正在删除账户...",
                    success: () => {
                        refresh(prev => prev + 1);
                        return "账户删除成功";
                    },
                    error: "账户删除失败"
                });
            };

            const handleToggleStatus = () => {
                const newStatus = account.status === 'ENABLED' ? 'DISABLED' : 'ENABLED';
                toast.promise(changeCustomerPlatformAccountStatus(account.id, newStatus), {
                    loading: `正在${newStatus === 'ENABLED' ? '启用' : '禁用'}账户...`,
                    success: () => {
                        refresh(prev => prev + 1);
                        return `账户${newStatus === 'ENABLED' ? '启用' : '禁用'}成功`;
                    },
                    error: `账户${newStatus === 'ENABLED' ? '启用' : '禁用'}失败`
                });
            };

            return (
                <div className="flex items-center gap-2">
                    <Button
                        variant="ghost"
                        size="sm"
                        onClick={handleEdit}
                        className="h-8 w-8 p-0"
                    >
                        <Settings className="h-4 w-4" />
                    </Button>
                    <Separator orientation="vertical" className="h-4" />
                    <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                            <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                                <span className="sr-only">打开菜单</span>
                                ⋮
                            </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                            <DropdownMenuItem onClick={handleToggleStatus}>
                                {account.status === 'ENABLED' ? '禁用账户' : '启用账户'}
                            </DropdownMenuItem>
                            <DropdownMenuItem
                                onClick={handleDelete}
                                className="text-destructive"
                            >
                                删除账户
                            </DropdownMenuItem>
                        </DropdownMenuContent>
                    </DropdownMenu>
                </div>
            );
        },
    },
];
