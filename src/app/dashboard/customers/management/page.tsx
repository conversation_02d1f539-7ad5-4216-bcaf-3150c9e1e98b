import useSearchParamsManager from "@/hooks/use-url-param";
import { pushModal } from "@/components/modals";
import { DataTable } from "@/components/table/base-table";
import {
    DataTableToolbar,
    ToolbarItem,
    ToolbarLeft,
    ToolbarRight
} from "@/components/table/toolbar/data-table-toolbar";
import { DataTableViewOptions } from "@/components/table/toolbar/data-table-view-options";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Plus, Search } from "lucide-react";
import { pageQueryCustomer } from "@/api/customer/customer-api";
import { CustomerColumns } from "./table/customer-manager-column";

export default function CustomerManagementPage() {

    const { addParam, getParam } = useSearchParamsManager();

    return (
        <div className="flex flex-col gap-4">
            <div className="flex items-center justify-between">
                <h1 className="text-2xl font-bold">客户管理</h1>
            </div>
            <div className="rounded-lg border shadow-sm p-4">
                <DataTable
                    columns={CustomerColumns}
                    onFetch={param => pageQueryCustomer(param)}
                    isFixedHeader={false}
                    containerHeight="620px"
                    toolbar={(table, tableId) => (
                        <DataTableToolbar table={table} tableId={tableId}>
                            <ToolbarLeft>
                                <ToolbarItem>
                                    <div className="relative">
                                        <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                                        <Input
                                            placeholder="搜索客户名称..."
                                            value={getParam('name') || ''}
                                            onChange={(e) => addParam('name', e.target.value)}
                                            className="pl-8"
                                        />
                                    </div>
                                </ToolbarItem>
                                <ToolbarItem>
                                    {/* 可以添加更多左侧项目 */}
                                </ToolbarItem>
                            </ToolbarLeft>


                            <ToolbarRight>
                                <ToolbarItem>
                                    <DataTableViewOptions table={table} tableId={tableId} />
                                </ToolbarItem>
                                <ToolbarItem>
                                    <Button className="h-8 text-green-600 bg-green-50 hover:bg-green-100" onClick={() => pushModal('CreateCustomerModal', { isEdit: false })}>
                                        <Plus className="mr-2 h-4 w-4" />
                                        添加客户
                                    </Button>
                                </ToolbarItem>
                            </ToolbarRight>
                        </DataTableToolbar>
                    )}
                    dependencies={[]}
                />
            </div>
        </div>
    )
}