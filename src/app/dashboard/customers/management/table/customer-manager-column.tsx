import {CustomerInfo, deleteCustomer} from '@/api/customer/customer-api';
import {refreshTable<PERSON>tom} from '@/state/common';
import {ColumnDef} from '@tanstack/react-table';
import {useSet<PERSON>tom} from 'jotai';
import {ClockIcon, HashIcon, MailIcon, UserIcon} from 'lucide-react';
import {toast} from 'sonner';
import {FormattedTime} from '@/components/common/formatted-time';
import {DoubleCheckBtn} from '@/components/buttons/double-check-btn';
import {Chip} from '@/components/buttons/chip';
import {Separator} from '@/components/ui/separator';
import {Button} from '@/components/ui/button';
import {Link} from 'react-router-dom';
import {pushModal} from '@/components/modals';

export const CustomerColumns: ColumnDef<CustomerInfo>[] = [
    {
        id: 'id',
        accessorKey: 'id',
        header: () => <div className='flex items-center'>
            <HashIcon className="inline-block w-4 h-4 mr-1" />
            <div>编号</div>
        </div>,
        size: 100,
        cell: ({ row }) => row.getValue('id'),
        meta: {
            title: '编号',
        },
    },
    {
        id: 'name',
        accessorKey: 'name',
        header: () => <div className=''>
            <UserIcon className="inline-block w-4 h-4 mr-1" />
            客户名称
        </div>,
        cell: ({ row }) => <div>{row.getValue('name')}</div>,
        meta: {
            title: '客户名称',
        },
    },
    {
        id: 'email',
        accessorKey: 'email',
        header: () => <div className=''>
            <MailIcon className="inline-block w-4 h-4 mr-1" />
            邮箱
        </div>,
        cell: ({ row }) => <div>{row.getValue('email')}</div>,
        meta: {
            title: '邮箱',
        },
    },
    {
        id: 'status',
        accessorKey: 'status',
        header: '状态',
        cell: ({ row }) => {
            const status = row.getValue('status') as string;
            return status === 'ENABLED' ?
                <Chip variant={'green'} label='启用' /> :
                <Chip variant={'pink'} label='禁用' />
        },
        meta: {
            title: '状态',
        },
    },
    {
        id: 'createdAt',
        accessorKey: 'createdAt',
        header: () => <div className=''>
            <ClockIcon className="inline-block w-4 h-4 mr-1" />
            创建时间
        </div>,
        cell: ({ row }) => {
            return <FormattedTime timestamp={row.getValue('createdAt')} formatter='DD/MM/YY HH:mm' className='text-gray-500' />
        },
        meta: {
            title: '创建时间',
        },
    },
    {
        id: 'actions',
        accessorKey: 'actions',
        header: '操作',
        cell: ({ row }) => {
            const setRefreshTable = useSetAtom(refreshTableAtom);
            return (
                <div className='flex items-center gap-2'>
                    {row.original.openapiKey ? (
                        <Button
                            variant="link"
                            className="h-auto p-0 text-blue-600 text-xs"
                            onClick={() => {
                                pushModal('CreateApiKeyModal', { customerId: row.getValue('id') as string, openapiKey: row.original.openapiKey });
                            }}>查看API密钥</Button>
                    ) : (
                        <Button
                            variant="link"
                            className="h-auto p-0 text-blue-600 text-xs"
                            onClick={() => {
                                pushModal('CreateApiKeyModal', { customerId: row.getValue('id') as string });
                            }}>创建API密钥</Button>
                    )}
                    <Separator orientation='vertical' className='h-4' />
                    <Button
                      variant="link"
                      className="h-auto p-0 text-blue-600 text-xs" asChild>
                        <Link to={`/dashboard/customers/management/financial/${row.getValue('id')}`}>
                            财务
                        </Link>
                    </Button>
                    <Separator orientation='vertical' className='h-4' />
                    <Button
                      variant="link"
                      className="h-auto p-0 text-blue-600 text-xs" asChild>
                        <Link to={`/dashboard/customers/management/tax-config/${row.getValue('id')}`}>
                            税率设置
                        </Link>
                    </Button>
                    <Separator orientation='vertical' className='h-4' />
                    {/* <Button
                        variant="link"
                        className="h-auto p-0 text-blue-600 text-xs"
                        onClick={() => {
                            pushModal('CreateCustomerPlatformAccountModal', {
                                customerId: row.getValue('id') as string,
                                onClose: () => {}
                            });
                        }}>
                        平台账户
                    </Button>
                    <Separator orientation='vertical' className='h-4' /> */}
                    <DoubleCheckBtn
                        className='h-auto p-0 text-red-600 text-xs'
                        variant='link'
                        title='删除客户'
                        description='删除客户将删除所有相关数据，请谨慎操作'
                        onConfirm={() => {
                            toast.promise(
                                deleteCustomer(row.getValue('id') as string).then(() => {
                                    setRefreshTable(prev => prev + 1);
                                }),
                                {
                                    loading: '删除中...',
                                    success: '删除成功',
                                    error: '删除失败',
                                }
                            );
                        }}
                        buttonText='删除'
                        onCancel={() => { }}
                    />
                </div>
            )
        },
        meta: {
            title: '操作',
        },
    }
];
