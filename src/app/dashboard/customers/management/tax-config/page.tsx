import {TaxSettingsForm} from "@/components/modals/customer/spu/create/tax-settings-form";
import {Button} from "@/components/ui/button";
import {Card, CardContent, CardHeader, CardTitle} from "@/components/ui/card";
import {useEffect, useState} from "react";
import {useNavigate, useParams} from "react-router-dom";
import {toast} from "sonner";
import {batchCreateCustomerTax, CountryStateTaxConfig, deleteCustomerTax, getCustomerTax} from "@/api/inventory/customer/customer-tax-api.ts";
import {ArrowLeft, User} from "lucide-react";
import {Badge} from "@/components/ui/badge.tsx";
import {useRequest} from "ahooks";
import {getCustomer} from "@/api/customer/customer-api.ts";

interface ExtendedTaxRate {
  countryId: string;
  countryName: string;
  vat: number | null;
  additionalTax: number | null;
  stateTaxConfigs?: CountryStateTaxConfig[];
}


export default function CustomerTaxDetailPage() {
  const {customerId} = useParams() as { customerId: string };

  const [taxRates, setTaxRates] = useState<ExtendedTaxRate[]>([]);

  const navigate = useNavigate()

  // 获取客户基本信息
  const { data: customerInfo } = useRequest(() => getCustomer(customerId ?? ''), {
    ready: !!customerId
  });

  useEffect(() => {
    getCustomerTax(customerId).then(taxes => {
      return taxes.map(tax => ({
        countryId: tax.countryId,
        countryName: tax.countryName,
        vat: tax.vatTax,
        additionalTax: tax.additionalTax,
        stateTaxConfigs: tax.stateTaxConfigs
      }))
    }).then((etr) => {
      setTaxRates(etr)
    }).catch(() => {
      toast.error("获取税率失败")
    })
  }, [customerId]);

  // 处理添加税率
  const handleAddTaxRate = async (newTaxRate: ExtendedTaxRate) => {
    try {
      // 确保stateTaxConfigs是一个有效的数组
      const stateTaxConfigs = newTaxRate.stateTaxConfigs?.map(state => ({
        enName: state.enName,
        cnName: state.cnName,
        isoAlphaTwo: state.isoAlphaTwo,
        additionalTax: state.additionalTax
      })) || [];

      await batchCreateCustomerTax({
        customerIds: [customerId],
        customerTaxReq: [{
          countryId: newTaxRate.countryId,
          countryName: newTaxRate.countryName,
          vatTax: newTaxRate.vat || 0,
          additionalTax: newTaxRate.additionalTax || 0,
          stateTaxConfigs: stateTaxConfigs
        }]
      });

      // 更新本地状态时也包含州税率信息
      setTaxRates(prev => [...prev, {
        ...newTaxRate,
        stateTaxConfigs: stateTaxConfigs
      }]);
      toast.success('税率添加成功');
    } catch (error: any) {
      toast.error('添加税率失败: ' + error.message);
    }
  };

  // 处理删除税率
  const handleDeleteTaxRate = async (countryId: string) => {
    try {
      await deleteCustomerTax(customerId, countryId);
      setTaxRates(prev => prev.filter(tax => tax.countryId !== countryId));
      toast.success('税率删除成功');
    } catch (error: any) {
      toast.error('删除税率失败: ' + error.message);
    }
  };

  // if (loading) {
  //   return <div>loading...</div>
  // }

  return (
    <>
      <div className="max-w-5xl mx-auto pt-10 pb-6 space-y-6">

        <div className="flex flex-col space-y-4 bg-background p-6 rounded-lg border shadow-sm">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <Button
                variant="ghost"
                size="icon"
                onClick={() => navigate(-1)}
                className="h-8 w-8"
              >
                <ArrowLeft className="h-4 w-4" />
              </Button>
              <div className="space-y-1">
                <div className="flex items-center gap-2">
                  <h1 className="text-3xl font-bold tracking-tight">客户税率设置</h1>
                  <span className="text-2xl font-semibold text-primary">- {customerInfo?.name}</span>
                </div>
                <p className="text-muted-foreground">查看和管理客户相关的税率信息</p>
              </div>
            </div>
            <Badge variant="secondary" className="h-7 px-3 flex items-center gap-2">
              <User className="h-4 w-4" />
              客户税率管理
            </Badge>
          </div>
          <div className="flex items-center gap-2 text-sm">
            <Badge variant="outline" className="h-6">
              客户ID: {customerId}
            </Badge>
            <Badge variant="outline" className="h-6">
              邮箱: {customerInfo?.email}
            </Badge>
          </div>
        </div>

        <Card>
          <CardHeader>
            <CardTitle>税率设置</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {/* 显示现有税率 */}
              {taxRates.length > 0 && (
                <div className="grid gap-4">
                  {taxRates.map((tax) => (
                    <div key={tax.countryId} className="space-y-4">
                      <div className="flex items-center justify-between p-4 border rounded-lg bg-card hover:bg-accent/5 transition-colors">
                        <div className="flex items-center gap-6">
                          <div className="min-w-[120px]">
                            <p className="text-lg font-semibold">{tax.countryName}</p>
                          </div>
                          <div className="flex gap-8">
                            <div className="flex flex-col items-center">
                              <span className="text-sm text-muted-foreground">VAT</span>
                              <p className="text-xl font-bold text-primary">
                                {tax.vat !== null ? `${tax.vat}` : '-'}
                              </p>
                            </div>
                            <div className="flex flex-col items-center">
                              <span className="text-sm text-muted-foreground">附加税/费</span>
                              <p className="text-xl font-bold text-primary">
                                {tax.additionalTax !== null ? `${tax.additionalTax}` : '-'}
                              </p>
                            </div>
                          </div>
                        </div>
                        <div className="flex items-center gap-2">
                          <Button
                            variant="destructive"
                            size="sm"
                            onClick={() => handleDeleteTaxRate(tax.countryId)}
                            className="h-8 px-3"
                          >
                            删除
                          </Button>
                        </div>
                      </div>

                      {/* 显示州/省税率 */}
                      {tax.stateTaxConfigs && tax.stateTaxConfigs.length > 0 && (
                        <div className="ml-8 space-y-2">
                          <h4 className="text-sm font-medium text-muted-foreground mb-2">州/省税率</h4>
                          <div className="grid gap-2">
                            {tax.stateTaxConfigs.map((state) => (
                              <div key={state.isoAlphaTwo} className="flex items-center justify-between p-3 border rounded-md bg-background">
                                <div className="flex items-center gap-4">
                                  <div>
                                    <p className="font-medium">{state.cnName}</p>
                                    <p className="text-sm text-muted-foreground">{state.enName} ({state.isoAlphaTwo})</p>
                                  </div>
                                  <div className="flex flex-col items-center">
                                    <span className="text-sm text-muted-foreground">附加税/费</span>
                                    <p className="font-semibold">{state.additionalTax}</p>
                                  </div>
                                </div>
                              </div>
                            ))}
                          </div>
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              )}

              {/* 添加新税率表单 */}
              <TaxSettingsForm
                onSubmit={handleAddTaxRate}
                onCancel={() => {
                  navigate(-1)
                }}
                existingCountries={taxRates.map(tax => tax.countryId)}
              />
            </div>
          </CardContent>
        </Card>
      </div>
    </>
  );
}