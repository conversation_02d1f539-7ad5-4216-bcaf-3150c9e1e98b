import {getCustomer, getCustomerFinancial} from "@/api/customer/customer-api";
import {mainOrderApi} from "@/api/order/mainorder/main-order-api";
import useSearchParamsManager from "@/hooks/use-url-param";
import {DataTable} from "@/components/table/base-table";
import {DataTableToolbar, ToolbarItem, ToolbarLeft} from "@/components/table/toolbar/data-table-toolbar";
import {Badge} from "@/components/ui/badge";
import {Button} from "@/components/ui/button";
import {Card, CardContent, CardHeader, CardTitle} from "@/components/ui/card";
import {DateRangePicker} from "@/components/ui/date-range-picker";
import {SelectWithInput} from "@/components/ui/select-with-input";
import {useRequest} from "ahooks";
import dayjs from "dayjs";
import {ArrowLeft, Banknote, Calculator, Receipt, User} from "lucide-react";
import {useNavigate, useParams} from "react-router-dom";
import {FinancialColumns} from "./table/financial-columns";

export default function FinancialPage() {
    const { customerId } = useParams();
    const navigate = useNavigate();
    const { addParam } = useSearchParamsManager();

    // 获取客户基本信息
    const { data: customerInfo } = useRequest(() => getCustomer(customerId ?? ''), {
        ready: !!customerId
    });

    // 获取客户财务统计数据
    const { data: financialData } = useRequest(() => getCustomerFinancial(customerId ?? ''), {
        ready: !!customerId
    });

    // 统计数据
    const totals = {
        expressCharge: financialData?.totalQuickChannelTax ?? 0,
        customerPayment: financialData?.totalCustomerNeedPayCost ?? 0,
        tax: financialData?.totalCustomerNeedPayTax ?? 0,
      correct: financialData?.correctWaybillCost ?? 0,
        total: (financialData?.totalQuickChannelTax ?? 0) +
            (financialData?.totalCustomerNeedPayCost ?? 0) +
            (financialData?.totalCustomerNeedPayTax ?? 0) +
            (financialData?.correctWaybillCost ?? 0)
    };

    return (
        <div className="min-h-screen bg-gradient-to-b from-muted/50 to-background">
            <div className="container mx-auto py-8 space-y-8">
                {/* 客户信息 */}
                <div className="flex flex-col space-y-4 bg-background p-6 rounded-lg border shadow-sm">
                    <div className="flex items-center justify-between">
                        <div className="flex items-center gap-4">
                            <Button
                                variant="ghost"
                                size="icon"
                                onClick={() => navigate(-1)}
                                className="h-8 w-8"
                            >
                                <ArrowLeft className="h-4 w-4" />
                            </Button>
                            <div className="space-y-1">
                                <div className="flex items-center gap-2">
                                    <h1 className="text-3xl font-bold tracking-tight">客户财务详情</h1>
                                    <span className="text-2xl font-semibold text-primary">- {customerInfo?.name}</span>
                                </div>
                                <p className="text-muted-foreground">查看和管理客户相关的所有财务信息</p>
                            </div>
                        </div>
                        <Badge variant="secondary" className="h-7 px-3 flex items-center gap-2">
                            <User className="h-4 w-4" />
                            客户管理
                        </Badge>
                    </div>
                    <div className="flex items-center gap-2 text-sm">
                        <Badge variant="outline" className="h-6">
                            客户ID: {customerId}
                        </Badge>
                        <Badge variant="outline" className="h-6">
                            邮箱: {customerInfo?.email}
                        </Badge>
                    </div>
                </div>

                {/* 统计卡片 */}
                <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-5">
                    <Card className="overflow-hidden">
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">累计快速通道费</CardTitle>
                            <div className="rounded-full p-2 bg-primary/10">
                                <Banknote className="h-4 w-4 text-primary" />
                            </div>
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">¥{totals.expressCharge.toLocaleString()}</div>
                        </CardContent>
                    </Card>
                    <Card className="overflow-hidden">
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">累计客户支付费用</CardTitle>
                            <div className="rounded-full p-2 bg-primary/10">
                                <Receipt className="h-4 w-4 text-primary" />
                            </div>
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">¥{totals.customerPayment.toLocaleString()}</div>
                        </CardContent>
                    </Card>
                    <Card className="overflow-hidden">
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">累计税费</CardTitle>
                            <div className="rounded-full p-2 bg-primary/10">
                                <Calculator className="h-4 w-4 text-primary" />
                            </div>
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">¥{totals.tax.toLocaleString()}</div>
                        </CardContent>
                    </Card>
                  <Card className="overflow-hidden">
                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                      <CardTitle className="text-sm font-medium">修正总计</CardTitle>
                      <div className="rounded-full p-2 bg-primary/10">
                        <Calculator className="h-4 w-4 text-primary"/>
                      </div>
                    </CardHeader>
                    <CardContent>
                      <div className="text-2xl font-bold">¥{totals.correct.toLocaleString()}</div>
                    </CardContent>
                  </Card>
                    <Card className="overflow-hidden bg-primary">
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2 border-b border-primary-foreground/10">
                            <CardTitle className="text-sm font-medium text-primary-foreground">费用总计</CardTitle>
                            <div className="rounded-full p-2 bg-primary-foreground/10">
                                <Banknote className="h-4 w-4 text-primary-foreground" />
                            </div>
                        </CardHeader>
                        <CardContent className="mt-2">
                            <div className="text-2xl font-bold text-primary-foreground">¥{totals.total.toLocaleString()}</div>
                        </CardContent>
                    </Card>
                </div>

                {/* 订单财务明细表格 */}
                <div className="rounded-lg border shadow-sm p-4">
                    <DataTable
                        columns={FinancialColumns}
                        onFetch={param => mainOrderApi.pageFinanceStatistic({
                            ...param,
                            searchParams: {
                                ...param.searchParams,
                                customerId: customerId
                            }
                        })}
                        isFixedHeader={false}
                        containerHeight="620px"
                        toolbar={(table, tableId) => (
                            <DataTableToolbar table={table} tableId={tableId}>
                                <ToolbarLeft>
                                    <ToolbarItem>
                                        <SelectWithInput
                                            options={[
                                                { label: "上传文件名", value: "fileName" },
                                                { label: "订单号", value: "id" },
                                            ]}
                                            inputPlaceholder="请输入搜索内容"
                                        />
                                    </ToolbarItem>
                                    <ToolbarItem>
                                        <DateRangePicker
                                            onUpdate={(values) => {
                                                addParam('createdAtFrom', dayjs(values.range.from).format('YYYY-MM-DD'))
                                                addParam('createdAtTo', dayjs(values.range.to).format('YYYY-MM-DD'))
                                            }}
                                            initialDateFrom={dayjs().subtract(365, 'day').toDate()}
                                            initialDateTo={dayjs().toDate()}
                                            align="start"
                                            locale="zh-CN"
                                            showCompare={false}
                                        />
                                    </ToolbarItem>
                                </ToolbarLeft>
                            </DataTableToolbar>
                        )}
                        dependencies={[customerId]}
                    />
                </div>
            </div>
        </div>
    );
}  