import {FinanceStatisticResponse} from '@/api/order/mainorder/main-order-api';
import {CopyText} from '@/components/buttons/copy-text-btn';
import {FormattedTime} from '@/components/common/formatted-time';
import {Button} from '@/components/ui/button';
import {ColumnDef} from '@tanstack/react-table';
import {Calendar, FileIcon, Hash} from 'lucide-react';
import {Link} from 'react-router-dom';

export const FinancialColumns: ColumnDef<FinanceStatisticResponse>[] = [
    {
        id: 'basicInfo',
        accessorKey: 'id',
        header: () => <div className='flex items-center'>
            <div>基本信息</div>
        </div>,
        size: 280,
        cell: ({ row }) => {
            const { id, fileName } = row.original;
            return (
                <div className="space-y-2">
                    <div className="flex items-center space-x-2">
                        <Hash className="h-4 w-4 text-gray-500" />
                        <CopyText value={id} />
                    </div>
                    <div className="flex items-center space-x-2">
                      <FileIcon className="h-4 w-4 text-gray-500" />
                      <Button variant="link" size="sm" className="text-sm text-gray-600" asChild>
                        <Link to={`/dashboard/order/main-order/financial/${id}?title=${fileName}`}>
                          <span className="text-sm text-blue-600">{fileName}</span>
                        </Link>
                      </Button>
                    </div>
                </div>
            )
        },
    },
    {
        id: 'financialInfo',
        header: '财务信息',
        size: 400,
        cell: ({ row }) => {
            const {
                totalCustomerNeedPayCost,
                totalCustomerNeedPayTax,
                totalQuickChannelTax,
                totalWaybillCost
            } = row.original;

            return (
                <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-1">
                        <div className="text-sm text-gray-500">客户应付费用</div>
                        <div className="text-base font-medium">¥{totalCustomerNeedPayCost.toLocaleString()}</div>
                    </div>
                    <div className="space-y-1">
                        <div className="text-sm text-gray-500">客户应付税费</div>
                        <div className="text-base font-medium">¥{totalCustomerNeedPayTax.toLocaleString()}</div>
                    </div>
                    <div className="space-y-1">
                        <div className="text-sm text-gray-500">快速通道税费</div>
                        <div className="text-base font-medium">¥{totalQuickChannelTax.toLocaleString()}</div>
                    </div>
                    <div className="space-y-1">
                        <div className="text-sm text-gray-500">运单费用</div>
                        <div className="text-base font-medium">¥{totalWaybillCost.toLocaleString()}</div>
                    </div>
                </div>
            )
        },
    },
    {
        accessorKey: "createdAt",
        header: "创建时间",
        cell: ({ row }) => {
            const { createdAt } = row.original;
            return (
                <div className="flex items-center space-x-2 text-sm">
                    <Calendar className="h-4 w-4 text-gray-500" />
                    <FormattedTime timestamp={createdAt} formatter='YYYY-MM-DD HH:mm:ss' />
                </div>
            )
        },
    },
]; 