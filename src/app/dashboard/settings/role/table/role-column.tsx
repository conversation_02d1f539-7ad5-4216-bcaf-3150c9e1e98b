import { deleteRole, Role, updateRole } from '@/api/user/role-api';
import { Chip } from '@/components/buttons/chip';
import { DoubleCheckBtn } from '@/components/buttons/double-check-btn';
import { FormattedTime } from '@/components/common/formatted-time';
import { pushModal } from '@/components/modals';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';
import { refreshTableAtom } from '@/state/common';
import { ColumnDef } from '@tanstack/react-table';
import { useRequest } from 'ahooks';
import { useSetAtom } from 'jotai';
import { CircleIcon, ClockIcon, FileIcon, HashIcon, ShieldIcon, LockIcon } from 'lucide-react';
import { toast } from 'sonner';
import { match } from 'ts-pattern';

export const RoleColumns: ColumnDef<Role>[] = [
    {
        id: 'id',
        accessorKey: 'id',
        header: () => <div className='flex items-center'>
            <HashIcon className="inline-block w-4 h-4 mr-1" />
            <div>编号</div>
        </div>,
        size: 100,
        cell: ({ row }) => row.getValue('id'),
        meta: {
            title: '编号',
        },
    },
    {
        id: 'name',
        accessorKey: 'name',
        header: () => <div className=''>
            <FileIcon className="inline-block w-4 h-4 mr-1" />
            角色名称
        </div>,
        cell: ({ row }) => <div>{row.getValue('name')}</div>,
        meta: {
            title: '角色名称',
        },
    },
    {
        id: 'permissionNames',
        accessorKey: 'permissionNames',
        header: () => <div className=''>
            <ShieldIcon className="inline-block w-4 h-4 mr-1" />
            权限列表
        </div>,
        cell: ({ row }) => {
            //@ts-ignore
            const permissions = row.original.permissionNames;
            return <div className="flex flex-wrap gap-1">
                {/* @ts-ignore */}
                {permissions.map((permission, index) => (
                    <Badge key={index} variant="outline">{permission}</Badge>
                ))}
            </div>
        },
        meta: {
            title: '权限列表',
        },
    },
    {
        id: 'limit',
        accessorKey: 'limit',
        header: () => <div className=''>
            <LockIcon className="inline-block w-4 h-4 mr-1" />
            数据权限
        </div>,
        cell: ({ row }) => {
            const limit = row.getValue('limit') as string;
            return match(limit)
                .with('ALL', () => <Badge variant="outline">所有人</Badge>)
                .with('SELF', () => <Badge variant="secondary">仅自己</Badge>)
                .otherwise(() => <Badge variant="outline">未知</Badge>)
        },
        meta: {
            title: '限制类型',
        },
    },
    {
        id: 'status',
        accessorKey: 'status',
        header: () => <div className=''>
            <CircleIcon className="inline-block w-4 h-4 mr-1" />
            状态
        </div>,
        cell: ({ row }) => {
            const status = row.getValue('status') as number;
            return match(status)
                .with(0, () => <Chip variant={'red'} label='禁用' />)
                .with(1, () => <Chip variant={'green'} label='启用' />)
                .otherwise(() => null)
        },
        meta: {
            title: '状态',
        },
    },
    {
        id: 'createdAt',
        accessorKey: 'createdAt',
        header: () => <div className=''>
            <ClockIcon className="inline-block w-4 h-4 mr-1" />
            创建时间
        </div>,
        cell: ({ row }) => {
            return <FormattedTime timestamp={row.getValue('createdAt')} formatter='DD/MM/YY HH:mm' className='text-gray-500' />
        },
        meta: {
            title: '创建时间',
        },
    },
    {
        id: 'actions',
        accessorKey: 'actions',
        header: '操作',
        cell: ({ row }) => {
            const setRefreshTable = useSetAtom(refreshTableAtom);
            const {runAsync: updateRoleAsync} = useRequest(updateRole,{
                manual: true,
            });
            return (
                <div className='flex items-center gap-2'>
                    <Button
                        variant="link"
                        className="h-auto p-0 text-blue-600 text-xs" onClick={() => {
                            // TODO: 实现编辑功能
                            pushModal('PermissionListModal', {
                                onPermissionChange: async (selectedPermissions) => {
                                    console.log('selectedPermissions', selectedPermissions)
                                    toast.promise(
                                        //@ts-ignore
                                        updateRoleAsync({
                                            id: row.original.id!,
                                            name: row.original.name,
                                            permissions: selectedPermissions.map((item) => item.id),
                                        }),
                                        {
                                            loading: '更新中...',
                                            success: () => {
                                                setRefreshTable(prev => prev + 1);
                                                return '更新成功';
                                            },
                                            error: '更新失败',
                                        }
                                    )
                                },
                                defaultSelectedPermissionIds: row.original.permissions
                            })
                        }}>编辑</Button>
                    <Separator orientation='vertical' className='h-4' />
                    <DoubleCheckBtn
                        className='h-auto p-0 text-red-600 text-xs'
                        variant='link'
                        title='禁用角色'
                        description='禁用角色将影响相关用户的权限，请谨慎操作'
                        onConfirm={() => {
                            toast.promise(  
                                deleteRole(row.getValue('id')),
                                {
                                    loading: '禁用中...',
                                    success: () => {
                                        setRefreshTable(prev => prev + 1);
                                        return '禁用成功';
                                    },
                                    error: '禁用失败',
                                }
                            );
                        }}
                        buttonText='禁用'
                        onCancel={() => { }}
                    />
                </div>
            )
        },
        meta: {
            title: '操作',
        },
    }
]; 