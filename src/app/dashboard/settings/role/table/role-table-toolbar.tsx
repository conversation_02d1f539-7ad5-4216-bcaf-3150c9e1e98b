import { Permission } from "@/api/user/permission-api";
import { createRole, RoleLimit } from "@/api/user/role-api";
import { pushModal } from "@/components/modals";
import { DataTableToolbarProps } from "@/components/table/toolbar/data-table-toolbar";
import { DataTableViewOptions } from "@/components/table/toolbar/data-table-view-options";
import { Button } from "@/components/ui/button";
import {
    Dialog,
    DialogContent,
    DialogDescription,
    DialogFooter,
    DialogHeader,
    DialogTitle,
    DialogTrigger,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from "@/components/ui/select";
import {
    Form,
    FormControl,
    FormField,
    FormItem,
    FormLabel,
    FormMessage,
} from "@/components/ui/form";
import { refreshTableAtom } from "@/state/common";
import { useSet<PERSON>tom } from "jotai";
import { useState } from "react";
import { toast } from "sonner";
import { FileIcon, ShieldIcon, LockIcon, Plus } from "lucide-react";
import { useForm } from "react-hook-form";
import * as z from "zod";
import { zodResolver } from "@hookform/resolvers/zod";

const formSchema = z.object({
    name: z.string().min(1, "角色名称不能为空"),
    limit: z.nativeEnum(RoleLimit),
    permissions: z.array(z.string()).min(1, "请至少选择一个权限"),
});

type FormValues = z.infer<typeof formSchema>;

export function RoleDataTableToolbar<TData>({
    table,
    tableId
}: DataTableToolbarProps<TData>) {
    const [open, setOpen] = useState(false);
    const setRefresh = useSetAtom(refreshTableAtom);

    const form = useForm<FormValues>({
        resolver: zodResolver(formSchema),
        defaultValues: {
            name: "",
            limit: RoleLimit.SELF,
            permissions: [],
        },
    });


    const onSubmit = async (values: FormValues) => {
        // 确保limit值是正确的
        const submitData = {
            ...values,
            limit: values.limit === RoleLimit.ALL ? RoleLimit.ALL : RoleLimit.SELF
        };
        toast.promise(createRole(submitData), {
            loading: '创建中...',
            success: () => { 
                setOpen(false);
                setRefresh((prev) => prev + 1);
                form.reset();
                return '创建成功';
            },
            error: '创建失败',
        })
    };

    return (
        <div className={"flex justify-end pt-8"}>
            <div className="flex flex-1 items-center space-x-2">
                {/* 其他按钮 */}
            </div>
            <div className={"flex flex-row space-x-2"}>
                <Dialog open={open} onOpenChange={setOpen}>
                    <DialogTrigger asChild>
                        <Button size="sm" className="h-8 gap-1">
                            <Plus className="h-4 w-4" />
                            <span className="sr-only sm:not-sr-only sm:whitespace-nowrap">
                                创建角色
                            </span>
                        </Button>
                    </DialogTrigger>
                    <DialogContent className="sm:max-w-[425px]">
                        <DialogHeader>
                            <DialogTitle>创建新角色</DialogTitle>
                            <DialogDescription>
                                请填写以下信息来创建新角色
                            </DialogDescription>
                        </DialogHeader>
                        <Form {...form}>
                            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
                                <FormField
                                    control={form.control}
                                    name="name"
                                    render={({ field }) => (
                                        <FormItem>
                                            <FormLabel className="flex items-center gap-2">
                                                <FileIcon className="h-4 w-4" />
                                                角色名称
                                            </FormLabel>
                                            <FormControl>
                                                <Input placeholder="请输入角色名称" {...field} />
                                            </FormControl>
                                            <FormMessage />
                                        </FormItem>
                                    )}
                                />

                                <FormField
                                    control={form.control}
                                    name="limit"
                                    render={({ field }) => (
                                        <FormItem>
                                            <FormLabel className="flex items-center gap-2">
                                                <LockIcon className="h-4 w-4" />
                                                权限限制
                                            </FormLabel>
                                            <Select
                                                onValueChange={(val) => {
                                                    console.log('Select值改变:', val);
                                                    field.onChange(val);
                                                }}
                                                value={field.value}
                                            >
                                                <FormControl>
                                                    <SelectTrigger>
                                                        <SelectValue placeholder="选择权限限制" />
                                                    </SelectTrigger>
                                                </FormControl>
                                                <SelectContent>
                                                    <SelectItem value={RoleLimit.SELF}>仅自己</SelectItem>
                                                    <SelectItem value={RoleLimit.ALL}>所有人</SelectItem>
                                                </SelectContent>
                                            </Select>
                                            <FormMessage />
                                        </FormItem>
                                    )}
                                />

                                <FormField
                                    control={form.control}
                                    name="permissions"
                                    render={({ field }) => (
                                        <FormItem>
                                            <FormLabel className="flex items-center gap-2">
                                                <ShieldIcon className="h-4 w-4" />
                                                权限选择
                                            </FormLabel>
                                            <FormControl>
                                                <Button
                                                    type="button"
                                                    variant="outline"
                                                    className="w-full"
                                                    onClick={() => {
                                                        pushModal('PermissionListModal', {
                                                            onPermissionChange: (selectedPermissions: Permission[]) => {
                                                                field.onChange(selectedPermissions.map(p => p.id));
                                                            },
                                                            defaultSelectedPermissionIds: field.value
                                                        });
                                                    }}
                                                >
                                                    选择权限
                                                </Button>
                                            </FormControl>
                                            <FormMessage />
                                        </FormItem>
                                    )}
                                />

                                <DialogFooter>
                                    <Button type="submit">
                                        确定
                                    </Button>
                                </DialogFooter>
                            </form>
                        </Form>
                    </DialogContent>
                </Dialog>
                <DataTableViewOptions table={table} tableId={tableId!} />
            </div>
        </div>
    )
}