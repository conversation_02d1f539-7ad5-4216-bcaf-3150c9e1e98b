import { DataTable } from "@/components/table/base-table";
import { RoleColumns } from "./table/role-column";
import { pageRole } from "@/api/user/role-api";
import { RoleDataTableToolbar } from "./table/role-table-toolbar";

export default function RolePage() {
    return (
        <div className="flex flex-col gap-4">
            <div className="flex items-center justify-between">
                <h1 className="text-2xl font-bold">角色管理</h1>
            </div>
            <div className="rounded-lg border shadow-sm p-4">
                <DataTable
                    columns={RoleColumns}
                    onFetch={param => pageRole(param)}
                    isFixedHeader={false}
                    containerHeight="620px"
                    toolbar={(table, tableId) => (
                        <RoleDataTableToolbar table={table} tableId={tableId} />
                    )}
                    dependencies={[]}
                />
            </div>
        </div>
    )
}
