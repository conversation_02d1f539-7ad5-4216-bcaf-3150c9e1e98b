import { pageUser } from "@/api/user/user-api";
import useSearchParamsManager from "@/hooks/use-url-param";
import { DataTable } from "@/components/table/base-table";
import {
    DataTableToolbar,
    ToolbarItem,
    ToolbarLeft,
    ToolbarRight
} from "@/components/table/toolbar/data-table-toolbar";
import { DataTableViewOptions } from "@/components/table/toolbar/data-table-view-options";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Plus, Search } from "lucide-react";
import { UserColumns } from "./table/user-column";
import { pushModal } from "@/components/modals";

export default function UserPage() {
    const { addParam, getParam } = useSearchParamsManager();

    return (
        <div className="flex flex-col gap-4">
            <div className="flex items-center justify-between">
                <h1 className="text-2xl font-bold">用户管理</h1>
            </div>
            <div className="rounded-lg border shadow-sm p-4">
                <DataTable
                    columns={UserColumns}
                    onFetch={param => pageUser(param)}
                    isFixedHeader={false}
                    containerHeight="620px"
                    toolbar={(table, tableId) => (
                        <DataTableToolbar table={table} tableId={tableId}>
                            <ToolbarLeft>
                                <ToolbarItem>
                                    <div className="relative">
                                        <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                                        <Input
                                            placeholder="搜索用户名..."
                                            value={getParam('name') || ''}
                                            onChange={(e) => addParam('name', e.target.value)}
                                            className="pl-8"
                                        />
                                    </div>
                                </ToolbarItem>
                            </ToolbarLeft>

                            <ToolbarRight>
                                <ToolbarItem>
                                    <DataTableViewOptions table={table} tableId={tableId} />
                                </ToolbarItem>
                                <ToolbarItem>
                                    <Button className="h-8 text-green-600 bg-green-50 hover:bg-green-100" onClick={() => pushModal('UserCreationModal')}>
                                        <Plus className="mr-2 h-4 w-4" />
                                        新增用户
                                    </Button>
                                </ToolbarItem>
                            </ToolbarRight>
                        </DataTableToolbar>
                    )}
                    dependencies={[]}
                />
            </div>
        </div>
    )
}