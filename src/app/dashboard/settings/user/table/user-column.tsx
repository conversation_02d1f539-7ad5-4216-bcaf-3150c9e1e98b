import { deleteUsers, User } from '@/api/user/user-api';
import { Chip } from '@/components/buttons/chip';
import { DoubleCheckBtn } from '@/components/buttons/double-check-btn';
import { FormattedTime } from '@/components/common/formatted-time';
import { Button } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';
import { refreshTableAtom } from '@/state/common';
import { ColumnDef } from '@tanstack/react-table';
import { useSetAtom } from 'jotai';
import { CircleIcon, ClockIcon, FileIcon, HashIcon, ShieldIcon, UserIcon } from 'lucide-react';
import { toast } from 'sonner';
import { match } from 'ts-pattern';

export const UserColumns: ColumnDef<User>[] = [
    {
        id: 'id',
        accessorKey: 'id',
        header: () => <div className='flex items-center'>
            <HashIcon className="inline-block w-4 h-4 mr-1" />
            <div>编号</div>
        </div>,
        size: 100,
        cell: ({ row }) => row.getValue('id'),
        meta: {
            title: '编号',
        },
    },
    {
        id: 'account',
        accessorKey: 'account',
        header: () => <div className=''>
            <UserIcon className="inline-block w-4 h-4 mr-1" />
            账号
        </div>,
        cell: ({ row }) => <div>{row.getValue('account')}</div>,
        meta: {
            title: '账号',
        },
    },
    {
        id: 'name',
        accessorKey: 'name',
        header: () => <div className=''>
            <FileIcon className="inline-block w-4 h-4 mr-1" />
            用户名称
        </div>,
        cell: ({ row }) => <div>{row.getValue('name')}</div>,
        meta: {
            title: '用户名称',
        },
    },
    {
        id: 'roleName',
        accessorKey: 'roleName',
        header: () => <div className=''>
            <ShieldIcon className="inline-block w-4 h-4 mr-1" />
            角色
        </div>,
        cell: ({ row }) => <div>{row.getValue('roleName')}</div>,
        meta: {
            title: '角色',
        },
    },
    {
        id: 'status',
        accessorKey: 'status',
        header: () => <div className=''>
            <CircleIcon className="inline-block w-4 h-4 mr-1" />
            状态
        </div>,
        cell: ({ row }) => {
            const status = row.getValue('status') as string;
            return match(status)
                .with('DISABLED', () => <Chip variant={'red'} label='禁用' />)
                .with('ENABLED', () => <Chip variant={'green'} label='启用' />)
                .otherwise(() => null)
        },
        meta: {
            title: '状态',
        },
    },
    {
        id: 'createdAt',
        accessorKey: 'createdAt',
        header: () => <div className=''>
            <ClockIcon className="inline-block w-4 h-4 mr-1" />
            创建时间
        </div>,
        cell: ({ row }) => {
            return <FormattedTime timestamp={row.getValue('createdAt')} formatter='DD/MM/YY HH:mm' className='text-gray-500' />
        },
        meta: {
            title: '创建时间',
        },
    },
    {
        id: 'actions',
        accessorKey: 'actions',
        header: '操作',
        cell: ({ row }) => {
            const setRefreshTable = useSetAtom(refreshTableAtom);
            return (
                <div className='flex items-center gap-2'>
                    <Button
                        variant="link"
                        className="h-auto p-0 text-blue-600 text-xs" onClick={() => {
                            // TODO: 实现编辑功能
                        }}>编辑</Button>
                    <Separator orientation='vertical' className='h-4' />
                    <DoubleCheckBtn
                        className='h-auto p-0 text-red-600 text-xs'
                        variant='link'
                        title='删除用户'
                        description='删除用户将清除该用户的所有数据，请谨慎操作'
                        onConfirm={() => {
                            // TODO: 实现删除功能
                            toast.promise(
                                deleteUsers([row.getValue('id')]),
                                {
                                    loading: '删除中...',
                                    success: '删除成功',
                                    error: '删除失败',
                                }
                            );
                            setRefreshTable(prev => prev + 1)
                        }}
                        buttonText='删除'
                        onCancel={() => { }}
                    />
                </div>
            )
        },
        meta: {
            title: '操作',
        },
    }
]; 
