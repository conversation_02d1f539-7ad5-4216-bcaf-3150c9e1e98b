import { Permission } from '@/api/user/permission-api';
import { DoubleCheckBtn } from '@/components/buttons/double-check-btn';
import { FormattedTime } from '@/components/common/formatted-time';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';
import { ColumnDef } from '@tanstack/react-table';
import { ClockIcon, FileIcon, FolderIcon, HashIcon, LayersIcon, ShieldIcon } from 'lucide-react';
import { toast } from 'sonner';

export const PermissionColumns: ColumnDef<Permission>[] = [
    {
        id: 'id',
        accessorKey: 'id',
        header: () => <div className='flex items-center'>
            <HashIcon className="inline-block w-4 h-4 mr-1" />
            <div>编号</div>
        </div>,
        size: 100,
        cell: ({ row }) => row.getValue('id'),
        meta: {
            title: '编号',
        },
    },
    {
        id: 'name',
        accessorKey: 'name',
        header: () => <div className=''>
            <FileIcon className="inline-block w-4 h-4 mr-1" />
            权限名称
        </div>,
        cell: ({ row }) => <div>{row.getValue('name')}</div>,
        meta: {
            title: '权限名称',
        },
    },
    {
        id: 'description',
        accessorKey: 'description',
        header: () => <div className=''>
            <LayersIcon className="inline-block w-4 h-4 mr-1" />
            权限描述
        </div>,
        cell: ({ row }) => <div>{row.getValue('description')}</div>,
        meta: {
            title: '权限描述',
        },
    },
    {
        id: 'parentId',
        accessorKey: 'parentId',
        header: () => <div className=''>
            <FolderIcon className="inline-block w-4 h-4 mr-1" />
            父权限
        </div>,
        meta: {
            title: '父权限',
        },
    },
    {
        id: 'resource',
        accessorKey: 'resource',
        header: () => <div className=''>
            <ShieldIcon className="inline-block w-4 h-4 mr-1" />
            资源
        </div>,
        cell: ({ row }) => <div>
            <Badge variant="outline">{row.getValue('resource')}</Badge>
        </div>,
        meta: {
            title: '资源',
        },
    },
    {
        id: 'createdAt',
        accessorKey: 'createdAt',
        header: () => <div className=''>
            <ClockIcon className="inline-block w-4 h-4 mr-1" />
            创建时间
        </div>,
        cell: ({ row }) => {
            return <FormattedTime timestamp={row.getValue('createdAt')} formatter='DD/MM/YY HH:mm' className='text-gray-500' />
        },
        meta: {
            title: '创建时间',
        },
    },
    {
        id: 'actions',
        accessorKey: 'actions',
        header: '操作',
        cell: () => {
            return (
                <div className='flex items-center gap-2'>
                    <Button
                        variant="link"
                        className="h-auto p-0 text-blue-600 text-xs" onClick={() => {
                            // TODO: 实现编辑功能
                        }}>编辑</Button>
                    <Separator orientation='vertical' className='h-4' />
                    <DoubleCheckBtn
                        className='h-auto p-0 text-red-600 text-xs'
                        variant='link'
                        title='删除权限'
                        description='删除权限将影响相关用户的访问权限，请谨慎操作'
                        onConfirm={() => {
                            // TODO: 实现删除功能
                            toast.promise(
                                Promise.resolve(),
                                {
                                    loading: '删除中...',
                                    success: '删除成功',
                                    error: '删除失败',
                                }
                            );
                        }}
                        buttonText='删除'
                        onCancel={() => { }}
                    />
                </div>
            )
        },
        meta: {
            title: '操作',
        },
    }
];
