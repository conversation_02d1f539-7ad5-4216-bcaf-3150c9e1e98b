import useSearchParamsManager from "@/hooks/use-url-param";
import { DataTable } from "@/components/table/base-table";
import {
    DataTableToolbar,
    ToolbarItem,
    ToolbarLeft,
    ToolbarRight
} from "@/components/table/toolbar/data-table-toolbar";
import { DataTableViewOptions } from "@/components/table/toolbar/data-table-view-options";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Plus, Search } from "lucide-react";
import { PermissionColumns } from "./table/permission-column";
import { pagePermission } from "@/api/user/permission-api";

export default function PermissionPage() {

    const { addParam, getParam } = useSearchParamsManager();

    return (
        <div className="flex flex-col gap-4">
            <div className="flex items-center justify-between">
                <h1 className="text-2xl font-bold">权限管理</h1>
            </div>
            <div className="rounded-lg border shadow-sm p-4">
                <DataTable
                    columns={PermissionColumns}
                    onFetch={param => pagePermission(param)}
                    isFixedHeader={false}
                    containerHeight="620px"
                    toolbar={(table, tableId) => (
                        <DataTableToolbar table={table} tableId={tableId}>
                            <ToolbarLeft>
                                <ToolbarItem>
                                    <div className="relative">
                                        <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                                        <Input
                                            placeholder="搜索权限名..."
                                            value={getParam('name') || ''}
                                            onChange={(e) => addParam('name', e.target.value)}
                                            className="pl-8"
                                        />
                                    </div>
                                </ToolbarItem>
                                <ToolbarItem>
                                </ToolbarItem>
                            </ToolbarLeft>


                            <ToolbarRight>
                                <ToolbarItem>
                                    <DataTableViewOptions table={table} tableId={tableId} />
                                </ToolbarItem>
                                <ToolbarItem>
                                    <Button className="h-8 text-green-600 bg-green-50 hover:bg-green-100">
                                        <Plus className="mr-2 h-4 w-4" />
                                        新增权限
                                    </Button>
                                </ToolbarItem>
                            </ToolbarRight>
                        </DataTableToolbar>
                    )}
                    dependencies={[]}
                />
            </div>
        </div>
    )
}