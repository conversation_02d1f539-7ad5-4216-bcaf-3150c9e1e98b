import {ColumnDef} from "@tanstack/react-table";
import {DownloadFile, DownloadFileStatus} from "@/api/supplier/supplier-order/supplier-order-model";
import {FormattedTime} from "@/components/common/formatted-time";
import {Chip} from "@/components/buttons/chip";
import {CopyText} from "@/components/buttons/copy-text-btn";
import {AlertCircle, ClockIcon, Download, FileIcon, FileText, Hash} from "lucide-react";
import {match} from "ts-pattern";
import {TruncatedUrl} from "@/components/buttons/truncated-url";

export const DownloadFileColumns: ColumnDef<DownloadFile>[] = [
    {
        id: 'id',
        accessorKey: 'id',
        header: () => <div className='flex items-center'>
            <Hash className="inline-block w-4 h-4 mr-1" />
            <div>编号</div>
        </div>,
        cell: ({ row }) => (
            <div className="space-y-1">
                <div className="flex items-center space-x-2">
                    <Hash className="h-4 w-4 text-gray-500" />
                    <CopyText value={row.getValue("id")} />
                </div>
            </div>
        ),
        meta: {
            title: '编号',
        },
    },
    {
        id: 'fileName',
        accessorKey: 'fileName',
        header: () => <div className=''>
            <FileText className="inline-block w-4 h-4 mr-1" />
            文件名称
        </div>,
        cell: ({ row }) => (
            <div className="flex items-center space-x-2">
                <FileIcon className="h-4 w-4 text-gray-500" />
                <span>{row.getValue('fileName')}</span>
            </div>
        ),
        meta: {
            title: '文件名称',
        },
    },
    {
        id: 'downloadUrls',
        header: '下载链接',
        cell: ({ row }) => (
            <div className="space-y-2">
                {row.original.zipUrl && (
                    <div className="flex items-center gap-2">
                        <Download className="h-4 w-4" />
                        <span className="text-sm font-medium">压缩包</span>
                        <TruncatedUrl url={row.original.zipUrl} />
                    </div>
                )}
                {row.original.fileUrl && (
                    <div className="flex items-center gap-2">
                        <FileText className="h-4 w-4" />
                        <span className="text-sm font-medium">{row.original.type === '供应商账单' ? "供应商账单" : "供应商订单文件"}</span>
                        <TruncatedUrl url={row.original.fileUrl} />
                    </div>
                )}
                {row.original.wayBillPdfMergeUrl && (
                    <div className="flex items-center gap-2">
                        <FileText className="h-4 w-4" />
                        <span className="text-sm font-medium">运单PDF</span>
                        <TruncatedUrl url={row.original.wayBillPdfMergeUrl} />
                    </div>
                )}
            </div>
        ),
        meta: {
            title: '下载链接',
        },
    },
    {
        id: 'status',
        accessorKey: 'status',
        header: () => <div className=''>
            <AlertCircle className="inline-block w-4 h-4 mr-1" />
            状态
        </div>,
        cell: ({ row }) => {
            const status = row.getValue('status') as DownloadFileStatus;
            return match(status)
                .with(DownloadFileStatus.PROCESSING, () => <Chip variant={'blue'} label='处理中' />)
                .with(DownloadFileStatus.SUCCESS, () => <Chip variant={'green'} label='成功' />)
                .with(DownloadFileStatus.FAILED, () => <Chip variant={'pink'} label='失败' />)
                .otherwise(() => null)
        },
        meta: {
            title: '状态',
        },
    },
    {
        id: 'createdAt',
        accessorKey: 'createdAt',
        header: () => <div className=''>
            <ClockIcon className="inline-block w-4 h-4 mr-1" />
            创建时间
        </div>,
        cell: ({ row }) => (
            <FormattedTime 
                timestamp={row.getValue('createdAt')} 
                formatter='DD/MM/YY HH:mm' 
                className='text-gray-500' 
            />
        ),
        meta: {
            title: '创建时间',
        },
    },
    {
        id: 'updatedAt',
        accessorKey: 'updatedAt',
        header: () => <div className=''>
            <ClockIcon className="inline-block w-4 h-4 mr-1" />
            更新时间
        </div>,
        cell: ({ row }) => (
            <FormattedTime 
                timestamp={row.getValue('updatedAt')} 
                formatter='DD/MM/YY HH:mm' 
                className='text-gray-500' 
            />
        ),
        meta: {
            title: '更新时间',
        },
    }
];