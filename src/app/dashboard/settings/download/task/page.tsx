import { DataTable } from "@/components/table/base-table";
import {
    DataTableToolbar,
    Toolbar<PERSON>tem,
    ToolbarLeft,
    ToolbarRight
} from "@/components/table/toolbar/data-table-toolbar";
import { DataTableViewOptions } from "@/components/table/toolbar/data-table-view-options";
import { pageDownloadFile } from "@/api/supplier/supplier-order/supplier-order-api";
import { DownloadFileColumns } from "./table/download-column";

export default function DownloadTaskPage() {

    return (
        <div className="flex flex-col gap-4">
            <div className="flex items-center justify-between">
                <h1 className="text-2xl font-bold">下载任务管理</h1>
            </div>
            <div className="rounded-lg border shadow-sm p-4">
                <DataTable
                    columns={DownloadFileColumns}
                    onFetch={param => pageDownloadFile(param)}
                    isFixedHeader={false}
                    containerHeight="620px"
                    toolbar={(table, tableId) => (
                        <DataTableToolbar table={table} tableId={tableId}>
                            <ToolbarLeft>
                               
                            </ToolbarLeft>


                            <ToolbarRight>
                                <ToolbarItem>
                                    <DataTableViewOptions table={table} tableId={tableId} />
                                </ToolbarItem>
                            </ToolbarRight>
                        </DataTableToolbar>
                    )}
                    dependencies={[]}
                />
            </div>
        </div>
    )
}