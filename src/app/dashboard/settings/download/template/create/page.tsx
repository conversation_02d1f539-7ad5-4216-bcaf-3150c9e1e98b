import { createDownloadTemplate, downloadTemplate, getDownloadTemplate, TemplateType, updateDownloadTemplate } from "@/api/order/templete/download-templete"
import { Badge } from "@/components/ui/badge"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Separator } from "@/components/ui/separator"
import { Switch } from "@/components/ui/switch"
import { useExcelDownload } from "@/hooks/use-export-file"
import {
    closestCenter,
    DndContext,
    KeyboardSensor,
    PointerSensor,
    useSensor,
    useSensors,
} from '@dnd-kit/core'
import {
    arrayMove,
    SortableContext,
    sortableKeyboardCoordinates,
    useSortable,
    verticalListSortingStrategy,
} from '@dnd-kit/sortable'
import { CSS } from '@dnd-kit/utilities'
import { useRequest } from "ahooks"
import { ArrowLeft, Columns, Download, FileSpreadsheet, GripVertical, Plus, Save, Trash2 } from "lucide-react"
import { useEffect, useState } from "react"
import { useNavigate, useSearchParams } from "react-router-dom"
import { toast } from "sonner"

type ColumnType =
    | "orderNo"
    | "url"
    | "search"
    | "text"
    | "number"
    | "userName"
    | "spu"
    | "size"
    | "color"
    | "qty"
    | "designUrl"
    | "designImgSearchFiled"
    | "effectUrl"
    | "effectImgSearchFiled"
    | "receiveName"
    | "addr1"
    | "addr2"
    | "country"
    | "state"
    | "city"
    | "postCode"
    | "phone"
    | "email"
    | "channel"
    | "shipMethod"
    | "detail"
    | "customerName"
    | "wayBillRelation"
    | "deliveryMethod"
    | "material"
    | "hsCode"

interface ColumnData {
    name: string;
    type: ColumnType;
}

// 添加类型映射对象
const columnTypeLabels: Record<ColumnType, string> = {
    orderNo: "订单号",
    url: "下载地址",
    search: "谷歌搜索",
    text: "文本",
    number: "数字",
    userName: "用户名",
    spu: "SPU",
    size: "尺寸",
    color: "颜色",
    qty: "数量",
    designUrl: "设计图链接",
    designImgSearchFiled: "设计图搜索",
    effectUrl: "效果图链接",
    effectImgSearchFiled: "效果图搜索",
    receiveName: "收件人",
    addr1: "地址1",
    addr2: "地址2",
    country: "国家",
    state: "州/省",
    city: "城市",
    postCode: "邮编",
    phone: "电话",
    email: "邮箱",
    channel: "渠道",
    shipMethod: "配送方式",
    detail: "详情",
    customerName: "客户名称",
    wayBillRelation: "关联运单号",
    deliveryMethod: "发货方式",
    material: "材质",
    hsCode: "HS编码"
}

interface SortableCardProps {
    column: ColumnData;
    index: number;
    isEditMode: boolean;
    updateColumn: (index: number, field: keyof ColumnData, value: string) => void;
    removeColumn: (index: number) => void;
}

function SortableCard({ column, index, isEditMode, updateColumn, removeColumn }: SortableCardProps) {
    const {
        attributes,
        listeners,
        setNodeRef,
        transform,
        transition,
    } = useSortable({ id: index });

    const style = {
        transform: CSS.Transform.toString(transform),
        transition,
    };

    return (
        <Card ref={setNodeRef} style={style} className="shadow-sm">
            <CardContent className="flex items-center justify-between py-3 space-x-2">
                {isEditMode && (
                    <div className="cursor-grab hover:cursor-grabbing active:cursor-grabbing" {...attributes} {...listeners}>
                        <GripVertical className="h-5 w-5 text-gray-400 hover:text-gray-600" />
                    </div>
                )}
                <div className="flex items-center space-x-2 flex-grow">
                    <Label htmlFor={`column-${index}`} className="w-16">
                        列名 {index + 1}:
                    </Label>
                    <Input
                        id={`column-${index}`}
                        value={column.name}
                        onChange={(e) => updateColumn(index, 'name', e.target.value)}
                        className="flex-grow"
                        disabled={!isEditMode}
                    />
                </div>
                <div className="flex items-center">
                    <Label htmlFor={`type-${index}`} className="w-16">类型:</Label>
                    <Select
                        disabled={!isEditMode}
                        value={column.type}
                        onValueChange={(value) => updateColumn(index, 'type', value as ColumnType)}
                    >
                        <SelectTrigger id={`type-${index}`} className="w-[120px]">
                            <SelectValue placeholder="选择类型" />
                        </SelectTrigger>
                        <SelectContent>
                            <SelectItem value="orderNo">订单号</SelectItem>
                            <SelectItem value="url">下载地址</SelectItem>
                            <SelectItem value="search">谷歌搜索</SelectItem>
                            <SelectItem value="userName">用户名</SelectItem>
                            <SelectItem value="spu">SPU</SelectItem>
                            <SelectItem value="size">尺寸</SelectItem>
                            <SelectItem value="color">颜色</SelectItem>
                            <SelectItem value="qty">数量</SelectItem>
                            <SelectItem value="designUrl">设计图链接</SelectItem>
                            <SelectItem value="designImgSearchFiled">设计图搜索</SelectItem>
                            <SelectItem value="effectUrl">效果图链接</SelectItem>
                            <SelectItem value="effectImgSearchFiled">效果图搜索</SelectItem>
                            <SelectItem value="receiveName">收件人</SelectItem>
                            <SelectItem value="addr1">地址1</SelectItem>
                            <SelectItem value="addr2">地址2</SelectItem>
                            <SelectItem value="country">国家</SelectItem>
                            <SelectItem value="state">州/省</SelectItem>
                            <SelectItem value="city">城市</SelectItem>
                            <SelectItem value="postCode">邮编</SelectItem>
                            <SelectItem value="phone">电话</SelectItem>
                            <SelectItem value="email">邮箱</SelectItem>
                            <SelectItem value="channel">渠道</SelectItem>
                            <SelectItem value="shipMethod">配送方式</SelectItem>
                            <SelectItem value="detail">详情</SelectItem>
                            <SelectItem value="customerName">客户名称</SelectItem>
                            <SelectItem value="wayBillRelation">关联运单号</SelectItem>
                            <SelectItem value="deliveryMethod">发货方式</SelectItem>
                            <SelectItem value="material">材质</SelectItem>
                            <SelectItem value="hsCode">HS编码</SelectItem>
                        </SelectContent>
                    </Select>
                </div>
                {isEditMode && (
                    <Button variant="ghost" size="sm" onClick={() => removeColumn(index)}>
                        <Trash2 className="h-4 w-4 text-red-500" />
                    </Button>
                )}
            </CardContent>
        </Card>
    );
}

export default function ExcelTemplateHeaderCreatePage() {
    const [searchParams] = useSearchParams()
    const id = searchParams.get("id")
    const navigate = useNavigate()

    const [columns, setColumns] = useState<ColumnData[]>([])
    const [templateName, setTemplateName] = useState("新模板")
    const [templateType, setTemplateType] = useState<TemplateType>(TemplateType.IMAGE)
    const [isEditMode, setIsEditMode] = useState(false)

    const { data } = useRequest(getDownloadTemplate, {
        defaultParams: [id || undefined],
        ready: !!id
    })

    useEffect(() => {
        if (data) {
            setTemplateName(data.name)
            setTemplateType(data.type)
            setColumns(data.content.slice(1).map(column => ({ name: column.name, type: column.type as ColumnType }))) // 去掉第一个orderNo
        }
    }, [data])


    const { downloadExcel, isLoading } = useExcelDownload(downloadTemplate)

    const addColumn = () => {
        const defaultType: ColumnType = "url"
        const newColumn: ColumnData = {
            name: columnTypeLabels[defaultType],
            type: defaultType
        }
        setColumns(prevColumns => [...prevColumns, newColumn])
    }

    const updateColumn = (index: number, field: keyof ColumnData, value: string) => {
        setColumns(prevColumns => {
            const newColumns = [...prevColumns]
            newColumns[index] = {
                ...newColumns[index],
                [field]: value,
                ...(field === 'type' && { name: columnTypeLabels[value as ColumnType] })
            }
            return newColumns
        })
    }

    const removeColumn = (index: number) => {
        const newColumns = columns.filter((_, i) => i !== index)
        setColumns(newColumns)
    }

    const { runAsync: createDownloadTemplateAsync } = useRequest(createDownloadTemplate, {
        manual: true
    })

    const { runAsync: updateDownloadTemplateAsync } = useRequest(updateDownloadTemplate, {
        manual: true
    })

    const saveTemplate = async () => {
        const templateData = {
            name: templateName,
            type: templateType,
            content: [{ name: "orderNo", type: "orderNo" }, ...columns]
        }

        try {
            const promise = id
                ? updateDownloadTemplateAsync({ id, ...templateData })
                : createDownloadTemplateAsync(templateData)

            toast.promise(promise, {
                loading: '模板保存中...',
                success: (data) => {
                    if (!id) {
                        navigate(`/dashboard/settings/download/template/create?id=${data.id}`)
                    }
                    return `模板${id ? '更新' : '创建'}成功！`
                },
                error: `模板${id ? '更新' : '创建'}失败！`,
            })

            setIsEditMode(false)
        } catch (error) {
            console.error('保存模板失败:', error)
        }
    }

    const sensors = useSensors(
        useSensor(PointerSensor),
        useSensor(KeyboardSensor, {
            coordinateGetter: sortableKeyboardCoordinates,
        })
    );

    const handleDragEnd = (event: any) => {
        const { active, over } = event;

        if (active.id !== over.id) {
            setColumns((items) => {
                const oldIndex = active.id;
                const newIndex = over.id;
                return arrayMove(items, oldIndex, newIndex);
            });
        }
    };

    return (
        <div className="container mx-auto p-4 space-y-6">
            <div className="flex items-center gap-2 justify-between">
                <div className="flex items-center gap-2">
                    <Button variant="ghost" size="sm" onClick={() => navigate(-1)}>
                        <ArrowLeft className="h-5 w-5" />
                    </Button>
                    <CardTitle className="text-2xl flex items-center gap-2">
                        <FileSpreadsheet className="h-6 w-6 " />
                        Excel模板表头设置
                    </CardTitle>
                </div>
                <Button onClick={() => downloadExcel(id)} variant="default" disabled={isLoading} className="bg-green-200 text-green-700 hover:bg-green-300">
                    {isLoading ? (
                        <div className="flex items-center">
                            <span className="loader mr-2"></span> 下载中...
                        </div>
                    ) : (
                        <div className="flex items-center ">
                            <Download className="mr-2 h-4 w-4" /> 下载Excel模版
                        </div>
                    )}
                </Button>
            </div>
            <Card className="shadow-sm">
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <div className="space-y-4 w-full">
                        <div className="space-y-1">
                            <div className="text-sm">文件名:</div>
                            {isEditMode ? (
                                <Input
                                    value={templateName}
                                    onChange={(e) => setTemplateName(e.target.value)}
                                    className="text-lg"
                                />
                            ) : (
                                <div className="text-lg">{templateName}</div>
                            )}
                        </div>
                        <div className="space-y-1">
                            <div className="text-sm">模板类型:</div>
                            <Select
                                disabled={id !== null || !isEditMode}
                                value={templateType}
                                onValueChange={(value) => setTemplateType(value as TemplateType)}
                            >
                                <SelectTrigger className="w-[200px]">
                                    <SelectValue placeholder="选择模板类型" />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem value={TemplateType.IMAGE}>图片模板</SelectItem>
                                    <SelectItem value={TemplateType.ORDER}>订单模板</SelectItem>
                                </SelectContent>
                            </Select>
                        </div>
                    </div>
                    <div className="flex items-center space-x-2">
                        <Label htmlFor="edit-mode" className="text-sm">
                            {isEditMode ? "编辑模式" : "查看模式"}
                        </Label>
                        <Switch
                            id="edit-mode"
                            checked={isEditMode}
                            onCheckedChange={setIsEditMode}
                        />
                    </div>
                </CardHeader>
                <CardContent className="space-y-4">
                    {isEditMode && (
                        <div className="flex justify-between items-center">
                            <Button onClick={addColumn} variant="outline">
                                <Plus className="mr-2 h-4 w-4" /> 添加列
                            </Button>
                            <Button onClick={saveTemplate} variant="default" className="bg-green-200 text-green-700 hover:bg-green-300">
                                <Save className="mr-2 h-4 w-4" /> {id ? '更新' : '保存'}模板
                            </Button>
                        </div>
                    )}

                    <Separator />

                    <Card>
                        <CardHeader>
                            <CardTitle className="text-lg flex items-center gap-2">
                                <Columns className="h-5 w-5" />
                                当前表头预览
                            </CardTitle>
                        </CardHeader>
                        <CardContent>
                            <ScrollArea className="w-full border rounded-lg h-[150px]">
                                <div className="bg-gray-50 border-b min-w-fit">
                                    <div className="flex flex-wrap">
                                        <div className="p-3 text-sm font-medium text-gray-900 text-center border-r border-b min-w-[200px]">
                                            <span>订单号</span>
                                            <Badge variant="secondary" className="ml-2 font-normal">
                                                orderNo
                                            </Badge>
                                        </div>
                                        {columns.map((column: ColumnData, index: number) => (
                                            <div key={index} className="p-3 text-sm font-medium text-gray-900 text-center border-r border-b min-w-[200px]">
                                                <span>{column.name}</span>
                                                <Badge variant="secondary" className="ml-2 font-normal">
                                                    {column.type}
                                                </Badge>
                                            </div>
                                        ))}
                                    </div>
                                </div>
                            </ScrollArea>
                        </CardContent>
                    </Card>

                    <Separator />

                    <DndContext
                        sensors={sensors}
                        collisionDetection={closestCenter}
                        onDragEnd={handleDragEnd}
                    >
                        <SortableContext
                            items={columns.map((_, index) => index)}
                            strategy={verticalListSortingStrategy}
                        >
                            <div className="space-y-4">
                                {columns.map((column: ColumnData, index: number) => (
                                    <SortableCard
                                        key={index}
                                        column={column}
                                        index={index}
                                        isEditMode={isEditMode}
                                        updateColumn={updateColumn}
                                        removeColumn={removeColumn}
                                    />
                                ))}
                            </div>
                        </SortableContext>
                    </DndContext>
                </CardContent>
            </Card>

        </div>
    )
}