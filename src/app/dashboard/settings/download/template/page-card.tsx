import {downloadTemplate, DownloadTemplate, setDefaultTemplate, TemplateType} from "@/api/order/templete/download-templete"
import {DoubleCheckBtn} from "@/components/buttons/double-check-btn"
import {FormattedTime} from "@/components/common/formatted-time"
import {Badge} from "@/components/ui/badge"
import {Button} from "@/components/ui/button"
import {Card, CardContent, CardHeader} from "@/components/ui/card"
import {useExcelDownload} from "@/hooks/use-export-file"
import {Calendar, Download, Edit, FileSpreadsheet, Image, ShoppingCart, Star, Trash2, User} from "lucide-react"
import {useState} from "react"
import {Link} from "react-router-dom"
import {toast} from "sonner"

export default function DownloadTemplatePageCard({ template, onDelete, onStar }: { template: DownloadTemplate, onDelete: () => void, onStar: () => void }) {
    const [isHovered, setIsHovered] = useState(false)
    const { downloadExcel } = useExcelDownload(downloadTemplate)

    const handleDownload = async () => {
        await downloadExcel(template.id)
    }

    const isDefault = template.onDefault
    const isImageType = template.type === TemplateType.IMAGE

    return (
        <Card
            className={`w-full max-w-md overflow-hidden hover:scale-105 transition-all duration-300 hover:cursor-pointer ${
                isImageType ? 'hover:shadow-purple-100' : 'hover:shadow-blue-100'
            } hover:shadow-lg`}
            onMouseEnter={() => setIsHovered(true)}
            onMouseLeave={() => setIsHovered(false)}
        >
            <CardHeader className="flex flex-row items-start justify-between space-y-0 pb-2">
                <div className="flex items-start space-x-4">
                    <div className={`rounded-full p-2 ${
                        isImageType ? 'bg-purple-100' : 'bg-blue-100'
                    }`}>
                        {isImageType ? (
                            <Image className="h-6 w-6 text-purple-600" />
                        ) : (
                            <ShoppingCart className="h-6 w-6 text-blue-600" />
                        )}
                    </div>
                    <div>
                        <h2 className="text-xl font-semibold">{template.name.length > 15 ? template.name.slice(0, 15) + '...' : template.name}</h2>
                        <div className="flex flex-col gap-1.5">
                            <Badge 
                                variant="outline" 
                                className={`w-fit ${isImageType 
                                    ? 'text-purple-600 border-purple-200 bg-purple-50' 
                                    : 'text-blue-600 border-blue-200 bg-blue-50'
                                }`}
                            >
                                {isImageType ? '图片模板' : '订单模板'}
                            </Badge>
                            <div className="flex items-center gap-1">
                                <FileSpreadsheet className="h-4 w-4 text-gray-500" />
                                <p className="text-sm text-muted-foreground">列数: {template.content.length}</p>
                            </div>
                        </div>
                    </div>
                </div>
                <div className="flex items-center space-x-2">
                    {/* {!isHovered && isNew && <Badge variant="secondary" className="text-blue-600 bg-[#EFF6FF]">新</Badge>} */}
                    {isDefault && <Badge variant="secondary" className="text-green-600 bg-[#F0FDF4]">默认</Badge>}
                    {isHovered && (
                        <>
                            <Button variant="ghost" size="icon" className="h-8 w-8"
                                onClick={() => {
                                    toast.promise(setDefaultTemplate(template.id).then(() => onStar()), {
                                        loading: '设置中...',
                                        success: '设置成功',
                                        error: '设置失败'
                                    })
                                }}
                            >
                                <Star className={`h-4 w-4 ${isDefault ? 'text-yellow-500' : 'text-gray-400'}`} />
                                <span className="sr-only">收藏</span>
                            </Button>
                            <Button variant="ghost" size="icon" className="h-8 w-8" onClick={handleDownload}>
                                <Download className="h-4 w-4 text-blue-500" />
                                <span className="sr-only">Download</span>
                            </Button>
                            <Button variant="ghost" size="icon" className="h-8 w-8" asChild>
                                <Link to={`/dashboard/download/template/create?id=${template.id}`}>
                                    <Edit className="h-4 w-4 text-green-500"/>
                                </Link>
                            </Button>
                            <DoubleCheckBtn
                                variant={'ghost'}
                                buttonText={<Trash2 className="h-4 w-4  text-red-500" />}
                                title="删除模板"
                                description="确定要删除这个模板吗？此操作不可恢复。"
                                onConfirm={onDelete}
                                onCancel={() => { }}
                            />
                        </>
                    )}

                </div>
            </CardHeader>
            <CardContent className="space-y-4">
            </CardContent>
            <div className="bg-muted px-6 py-4">
                <div className="flex items-center justify-between">
                    <div className="flex items-center text-sm text-muted-foreground">
                        <Calendar className="mr-2 h-4 w-4" />
                        <p className="">创建时间:</p>
                        <FormattedTime timestamp={template.createdAt} formatter='YYYY-MM-DD HH:mm' className='text-gray-500 ' />
                    </div>
                    <div className="flex -space-x-1 overflow-hidden">
                        <div className="rounded-full bg-secondary text-center text-xs font-medium leading-8 flex  items-center gap-2">
                            <User className="h-4 w-4 text-gray-500" />
                            <div className="text-gray-500">
                                {template.createdBy.toUpperCase()}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </Card>
    )
}