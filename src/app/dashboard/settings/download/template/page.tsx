import {deleteDownloadTemplate, queryDownloadTemplate, TemplateType} from "@/api/order/templete/download-templete"
import {Button} from "@/components/ui/button"
import {Input} from "@/components/ui/input"
import {useRequest} from "ahooks"
import {Image, Plus, Search, ShoppingCart} from "lucide-react"
import {useState} from "react"
import {Link} from "react-router-dom"
import {toast} from "sonner"
import DownloadTemplatePageCard from "./page-card"

export default function ExcelTemplateListPage() {
    const [searchTerm, setSearchTerm] = useState("")

    const { data: templates, refresh } = useRequest(() => queryDownloadTemplate(searchTerm), {
        refreshDeps: [searchTerm]
    })

    const { runAsync: deleteTemplate } = useRequest(deleteDownloadTemplate, {
        manual: true
    })

    const handleDelete = async (id: string) => {
        try {
            await deleteTemplate(id)
            toast.success('模板删除成功')
            refresh()
        } catch (error: any) {
            toast.error('模板删除失败: ' + error.message)
        }
    }

    const filteredTemplates = templates
        ?.filter(template => template.name.toLowerCase().includes(searchTerm.toLowerCase()))
        .sort((a, b) => {
            if (a.onDefault && !b.onDefault) return -1
            if (!a.onDefault && b.onDefault) return 1
            return 0
        })

    const imageTemplates = filteredTemplates?.filter(template => template.type === TemplateType.IMAGE) || []
    const orderTemplates = filteredTemplates?.filter(template => template.type === TemplateType.ORDER) || []

    return (
        <div className="container mx-auto p-4 space-y-6">
            <div className="flex justify-between items-center">
                <h1 className="text-3xl font-bold">Excel 模板列表</h1>
                <Button variant="outline" asChild>
                    <Link to='/dashboard/download/template/create'>
                    <Plus className="mr-2 h-4 w-4" /> 创建新模板
                    </Link>
                </Button>
            </div>

            <div className="relative">
                <Search className="absolute left-2 top-1/2 transform -translate-y-1/2 text-gray-400" />
                <Input
                    type="text"
                    placeholder="搜索模板..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10"
                />
            </div>

            <div className="space-y-8">
                {/* 图片模板区域 */}
                <div className="space-y-4">
                    <div className="flex items-center gap-2 border-b pb-2">
                        <Image className="h-5 w-5 text-purple-600" />
                        <h2 className="text-xl font-semibold text-purple-600">图片模板</h2>
                        <div className="text-sm text-gray-500">({imageTemplates.length})</div>
                    </div>
                    <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
                        {imageTemplates.map((template) => (
                            <DownloadTemplatePageCard
                                key={template.id}
                                template={template}
                                onDelete={() => handleDelete(template.id)}
                                onStar={() => refresh()}
                            />
                        ))}
                    </div>
                </div>

                {/* 订单模板区域 */}
                <div className="space-y-4">
                    <div className="flex items-center gap-2 border-b pb-2">
                        <ShoppingCart className="h-5 w-5 text-blue-600" />
                        <h2 className="text-xl font-semibold text-blue-600">订单模板</h2>
                        <div className="text-sm text-gray-500">({orderTemplates.length})</div>
                    </div>
                    <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
                        {orderTemplates.map((template) => (
                            <DownloadTemplatePageCard
                                key={template.id}
                                template={template}
                                onDelete={() => handleDelete(template.id)}
                                onStar={() => refresh()}
                            />
                        ))}
                    </div>
                </div>
            </div>
        </div>
    )
}