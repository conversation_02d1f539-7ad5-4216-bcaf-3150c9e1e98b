"use client"

export default function ChannelPage() {
    return (
        <div className="flex flex-col gap-4">
            <div className="flex items-center justify-between">
                <h1 className="text-2xl font-bold">渠道管理</h1>
            </div>
            {/* <div className="rounded-lg border shadow-sm p-4">
                <DataTable
                    columns={ChannelColumns}
                    onFetch={async (params) => {
                        const channels = await getChannels();
                        return {
                            data: channels,
                            total: channels.length,
                            page: 1,
                            pageSize: channels.length
                        };
                    }}
                    isFixedHeader={false}
                    containerHeight="620px"
                    toolbar={(table, tableId) => (
                        <ChannelDataTableToolbar table={table} tableId={tableId} />
                    )}
                    dependencies={[]}
                />
            </div> */}
        </div>
    )
}
