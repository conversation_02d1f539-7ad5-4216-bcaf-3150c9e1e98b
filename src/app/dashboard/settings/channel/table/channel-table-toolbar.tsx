"use client"

import { DataTableToolbar, ToolbarLeft } from "@/components/table/toolbar/data-table-toolbar";
import { Table } from "@tanstack/react-table";
import { Channel } from "@/api/channel/channel-model";

interface ChannelDataTableToolbarProps {
    table: Table<Channel>;
    tableId: string;
}

export function ChannelDataTableToolbar({ table, tableId }: ChannelDataTableToolbarProps) {
    return (
        <DataTableToolbar table={table} tableId={tableId}>
            <ToolbarLeft>
                <div className="text-sm text-gray-600">
                    共 {table.getFilteredRowModel().rows.length} 个渠道
                </div>
            </ToolbarLeft>
        </DataTableToolbar>
    );
}
