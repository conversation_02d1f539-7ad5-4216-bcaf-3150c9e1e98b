"use client"

import { Channel } from "@/api/channel/channel-model";
import { updateChannel } from "@/api/channel/channel-api";
import { Button } from "@/components/ui/button";
import { pushModal } from "@/components/modals";
import { refreshTableAtom } from "@/state/common";
import { ColumnDef } from "@tanstack/react-table";
import { useSetAtom } from "jotai";
import { Edit, Package, Settings, Tag } from "lucide-react";
import React from "react";
import { toast } from "sonner";
import { useRequest } from "ahooks";

interface ChannelActionsProps {
    channel: Channel;
}

const ChannelActions: React.FC<ChannelActionsProps> = ({ channel }) => {
    const refresh = useSetAtom(refreshTableAtom);

    const { runAsync: updateChannelAsync } = useRequest(updateChannel, {
        manual: true,
    });

    const handleEdit = () => {
        pushModal('EditChannelModal', {
            channel: channel,
            onSave: async (updatedChannel: any) => {
                try {
                    await updateChannelAsync(channel.id, updatedChannel);
                    toast.success('更新成功');
                    refresh(prev => prev + 1);
                } catch (error: any) {
                    toast.error('更新失败: ' + error.message);
                }
            },
            onClose: () => { },
        });
    };

    return (
        <div className='flex items-center gap-2'>
            <Button
                variant="link"
                className="h-auto p-0 text-blue-600 text-xs"
                onClick={handleEdit}
            >
                <Edit className="h-3 w-3 mr-1" />
                编辑
            </Button>
        </div>
    );
};

export const ChannelColumns: ColumnDef<Channel>[] = [
    {
        accessorKey: "displayName",
        header: '渠道名称',
        cell: ({ row }) => (
            <div className="flex items-center gap-2">
                <Package className="h-4 w-4 text-blue-500" />
                <span className="font-medium">{row.original.displayName}</span>
            </div>
        ),
        meta: {
            title: '渠道名称',
        },
    },
    {
        accessorKey: "name",
        header: '渠道代码',
        cell: ({ row }) => (
            <div className="flex items-center gap-2">
                <Tag className="h-4 w-4 text-gray-500" />
                <span className="text-sm font-mono">{row.original.name}</span>
            </div>
        ),
        meta: {
            title: '渠道代码',
        },
    },
    {
        accessorKey: "methodCode",
        header: '方法代码',
        cell: ({ row }) => (
            <span className="text-sm font-mono">{row.original.methodCode}</span>
        ),
        meta: {
            title: '方法代码',
        },
    },
    {
        accessorKey: "methodName",
        header: '方法名称',
        cell: ({ row }) => (
            <span className="text-sm">{row.original.methodName}</span>
        ),
        meta: {
            title: '方法名称',
        },
    },
    {
        accessorKey: "iossNumber",
        header: 'IOSS税号',
        cell: ({ row }) => (
            <span className="text-sm font-mono">
                {row.original.iossNumber || <span className="text-gray-400">-</span>}
            </span>
        ),
        meta: {
            title: 'IOSS税号',
        },
    },
    {
        accessorKey: "enabled",
        header: '状态',
        cell: ({ row }) => (
            <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                row.original.enabled 
                    ? 'bg-green-100 text-green-800' 
                    : 'bg-red-100 text-red-800'
            }`}>
                {row.original.enabled ? '启用' : '禁用'}
            </span>
        ),
        meta: {
            title: '状态',
        },
    },
    {
        id: "actions",
        header: '操作',
        cell: ({ row }) => <ChannelActions channel={row.original} />,
        meta: {
            title: '操作',
        },
    },
];
