import { OrderImageTask, retryDownload } from '@/api/order/image-task/order-image-task';
import { Chip } from '@/components/buttons/chip';
import { CopyText } from '@/components/buttons/copy-text-btn';
import { TruncatedUrl } from '@/components/buttons/truncated-url';
import { FormattedTime } from '@/components/common/formatted-time';
import { pushModal } from '@/components/modals';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';
import { refreshTableAtom } from '@/state/common';
import { ColumnDef } from '@tanstack/react-table';
import { useSetAtom } from 'jotai';
import { AlertCircle, ClipboardListIcon, ClockIcon, Download, FileIcon, Tag } from 'lucide-react';
import { toast } from 'sonner';
import { match } from 'ts-pattern';

export const OrderImageTaskColumns: ColumnDef<OrderImageTask>[] = [
    {
        id: 'orderNo',
        accessorKey: 'orderNo',
        header: () => <div className=''>
            <FileIcon className="inline-block w-4 h-4 mr-1" />
            订单号
        </div>,
        cell: ({ row }) => <CopyText className='font-bold' value={row.getValue('orderNo')} />,
        meta: {
            title: '订单号',
        },
    },
    {
        id: "downloadUrl",
        accessorKey: "downloadUrl",
        header: () => <div className=''>
            <Download className="inline-block w-4 h-4 mr-1" />
            下载地址
        </div>,
        cell: ({ row }) => (
            <TruncatedUrl url={row.getValue('downloadUrl')} />
        ),
        meta: {
            title: '下载地址',
        },
    },
    // {
    //     id: 'orderId',
    //     accessorKey: 'orderId',
    //     header: () => <div className=''>
    //         <HashIcon className="inline-block w-4 h-4 mr-1" />
    //         订单ID
    //     </div>,
    //     cell: ({ row }) => <div> {row.getValue('orderId')}</div >,
    //     meta: {
    //         title: '订单ID',
    //     },
    // },
    {
        id: 'tag',
        accessorKey: 'tag',
        header: () => <div className=''>
            <Tag className="inline-block w-4 h-4 mr-1" />
            标签
        </div>,
        cell: ({ row }) => <div className='font-bold'>{row.getValue('tag')}</div>,
        meta: {
            title: '标签',
        },
    },
    {
        id: 'status',
        accessorKey: 'status',
        header: () => <div className=''>
            <ClipboardListIcon className="inline-block w-4 h-4 mr-1" />
            状态
        </div>,
        cell: ({ row }) => {
            const status = row.getValue('status');
            return match(status)
                .with('CREATED', () => <Chip variant={'yellow'} label='排队中' />)
                .with('FAILED', () => <Chip variant={'pink'} label='失败' />)
                .with('PROCESSING', () => <Chip variant={'blue'} label='处理中' />)
                .with('COMPLETED', () => <Chip variant={'green'} label='完成' showIcon={false} icon={<span className="text-green-500">✓</span>} />)
                .otherwise(() => null)
        },
        meta: {
            title: '状态',
        },
    },
    {
        id: 'errorMessage',
        accessorKey: 'errorMessage',
        header: () => <div className=''>
            <AlertCircle className="inline-block w-4 h-4 mr-1" />
            错误信息
        </div>,
        cell: ({ row }) => (
            row.getValue('errorMessage') ? (
                <Alert variant="destructive" className="p-2 max-w-[350px]">
                    {/* <AlertCircle className="h-4 w-4" /> */}
                    <AlertDescription className="text-xs">
                        {(row.getValue('errorMessage') as string).split(':')[0]}
                    </AlertDescription>
                </Alert>
            ) : null
        ),
        meta: {
            title: '错误信息',
        },
    },

    {
        id: 'createdAt',
        accessorKey: 'createdAt',
        header: () => <div className=''>
            <ClockIcon className="inline-block w-4 h-4 mr-1" />
            创建时间
        </div>,
        cell: ({ row }) => {
            return <FormattedTime timestamp={row.getValue('createdAt')} formatter='MM/DD/YY HH:mm' className='text-gray-500' />
        },
        meta: {
            title: '创建时间',
        },
    },

    {
        id: 'actions',
        header: '操作',
        meta: {
            title: '操作',
        },
        cell: ({ row }) => {
            const setRefreshTable = useSetAtom(refreshTableAtom);

            return (
                <div className="flex items-center gap-2 text-sm">
                    <Button
                        variant="link"
                        className="h-auto p-0 text-blue-600 text-xs"
                        onClick={() => pushModal('ImageGalleryModal', { imageUrls: row.original.uploadUrl.split(',') })}
                    >
                        查看图片
                    </Button>
                    {row.original.status !== 'COMPLETED' && (
                        <>
                            <Separator orientation='vertical' className='h-4' />
                            <Button
                                variant="link"
                                className="h-auto p-0 text-red-600 text-xs"
                                onClick={() => {
                                    toast.promise(
                                        retryDownload(row.original.id as string).then(() => {
                                            setRefreshTable(prev => prev + 1);
                                        }),
                                        {
                                            loading: '重试中...',
                                            success: '重试成功',
                                            error: (error) => {
                                                return '重试失败: ' + error.message;
                                            },
                                        }
                                    );
                                }}
                            >
                                重试
                            </Button>
                        </>
                    )}
                </div>
            );
        },
    },
];
