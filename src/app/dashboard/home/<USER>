import { pageQueryOrderImageTask } from "@/api/order/image-task/order-image-task";
import { DataTable } from "@/components/table/base-table";
import {
    DataTableToolbar,
    ToolbarCenter,
    ToolbarItem,
    ToolbarLeft,
    ToolbarRight
} from "@/components/table/toolbar/data-table-toolbar";
import { DataTableViewOptions } from "@/components/table/toolbar/data-table-view-options";
import { Ta<PERSON>, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Textarea } from "@/components/ui/textarea";
import useSearchParamsManager from "@/hooks/use-url-param";
import { OrderImageTaskColumns } from "./table/download-task-column";

// 定义下载状态类型
const DOWNLOAD_STATUS = {
    ALL: 'ALL',
    PENDING: 'CREATED',
    DOWNLOADING: 'PROCESSING',
    COMPLETED: 'COMPLETED',
    FAILED: 'FAILED',
} as const

export function HomePage() {
    const { addParam, getParam, deleteParam } = useSearchParamsManager();

    const defaultValue = getParam('status') || DOWNLOAD_STATUS.ALL;

    return (
        <div className="flex flex-col gap-4">
            <div className="flex items-center justify-between">
                <h1 className="text-2xl font-bold">下载任务</h1>
            </div>
            <div className="rounded-lg border shadow-sm p-4">
                <DataTable
                    columns={OrderImageTaskColumns}
                    onFetch={param => pageQueryOrderImageTask({
                        ...param,
                    })}
                    isFixedHeader={true}
                    containerHeight="600px"
                    toolbar={(table, tableId) => (
                        <DataTableToolbar table={table} tableId={tableId}>
                            <ToolbarLeft>
                                <div className="flex flex-col gap-2">
                                    <ToolbarItem>
                                        <Tabs
                                            defaultValue={defaultValue}
                                            className="w-full"
                                            onValueChange={(value) => {
                                                if (value === DOWNLOAD_STATUS.ALL) {
                                                    deleteParam('status');
                                                } else {
                                                    addParam('status', value);
                                                }
                                            }}
                                        >
                                            <TabsList>
                                                <TabsTrigger value={DOWNLOAD_STATUS.ALL}>全部</TabsTrigger>
                                                <TabsTrigger value={DOWNLOAD_STATUS.PENDING}>待下载</TabsTrigger>
                                                <TabsTrigger value={DOWNLOAD_STATUS.DOWNLOADING}>下载中</TabsTrigger>
                                                <TabsTrigger value={DOWNLOAD_STATUS.COMPLETED}>已完成</TabsTrigger>
                                                <TabsTrigger value={DOWNLOAD_STATUS.FAILED}>失败</TabsTrigger>
                                            </TabsList>
                                        </Tabs>
                                    </ToolbarItem>
                                    <div className="flex items-start gap-2">
                                        <ToolbarItem>
                                            {/* <div className="relative"> */}
                                            {/* <Search className="absolute left-2 top-2 h-4 w-4 text-muted-foreground text-xs" /> */}
                                            <Textarea
                                                placeholder="搜索订单,多个订单用逗号隔开..."
                                                className="text-xs w-96"
                                                defaultValue={getParam('orderNo') || ''}
                                                onChange={(e) => addParam('orderNo', e.target.value)}
                                            />
                                            {/* </div> */}
                                        </ToolbarItem>
                                        <ToolbarItem>
                                            {/* <div className="relative"> */}
                                            {/* <Search className="absolute left-2 top-2 h-4 w-4 text-muted-foreground text-xs" /> */}
                                            <Textarea
                                                    placeholder="根据主订单编号搜索..."
                                                className="text-xs w-96"
                                                defaultValue={getParam('orderId') || ''}
                                                onChange={(e) => addParam('orderId', e.target.value)}
                                            />
                                            {/* </div> */}
                                        </ToolbarItem> 
                                    </div>
                                </div>
                                
                            </ToolbarLeft>

                            <ToolbarCenter>
                                <ToolbarItem>
                                    {/* 中间的组件 */}
                                </ToolbarItem>
                            </ToolbarCenter>

                            <ToolbarRight>
                                <ToolbarItem>
                                    <DataTableViewOptions table={table} tableId={tableId} />
                                </ToolbarItem>
                            </ToolbarRight>
                        </DataTableToolbar>
                    )}
                    dependencies={[status]} // 添加 status 作为依赖项，当状态改变时重新获取数据
                />
            </div>
        </div>
    )
}
