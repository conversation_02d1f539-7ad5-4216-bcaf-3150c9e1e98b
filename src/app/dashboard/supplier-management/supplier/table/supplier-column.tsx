import { deleteSupplier, enableSupplier, disableSupplier } from '@/api/supplier/supplier-api';
import { SupplierPageResponse } from '@/api/supplier/supplier-model';
import { Chip } from '@/components/buttons/chip';
import { DoubleCheckBtn } from '@/components/buttons/double-check-btn';
import { FormattedTime } from '@/components/common/formatted-time';
import { Button } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';
import { refreshTableAtom } from '@/state/common';
import { ColumnDef } from '@tanstack/react-table';
import { useSetAtom } from 'jotai';
import { Building2, ClockIcon, FileIcon, HashIcon, Phone, Star, User } from 'lucide-react';
import { toast } from 'sonner';
import { match } from 'ts-pattern';
import { pushModal } from '@/components/modals';

export const SupplierColumns: ColumnDef<SupplierPageResponse>[] = [
    {
        id: 'bizId',
        accessorKey: 'bizId',
        header: () => <div className='flex items-center'>
            <HashIcon className="inline-block w-4 h-4 mr-1" />
            <div>供应商编号</div>
        </div>,
        size: 100,
        cell: ({ row }) => row.getValue('bizId'),
        meta: {
            title: '供应商编号',
        },
    },
    {
        id: 'name',
        accessorKey: 'name',
        header: () => <div className=''>
            <Building2 className="inline-block w-4 h-4 mr-1" />
            供应商名称
        </div>,
        cell: ({ row }) => <div>{row.getValue('name')}</div>,
        meta: {
            title: '供应商名称',
        },
    },
    {
        id: 'phone',
        accessorKey: 'phone',
        header: () => <div className=''>
            <Phone className="inline-block w-4 h-4 mr-1" />
            联系电话
        </div>,
        cell: ({ row }) => <div>{row.getValue('phone')}</div>,
        meta: {
            title: '联系电话',
        },
    },
    {
        id: 'description',
        accessorKey: 'description',
        header: () => <div className=''>
            <FileIcon className="inline-block w-4 h-4 mr-1" />
            描述
        </div>,
        cell: ({ row }) => <div>{row.getValue('description')}</div>,
        meta: {
            title: '描述',
        },
    },
    {
        accessorKey: "status",
        header: "状态",
        cell: ({ row }) => {
            return match(row.original.status)
                .with('DISABLED', () => (
                    <div className="flex items-center space-x-2">
                        <div className="w-2 h-2 rounded-full bg-red-500"></div>
                        <Chip label="禁用" variant="red" />
                    </div>
                ))
                .with('ENABLED', () => (
                    <div className="flex items-center space-x-2">
                        <div className="w-2 h-2 rounded-full bg-green-500"></div>
                        <Chip label="启用" variant="green" />
                    </div>
                ))
                .otherwise(() => (
                    <div className="flex items-center space-x-2">
                        <div className="w-2 h-2 rounded-full bg-purple-500"></div>
                        <Chip label="未知状态" variant="purple" />
                    </div>
                ));
        },
        meta: {
            title: '状态',
        },
    },
    {
        id: 'priority',
        accessorKey: 'priority',
        header: () => <div className=''>
            <Star className="inline-block w-4 h-4 mr-1" />
            优先级
        </div>,
        cell: ({ row }) => <div>{row.getValue('priority')}</div>,
        meta: {
            title: '优先级',
        },
    },
    {
        id: 'createdBy',
        accessorKey: 'createdBy',
        header: () => <div className=''>
            <User className="inline-block w-4 h-4 mr-1" />
            创建人
        </div>,
        cell: ({ row }) => <div>{row.getValue('createdBy')}</div>,
        meta: {
            title: '创建人',
        },
    },
    {
        id: 'createdAt',
        accessorKey: 'createdAt',
        header: () => <div className=''>
            <ClockIcon className="inline-block w-4 h-4 mr-1" />
            创建时间
        </div>,
        cell: ({ row }) => {
            return <FormattedTime timestamp={row.getValue('createdAt')} formatter='DD/MM/YY HH:mm' className='text-gray-500' />
        },
        meta: {
            title: '创建时间',
        },
    },
    {
        id: 'actions',
        accessorKey: 'actions',
        header: '操作',
        cell: ({ row }) => {
            const setRefreshTable = useSetAtom(refreshTableAtom);
            return (
                <div className='flex items-center gap-2'>
                    <Button
                        variant="link"
                        className="h-auto p-0 text-blue-600 text-xs"
                        onClick={() => {
                            pushModal('EditSupplierModal', {
                                supplier: {
                                    id: row.original.id,
                                    name: row.original.name,
                                    phone: row.original.phone,
                                    description: row.original.description,
                                    priority: parseInt(row.original.priority),
                                    config: row.original.config,
                                },
                                onSuccess: () => {
                                    setRefreshTable(prev => prev + 1);
                                },
                                onClose: () => { }
                            });
                        }}>
                        编辑
                    </Button>
                    <Separator orientation='vertical' className='h-4' />
                    <Button
                        variant="link"
                        className="h-auto p-0 text-blue-600 text-xs"
                        onClick={() => {
                            const action = row.original.status === 'ENABLED' ? disableSupplier : enableSupplier;
                            const actionText = row.original.status === 'ENABLED' ? '停用' : '启用';
                            toast.promise(
                                action(row.original.id),
                                {
                                    loading: `${actionText}中...`,
                                    success: () => {
                                        setRefreshTable(prev => prev + 1);
                                        return `${actionText}成功`;
                                    },
                                    error: `${actionText}失败`,
                                }
                            );
                        }}>
                        {row.original.status === 'ENABLED' ? '停用' : '启用'}
                    </Button>
                    <Separator orientation='vertical' className='h-4' />
                    <DoubleCheckBtn
                        className='h-auto p-0 text-red-600 text-xs'
                        variant='link'
                        title='删除供应商'
                        description='删除供应商将影响相关业务数据，请谨慎操作'
                        onConfirm={() => {
                            toast.promise(
                                deleteSupplier(row.original.id),
                                {
                                    loading: '删除中...',
                                    success: () => {
                                        setRefreshTable(prev => prev + 1);
                                        return '删除成功';
                                    },
                                    error: '删除失败',
                                }
                            );
                        }}
                        buttonText='删除'
                        onCancel={() => { }}
                    />
                </div>
            )
        },
        meta: {
            title: '操作',
        },
    }
];
