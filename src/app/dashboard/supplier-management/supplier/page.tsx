import { DataTableToolbar, Too<PERSON><PERSON><PERSON><PERSON>, ToolbarLeft } from "@/components/table/toolbar/data-table-toolbar";
import { Search } from "lucide-react";
import { DataTable } from "@/components/table/base-table";
import useSearchParamsManager from "@/hooks/use-url-param";
import { pageSupplier } from "@/api/supplier/supplier-api";
import { Input } from "@/components/ui/input";
import { SupplierColumns } from "./table/supplier-column";


const SupplierPage = () => {

    const { addParam, getParam } = useSearchParamsManager();

    return (
        <div className="flex flex-col gap-4">
            <div className="flex items-center justify-between">
                <h1 className="text-2xl font-bold">供应商管理</h1>
            </div>
            <div className="rounded-lg border shadow-sm p-4">
                <DataTable
                    columns={SupplierColumns}
                    onFetch={param => pageSupplier(param)}
                    isFixedHeader={false}
                    containerHeight="620px"
                    toolbar={(table, tableId) => (
                        <DataTableToolbar table={table} tableId={tableId}>
                            <ToolbarLeft>
                                <ToolbarItem>
                                    <div className="relative">
                                        <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                                        <Input
                                            placeholder="搜索供应商..."
                                            value={getParam('name') || ''}
                                            onChange={(e) => addParam('name', e.target.value)}
                                            className="pl-8"
                                        />
                                    </div>
                                </ToolbarItem>
                                <ToolbarItem>
                                </ToolbarItem>
                            </ToolbarLeft>
                        </DataTableToolbar>
                    )}
                    dependencies={[]}
                />
            </div>
        </div>
    )
};

export default SupplierPage;
