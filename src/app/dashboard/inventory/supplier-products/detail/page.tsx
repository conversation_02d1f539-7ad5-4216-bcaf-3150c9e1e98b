import { createSupplierSpu, findSupplierSpu, updateSupplierSpu } from "@/api/supplier/supplier-spu/supplier-spu-api";
import { SupplierSpuAndSkuResponse, SupplierSpuAndSkuResponseSkus } from "@/api/supplier/supplier-spu/supplier-spu-model";
import { SupplierSpuForm, SupplierSpuFormData } from "@/app/dashboard/inventory/supplier-products/detail/suppliper-spu-form";
import { useRequest } from "ahooks";
import { useNavigate, useSearchParams } from "react-router-dom";
import { toast } from "sonner";
import {
    AlertDialog,
    AlertDialogAction,
    AlertDialogCancel,
    AlertDialogContent,
    AlertDialogDescription,
    AlertDialogFooter,
    AlertDialogHeader,
    AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { useState } from "react";

export default function SupplierSpuDetailPage() {
    const [searchParams] = useSearchParams();
    const supplierSpuId = searchParams.get("id");
    const supplierId = searchParams.get("supplierId");
    const isCreate = supplierSpuId === null;
    const [showConfirmDialog, setShowConfirmDialog] = useState(false);
    const [lastSubmittedData, setLastSubmittedData] = useState<any>(null);

    const navigate = useNavigate();

    const { data, loading, refresh } = useRequest(findSupplierSpu, {
        defaultParams: [supplierSpuId],
    })

    const { runAsync: create } = useRequest(createSupplierSpu, {
        manual: true,
        onSuccess: () => {
            toast.success("创建成功")
            setShowConfirmDialog(true)
        },
        onError: (err) => {
            toast.error(err.message)
        }
    })

    const { runAsync: update } = useRequest(updateSupplierSpu, {
        manual: true,
        onSuccess: () => {
            refresh()
            toast.success("更新成功")
            navigate(`/dashboard/inventory/supplier/products`)
        },
        onError: (err) => {
            toast.error(err.message)
        }
    })

    function convertSupplierSpuResponseToFormData(response: SupplierSpuAndSkuResponse): SupplierSpuFormData {
        return {
            id: response.id,
            systemSpuId: response.systemSpuId,
            supplierSpuCode: response.supplierSpuCode,
            title: response.title,
            name: response.name,
            cnName: response.cnName,
            category: response.category,
            supplierId: response.supplierId,
            productImage: '',
            showImages: [],
            skus: response.skus.map((sku: SupplierSpuAndSkuResponseSkus) => ({
                id: sku.id,
                systemSkuCode: sku.systemSkuCode,
                systemSpuId: sku.systemSpuId,
                systemSkuId: sku.systemSkuId,
                size: sku.size,
                color: sku.color,
                purchaseCost: sku.purchaseCost,
                purchaseCostCurrency: sku.purchaseCostCurrency,
            })),
        };
    }

    const handleSubmit = async (data: any) => {
        setLastSubmittedData(data);
        if (isCreate) {
            await create(data)
        } else {
            await update(supplierSpuId, data)
        }
    };

    const handleContinueCreate = () => {
        setShowConfirmDialog(false);
        // 重置表单，保持相同供应商
        //@ts-ignore
        const defaultValues = {
            supplierId: lastSubmittedData.supplierId,
        };
        window.location.reload();
    };

    const handleFinishCreate = () => {
        setShowConfirmDialog(false);
        navigate(`/dashboard/inventory/supplier/products`);
    };

    if (loading) {
        return <div>loading...</div>
    }

    return (
        <>
            <div className="max-w-5xl mx-auto pt-10 pb-6">
                <SupplierSpuForm
                    isCreate={isCreate}
                    onSubmit={handleSubmit}
                    defaultValues={isCreate ? undefined : convertSupplierSpuResponseToFormData(data!!)}
                    supplierId={supplierId!!}
                />
            </div>

            <AlertDialog open={showConfirmDialog} onOpenChange={setShowConfirmDialog}>
                <AlertDialogContent>
                    <AlertDialogHeader>
                        <AlertDialogTitle>创建成功</AlertDialogTitle>
                        <AlertDialogDescription>
                            是否继续创建新的供应商产品？
                        </AlertDialogDescription>
                    </AlertDialogHeader>
                    <AlertDialogFooter>
                        <AlertDialogCancel onClick={handleFinishCreate}>返回列表</AlertDialogCancel>
                        <AlertDialogAction onClick={handleContinueCreate}>继续创建</AlertDialogAction>
                    </AlertDialogFooter>
                </AlertDialogContent>
            </AlertDialog>
        </>
    );
}