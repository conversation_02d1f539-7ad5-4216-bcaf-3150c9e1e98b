import DecimalInput from "@/components/inputs/demical-input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { userInfoAtom } from "@/state/common";
import { useAtomValue } from "jotai";
import React from 'react';

export type SupplierSKU = {
    id?: string;
    systemSkuId: string;
    systemSkuCode: string;
    systemSpuId: string;
    size: string;
    color: string;
    purchaseCost: number;
    purchaseCostCurrency: string;
};

type SupplierSkuTableProps = {
    skus: SupplierSKU[];
    onSkuChange: (index: number, field: keyof SupplierSKU, value: string | number) => void;
    onBulkEdit?: (field: keyof SupplierSKU, value: string | number) => void;
};

const SupplierSkuTable: React.FC<SupplierSkuTableProps> = ({ skus, onSkuChange, onBulkEdit }) => {
    const userInfo = useAtomValue(userInfoAtom);
    const isSupplier = userInfo?.supplierId != undefined;

    const [bulkEditValues, setBulkEditValues] = React.useState({
        purchaseCost: 0,
        purchaseCostCurrency: '',
    });

    const handleBulkEdit = (field: keyof SupplierSKU, value: string | number) => {
        if (!onBulkEdit) return;
        setBulkEditValues(prev => ({ ...prev, [field]: value }));
        onBulkEdit(field, value);
    };

    return (
        <Table>
            <TableHeader>
                <TableRow>
                    <TableHead className="w-50">SKU 编码</TableHead>
                    <TableHead className="w-20">尺寸</TableHead>
                    <TableHead className="w-20">颜色</TableHead>
                    <TableHead>采购成本</TableHead>
                    <TableHead>币种</TableHead>
                </TableRow>
            </TableHeader>
            <TableBody>
                {onBulkEdit && (
                    <TableRow>
                        <TableCell colSpan={3}>批量编辑：</TableCell>
                        <TableCell>
                            <DecimalInput
                                value={bulkEditValues.purchaseCost}
                                onChange={(value) => handleBulkEdit('purchaseCost', value)}
                                placeholder="批量设置采购成本"
                                min={0}
                            />
                        </TableCell>
                        <TableCell>
                            <Select
                                disabled={isSupplier}
                                value={bulkEditValues.purchaseCostCurrency}
                                onValueChange={(value) => handleBulkEdit('purchaseCostCurrency', value)}
                            >
                                <SelectTrigger>
                                    <SelectValue placeholder="批量设置币种" />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem value="USD">USD</SelectItem>
                                    <SelectItem value="CNY">CNY</SelectItem>
                                    <SelectItem value="EUR">EUR</SelectItem>
                                    <SelectItem value="GBP">GBP</SelectItem>
                                    <SelectItem value="AUD">AUD</SelectItem>
                                    <SelectItem value="JPY">JPY</SelectItem>
                                    <SelectItem value="HKD">HKD</SelectItem>
                                    <SelectItem value="SGD">SGD</SelectItem>
                                </SelectContent>
                            </Select>
                        </TableCell>
                    </TableRow>
                )}
                {skus.map((sku, index) => (
                    <TableRow key={sku.id || `${sku.size}-${sku.color}`}>
                        <TableCell>{sku.systemSkuCode}</TableCell>
                        <TableCell>{sku.size}</TableCell>
                        <TableCell>{sku.color}</TableCell>
                        <TableCell>
                            <DecimalInput
                                value={sku.purchaseCost}
                                onChange={(value) => onSkuChange(index, 'purchaseCost', value)}
                                placeholder="输入采购成本"
                            />
                        </TableCell>
                        <TableCell>
                            <Select
                                disabled={isSupplier}
                                value={sku.purchaseCostCurrency}
                                onValueChange={(value) => onSkuChange(index, 'purchaseCostCurrency', value)}
                            >
                                <SelectTrigger>
                                    <SelectValue>{sku.purchaseCostCurrency}</SelectValue>
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem value="USD">USD</SelectItem>
                                    <SelectItem value="CNY">CNY</SelectItem>
                                    <SelectItem value="EUR">EUR</SelectItem>
                                    <SelectItem value="GBP">GBP</SelectItem>
                                    <SelectItem value="AUD">AUD</SelectItem>
                                    <SelectItem value="JPY">JPY</SelectItem>
                                    <SelectItem value="HKD">HKD</SelectItem>
                                    <SelectItem value="SGD">SGD</SelectItem>
                                </SelectContent>
                            </Select>
                        </TableCell>
                    </TableRow>
                ))}
            </TableBody>
        </Table>
    );
};

export default React.memo(SupplierSkuTable);