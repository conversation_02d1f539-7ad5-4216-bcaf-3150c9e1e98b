import {zod<PERSON>esolver} from "@hookform/resolvers/zod"
import {useForm} from "react-hook-form"
import {z} from "zod"
import {Button} from "@/components/ui/button"
import {Form, FormControl, FormField, FormItem, FormLabel, FormMessage} from "@/components/ui/form"
import {Input} from "@/components/ui/input"
import {Textarea} from "@/components/ui/textarea"
import {useEffect} from 'react'
import {toast} from "sonner"
import {Card, CardContent, CardHeader, CardTitle} from "@/components/ui/card"
import {ArrowLeft, Info, Package2} from "lucide-react"
import SupplierSkuTable, {SupplierSKU} from "./supplier-sku-table"
import {Command, CommandEmpty, CommandGroup, CommandInput, CommandItem, CommandList} from "@/components/ui/command"
import {Popover, PopoverContent, PopoverTrigger} from "@/components/ui/popover"
import {CaretSortIcon, CheckIcon} from "@radix-ui/react-icons"
import {cn} from "@/lib/utils"
import {useRequest} from "ahooks"
import {findSkus, selectSpu} from "@/api/inventory/spu/spu-api"
import {SpuSelectResponse} from "@/api/inventory/spu/spu-model"
import {SkuResponse} from "@/api/inventory/sku/sku-model"
import {Link} from "react-router-dom"

const formSchema = z.object({
    systemSpuId: z.string().min(1, "系统SPU是必填项"),
    supplierSpuCode: z.string().min(1, "SPU编码是必填项"),
    supplierId: z.string(),
    title: z.string(),
    name: z.string(),
    cnName: z.string(),
    category: z.string(),
    productImage: z.string(),
    showImages: z.array(z.string()),
    description: z.string().optional(),
    skus: z.array(z.object({
        id: z.string().optional(),
        systemSkuId: z.string(),
        systemSkuCode: z.string(),
        systemSpuId: z.string(),
        size: z.string(),
        color: z.string(),
        purchaseCost: z.number(),
        purchaseCostCurrency: z.string(),
    })),
})

type FormData = z.infer<typeof formSchema>

interface Props {
    defaultValues?: FormData
    onSubmit: (data: FormData) => void
    readOnly?: boolean
    supplierId: string
    isCreate?: boolean
}

export interface SupplierSpuFormData extends FormData {
    id?: string;
}

export function SupplierSpuForm({ defaultValues, onSubmit: onFormSubmit, readOnly, supplierId, isCreate }: Props) {
    const { data: systemSpus } = useRequest(selectSpu, {
        defaultParams: [isCreate && supplierId ? supplierId : null],
    });

    const form = useForm<FormData>({
        resolver: zodResolver(formSchema),
        defaultValues: defaultValues || {
            systemSpuId: "",
            supplierSpuCode: "",
            title: "",
            name: "",
            cnName: "",
            category: "",
            productImage: "",
            showImages: [],
            description: "",
            skus: [],
        },
    })

    useEffect(() => {
        if (defaultValues) {
            form.reset(defaultValues)
        }
    }, [defaultValues, form])

    const { watch, setValue } = form
    const skus = watch('skus')
    //@ts-ignore
    const systemSpuId = watch('systemSpuId')

    const handleSkuChange = (index: number, field: keyof SupplierSKU, value: string | number) => {
        const updatedSkus = [...skus]
        updatedSkus[index] = { ...updatedSkus[index], [field]: value }
        setValue('skus', updatedSkus)
    }

    const handleBulkEdit = (field: keyof SupplierSKU, value: string | number) => {
        const updatedSkus = skus.map(sku => ({
            ...sku,
            [field]: value
        }));
        setValue('skus', updatedSkus);
    };

    const handleSystemSpuChange = async (newSystemSpuId: string) => {
        if (!readOnly) {
            const skus = await findSkus(newSystemSpuId);
            try {
                const selectedSpu = systemSpus?.find((spu: SpuSelectResponse) => spu.id === newSystemSpuId);
                if (selectedSpu) {
                    setValue('systemSpuId', newSystemSpuId);
                    setValue('supplierSpuCode', selectedSpu.spuCode);
                    setValue('title', selectedSpu.title);
                    setValue('name', selectedSpu.name);
                    setValue('cnName', selectedSpu.cnName);
                    setValue('category', selectedSpu.category);
                    setValue('productImage', "");
                    setValue('showImages', []);
                    setValue('description', "");
                    setValue('supplierId', supplierId);
                    setValue('skus', skus.map((sku: SkuResponse) => ({
                        systemSkuCode: sku.skuCode,
                        systemSpuId: sku.spuId,
                        systemSkuId: sku.id,
                        size: sku.size,
                        color: sku.color,
                        purchaseCost: 0,
                        purchaseCostCurrency: 'USD',
                    })));
                }
            } catch (error) {
                console.error("获取系统SPU详情时出错:", error);
                toast.error("获取系统SPU详情失败。请重试。");
            }
        }
    };

    async function onSubmit(values: FormData) {
        try {
            await onFormSubmit(values)
        } catch (error: any) {
            toast.error("保存失败: " + error.message)
        }
    }

    return (
        <div className="space-y-6">
            <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                    <Button variant="ghost" size="icon" asChild>
                        <Link to="/dashboard/inventory/supplier/products">
                            <ArrowLeft className="h-4 w-4" />
                        </Link>
                    </Button>
                    <h2 className="text-lg font-medium">{isCreate ? '创建供应商SPU' : '编辑供应商SPU'}</h2>
                </div>
            </div>

            <Form {...form}>
                <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
                    <Card>
                        <CardHeader>
                            <CardTitle className="flex items-center gap-2">
                                <Info className="w-5 h-5" />
                                基本信息
                            </CardTitle>
                        </CardHeader>
                        <CardContent className="space-y-4">
                            <FormField
                                control={form.control}
                                name="systemSpuId"
                                render={({ field }) => (
                                    <FormItem className="flex flex-col">
                                        <FormLabel>系统SPU</FormLabel>
                                        <Popover>
                                            <PopoverTrigger asChild>
                                                <FormControl>
                                                    <Button
                                                        variant="outline"
                                                        role="combobox"
                                                        disabled={!isCreate}
                                                        className={cn(
                                                            "w-[800px] justify-between",
                                                            !field.value && "text-muted-foreground"
                                                        )}
                                                    >
                                                        {field.value
                                                            ? systemSpus?.find((spu: SpuSelectResponse) => spu.id === field.value)?.spuCode
                                                            : "选择系统SPU"}
                                                        <CaretSortIcon className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                                                    </Button>
                                                </FormControl>
                                            </PopoverTrigger>
                                            <PopoverContent className="w-[800px] p-0">
                                                <Command className="w-full">
                                                    <CommandInput placeholder="搜索系统SPU..." />
                                                    <CommandEmpty>未找到系统SPU</CommandEmpty>
                                                    <CommandGroup className='max-h-[300px] overflow-y-auto'>
                                                        <CommandList>
                                                            {systemSpus?.map((spu: SpuSelectResponse) => (
                                                                <CommandItem
                                                                    value={spu.spuCode}
                                                                    key={spu.id}
                                                                    onSelect={() => {
                                                                        handleSystemSpuChange(spu.id);
                                                                        form.setValue("systemSpuId", spu.id);
                                                                    }}
                                                                >
                                                                    <CheckIcon
                                                                        className={cn(
                                                                            "mr-2 h-4 w-4",
                                                                            spu.id === field.value
                                                                                ? "opacity-100"
                                                                                : "opacity-0"
                                                                        )}
                                                                    />
                                                                    {spu.spuCode}
                                                                </CommandItem>
                                                            ))}
                                                        </CommandList>
                                                    </CommandGroup>
                                                </Command>
                                            </PopoverContent>
                                        </Popover>
                                        <FormMessage />
                                    </FormItem>
                                )}
                            />

                            <div className="grid grid-cols-2 gap-4">
                                <FormField
                                    control={form.control}
                                    name="supplierSpuCode"
                                    render={({ field }) => (
                                        <FormItem>
                                            <FormLabel>SPU编码</FormLabel>
                                            <FormControl>
                                                <Input {...field} disabled={false} />
                                            </FormControl>
                                            <FormMessage />
                                        </FormItem>
                                    )}
                                />
                                <FormField
                                    control={form.control}
                                    name="title"
                                    render={({ field }) => (
                                        <FormItem>
                                            <FormLabel>标题</FormLabel>
                                            <FormControl>
                                                <Input {...field} disabled={true} />
                                            </FormControl>
                                            <FormMessage />
                                        </FormItem>
                                    )}
                                />
                                <FormField
                                    control={form.control}
                                    name="name"
                                    render={({ field }) => (
                                        <FormItem>
                                            <FormLabel>英文名称</FormLabel>
                                            <FormControl>
                                                <Input {...field} disabled={true} />
                                            </FormControl>
                                            <FormMessage />
                                        </FormItem>
                                    )}
                                />
                                <FormField
                                    control={form.control}
                                    name="cnName"
                                    render={({ field }) => (
                                        <FormItem>
                                            <FormLabel>中文名称</FormLabel>
                                            <FormControl>
                                                <Input {...field} disabled={true} />
                                            </FormControl>
                                            <FormMessage />
                                        </FormItem>
                                    )}
                                />
                                <FormField
                                    control={form.control}
                                    name="category"
                                    render={({ field }) => (
                                        <FormItem>
                                            <FormLabel>分类</FormLabel>
                                            <FormControl>
                                                <Input {...field} disabled={true} />
                                            </FormControl>
                                            <FormMessage />
                                        </FormItem>
                                    )}
                                />
                            </div>
                            <FormField
                                control={form.control}
                                name="description"
                                render={({ field }) => (
                                    <FormItem>
                                        <FormLabel>描述</FormLabel>
                                        <FormControl>
                                            <Textarea {...field} disabled={true} />
                                        </FormControl>
                                        <FormMessage />
                                    </FormItem>
                                )}
                            />
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader>
                            <CardTitle className="flex items-center gap-2">
                                <Package2 className="w-5 h-5" />
                                SKU信息
                            </CardTitle>
                        </CardHeader>
                        <CardContent>
                            <SupplierSkuTable
                                skus={skus}
                                onSkuChange={handleSkuChange}
                                onBulkEdit={handleBulkEdit}
                            />
                        </CardContent>
                    </Card>

                    {!readOnly && (
                        <Button type="submit">保存</Button>
                    )}
                </form>
            </Form>
        </div>
    )
}