import { getDictName } from "@/api/common-api";
import { selectSupplier } from "@/api/supplier/supplier-api";
import { pageSupplierSpu } from "@/api/supplier/supplier-spu/supplier-spu-api";
import useSearchParamsManager from "@/hooks/use-url-param";
import { pushModal } from "@/components/modals";
import { DataTable } from "@/components/table/base-table";
import { DataTableToolbar, ToolbarItem, ToolbarLeft, ToolbarRight } from "@/components/table/toolbar/data-table-toolbar";
import { DataTableViewOptions } from "@/components/table/toolbar/data-table-view-options";
import { Button } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { SelectWithInput } from "@/components/ui/select-with-input";
import { useRequest } from "ahooks";
import { Plus } from "lucide-react";
import { useNavigate } from "react-router-dom";
import { SupplierProductColumns } from "./table/supplier-product-column";
import { userOnNotSupplier } from "@/api/user/user-api";
import { userInfoAtom } from "@/state/common";
import { useAtomValue } from "jotai";


const SupplierProductPage = () => {

    const navigate = useNavigate();
    const user = useAtomValue(userInfoAtom);
    const { getParam, addParam } = useSearchParamsManager();


    const handleSelectSupplier = () => {
        pushModal('SelectSupplierModal', {
            onSelect: (supplierId: string) => {
                navigate(`/dashboard/inventory/supplier/products/detail?supplierId=${supplierId}`);
            },
            onClose: () => {
                // do nothing
            }
        });
    };

    const { data: suppliers } = useRequest(selectSupplier, {
        manual: false,
    })

    const { data: nameContainer } = useRequest(getDictName)

    return (
        <div className="flex flex-col gap-4">
            <div className="flex items-center justify-between">
                <h1 className="text-2xl font-bold">供应商产品管理</h1>
            </div>
            <div className="rounded-lg border shadow-sm p-4">
                <DataTable
                    columns={SupplierProductColumns(nameContainer?.supplierNames ?? {})}
                    onFetch={param => pageSupplierSpu(param)}
                    isFixedHeader={false}
                    containerHeight="620px"
                    toolbar={(table, tableId) => (
                        <DataTableToolbar table={table} tableId={tableId}>
                            <ToolbarLeft>
                                <ToolbarItem>
                                    <Select
                                        value={getParam('supplierId') || 'ALL'}
                                        onValueChange={(value) => {
                                            if (value === 'ALL') {
                                                addParam('supplierId', '');
                                            } else {
                                                addParam('supplierId', value);
                                            }
                                        }}
                                    >
                                        <SelectTrigger className="w-[200px] h-8 text-sm">
                                            <SelectValue placeholder="选择供应商" />
                                        </SelectTrigger>
                                        <SelectContent>
                                            <SelectItem value="ALL">全部供应商</SelectItem>
                                            {suppliers?.map((supplier) => (
                                                <SelectItem key={supplier.id} value={supplier.id}>
                                                    {supplier.name}
                                                </SelectItem>
                                            ))}
                                        </SelectContent>
                                    </Select>
                                </ToolbarItem>
                                <ToolbarItem>
                                    <SelectWithInput
                                        options={[
                                            { label: "系统商品编码", value: "systemSpuCode" },
                                            { label: "供应商商品编码", value: "supplierSpuCode" },
                                            { label: "商品标题", value: "title" },
                                        ]}
                                        inputPlaceholder="请输入搜索内容"
                                    />
                                </ToolbarItem>
                            </ToolbarLeft>

                            <ToolbarRight>
                                <ToolbarItem>
                                    <DataTableViewOptions table={table} tableId={tableId} />
                                </ToolbarItem>
                                <ToolbarItem>
                                    {userOnNotSupplier(user) && (
                                        <Button className="h-8 text-green-600 bg-green-50 hover:bg-green-100" onClick={handleSelectSupplier}>
                                            <Plus className="mr-2 h-4 w-4" />
                                            新增产品
                                        </Button>
                                    )}
                                </ToolbarItem>
                            </ToolbarRight>
                        </DataTableToolbar>
                    )}
                    dependencies={[]}
                />
            </div>
        </div>
    )
};

export default SupplierProductPage;
