import {SupplierSpuPageResponse} from "@/api/supplier/supplier-spu/supplier-spu-model";
import {Chip} from "@/components/buttons/chip";
import {FormattedTime} from "@/components/common/formatted-time";
import {ColumnDef} from "@tanstack/react-table";
import {ClockIcon, EditIcon, FileText, HashIcon, User} from "lucide-react";
import {match} from "ts-pattern";
import {Button} from "@/components/ui/button";
import {Separator} from "@/components/ui/separator";
import {DoubleCheckBtn} from "@/components/buttons/double-check-btn";
import {Link} from "react-router-dom";
import {useAtomValue, useSetAtom} from "jotai";
import {refreshTableAtom, userInfoAtom} from "@/state/common";
import {toast} from "sonner";
import {deleteSupplierSpu, updateSupplierSpuStatus} from "@/api/supplier/supplier-spu/supplier-spu-api";
import {Toolt<PERSON>, TooltipContent, TooltipProvider, TooltipTrigger,} from "@/components/ui/tooltip";
import {userOnNotSupplier} from "@/api/user/user-api";

export const SupplierProductColumns: (supplierNames: Record<string, string>) => ColumnDef<SupplierSpuPageResponse>[] = (supplierNames) => [
    {
        id: 'bizId',
        accessorKey: 'bizId',
        header: () => <div className='flex items-center text-gray-700'>
            <HashIcon className="inline-block w-4 h-4 mr-1" />
            <div>产品编号</div>
        </div>,
        cell: ({ row }) => (
            <TooltipProvider>
                <Tooltip>
                    <TooltipTrigger>
                        <div className="font-medium text-gray-900">{row.original.id}</div>
                    </TooltipTrigger>
                    <TooltipContent>
                        <p>产品唯一标识符</p>
                    </TooltipContent>
                </Tooltip>
            </TooltipProvider>
        ),
        meta: {
            title: '产品编号',
        },
    },
    {
        id: 'supplierId',
        accessorKey: 'supplierId',
        header: () => <div className='flex items-center text-gray-700'>
            <User className="inline-block w-4 h-4 mr-1" />
            <div>供应商</div>
        </div>,
        cell: ({ row }) => {
            return supplierNames[row.original.supplierId];
        },
        meta: {
            title: '供应商',
        },
    },
    {
        id: 'productInfo',
        accessorKey: 'title',
        header: () => <div className='flex items-center text-gray-700'>
            <FileText className="inline-block w-4 h-4 mr-1" />
            <div>产品信息</div>
        </div>,
        cell: ({ row }) => (
            <TooltipProvider>
                <Tooltip>
                    <TooltipTrigger>
                        <div className="space-y-1">
                            <div className="font-medium text-gray-900">{row.original.title}</div>
                            <div className="flex flex-col gap-0.5">
                                <div className="text-sm text-indigo-600">
                                    供应商SPU: {row.original.supplierSpuCode}
                                </div>
                                <div className="text-sm text-gray-600">
                                    系统SPU: {row.original.systemSpuCode}
                                </div>
                            </div>
                        </div>
                    </TooltipTrigger>
                    <TooltipContent>
                        <div className="text-xs space-y-1">
                            <p>供应商SPU: {row.original.supplierSpuCode}</p>
                            <p>系统SPU: {row.original.systemSpuCode}</p>
                        </div>
                    </TooltipContent>
                </Tooltip>
            </TooltipProvider>
        ),
        meta: {
            title: '产品信息',
        },
    },
    {
        id: 'status',
        accessorKey: "status",
        header: () => <div className="text-gray-700">状态</div>,
        cell: ({ row }) => {
            return match(row.original.status)
                .with('DISABLED', () => (
                    <TooltipProvider>
                        <Tooltip>
                            <TooltipTrigger>
                                <div className="flex items-center space-x-2">
                                    <Chip label="禁用" variant="red" />
                                </div>
                            </TooltipTrigger>
                            <TooltipContent>
                                <p>当前已禁用</p>
                            </TooltipContent>
                        </Tooltip>
                    </TooltipProvider>
                ))
                .with('ENABLED', () => (
                    <TooltipProvider>
                        <Tooltip>
                            <TooltipTrigger>
                                <div className="flex items-center space-x-2">
                                    <Chip label="启用" variant="green" />
                                </div>
                            </TooltipTrigger>
                            <TooltipContent>
                                <p>当前已启用</p>
                            </TooltipContent>
                        </Tooltip>
                    </TooltipProvider>
                ))
                .otherwise(() => (
                    <div className="flex items-center space-x-2">
                        <Chip label="未知状态" variant="purple" />
                    </div>
                ));
        },
        meta: {
            title: '状态',
        },
    },
    {
        id: 'updateInfo',
        accessorKey: 'createdAt',
        header: () => <div className='flex items-center text-gray-700'>
            <ClockIcon className="inline-block w-4 h-4 mr-1" />
            <div>更新信息</div>
        </div>,
        cell: ({ row }) => {
            return (
                <TooltipProvider>
                    <Tooltip>
                        <TooltipTrigger>
                            <div className="space-y-1">
                                <div className="text-sm text-gray-500">
                                    <FormattedTime timestamp={row.original.updatedAt || row.original.createdAt} formatter='DD/MM/YY HH:mm' />
                                </div>
                                <div className="text-xs text-gray-400">
                                    by {row.original.createdByName}
                                </div>
                            </div>
                        </TooltipTrigger>
                        <TooltipContent>
                            <div className="space-y-1">
                                <p>创建时间: <FormattedTime timestamp={row.original.createdAt} formatter='YYYY-MM-DD HH:mm:ss' /></p>
                                <p>更新时间: <FormattedTime timestamp={row.original.updatedAt || row.original.createdAt} formatter='YYYY-MM-DD HH:mm:ss' /></p>
                            </div>
                        </TooltipContent>
                    </Tooltip>
                </TooltipProvider>
            );
        },
        meta: {
            title: '更新信息',
        },
    },
    {
        id: 'action',
        accessorKey: 'action',
        header: () => <div className='flex items-center text-gray-700'>
            <EditIcon className="inline-block w-4 h-4 mr-1" />
            <div>操作</div>
        </div>,
        cell: ({ row }) => {
            const user = useAtomValue(userInfoAtom);
            const setRefreshTable = useSetAtom(refreshTableAtom);

            return (
                <div className='flex items-center gap-2'>
                    <Button
                        variant="link"
                        className="h-auto p-0 text-blue-600 text-xs"
                        asChild
                    >
                        <Link to={`/dashboard/inventory/supplier/products/detail?id=${row.original.id}&supplierId=${row.original.supplierId}`}>
                            编辑
                        </Link>
                    </Button>
                    {userOnNotSupplier(user) && (
                        <>
                            <Separator orientation='vertical' className='h-4' />
                            <Button
                                variant="link"
                                className="h-auto p-0 text-orange-600 text-xs"
                                onClick={() => {
                                    const newStatus = row.original.status === 'ENABLED' ? 'DISABLED' : 'ENABLED';
                                    const actionText = newStatus === 'ENABLED' ? '启用' : '禁用';
                                    toast.promise(
                                        updateSupplierSpuStatus(row.original.id, newStatus),
                                        {
                                            loading: `${actionText}中...`,
                                            success: () => {
                                                setRefreshTable(prev => prev + 1);
                                                return `${actionText}成功`;
                                            },
                                            error: `${actionText}失败`,
                                        }
                                    );
                                }}
                            >
                                {row.original.status === 'ENABLED' ? '禁用' : '启用'}
                            </Button>
                        </>
                    )}
                    {userOnNotSupplier(user) && (
                        <>
                            <Separator orientation='vertical' className='h-4' />
                            <DoubleCheckBtn
                                className='h-auto p-0 text-red-600 text-xs'
                                variant='link'
                                title='删除供应商产品'
                                description='删除供应商产品后将无法恢复，请谨慎操作'
                                onConfirm={() => {
                                    toast.promise(
                                        deleteSupplierSpu(row.original.id),
                                        {
                                            loading: '删除中...',
                                            success: () => {
                                                setRefreshTable(prev => prev + 1);
                                                return '删除成功';
                                            },
                                            error: '删除失败',
                                        }
                                    );
                                }}
                                buttonText='删除'
                                onCancel={() => { }}
                            />
                        </>
                    )}
                </div>
            );
        },
        meta: {
            title: '操作',
        },
    }
];
