import {refresh<PERSON><PERSON><PERSON><PERSON>} from "@/state/common";
import {ColumnDef} from "@tanstack/table-core";
import {useSet<PERSON><PERSON>} from "jotai";
import React from "react";
import {toast} from "sonner";
import {CopyText} from "@/components/buttons/copy-text-btn";
import {match} from "ts-pattern";
import {Chip} from "@/components/buttons/chip";
import {FormattedTime} from "@/components/common/formatted-time";
import {Link} from "react-router-dom";
import {Barcode, Box, Calendar, Info, Tag} from "lucide-react";
import {Button} from "@/components/ui/button";
import {Separator} from "@/components/ui/separator";
import {DoubleCheckBtn} from "@/components/buttons/double-check-btn";
import {ImagePreviewDialog} from "@/components/buttons/image-preview";
import {Popover, PopoverContent, PopoverTrigger} from "@/components/ui/popover";
import {ComboSpuView, deleteComboSpu} from "@/api/inventory/spu/combo-spu-api.ts";
import {BaseStatus} from "@/api/order/templete/download-templete.tsx";

export const ComboSpuColumns: ColumnDef<ComboSpuView>[] = [
  {
    accessorKey: "id",
    header: "基础信息",
    cell: ({row}) => (
      <div className="space-y-1">
        {/* <div className="flex items-center space-x-2">
                    <Hash className="h-4 w-4 text-gray-500" />
                    <span className="font-medium">{row.getValue("id")}</span>
                </div> */}
        <div className="flex items-center space-x-2">
          <Barcode className="h-4 w-4 text-gray-500"/>
          <div className="flex flex-col">
            {row.original.spuCodes.map(skuCode =>
              <CopyText key={skuCode} value={skuCode}/>
            )}
          </div>

        </div>
      </div>
    ),
    meta: {
      title: '基础信息',
    },
  },
  {
    accessorKey: "title",
    header: "商品信息",
    cell: ({row}) => (
      <div className="space-y-1">
        <div className="flex items-center space-x-2">
          <Tag className="h-4 w-4 text-blue-500"/>
          <CopyText value={row.original.title}/>
        </div>
        <div className="text-sm text-gray-500">
          {row.original.name} | {row.original.cnName}
        </div>
        <div className="flex items-center space-x-2 text-sm text-gray-500">
          <Box className="h-4 w-4"/>
          <span>{row.original.category}</span>
        </div>
      </div>
    ),
    meta: {
      title: '商品信息',
    },
  },
  {
    accessorKey: "productImage",
    header: "图片信息",
    cell: ({row}) => {
      const [isPreviewOpen, setIsPreviewOpen] = React.useState(false);
      const [selectedImageIndex, setSelectedImageIndex] = React.useState(0);
      const allImages = [row.original.productImage, ...row.original.showImages];

      return (
        <>
          <div className="flex space-x-4">
            <div className="w-16 h-16 cursor-pointer" onClick={() => {
              setSelectedImageIndex(0);
              setIsPreviewOpen(true);
            }}>
              <img
                src={row.original.productImage}
                alt={row.original.name}
                className="w-full h-full object-cover rounded-lg shadow-sm hover:opacity-80 transition-opacity"
              />
            </div>
            <div className="flex space-x-1">
              {(row.original.showImages).slice(0, 3).map((image, index) => (
                <div
                  key={index}
                  className="cursor-pointer"
                  onClick={() => {
                    setSelectedImageIndex(index + 1);
                    setIsPreviewOpen(true);
                  }}
                >
                  <img
                    src={image}
                    alt={`展示图片 ${index + 1}`}
                    className="w-12 h-12 object-cover rounded-lg shadow-sm hover:opacity-80 transition-opacity"
                  />
                </div>
              ))}
              {(row.original.showImages).length > 3 && (
                <div className="w-12 h-12 bg-gray-100 rounded-lg shadow-sm flex items-center justify-center text-sm text-gray-500">
                  +{(row.original.showImages).length - 3}
                </div>
              )}
            </div>
          </div>

          <ImagePreviewDialog
            images={allImages}
            currentIndex={selectedImageIndex}
            isOpen={isPreviewOpen}
            onClose={() => setIsPreviewOpen(false)}
            onNavigate={setSelectedImageIndex}
          />
        </>
      );
    },
    meta: {
      title: '图片信息',
    },
  },
  {
    accessorKey: "status",
    header: "状态",
    cell: ({row}) => {
      return match(row.original.status)
        .with(BaseStatus.DISABLED, () => (
          <div className="flex items-center space-x-2">
            <Chip label="禁用" variant="red"/>
          </div>
        ))
        .with(BaseStatus.ENABLED, () => (
          <div className="flex items-center space-x-2">
            <Chip label="启用" variant="green"/>
          </div>
        ))
        .otherwise(() => (
          <div className="flex items-center space-x-2">
            <Chip label="未知状态" variant="purple"/>
          </div>
        ));
    },
    meta: {
      title: '状态',
    },
  },
  {
    accessorKey: "timeInfo",
    header: () => (
      <div className="flex items-center space-x-2 min-w-[200px]">
        <Calendar className="h-4 w-4 text-gray-500 mt-0.5"/>
        <span>时间信息</span>
      </div>
    ),
    cell: ({row}) => (
      <div className="space-y-2">
        <div className="flex items-center space-x-2 text-sm">
          <Calendar className="h-4 w-4 text-gray-500"/>
          <div>
            <div className="text-gray-600">创建: <FormattedTime timestamp={row.original.createdAt} formatter='DD/MM/YY HH:mm'/></div>
            <div className="text-gray-500 text-xs">由 {row.original.createdByName}</div>
          </div>
        </div>
        <div className="flex items-center space-x-2 text-sm">
          <Calendar className="h-4 w-4 text-gray-500"/>
          <div>
            <div className="text-gray-600">更新: <FormattedTime timestamp={row.original.updatedAt} formatter='DD/MM/YY HH:mm'/></div>
            <div className="text-gray-500 text-xs">由 {row.original.updatedByName}</div>
          </div>
        </div>
      </div>
    ),
    meta: {
      title: '时间信息',
    },
  },
  {
    accessorKey: "description",
    header: () => (
      <div className="flex items-center space-x-2 !max-w-[100px]">
        <Info className="h-4 w-4 text-gray-500 mt-0.5"/>
        <span>描述</span>
      </div>
    ),
    cell: ({row}) => (
      <div className="flex items-start space-x-2">
        <Popover>
          <PopoverTrigger>
            <div className="max-w-[100px] truncate cursor-pointer hover:text-blue-500">
              {row.original.description}
            </div>
          </PopoverTrigger>
          <PopoverContent className="w-[300px] p-4">
            <div className="text-sm text-gray-700 whitespace-pre-wrap">
              {row.original.description || '暂无描述'}
            </div>
          </PopoverContent>
        </Popover>
      </div>
    ),
    meta: {
      title: '描述',
    },
  }
  , {
    accessorKey: "actions",
    header: "操作",
    cell: ({row}) => {
      const setRefreshTable = useSetAtom(refreshTableAtom);
      return (
        <div className='flex items-center gap-2'>
          <Button
            variant="link"
            className="h-auto p-0 text-blue-600 text-xs" asChild>
            <Link to={`/dashboard/inventory/combo-products/details?id=${row.original.id}`}>
              编辑
            </Link>
          </Button>
          <Separator orientation='vertical' className='h-4'/>
          <DoubleCheckBtn
            className='h-auto p-0 text-red-600 text-xs'
            variant='link'
            title='删除套装'
            description='删除套装将影响相关库存，请谨慎操作'
            onConfirm={() => {
              toast.promise(
                deleteComboSpu(row.original.id),
                {
                  loading: '删除中...',
                  success: () => {
                    setRefreshTable(prev => prev + 1);
                    return '删除成功';
                  },
                  error: '删除失败',
                }
              );
            }}
            buttonText='删除'
            onCancel={() => {
            }}
          />
        </div>
      )
    },
    meta: {
      title: '操作',
    },
  }
];