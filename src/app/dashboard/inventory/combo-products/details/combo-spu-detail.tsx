import {zod<PERSON><PERSON><PERSON><PERSON>} from "@hookform/resolvers/zod"
import {useForm} from "react-hook-form"
import {z} from "zod"
import {Button} from "@/components/ui/button.tsx"
import {Form, FormControl, FormField, FormItem, FormLabel, FormMessage,} from "@/components/ui/form.tsx"
import {Input} from "@/components/ui/input.tsx"
import {Textarea} from "@/components/ui/textarea.tsx"
import {useEffect, useState} from 'react'
import ImageUpload from "@/components/buttons/image-upload.tsx";

import {useRequest} from "ahooks";
import {toast} from "sonner";
import {Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger} from "@/components/ui/dialog.tsx"
import {addDictValue, getDictValue} from "@/api/common-api.ts"
import {CategoryCombobox} from "@/components/form/category-combox.tsx"
import {uploadImage} from "@/api/file-api.ts"
import MultiImageUpload<PERSON>ield from "@/components/buttons/muti-img-upload.tsx"
import {Link, useNavigate} from "react-router-dom"
import {Card, CardContent, CardHeader, CardTitle} from "@/components/ui/card.tsx"
import {ArrowLeft, Boxes, Image as ImageIcon, Info, Package2, Plus} from "lucide-react"
import {ComboSpuCreateRequest, createComboSpu, fetchFlatSpu, FlatSpu, RenderAbleComboSkuView, RenderAbleComboSpuView, searchFlatSpu, SkuOfComboSkuCreateRequest, updateComboSpu} from "@/api/inventory/spu/combo-spu-api.ts";
import MultiSearchSelect from "@/components/select/multi-search-select.tsx";
import {Badge} from "@/components/ui/badge.tsx"
import ComboSkuTable from "@/app/dashboard/inventory/combo-products/details/combo-sku-table.tsx";
import {BaseStatus} from "@/api/order/templete/download-templete.tsx";

const formSchema = z.object({
  spuCodes: z.array(z.string()).min(2, "At least two SPU codes are required"),
  title: z.string().min(1, "Title is required"),
  name: z.string().min(1, "Name is required"),
  cnName: z.string().min(1, "Chinese Name is required"),
  category: z.string().min(1, "Category is required"),
  productImage: z.string().min(1, "Product Image is required"),
  packageQuantity: z.number().int().positive("Package Quantity must be a positive integer"),
  showImages: z.array(z.string()).min(1, "At least one show image is required"),
  description: z.string().optional(),
  hsCode: z.string().optional(),
  skus: z.array(z.object({
    spuId: z.string(),
    spuCode: z.string(),
    systemSkuId: z.string(),
    skuCode: z.string(),
    color: z.string(),
    size: z.string(),
    purchaseCost: z.number(),
    purchaseCostCurrency: z.string(),
    weight: z.number(),
    volume: z.number(),
    salePrice: z.number(),
  })),
})

interface Props {
  id?: string | undefined | null
  product?: RenderAbleComboSpuView
  onUpdated: () => void
}

const AddOptionDialog: React.FC<{
  title: string;
  onAdd: (value: string) => void;
}> = ({title, onAdd}) => {
  const [newValue, setNewValue] = useState('');
  const [isOpen, setIsOpen] = useState(false);

  const handleAdd = () => {
    if (newValue.trim()) {
      onAdd(newValue.trim());
      setNewValue('');
      setIsOpen(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        <Button variant="outline" size="sm" className="flex items-center gap-1">
          <Plus className="w-4 h-4"/>
          新增{title}
        </Button>
      </DialogTrigger>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>新增{title}</DialogTitle>
        </DialogHeader>
        <Input
          value={newValue}
          onChange={(e) => setNewValue(e.target.value)}
          placeholder={`输入新的${title.toLowerCase()}`}
        />
        <Button onClick={handleAdd}>新增</Button>
      </DialogContent>
    </Dialog>
  );
};


export function ComboProductForm({id, product, onUpdated}: Props) {

  const navigate = useNavigate();

  const handleAddOption = async (type: 'spu-category', value: string) => {
    try {
      await addDictValue(type, value);
      toast.success(`Added new ${type}: ${value}`);
      mutateCategory([...categoryOptions, value])
    } catch (error) {
      console.log("add dict value error", error);
      toast.error(`Failed to add new ${type}`);
    }
  };

  const isEdit = id != null

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      spuCodes: [],
      title: "",
      name: "",
      cnName: "",
      category: "",
      productImage: "",
      packageQuantity: 1,
      showImages: [],
      description: "",
      hsCode: "",
      skus: [],
    },
  })

  useEffect(() => {
    if (product) {
      form.reset({
        spuCodes: product.spuCodes,
        title: product.title,
        name: product.name,
        cnName: product.cnName,
        category: product.category,
        productImage: product.productImage,
        packageQuantity: product.packageQuantity,
        showImages: product.showImages,
        description: product.description,
        hsCode: product.hsCode,
        skus: product.skus || [],
      });
    }
  }, [product, form]);

  const [flatSpus, setFlatSpus] = useState<FlatSpu[]>([]);

  const {setValue} = form;
  const {data: categoryOptions = [], mutate: mutateCategory} = useRequest(getDictValue, {
    defaultParams: ['spu-category'],
  });

  const refreshFlatSpus = async (spuCodes: string[]) => {
    const resp = await fetchFlatSpu(spuCodes)
    setFlatSpus(resp)
  }

  useEffect(() => {
    refreshFlatSpus(product?.spuCodes ?? [])
  }, [product]);

  const [comboSkus, setComboSkus] = useState<RenderAbleComboSkuView[]>([]);

  useEffect(() => {
    if (product) {
      setComboSkus(product.skus.map(sku => ({
        spuId: sku.spuId,
        spuCode: sku.spuCode,
        skuCode: sku.skuCode,
        systemSkuId: sku.systemSkuId,
        size: sku.size,
        color: sku.color,
        purchaseCost: sku.purchaseCost,
        purchaseCostCurrency: sku.purchaseCostCurrency,
        weight: sku.weight,
        volume: sku.volume,
        salePrice: sku.salePrice,
      })));
    } else {
      setComboSkus(flatSpus.flatMap(spu => spu.skus).map(sku => {
        return {
          spuId: sku.spuId,
          spuCode: sku.spuCode,
          skuCode: sku.skuCode,
          systemSkuId: sku.id,
          size: sku.size,
          color: sku.color,
          purchaseCost: sku.purchaseCost,
          purchaseCostCurrency: sku.purchaseCostCurrency,
          weight: sku.weight,
          volume: sku.volume,
          salePrice: sku.salePrice,
        } as RenderAbleComboSkuView
      }))
    }
  }, [product, flatSpus]);

  useEffect(() => {
    setValue('spuCodes', flatSpus.map(spu => spu.id));
  }, [flatSpus, setValue]);

  useEffect(() => {
    setValue('skus', comboSkus);
  }, [comboSkus, setValue]);

  const handleSkuChange = (index: number, field: keyof RenderAbleComboSkuView, value: string | number) => {
    const updatedSkus = [...comboSkus];
    updatedSkus[index] = {...updatedSkus[index], [field]: value};
    setValue('skus', updatedSkus);
  };

  const handleSkuBulkEdit = (field: keyof RenderAbleComboSkuView, value: string | number) => {
    const updatedSkus = comboSkus.map(sku => ({
      ...sku,
      [field]: value
    }));
    setComboSkus(updatedSkus)
  }

  async function onSubmit(values: z.infer<typeof formSchema>) {
    try {
      const processedValues = {
        ...values,
        hsCode: values.hsCode?.trim() || null,
        status: BaseStatus.ENABLED
      };

      const updateRequest: ComboSpuCreateRequest = {
        ...processedValues,
        spuCodes: flatSpus.map(spu => spu.spuCode),
        packageQuantity: Number(values.packageQuantity),
        skus: values.skus.map(sku => {
          return {
            spuId: sku.spuId,
            spuCode: sku.spuCode,
            skuCode: sku.skuCode,
            systemSkuId: sku.systemSkuId,
            size: sku.size,
            color: sku.color,
            purchaseCost: sku.purchaseCost,
            purchaseCostCurrency: sku.purchaseCostCurrency,
            weight: sku.weight,
            volume: sku.volume,
            salePrice: sku.salePrice,
          } as SkuOfComboSkuCreateRequest
        })
      } as ComboSpuCreateRequest;
      console.log(JSON.stringify(updateRequest))
      if (id) {
        await updateComboSpu(id, updateRequest);
        toast.success("更新成功");
        onUpdated()
      } else {
        const res = await createComboSpu(updateRequest)
        navigate(`/dashboard/inventory/combo-products/details?id=${res}`)
        toast.success("创建成功");
      }

    } catch (error) {
      console.error("Error updating product:", error);
      toast.error("Failed to update product. Please try again.");
    }
  }

  const handleFlatSpuSearch = async (search: string) => {
    return searchFlatSpu(search)
  }

  const renderSearchItem = (flatSpu: FlatSpu) => {
    return <>
      <div className="font-bold">{flatSpu.name}</div>
      <div className="text-sm text-muted-foreground">{flatSpu.spuCode}</div>
      <div className="text-xs text-muted-foreground">{flatSpu.category}</div>
      <div className="flex gap-1 mt-1 flex-wrap">
        {flatSpu.skus.map((sku) => (
          <Badge key={sku.id} variant="secondary" className="text-xs">
            {sku.skuCode}
          </Badge>
        ))}
      </div>
    </>
  }

  const renderPreviewItem = (flatSpu: FlatSpu) => {
    return <div className="space-y-4">
      <div>
        <h4 className="font-medium mb-2">SPU</h4>
        <p className="text-muted-foreground">{flatSpu.spuCode}</p>
      </div>
      <div>
        <h4 className="font-medium mb-2">分类</h4>
        <Badge variant="outline">{flatSpu.category}</Badge>
      </div>
      <div>
        <h4 className="font-medium mb-2">SKU</h4>
        <div className="flex gap-1 flex-wrap">
          {flatSpu.skus.map((sku) => (
            <Badge key={sku.id} variant="secondary">
              {sku.skuCode}
            </Badge>
          ))}
        </div>
      </div>
    </div>
  }

  return (
    <Form {...form}>
      <div className="flex items-center gap-4 mb-6">
        <Button
          variant="ghost"
          size="icon"
          className="hover:bg-muted"
          asChild
        >
          <Link to="/dashboard/inventory/combo-products">
            <ArrowLeft className="w-5 h-5"/>
          </Link>
        </Button>
        <div>
          <h1 className="text-2xl font-semibold tracking-tight">
            {isEdit ? "编辑商品套装" : "新增商品套装"}
          </h1>
          <p className="text-sm text-muted-foreground">
            {isEdit
              ? `正在编辑 ${product?.name || 'SPU'} (${product?.spuCodes?.length ?? 0})`
              : "创建一个新的商品套装并设置其属性"
            }
          </p>
        </div>
      </div>

      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
        <Card>
          <CardHeader className="flex flex-row items-center space-y-0 gap-2">
            <Info className="w-5 h-5 text-muted-foreground"/>
            <CardTitle>基本信息</CardTitle>
          </CardHeader>
          <CardContent className="grid grid-cols-2 gap-6">
            <FormField
              control={form.control}
              name="spuCodes"
              render={() => (
                <FormItem>
                  <FormLabel>包含SPU</FormLabel>
                  <FormControl>
                    <MultiSearchSelect disabled={isEdit} value={flatSpus} onChange={setFlatSpus} searchItems={handleFlatSpuSearch} renderSearchItem={renderSearchItem} itemToString={(flatSpu) => flatSpu.title} renderPreviewItem={renderPreviewItem}/>
                  </FormControl>
                  <FormMessage/>
                </FormItem>
              )}
            />


            <FormField
              control={form.control}
              name="title"
              render={({field}) => (
                <FormItem>
                  <FormLabel>标题</FormLabel>
                  <FormControl>
                    <Input placeholder="Enter Title" {...field} />
                  </FormControl>
                  <FormMessage/>
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="name"
              render={({field}) => (
                <FormItem>
                  <FormLabel>英文名称</FormLabel>
                  <FormControl>
                    <Input placeholder="Enter English Name" {...field} />
                  </FormControl>
                  <FormMessage/>
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="cnName"
              render={({field}) => (
                <FormItem>
                  <FormLabel>中文名称</FormLabel>
                  <FormControl>
                    <Input placeholder="Enter Chinese Name" {...field} />
                  </FormControl>
                  <FormMessage/>
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="category"
              render={({field}) => (
                <FormItem className="col-span-2">
                  <FormLabel>类别</FormLabel>
                  <FormControl>
                    <div className="flex items-center space-x-2">
                      <CategoryCombobox
                        value={field.value}
                        onChange={field.onChange}
                        categoryOptions={categoryOptions}
                      />
                      <AddOptionDialog
                        title="分类"
                        onAdd={(value) => handleAddOption('spu-category', value)}
                      />
                    </div>
                  </FormControl>
                  <FormMessage/>
                </FormItem>
              )}
            />
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center space-y-0 gap-2">
            <ImageIcon className="w-5 h-5 text-muted-foreground"/>
            <CardTitle>图片信息</CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            <FormField
              control={form.control}
              name="productImage"
              render={({field}) => (
                <FormItem>
                  <FormLabel>产品主图</FormLabel>
                  <FormControl>
                    <ImageUpload
                      onImageUpload={async (file) => {
                        const dummyUrl = await uploadImage(file);
                        field.onChange(dummyUrl)
                      }}
                      onImageRemove={() => field.onChange("")}
                      initialImage={field.value}
                    />
                  </FormControl>
                  <FormMessage/>
                </FormItem>
              )}
            />

            <MultiImageUploadField
              form={form}
              name="showImages"
              label="展示图片 (最多三张)"
              maxImages={3}
              uploadImage={uploadImage}
            />
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center space-y-0 gap-2">
            <Package2 className="w-5 h-5 text-muted-foreground"/>
            <CardTitle>商品属性</CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="grid grid-cols-2 gap-6">
              <FormField
                control={form.control}
                name="packageQuantity"
                render={({field}) => (
                  <FormItem>
                    <FormLabel>包装数量</FormLabel>
                    <FormControl>
                      <Input type="number" {...field}
                             onChange={e => field.onChange(parseInt(e.target.value))}/>
                    </FormControl>
                    <FormMessage/>
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="hsCode"
                render={({field}) => (
                  <FormItem>
                    <FormLabel>HS编码</FormLabel>
                    <FormControl>
                      <Input
                        placeholder="输入HS编码"
                        {...field}
                        value={field.value || ""}
                      />
                    </FormControl>
                    <FormMessage/>
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="description"
                render={({field}) => (
                  <FormItem className="col-span-2">
                    <FormLabel>描述</FormLabel>
                    <FormControl>
                      <Textarea placeholder="Enter Description" {...field} />
                    </FormControl>
                    <FormMessage/>
                  </FormItem>
                )}
              />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center space-y-0 gap-2">
            <Boxes className="w-5 h-5 text-muted-foreground"/>
            <CardTitle>SKU信息</CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            <FormField
              control={form.control}
              name="skus"
              render={() => (
                <FormItem>
                  <FormLabel>SKU列表</FormLabel>
                  <FormControl>
                    <ComboSkuTable
                      flatSpus={flatSpus}
                      skus={comboSkus}
                      onSkuChange={handleSkuChange}
                      onBulkEdit={handleSkuBulkEdit}
                    />
                  </FormControl>
                  <FormMessage/>
                </FormItem>
              )}
            />
          </CardContent>
        </Card>

        <div className="flex justify-end pt-4 border-t">
          <Button
            type="submit"
            size="lg"
            className="min-w-[120px]"
          >
            {isEdit ? "保存修改" : "创建商品"}
          </Button>
        </div>
      </form>
    </Form>
  )
}