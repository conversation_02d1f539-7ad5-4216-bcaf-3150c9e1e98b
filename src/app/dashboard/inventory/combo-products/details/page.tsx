import {useSearchParams} from "react-router-dom";
import {useRequest} from "ahooks";
import {ComboProductForm} from "@/app/dashboard/inventory/combo-products/details/combo-spu-detail.tsx";
import {getComboSpu} from "@/api/inventory/spu/combo-spu-api.ts";

export default function ComboProductDetail() {

  const [searchParams] = useSearchParams();
  const id = searchParams.get('id');

  const {data, refresh} = useRequest(getComboSpu, {
    defaultParams: [id ?? null]
  })


  return (
    <div className="max-w-5xl mx-auto pt-10">
      <ComboProductForm product={data || undefined} id={id} onUpdated={() => refresh()}/>
      <div className={'pt-20'}>
      </div>
    </div>
  )
    ;
}
