import {DataTable} from "@/components/table/base-table";
import {DataTableToolbar, ToolbarItem, ToolbarLeft, ToolbarRight} from "@/components/table/toolbar/data-table-toolbar";
import {DataTableViewOptions} from "@/components/table/toolbar/data-table-view-options";
import {Button} from "@/components/ui/button";
import {Plus} from "lucide-react";
import {Link} from "react-router-dom";
import {SelectWithInput} from "@/components/ui/select-with-input";
import {pageComboSpu} from "@/api/inventory/spu/combo-spu-api.ts";
import {ComboSpuColumns} from "@/app/dashboard/inventory/combo-products/table/combo-spu-colmun.tsx";

export default function ComboProductPage() {
    return (
        <div className="flex flex-col gap-4">
            <div className="flex items-center justify-between">
                <h1 className="text-2xl font-bold">套装产品管理</h1>
            </div>
            <div className="rounded-lg border shadow-sm p-4">
                <DataTable
                    columns={ComboSpuColumns}
                    onFetch={param => pageComboSpu(param)}
                    isFixedHeader={false}
                    containerHeight="620px"
                    isNeedSelect={true}
                    toolbar={(table, tableId) => (
                        <DataTableToolbar table={table} tableId={tableId}>
                            <ToolbarLeft>
                                <ToolbarItem>
                                    <SelectWithInput
                                        options={[
                                            { label: "套装商品编码组", value: "spuCode" },
                                            { label: "套装商品标题", value: "title" },
                                        ]}
                                        inputPlaceholder="请输入搜索内容"
                                    />
                                </ToolbarItem>
                            </ToolbarLeft>

                            <ToolbarRight>
                                <ToolbarItem>
                                    <DataTableViewOptions table={table} tableId={tableId} />
                                </ToolbarItem>
                                <ToolbarItem>
                                    <Button className="h-8 text-green-600 bg-green-50 hover:bg-green-100"
                                            asChild
                                    >
                                      <Link to='/dashboard/inventory/combo-products/details'>
                                        <Plus className="mr-2 h-4 w-4" />
                                        新增套装产品
                                      </Link>
                                    </Button>
                                </ToolbarItem>
                            </ToolbarRight>
                        </DataTableToolbar>
                    )}
                    dependencies={[]}
                />
            </div>
        </div>
    )
}