// src/components/form/customer-spu/customer-spu-form.tsx
import {findCountries} from '@/api/common-api';
import {SkuCountryPrices, SkuPcsPrices} from '@/api/inventory/customer/types';
import {Button} from "@/components/ui/button";
import {Card, CardContent, CardHeader, CardTitle} from "@/components/ui/card";
import {Command, CommandEmpty, CommandGroup, CommandInput, CommandItem, CommandList} from "@/components/ui/command";
import {Form, FormControl, FormField, FormItem, FormLabel, FormMessage} from "@/components/ui/form";
import {Input} from "@/components/ui/input";
import {Popover, PopoverContent, PopoverTrigger} from "@/components/ui/popover";
import {ScrollArea} from "@/components/ui/scroll-area";
import {cn} from "@/lib/utils";
import {zodResolver} from "@hookform/resolvers/zod";
import {useRequest} from "ahooks";
import {ArrowLeft, Check, ChevronsUpDown, Info, Package2} from "lucide-react";
import {useEffect} from 'react';
import {useForm} from "react-hook-form";
import {Link} from "react-router-dom";
import {toast} from "sonner";
import * as z from "zod";
import ComboCustomerSkuTable from './combo-customer-sku-table.tsx';
import {ComboSpuView, getComboSpu, selectComboCustomerSpu} from "@/api/inventory/spu/combo-spu-api.ts";
import {RenderableComboCustomerSkuView} from "@/api/inventory/customer/combo-customer-product-api.ts";

const formSchema = z.object({
  id: z.string().optional(),
  customerId: z.string(),
  systemSpuIds: z.array(z.string()).min(2),
  systemSpuCodes: z.array(z.string()).min(2),
  systemComboSpuId: z.string(),
  triggerDiscountQuantity: z.number().min(1, "Trigger discount quantity must be at least 1"),
  discount: z.number().min(0),
  skus: z.array(z.object({
    id: z.string().optional(),
    customerId: z.string(),
    systemSpuIdsOfCombo: z.array(z.string()),
    systemSpuCodesOfCombo: z.array(z.string()),
    comboSpuId: z.string(),
    comboSkuId: z.string(),
    systemSpuId: z.string(),
    systemSpuCode: z.string(),
    systemSkuId: z.string(),
    systemSkuCode: z.string(),
    size: z.string(),
    color: z.string(),
    offlinePrice: z.number(),
    offlinePriceCurrency: z.string(),
    skuCountryPrices: z.record(z.string(), z.object({
      price: z.number(),
      countryName: z.string(),
      isoAlphaTwo: z.string(),
      skuPcsPrices: z.array(z.object({
        price: z.number(),
        atLeastPcs: z.number(),
      })).optional(),
    })),
    skuPcsPrices: z.array(z.object({
      price: z.number(),
      atLeastPcs: z.number(),
    })),
    triggerDiscountQuantity: z.number().optional(),
    discount: z.number().optional(),
  })),
});

export type ComboCustomerSpuFormData = z.infer<typeof formSchema>;

interface Props {
  id?: string | null;
  customerId: string
  isCreate?: boolean;
  initialData?: ComboCustomerSpuFormData;
  onSubmit: (data: ComboCustomerSpuFormData) => Promise<void>;
}

export function ComboCustomerSpuForm({id, customerId, isCreate, initialData, onSubmit}: Props) {
  const {data: systemComboSpus = []} = useRequest(selectComboCustomerSpu, {
    defaultParams: [isCreate ? customerId : null],
  });

  const {data: countries} = useRequest(findCountries);

  const form = useForm<ComboCustomerSpuFormData>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      skus: [],
      triggerDiscountQuantity: 1,
      discount: 0,
    },
  });

  useEffect(() => {
    if (initialData) {
      form.reset(initialData);
    }
  }, [initialData, form]);

  const {setValue, watch} = form;
  const skus = watch('skus');

  const handleSkuChange = (index: number, field: keyof RenderableComboCustomerSkuView, value: string | number | SkuCountryPrices | SkuPcsPrices) => {
    const updatedSkus = [...skus];
    updatedSkus[index] = {...updatedSkus[index], [field]: value};
    setValue('skus', updatedSkus);
  };

  const handleBulkEdit = (field: keyof RenderableComboCustomerSkuView, value: string | number) => {
    const updatedSkus = skus.map(sku => ({
      ...sku,
      [field]: value
    }));
    setValue('skus', updatedSkus);
  };

  const handleSystemComboSpuChange = async (newSystemComboSpuId: string) => {
    if (!id) {
      const comboSpu = (await getComboSpu(newSystemComboSpuId))!;
      try {
        setValue('systemSpuIds', comboSpu.spuIds);
        setValue('systemSpuCodes', comboSpu.spuCodes);
        setValue('systemComboSpuId', newSystemComboSpuId);
        setValue('customerId', customerId)
        const skus = comboSpu.skus.map(comboSku => {
          return {
            customerId: customerId,
            systemSpuIdsOfCombo: comboSpu.spuIds,
            systemSpuCodesOfCombo: comboSpu.spuCodes,
            comboSpuId: comboSpu.id,
            comboSkuId: comboSku.id,
            systemSpuId: comboSku.spuId,
            systemSpuCode: comboSku.spuCode,
            systemSkuId: comboSku.systemSkuId,
            systemSkuCode: comboSku.skuCode,
            size: comboSku.size,
            color: comboSku.color,
            offlinePrice: 0,
            offlinePriceCurrency: 'USD',
            skuCountryPrices: {},
            skuPcsPrices: [],
            triggerDiscountQuantity: undefined,
            discount: undefined,
          } as RenderableComboCustomerSkuView
        })
        setValue('skus', skus);
      } catch (error) {
        console.error("Error fetching system SPU details:", error);
        toast.error("Failed to fetch system SPU details. Please try again.");
      }
    }
  };

  async function handleSubmit(values: ComboCustomerSpuFormData) {
    try {
      // 在提交前,将skuPcsPrices进行排序,根据atLeastPcs字段从大到小排序
      const clonedObject = structuredClone(values)
      clonedObject.skus.forEach(sku => {
        if (sku.skuPcsPrices) {
          sku.skuPcsPrices = sku.skuPcsPrices.sort((a, b) => b.atLeastPcs - a.atLeastPcs);
        }
        if (sku.skuCountryPrices) {
          const countryIds = Object.keys(sku.skuCountryPrices)
          for (const countryId of countryIds) {
            const countryPrice = sku.skuCountryPrices[countryId];
            if (!countryPrice) {
              continue
            }
            if (!countryPrice.skuPcsPrices) {
              continue
            }
            countryPrice.skuPcsPrices = countryPrice.skuPcsPrices.sort((a, b) => b.atLeastPcs - a.atLeastPcs);
          }
        }
      })

      await onSubmit(clonedObject);
    } catch (error) {
      console.error("Error submitting form:", error);
      toast.error("Failed to submit form. Please try again.");
    }
  }

  function displayComboSpu(comboSpu: ComboSpuView | string | undefined) {
    if (typeof comboSpu === 'undefined') {
      return "请选择系统SPU套装"
    } else if (typeof comboSpu === 'string') {
      return displayComboSpu(systemComboSpus.find(scs => scs.id === comboSpu))
    } else {
      console.log('comboSpu', comboSpu)
      return `${comboSpu.title} (${comboSpu.cnName}) | ${comboSpu.spuCodes.join(', ')}`
    }
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <Button
            variant="ghost"
            size="icon"
            asChild
          >
            <Link to="/dashboard/inventory/customer/combo-products">
              <ArrowLeft className="h-4 w-4"/>
            </Link>
          </Button>
          <h2 className="text-lg font-medium">{isCreate ? '创建客户SPU套装' : '编辑客户SPU套装'}</h2>
        </div>
      </div>

      <Form {...form}>
        <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-8">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Info className="w-5 h-5"/>
                基本信息
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 gap-4">
                <FormField
                  control={form.control}
                  name="systemComboSpuId"
                  render={({field}) => (
                    <FormItem>
                      <FormLabel>系统SPU套装</FormLabel>
                      <FormControl>
                        <Popover>
                          <PopoverTrigger asChild>
                            <Button
                              variant="outline"
                              role="combobox"
                              disabled={!!id}
                              className="w-full justify-between"
                            >
                              {displayComboSpu(field.value)}
                              <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50"/>
                            </Button>
                          </PopoverTrigger>
                          <PopoverContent className="w-[--radix-popover-trigger-width] p-0 min-w-[200px]">
                            <Command>
                              <CommandInput placeholder="搜索系统 SPU..."/>
                              <CommandList>
                                <CommandEmpty>未找到匹配的 SPU</CommandEmpty>
                                <CommandGroup>
                                  <ScrollArea className="h-72">
                                    {systemComboSpus.map((spu) => (
                                      <CommandItem
                                        key={spu.id}
                                        value={spu.id}
                                        onSelect={() => {
                                          handleSystemComboSpuChange(spu.id);
                                        }}
                                      >
                                        <Check
                                          className={cn(
                                            "mr-2 h-4 w-4",
                                            field.value === spu.id ? "opacity-100" : "opacity-0"
                                          )}
                                        />
                                        {displayComboSpu(spu)}
                                      </CommandItem>
                                    ))}
                                  </ScrollArea>
                                </CommandGroup>
                              </CommandList>
                            </Command>
                          </PopoverContent>
                        </Popover>
                      </FormControl>
                      <FormMessage/>
                    </FormItem>
                  )}
                />

              </div>

              <div className="grid grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="triggerDiscountQuantity"
                  render={({field}) => (
                    <FormItem>
                      <FormLabel>触发折扣数量</FormLabel>
                      <FormControl>
                        <Input
                          type="number"
                          placeholder="输入触发折扣数量"
                          {...field}
                          onChange={e => field.onChange(Number(e.target.value))}
                        />
                      </FormControl>
                      <FormMessage/>
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="discount"
                  render={({field}) => (
                    <FormItem>
                      <FormLabel>折扣</FormLabel>
                      <FormControl>
                        <Input
                          type="number"
                          step="0.01"
                          placeholder="输入折扣 (0-1)"
                          {...field}
                          onChange={e => field.onChange(Number(e.target.value))}
                        />
                      </FormControl>
                      <FormMessage/>
                    </FormItem>
                  )}
                />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Package2 className="w-5 h-5"/>
                SKU 信息
              </CardTitle>
            </CardHeader>
            <CardContent>
              <FormField
                control={form.control}
                name="skus"
                render={({field}) => (
                  <FormItem>
                    <FormControl>
                      <ComboCustomerSkuTable
                        skus={field.value}
                        countries={countries || []}
                        onSkuChange={handleSkuChange}
                        onBulkEdit={handleBulkEdit}
                        disabled={form.formState.isSubmitting}
                      />
                    </FormControl>
                    <FormMessage/>
                  </FormItem>
                )}
              />
            </CardContent>
          </Card>

          <div className="flex justify-end space-x-4">
            <Button
              type="submit"
              disabled={form.formState.isSubmitting}
            >
              {isCreate ? '创建' : '保存'}
            </Button>
          </div>
        </form>
      </Form>
    </div>
  );
}