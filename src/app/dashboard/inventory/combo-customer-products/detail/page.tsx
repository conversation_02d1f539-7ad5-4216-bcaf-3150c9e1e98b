import {AlertDialog, AlertDialogAction, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle,} from "@/components/ui/alert-dialog";
import {useRequest} from "ahooks";
import {useState} from "react";
import {useNavigate, useSearchParams} from "react-router-dom";
import {toast} from "sonner";
import {ComboCustomerSpuForm, ComboCustomerSpuFormData} from "./combo-customer-spu-form.tsx";
import {ComboCustomerSpuView, createComboCustomerSpu, getComboCustomerSpu, updateComboCustomerSpu} from "@/api/inventory/customer/combo-customer-product-api.ts";

export default function ComboCustomerSpuDetailPage() {
  const [searchParams] = useSearchParams();
  const comboCustomerSpuId = searchParams.get("id");
  const customerId = searchParams.get("customerId");
  const isCreate = comboCustomerSpuId === null;
  const [showSuccessDialog, setShowSuccessDialog] = useState(false);

  const navigate = useNavigate();

  const {data, loading, refresh} = useRequest(
    async (id: string | null) => {
      if (!id) return undefined;
      return await getComboCustomerSpu(id) ;
    },
    {
      defaultParams: [comboCustomerSpuId],
    }
  );

  const {runAsync: create} = useRequest(createComboCustomerSpu,
    {
      manual: true,
      onSuccess: () => {
        setShowSuccessDialog(true);
      },
      onError: (err) => {
        toast.error(err.message)
      }
    }
  );

  const {runAsync: update} = useRequest(updateComboCustomerSpu,
    {
      manual: true,
      onSuccess: () => {
        refresh()
        toast.success("更新成功")
      },
      onError: (err) => {
        toast.error(err.message)
      }
    }
  );

  function convertComboCustomerSpuResponseToFormData(response: ComboCustomerSpuView): ComboCustomerSpuFormData {
    return {
      id: response.id,
      systemSpuIds: response.systemSpuIds,
      systemSpuCodes: response.systemSpuCodes,
      systemComboSpuId: response.systemComboSpuId,
      customerId: response.customerId.toString(),
      triggerDiscountQuantity: response.triggerDiscountQuantity,
      discount: response.discount,
      skus: response.skus.map((sku) => ({
        id: sku.id,
        bizId: sku.bizId,
        customerId: sku.customerId,
        systemSpuIdsOfCombo: sku.systemSpuIdsOfCombo,
        systemSpuCodesOfCombo: sku.systemSpuCodesOfCombo,
        comboSpuId: sku.comboSpuId,
        comboSkuId: sku.comboSkuId,
        systemSpuId: sku.systemSpuId,
        systemSpuCode: sku.systemSpuCode,
        systemSkuId: sku.systemSkuId,
        systemSkuCode: sku.systemSkuCode,
        size: sku.size,
        color: sku.color,
        offlinePrice: sku.offlinePrice,
        offlinePriceCurrency: sku.offlinePriceCurrency,
        skuCountryPrices: sku.skuCountryPrices,
        skuPcsPrices: sku.skuPcsPrices,
        discount: sku.discount,
        triggerDiscountQuantity: sku.triggerDiscountQuantity,
      })),
    };
  }

  const handleSubmit = async (data: ComboCustomerSpuFormData) => {
    if (isCreate) {
      await create(data);
    } else {
      await update(comboCustomerSpuId, data);
    }
  };

  const handleContinueCreate = () => {
    setShowSuccessDialog(false);
    // 重置表单状态
    window.location.reload();
  };

  const handleReturnList = () => {
    navigate('/dashboard/inventory/customer/combo-products');
  };

  if (loading) {
    return <div>loading...</div>
  }

  return (
    <>
      <div className="max-w-5xl mx-auto pt-10 pb-6 space-y-6">
        <ComboCustomerSpuForm
          isCreate={isCreate}
          id={comboCustomerSpuId}
          onSubmit={handleSubmit}
          initialData={isCreate ? undefined : convertComboCustomerSpuResponseToFormData(data!)}
          customerId={customerId!}
        />
      </div>

      <AlertDialog open={showSuccessDialog} onOpenChange={setShowSuccessDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>创建成功</AlertDialogTitle>
            <AlertDialogDescription>
              客户SPU创建成功！请选择下一步操作。
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogAction onClick={handleReturnList}>
              返回列表
            </AlertDialogAction>
            <AlertDialogAction onClick={handleContinueCreate}>
              继续创建
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
}