import {Country} from "@/api/common-api";
import {SkuCountryPrices, SkuPcsPrices} from "@/api/inventory/customer/types";
import DecimalInput from "@/components/inputs/demical-input";
import {But<PERSON>} from "@/components/ui/button";
import {Card, CardContent, CardHeader, CardTitle} from "@/components/ui/card";
import {Select, SelectContent, SelectItem, SelectTrigger, SelectValue} from "@/components/ui/select";
import {Table, TableBody, TableCell, TableFooter, TableHead, TableHeader, TableRow} from "@/components/ui/table";
import React from "react";
import {Checkbox} from "@/components/ui/checkbox.tsx";
import {Label} from "@/components/ui/label.tsx";
import {RenderableComboCustomerSkuView} from "@/api/inventory/customer/combo-customer-product-api.ts";

type CustomerSkuTableProps = {
    skus: RenderableComboCustomerSkuView[];
    countries: Country[]; // 添加可选国家列表
    onSkuChange: (index: number, field: keyof RenderableComboCustomerSkuView, value: string | number | SkuCountryPrices | SkuPcsPrices) => void;
    onBulkEdit?: (field: keyof RenderableComboCustomerSkuView, value: string | number) => void;
    disabled?: boolean;
};

const ComboCustomerSkuTable: React.FC<CustomerSkuTableProps> = ({ skus, countries, onSkuChange, onBulkEdit, disabled }) => {
    const [bulkEditValues, setBulkEditValues] = React.useState({
        offlinePrice: 0,
        offlinePriceCurrency: '',
    });

    const [selectedSku, setSelectedSku] = React.useState<number | null>(null);
    const [expandSkuMoreMode, setExpandSkuMoreMode] = React.useState<'countryPrices' | 'pcsPrices' | 'pcsPricesForCountry'>('countryPrices');

    const [expandPcsPricesForCountry, setExpandPcsPricesForCountry] = React.useState<Country | null>(null);

    const handleBulkEdit = (field: keyof RenderableComboCustomerSkuView, value: string | number) => {
        if (!onBulkEdit) return;
        setBulkEditValues(prev => ({ ...prev, [field]: value }));
        onBulkEdit(field, value);
    };

    const handleCountryPriceChange = (sku: RenderableComboCustomerSkuView, skuIndex: number, country: Country, price: number) => {
        const updatedCountryPrices = {
            ...sku.skuCountryPrices,
        }
        if (isNaN(price)) {
            delete updatedCountryPrices[country.id];
        } else {
            const old = sku.skuCountryPrices?.[country.id]
            updatedCountryPrices[country.id] = {
                ...old,
                price: price,
                countryName: country.countryName,
                isoAlphaTwo: country.isoAlphaTwo,
            }
        }
        onSkuChange(skuIndex, 'skuCountryPrices', updatedCountryPrices);
    };

    const handlePcsPriceChange = (skuIndex: number, atLeastPcs: number, price: number, pcsIndex: number) => {
        const sku = skus[skuIndex]
        const oldPcsPrices = sku.skuPcsPrices || [];
        const updatedPcsPrices = [...oldPcsPrices];
        if (pcsIndex >= updatedPcsPrices.length || pcsIndex == -1) {
            updatedPcsPrices.push({atLeastPcs, price});
        } else {
            updatedPcsPrices[pcsIndex] = {atLeastPcs, price};
        }
        onSkuChange(skuIndex, 'skuPcsPrices', updatedPcsPrices);
    }

    const handlePcsPriceForCountryChange = (skuIndex: number, country: Country, atLeastPcs: number, price: number, pcsIndex: number) => {
        const sku = skus[skuIndex]
        const oldCountryPrice = sku.skuCountryPrices?.[country.id]
        const updatedPcsPrices = [...(oldCountryPrice?.skuPcsPrices || [])];
        if (pcsIndex >= updatedPcsPrices.length || pcsIndex == -1) {
            updatedPcsPrices.push({atLeastPcs, price});
        } else {
            updatedPcsPrices[pcsIndex] = {atLeastPcs, price};
        }
        const updatedCountryPrices = {
            ...sku.skuCountryPrices,
            [country.id]: {
                ...oldCountryPrice,
                countryName: country.countryName,
                isoAlphaTwo: country.isoAlphaTwo,
                skuPcsPrices: updatedPcsPrices
            }
        }
        onSkuChange(skuIndex, 'skuCountryPrices', updatedCountryPrices as SkuCountryPrices);
    }

    const handleDeletePcsPrice = (skuIndex: number, pcsIndex: number) => {
        const sku = skus[skuIndex]
        const oldPcsPrices = sku.skuPcsPrices || [];
        const updatedPcsPrices = [...oldPcsPrices];
        updatedPcsPrices.splice(pcsIndex, 1);
        onSkuChange(skuIndex, 'skuPcsPrices', updatedPcsPrices);
    }

    const handleDeletePcsPriceForCountry = (skuIndex: number, country: Country, pcsIndex: number) => {
        const sku = skus[skuIndex]
        if (!sku.skuCountryPrices) {
            sku.skuCountryPrices = {} as SkuCountryPrices
        }
        if (!sku.skuCountryPrices[country.id]) {
            sku.skuCountryPrices[country.id] = {price: 0, countryName: country.countryName, isoAlphaTwo: country.isoAlphaTwo}
        }
        const oldPcsPrices = sku.skuCountryPrices[country.id].skuPcsPrices || [];
        const updatedPcsPrices = [...oldPcsPrices];
        updatedPcsPrices.splice(pcsIndex, 1);
        sku.skuCountryPrices[country.id].skuPcsPrices = updatedPcsPrices;
        const copiedObject = {...sku.skuCountryPrices}
        onSkuChange(skuIndex, 'skuCountryPrices', copiedObject)
    }

    return (
        <Card>
            <CardHeader>
                <div className="flex justify-between items-center">
                    <CardTitle>SKU 信息</CardTitle>
                </div>
            </CardHeader>
            <CardContent>
                <Table>
                    <TableHeader>
                        <TableRow>
                            <TableHead className="w-50">SKU 编码</TableHead>
                            <TableHead className="w-20">尺寸</TableHead>
                            <TableHead className="w-20">颜色</TableHead>
                            <TableHead>默认价格</TableHead>
                            <TableHead>默认币种</TableHead>
                            <TableHead>折扣</TableHead>
                            <TableHead>触发折扣数量</TableHead>
                            <TableHead>国家特定价格</TableHead>
                            <TableHead>数量特定价格</TableHead>
                        </TableRow>
                    </TableHeader>
                    <TableBody>
                        {onBulkEdit && (
                            <TableRow>
                                <TableCell colSpan={3}>批量编辑：</TableCell>
                                <TableCell>
                                    <DecimalInput
                                        value={bulkEditValues.offlinePrice}
                                        onChange={(value) => handleBulkEdit('offlinePrice', value)}
                                        placeholder="批量设置默认价格"
                                        min={0}
                                    />
                                </TableCell>
                                <TableCell>
                                    <Select
                                        value={bulkEditValues.offlinePriceCurrency}
                                        onValueChange={(value) => handleBulkEdit('offlinePriceCurrency', value)}
                                    >
                                        <SelectTrigger>
                                            <SelectValue placeholder="批量设置币种" />
                                        </SelectTrigger>
                                        <SelectContent>
                                            <SelectItem value="USD">USD</SelectItem>
                                            <SelectItem value="CNY">CNY</SelectItem>
                                            <SelectItem value="EUR">EUR</SelectItem>
                                            <SelectItem value="GBP">GBP</SelectItem>
                                            <SelectItem value="AUD">AUD</SelectItem>
                                            <SelectItem value="JPY">JPY</SelectItem>
                                            <SelectItem value="HKD">HKD</SelectItem>
                                            <SelectItem value="SGD">SGD</SelectItem>
                                        </SelectContent>
                                    </Select>
                                </TableCell>
                                <TableCell/>
                                <TableCell/>
                            </TableRow>
                        )}
                        {skus.map((sku, skuIndex) => (
                          <React.Fragment key={`fragment-sku-${skuIndex}`}>
                              <TableRow key={`row-sku-${skuIndex}-base`}>
                                    <TableCell>{sku.systemSkuCode}</TableCell>
                                    <TableCell>{sku.size}</TableCell>
                                    <TableCell>{sku.color}</TableCell>
                                    <TableCell>
                                        <DecimalInput
                                            value={sku.offlinePrice}
                                            onChange={(value) => onSkuChange(skuIndex, 'offlinePrice', value)}
                                            placeholder="输入默认价格"
                                            disabled={disabled}
                                        />
                                    </TableCell>
                                    <TableCell>
                                        <Select
                                            value={sku.offlinePriceCurrency}
                                            onValueChange={(value) => onSkuChange(skuIndex, 'offlinePriceCurrency', value)}
                                            disabled={disabled}
                                        >
                                            <SelectTrigger>
                                                <SelectValue>{sku.offlinePriceCurrency}</SelectValue>
                                            </SelectTrigger>
                                            <SelectContent>
                                                <SelectItem value="USD">USD</SelectItem>
                                                <SelectItem value="CNY">CNY</SelectItem>
                                                <SelectItem value="EUR">EUR</SelectItem>
                                                <SelectItem value="GBP">GBP</SelectItem>
                                                <SelectItem value="AUD">AUD</SelectItem>
                                                <SelectItem value="JPY">JPY</SelectItem>
                                                <SelectItem value="HKD">HKD</SelectItem>
                                                <SelectItem value="SGD">SGD</SelectItem>
                                            </SelectContent>
                                        </Select>
                                    </TableCell>
                                    <TableCell>
                                        <DecimalInput
                                            value={sku.discount}
                                            onChange={(value) => onSkuChange(skuIndex, 'discount', value)}
                                        />
                                    </TableCell>
                                    <TableCell>
                                        <DecimalInput
                                            step="1"
                                            min={1}
                                            value={sku.triggerDiscountQuantity}
                                            onChange={(value) => onSkuChange(skuIndex, 'triggerDiscountQuantity', value)}
                                        />
                                    </TableCell>
                                  <TableCell>
                                      <Button
                                        variant="ghost"
                                        onClick={(e) => {
                                            e.preventDefault();
                                            if (expandSkuMoreMode === 'countryPrices' || expandSkuMoreMode === 'pcsPricesForCountry') {
                                                setSelectedSku(selectedSku === skuIndex ? null : skuIndex)
                                            } else {
                                                setExpandSkuMoreMode('countryPrices')
                                                setSelectedSku(skuIndex)
                                            }
                                        }}
                                        disabled={disabled}
                                      >
                                          {(selectedSku === skuIndex && (expandSkuMoreMode === 'countryPrices' || expandSkuMoreMode === 'pcsPricesForCountry')) ? '收起' : '管理国家价格'}
                                      </Button>
                                  </TableCell>
                                  <TableCell>
                                      <Button
                                        variant="ghost"
                                        className="p-2"
                                        onClick={(e) => {
                                            e.preventDefault()
                                            if (expandSkuMoreMode === 'pcsPrices') {
                                                setSelectedSku(selectedSku === skuIndex ? null : skuIndex)
                                            } else {
                                                setExpandSkuMoreMode('pcsPrices')
                                                setExpandPcsPricesForCountry(null)
                                                setSelectedSku(skuIndex)
                                            }
                                        }}
                                        disabled={disabled}
                                      >
                                          {(selectedSku === skuIndex && expandSkuMoreMode === 'pcsPrices') ? '收起' : '管理数量价格'}
                                      </Button>
                                  </TableCell>
                                </TableRow>
                              {(selectedSku === skuIndex && (expandSkuMoreMode === 'countryPrices' || expandSkuMoreMode === 'pcsPricesForCountry')) && (
                                <TableRow key={`row-sku-${skuIndex}-countries-price`}>
                                        <TableCell colSpan={6}>
                                            <div className="p-4">
                                                <Table>
                                                    <TableHeader>
                                                        <TableRow>
                                                            <TableHead className="w-40">国家</TableHead>
                                                            <TableHead>价格 ({sku.offlinePriceCurrency})</TableHead>
                                                            <TableHead>数量特定价格</TableHead>
                                                        </TableRow>
                                                    </TableHeader>
                                                    <TableBody>
                                                        {countries.map((country, countryIndex) => (
                                                          <React.Fragment key={`fragment-${skuIndex}-${country.id}-${countryIndex}`}>
                                                              <TableRow key={`${skuIndex}-${country.id}-${countryIndex}`}>
                                                                  <RenderCountryRowCells
                                                                    country={country}
                                                                    sku={sku}
                                                                    skuIndex={skuIndex}
                                                                    handleCountryPriceChange={handleCountryPriceChange}
                                                                    setExpandPcsPricesForCountry={setExpandPcsPricesForCountry}
                                                                    setExpandSkuMoreMode={setExpandSkuMoreMode}
                                                                    expandSkuMoreMode={expandSkuMoreMode}
                                                                    expandPcsPricesForCountry={expandPcsPricesForCountry}
                                                                    disabled={disabled}/>
                                                              </TableRow>
                                                              {expandSkuMoreMode === 'pcsPricesForCountry' && expandPcsPricesForCountry === country && (
                                                                <TableRow key={`${skuIndex}-${country.id}-${countryIndex}-pcs-prices`}>
                                                                    <TableCell colSpan={6}>
                                                                        <div className="p-4">
                                                                            <Table>
                                                                                <TableHeader>
                                                                                    <TableRow>
                                                                                        <TableHead className="w-40">至少达到数量</TableHead>
                                                                                        <TableHead>价格 ({sku.offlinePriceCurrency})</TableHead>
                                                                                        <TableHead>操作</TableHead>
                                                                                    </TableRow>
                                                                                </TableHeader>
                                                                                <TableBody>
                                                                                    {sku.skuCountryPrices?.[country.id]?.skuPcsPrices?.map((pcsPrices, pcsIndex) => (
                                                                                      <TableRow key={`${skuIndex}-pfc-${pcsIndex}`}>
                                                                                          <TableCell className="font-medium">
                                                                                              <DecimalInput
                                                                                                min={1}
                                                                                                step="1"
                                                                                                value={pcsPrices.atLeastPcs}
                                                                                                onChange={(value) => handlePcsPriceForCountryChange(skuIndex, country, Number(value), pcsPrices.price, pcsIndex)}
                                                                                              />
                                                                                          </TableCell>
                                                                                          <TableCell>
                                                                                              <DecimalInput
                                                                                                value={pcsPrices.price}
                                                                                                onChange={(value) => handlePcsPriceForCountryChange(skuIndex, country, pcsPrices.atLeastPcs, Number(value), pcsIndex)}
                                                                                                placeholder="输入价格"
                                                                                                disabled={disabled}
                                                                                              />
                                                                                          </TableCell>
                                                                                          <TableCell>
                                                                                              <Button
                                                                                                variant="outline"
                                                                                                onClick={() => handleDeletePcsPriceForCountry(skuIndex, country, pcsIndex)}
                                                                                                type="button"
                                                                                                disabled={disabled}
                                                                                              >
                                                                                                  删除
                                                                                              </Button>
                                                                                          </TableCell>
                                                                                      </TableRow>
                                                                                    ))}
                                                                                </TableBody>
                                                                                <TableFooter>
                                                                                    <TableRow>
                                                                                        <TableCell colSpan={2}>
                                                                                            <Button
                                                                                              variant="outline"
                                                                                              onClick={() => handlePcsPriceForCountryChange(selectedSku, country, 0, 0, -1)}
                                                                                              type="button"
                                                                                              disabled={disabled}
                                                                                            >
                                                                                                添加数量价格
                                                                                            </Button>
                                                                                        </TableCell>
                                                                                    </TableRow>
                                                                                </TableFooter>
                                                                            </Table>
                                                                        </div>
                                                                    </TableCell>
                                                                </TableRow>
                                                              )}
                                                          </React.Fragment>
                                                        ))}
                                                    </TableBody>
                                                </Table>
                                            </div>
                                        </TableCell>
                                    </TableRow>
                              )}
                              {(selectedSku === skuIndex && expandSkuMoreMode === 'pcsPrices') && (
                                <TableRow key={`row-sku-${skuIndex}-pcs-prices`}>
                                    <TableCell colSpan={6}>
                                        <div className="p-4">
                                            <Table>
                                                <TableHeader>
                                                    <TableRow>
                                                        <TableHead className="w-40">至少达到数量</TableHead>
                                                        <TableHead>价格 ({sku.offlinePriceCurrency})</TableHead>
                                                        <TableHead>操作</TableHead>
                                                    </TableRow>
                                                </TableHeader>
                                                <TableBody>
                                                    {sku.skuPcsPrices?.map((pcsPrices, pcsIndex) => (
                                                      <TableRow key={`${skuIndex}-pcs-${pcsIndex}`}>
                                                          <TableCell className="font-medium">
                                                              <DecimalInput
                                                                value={pcsPrices.atLeastPcs}
                                                                onChange={(value) => handlePcsPriceChange(skuIndex, Number(value), pcsPrices.price, pcsIndex)}
                                                              />
                                                          </TableCell>
                                                          <TableCell>
                                                              <DecimalInput
                                                                value={pcsPrices.price}
                                                                onChange={(value) => handlePcsPriceChange(skuIndex, pcsPrices.atLeastPcs, Number(value), pcsIndex)}
                                                                placeholder="输入价格"
                                                                disabled={disabled}
                                                              />
                                                          </TableCell>
                                                          <TableCell>
                                                              <Button
                                                                variant="outline"
                                                                onClick={() => handleDeletePcsPrice(skuIndex, pcsIndex)}
                                                                type="button"
                                                                disabled={disabled}
                                                              >
                                                                  删除
                                                              </Button>
                                                          </TableCell>
                                                      </TableRow>
                                                    ))}
                                                </TableBody>
                                                <TableFooter>
                                                    <TableRow>
                                                        <TableCell colSpan={2}>
                                                            <Button
                                                              variant="outline"
                                                              onClick={() => handlePcsPriceChange(skuIndex, 0, 0, -1)}
                                                              type="button"
                                                              disabled={disabled}
                                                            >
                                                                添加数量价格
                                                            </Button>
                                                        </TableCell>
                                                    </TableRow>
                                                </TableFooter>
                                            </Table>
                                        </div>
                                    </TableCell>
                                </TableRow>
                                )}
                            </React.Fragment>
                        ))}
                    </TableBody>
                </Table>
            </CardContent>
        </Card>
    );
};

interface RenderCountryRowCellsPros {
    sku: RenderableComboCustomerSkuView,
    skuIndex: number,
    country: Country;
    disabled?: boolean;
    handleCountryPriceChange: (sku: RenderableComboCustomerSkuView, skuIndex: number, country: Country, price: number) => void;
    expandSkuMoreMode: 'countryPrices' | 'pcsPrices' | 'pcsPricesForCountry',
    setExpandSkuMoreMode: (mode: 'countryPrices' | 'pcsPrices' | 'pcsPricesForCountry') => void;
    expandPcsPricesForCountry: Country | null;
    setExpandPcsPricesForCountry: (country: Country | null) => void;
}

const RenderCountryRowCells = (
  {
      sku, skuIndex, country, disabled, handleCountryPriceChange, expandPcsPricesForCountry, expandSkuMoreMode, setExpandPcsPricesForCountry, setExpandSkuMoreMode
  }: RenderCountryRowCellsPros) => {
    const [followDefault, setFollowDefault] = React.useState(sku.skuCountryPrices?.[country.id]?.price === undefined || sku.skuCountryPrices?.[country.id]?.price === null);

    const handleCheckChange = (checked: boolean) => {
        if (checked) {
            handleCountryPriceChange(sku, skuIndex, country, NaN)
            if (expandPcsPricesForCountry == country) {
                setExpandPcsPricesForCountry(null)
            }
        } else {
            handleCountryPriceChange(sku, skuIndex, country, 0)
        }

        setFollowDefault(checked)
    }

    return <>
        <TableCell className="font-medium">
            {country.countryName}
        </TableCell>
        <TableCell className="flex flex-row">
            <div className="flex flex-row items-center mx-3">
                <Checkbox className="m-1" id={`id_folc_${country.id}_${skuIndex}`} checked={followDefault} onCheckedChange={handleCheckChange}></Checkbox>
                <Label htmlFor={`id_folc_${country.id}_${skuIndex}`}>跟随默认</Label>
            </div>
            <DecimalInput
              className="flex-1"
              value={followDefault ? sku.offlinePrice : (sku.skuCountryPrices?.[country.id]?.price || 0)}
              onChange={(value) => handleCountryPriceChange(sku, skuIndex, country, Number(value))}
              placeholder="输入价格"
              disabled={disabled || followDefault}
            />
        </TableCell>
        <TableCell>
            <Button variant="outline"
                    onClick={(e) => {
                        e.preventDefault()
                        if (expandSkuMoreMode === 'pcsPricesForCountry') {
                            setExpandPcsPricesForCountry(expandPcsPricesForCountry === country ? null : country)
                        } else {
                            setExpandSkuMoreMode('pcsPricesForCountry')
                            setExpandPcsPricesForCountry(country)
                        }
                    }} disabled={disabled || followDefault}>
                {followDefault ? "跟随默认" : ((expandSkuMoreMode === 'pcsPricesForCountry' && expandPcsPricesForCountry === country) ? '收起' : '管理数量价格')}
            </Button>
        </TableCell>
    </>
}


export default React.memo(ComboCustomerSkuTable);