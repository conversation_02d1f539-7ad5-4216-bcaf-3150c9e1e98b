import {Row} from "@tanstack/react-table";
import {Button} from "@/components/ui/button";
import {Separator} from "@/components/ui/separator";
import {DoubleCheckBtn} from "@/components/buttons/double-check-btn";
import {Link} from "react-router-dom";
import {useSetAtom} from "jotai";
import {refreshTableAtom} from "@/state/common";
import {toast} from "sonner";
import {BaseStatus} from "@/api/order/templete/download-templete.tsx";
import {ComboCustomerSpuView, deleteComboCustomerSpu, updateComboCustomerSpuStatus} from "@/api/inventory/customer/combo-customer-product-api.ts";

interface CustomerSpuActionsProps {
  row: Row<ComboCustomerSpuView>;
}

export function ComboCustomerSpuActions({row}: CustomerSpuActionsProps) {
  const setRefreshTable = useSetAtom(refreshTableAtom);

  const handleStatusChange = async (id: string, newStatus: BaseStatus) => {
    const actionText = newStatus === BaseStatus.ENABLED ? '启用' : '禁用';
    try {
      await updateComboCustomerSpuStatus(id, newStatus);
      setRefreshTable(prev => prev + 1);
      toast.success(`${actionText}成功`);
    } catch (error) {
      toast.error(`${actionText}失败`);
    }
  };

  const handleDelete = async () => {
    try {
      await deleteComboCustomerSpu(row.original.id);
      setRefreshTable(prev => prev + 1);
      toast.success('删除成功');
    } catch (error) {
      toast.error('删除失败');
    }
  };

  return (
    <div className='flex items-center gap-2'>
      <Button
        variant="link"
        className="h-auto p-0 text-blue-600 text-xs"
        asChild
      >
        <Link to={`/dashboard/inventory/customer/combo-products/detail?id=${row.original.id}`}>
          编辑
        </Link>
      </Button>
      <Separator orientation='vertical' className='h-4'/>
      <Button
        variant="link"
        className="h-auto p-0 text-orange-600 text-xs"
        onClick={() => {
          const newStatus = row.original.status === BaseStatus.ENABLED ? BaseStatus.DISABLED : BaseStatus.ENABLED;
          handleStatusChange(row.original.id, newStatus);
        }}
      >
        {row.original.status === 'ENABLED' ? '禁用' : '启用'}
      </Button>
      <Separator orientation='vertical' className='h-4'/>
      <DoubleCheckBtn
        className='h-auto p-0 text-red-600 text-xs'
        variant='link'
        title='删除客户产品'
        description='删除客户产品后将无法恢复，请谨慎操作'
        onConfirm={() => handleDelete()}
        buttonText='删除'
        onCancel={() => {
        }}
      />
    </div>
  );
} 