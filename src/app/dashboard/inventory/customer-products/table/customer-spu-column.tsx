import { ColumnDef } from "@tanstack/react-table";
import { match } from "ts-pattern";
import { Chip } from "@/components/buttons/chip";
import { FormattedTime } from "@/components/common/formatted-time";
import { Box, ClockIcon, EditIcon, HashIcon, User } from "lucide-react";
import { CustomerSpuActions } from "@/components/customer-spu-actions";
import { CustomerSpuPageResponse } from "@/api/inventory/customer/customer-product-api";
import {
    Tooltip,
    TooltipContent,
    TooltipProvider,
    TooltipTrigger,
} from "@/components/ui/tooltip";
import { CustomerInfo } from "@/api/customer/customer-api";

export const CustomerSpuColumns = (customerMap: Record<string, CustomerInfo> | undefined): ColumnDef<CustomerSpuPageResponse>[] => {
    return [
        {
            accessorKey: "bizId",
            meta: {
                title: '业务ID',
            },
            header: () => <div className='flex items-center text-gray-700'>
                <HashIcon className="inline-block w-4 h-4 mr-1" />
                <div>业务ID</div>
            </div>,
            cell: ({ row }) => (
                <TooltipProvider>
                    <Tooltip>
                        <TooltipTrigger>
                            <div className="font-medium text-gray-900">{row.original.id}</div>
                        </TooltipTrigger>
                        <TooltipContent>
                            <p>业务标识符</p>
                        </TooltipContent>
                    </Tooltip>
                </TooltipProvider>
            ),
        },
        {
            accessorKey: "customerId",
            meta: {
                title: '客户',
            },
            header: () => <div className='flex items-center text-gray-700'>
                <User className="inline-block w-4 h-4 mr-1" />
                <div>客户</div>
            </div>,
            cell: ({ row }) => <div className="font-medium text-blue-600">{customerMap?.[row.original.customerId]?.name ?? row.original.customerId}</div>,
        },
        {
            accessorKey: "spuInfo",
            meta: {
                title: 'SPU信息',
            },
            header: () => <div className='flex items-center text-gray-700'>
                <Box className="inline-block w-4 h-4 mr-1" />
                <div>SPU信息</div>
            </div>,
            cell: ({ row }) => (
                <TooltipProvider>
                    <Tooltip>
                        <TooltipTrigger>
                            <div className="space-y-1">
                                <div className="font-medium text-indigo-600">
                                    客户SPU: {row.original.customerSpuCode}
                                </div>
                                <div className="text-sm text-gray-500">
                                    系统SPU: {row.original.systemSpuCode}
                                </div>
                            </div>
                        </TooltipTrigger>
                        <TooltipContent>
                            <p className="font-mono text-xs">系统SPU ID: {row.original.systemSpuId}</p>
                        </TooltipContent>
                    </Tooltip>
                </TooltipProvider>
            ),
        },
        {
            accessorKey: "discountInfo",
            meta: {
                title: '折扣信息',
            },
            header: () => <div className='flex items-center text-gray-700'>
                <div>折扣信息</div>
            </div>,
            cell: ({ row }) => (
                <div className="space-y-1">
                    <div className="font-medium text-green-600">
                        折扣: {(row.original.discount).toFixed(2)}
                    </div>
                    <div className="text-sm text-orange-600">
                        触发数量: {row.original.triggerDiscountQuantity}
                    </div>
                </div>
            ),
        },
        {
            accessorKey: "status",
            meta: {
                title: '状态',
            },
            header: () => <div className="text-gray-700">状态</div>,
            cell: ({ row }) => {
                const value = row.getValue("status");
                return match(value)
                    .with("DISABLED", () => (
                        <TooltipProvider>
                            <Tooltip>
                                <TooltipTrigger>
                                    <div className="flex items-center space-x-2">
                                        <Chip label="禁用" variant="red" />
                                    </div>
                                </TooltipTrigger>
                                <TooltipContent>
                                    <p>当前已禁用</p>
                                </TooltipContent>
                            </Tooltip>
                        </TooltipProvider>
                    ))
                    .with("ENABLED", () => (
                        <TooltipProvider>
                            <Tooltip>
                                <TooltipTrigger>
                                    <div className="flex items-center space-x-2">
                                        <Chip label="启用" variant="green" />
                                    </div>
                                </TooltipTrigger>
                                <TooltipContent>
                                    <p>当前已启用</p>
                                </TooltipContent>
                            </Tooltip>
                        </TooltipProvider>
                    ))
                    .otherwise(() => (
                        <div className="flex items-center space-x-2">
                            <Chip label="Unknown" variant="purple" />
                        </div>
                    ));
            },
        },
        {
            accessorKey: "updatedInfo",
            meta: {
                title: '更新信息',
            },
            header: () => <div className='flex items-center text-gray-700'>
                <ClockIcon className="inline-block w-4 h-4 mr-1" />
                <div>更新信息</div>
            </div>,
            cell: ({ row }) => {
                return (
                    <TooltipProvider>
                        <Tooltip>
                            <TooltipTrigger>
                                <div className="space-y-1">
                                    <div className="text-sm text-gray-500">
                                        <FormattedTime timestamp={row.original.updatedAt ?? ''} formatter='DD/MM/YY HH:mm' />
                                    </div>
                                    <div className="text-xs text-gray-400">
                                        by {row.original.updatedByName}
                                    </div>
                                </div>
                            </TooltipTrigger>
                            <TooltipContent>
                                <div className="space-y-1">
                                    <p>创建时间: <FormattedTime timestamp={row.original.createdAt ?? ''} formatter='YYYY-MM-DD HH:mm:ss' /></p>
                                    <p>更新时间: <FormattedTime timestamp={row.original.updatedAt ?? ''} formatter='YYYY-MM-DD HH:mm:ss' /></p>
                                </div>
                            </TooltipContent>
                        </Tooltip>
                    </TooltipProvider>
                );
            },
        },
        {
            accessorKey: "actions",
            header: () => <div className='flex items-center text-gray-700'>
                <EditIcon className="inline-block w-4 h-4 mr-1" />
                <div>操作</div>
            </div>,
            cell: ({ row }) => <CustomerSpuActions row={row} />,
            meta: {
                title: '操作',
            },
        },
    ]
}