import {CustomerInfo, listCustomer} from "@/api/customer/customer-api";
import { pageCustomerSpu} from "@/api/inventory/customer/customer-product-api";
import useSearchParamsManager from "@/hooks/use-url-param";
import {pushModal} from "@/components/modals";
import {DataTable} from "@/components/table/base-table";
import {DataTableToolbar, ToolbarItem, ToolbarLeft, ToolbarRight} from "@/components/table/toolbar/data-table-toolbar";
import {DataTableViewOptions} from "@/components/table/toolbar/data-table-view-options";
import {Button} from "@/components/ui/button";
import {Select, SelectContent, SelectItem, SelectTrigger, SelectValue} from "@/components/ui/select";
import {Textarea} from "@/components/ui/textarea";
import {useRequest} from "ahooks";
import {Plus, Search, X} from "lucide-react";
import {useMemo} from "react";
import {useNavigate} from "react-router-dom";
import {CustomerSpuColumns} from "./table/customer-spu-column";


const SupplierProductPage = () => {

    const navigate = useNavigate();

    const { addParam, getParam } = useSearchParamsManager();


    const handleSelectCustomer = () => {
        pushModal('SelectCustomerModal', {
            onSelect: (customerId: string) => {
                navigate(`/dashboard/inventory/customer/products/detail?customerId=${customerId}`);
            },
            onClose: function (): void {
                throw new Error("Function not implemented.");
            }
        });
    };

    const { data: customerList } = useRequest(listCustomer);

    const customerMap = useMemo(() => {
        return customerList?.reduce((acc, customer) => {
            acc[customer.id] = customer;
            return acc;
        }, {} as Record<string, CustomerInfo>);
    }, [customerList]);


    return (
        <div className="flex flex-col gap-4">
            <div className="flex items-center justify-between">
                <h1 className="text-2xl font-bold">客户产品管理</h1>
            </div>
            <div className="rounded-lg border shadow-sm p-4">
                <DataTable
                    columns={CustomerSpuColumns(customerMap)}
                    onFetch={param => pageCustomerSpu(param)}
                    isFixedHeader={false}
                    containerHeight="620px"
                    isNeedSelect={true}
                    toolbar={(table, tableId) => (
                        <DataTableToolbar table={table} tableId={tableId}>
                            <ToolbarLeft>
                              
                                <ToolbarItem>
                                    <div className="relative">
                                        <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                                        <Textarea
                                            placeholder="搜索系统产品名,换行分割多个系统产品名"
                                            value={getParam('systemSpuCode') || ''}
                                            onChange={(e) => addParam('systemSpuCode', e.target.value)}
                                            className="pl-8 w-80"
                                        />
                                    </div>
                                </ToolbarItem>
                                <ToolbarItem>
                                    <Select
                                        value={getParam('customerId') || ''}
                                        onValueChange={(value) => addParam('customerId', value)}
                                    >
                                        <SelectTrigger className="w-[200px]">
                                            <SelectValue placeholder="选择客户" />
                                        </SelectTrigger>
                                        <SelectContent>
                                            {customerList?.map((customer) => (
                                                <SelectItem key={customer.id} value={customer.id}>
                                                    {customer.name}
                                                </SelectItem>
                                            ))}
                                        </SelectContent>
                                    </Select>
                                </ToolbarItem>
                                <ToolbarItem>
                                    {(getParam('customerId') || getParam('systemSpuCode')) && (
                                        <Button
                                            variant="outline"
                                            size="sm"
                                            onClick={() => {
                                                addParam('customerId', '');
                                                addParam('systemSpuCode', '');
                                            }}
                                            className="h-8 px-2 lg:px-3"
                                        >
                                            重置
                                            <X className="ml-2 h-4 w-4" />
                                        </Button>
                                    )}
                                </ToolbarItem>
                            </ToolbarLeft>


                            <ToolbarRight>
                                <ToolbarItem>
                                    <DataTableViewOptions table={table} tableId={tableId} />
                                </ToolbarItem>
                                <ToolbarItem>
                                    <Button className="h-8 text-green-600 bg-green-50 hover:bg-green-100" onClick={handleSelectCustomer}>
                                        <Plus className="mr-2 h-4 w-4" />
                                        新增客户产品
                                    </Button>
                                </ToolbarItem>
                            </ToolbarRight>
                        </DataTableToolbar>
                    )}
                    dependencies={[]}
                />
            </div>
        </div>
    )
};

export default SupplierProductPage;
