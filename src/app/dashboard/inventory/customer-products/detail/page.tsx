import {createCustomerSpu, CustomerSpuResponse, getCustomerSpu, updateCustomerSpu} from "@/api/inventory/customer/customer-product-api";
import {AlertDialog, AlertDialogAction, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle,} from "@/components/ui/alert-dialog";
import {useRequest} from "ahooks";
import {useState} from "react";
import {useNavigate, useSearchParams} from "react-router-dom";
import {toast} from "sonner";
import {CustomerSpuForm, CustomerSpuFormData} from "./customer-spu-form";

export default function CustomerSpuDetailPage() {
    const [searchParams] = useSearchParams();
    const customerSpuId = searchParams.get("id");
    const customerId = searchParams.get("customerId");
    const isCreate = customerSpuId === null;
    const [showSuccessDialog, setShowSuccessDialog] = useState(false);

    const navigate = useNavigate();

    const { data, loading, refresh } = useRequest(
        async (id: string | null) => {
            if (!id) return undefined;
            return await getCustomerSpu(id) ;
        },
        {
            defaultParams: [customerSpuId],
        }
    );

    const { runAsync: create } = useRequest(
        async (formData: CustomerSpuFormData) => {
            await createCustomerSpu(formData);
        },
        {
            manual: true,
            onSuccess: () => {
                setShowSuccessDialog(true);
            },
            onError: (err) => {
                toast.error(err.message)
            }
        }
    );

    const { runAsync: update } = useRequest(
        async (id: string | null, formData: CustomerSpuFormData) => {
            if (!id) return;
            await updateCustomerSpu(id, formData);
        },
        {
            manual: true,
            onSuccess: () => {
                refresh()
                toast.success("更新成功")
            },
            onError: (err) => {
                toast.error(err.message)
            }
        }
    );

    function convertCustomerSpuResponseToFormData(response: CustomerSpuResponse): CustomerSpuFormData {
        return {
            id: response.id,
            systemSpuId: response.systemSpuId.toString(),
            customerSpuCode: response.customerSpuCode || '',
            customerId: response.customerId.toString(),
            systemSpuCode: response.systemSpuCode || '',
            triggerDiscountQuantity: response.triggerDiscountQuantity,
            discount: response.discount,
            skus: response.skus?.map((sku) => ({
                id: sku.id.toString(),
                systemSkuCode: sku.systemSkuCode,
                systemSpuId: sku.systemSpuId.toString(),
                systemSkuId: sku.systemSkuId.toString(),
                size: sku.size,
                color: sku.color,
                offlinePrice: sku.offlinePrice,
                offlinePriceCurrency: sku.offlinePriceCurrency,
                skuCountryPrices: sku.skuCountryPrices,
                skuPcsPrices: sku.skuPcsPrices,
                discount: sku.discount,
                triggerDiscountQuantity: sku.triggerDiscountQuantity,
            })) || [],
            supplierCapacities: response.supplierCapacities?.map(capacity => ({
                supplierId: capacity.supplierId || '',
                supplierName: capacity.supplierName || '',
                totalCapacity: capacity.totalCapacity || 0
            })) || []
        };
    }

    const handleSubmit = async (data: CustomerSpuFormData) => {
        if (isCreate) {
            await create(data);
        } else {
            await update(customerSpuId, data);
        }
    };

    const handleContinueCreate = () => {
        setShowSuccessDialog(false);
        // 重置表单状态
        window.location.reload();
    };

    const handleReturnList = () => {
        navigate('/dashboard/inventory/customer/products');
    };

    if (loading) {
        return <div>loading...</div>
    }

    return (
        <>
            <div className="max-w-5xl mx-auto pt-10 pb-6 space-y-6">
                <CustomerSpuForm
                    isCreate={isCreate}
                    id={customerSpuId}
                    onSubmit={handleSubmit}
                    initialData={isCreate ? undefined : convertCustomerSpuResponseToFormData(data!)}
                    customerId={customerId!}
                />
            </div>

            <AlertDialog open={showSuccessDialog} onOpenChange={setShowSuccessDialog}>
                <AlertDialogContent>
                    <AlertDialogHeader>
                        <AlertDialogTitle>创建成功</AlertDialogTitle>
                        <AlertDialogDescription>
                            客户SPU创建成功！请选择下一步操作。
                        </AlertDialogDescription>
                    </AlertDialogHeader>
                    <AlertDialogFooter>
                        <AlertDialogAction onClick={handleReturnList}>
                            返回列表
                        </AlertDialogAction>
                        <AlertDialogAction onClick={handleContinueCreate}>
                            继续创建
                        </AlertDialogAction>
                    </AlertDialogFooter>
                </AlertDialogContent>
            </AlertDialog>
        </>
    );
}