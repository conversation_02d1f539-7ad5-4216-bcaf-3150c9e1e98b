import { SupplierCapacity } from "@/api/inventory/customer/customer-product-api";
import { selectSupplier } from "@/api/supplier/supplier-api";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { useRequest } from "ahooks";
import { Plus, Trash2 } from "lucide-react";
import { useEffect, useState } from "react";
import { toast } from "sonner";

interface SupplierCapacityCardProps {
    value?: SupplierCapacity[];
    onChange?: (capacities: SupplierCapacity[]) => void;
    disabled?: boolean;
}

export function SupplierCapacityCard({ value = [], onChange, disabled = false }: SupplierCapacityCardProps) {
    const [capacities, setCapacities] = useState<SupplierCapacity[]>(value);

    // 获取供应商列表
    const { data: suppliers = [] } = useRequest(selectSupplier);

    useEffect(() => {
        setCapacities(value);
    }, [value]);

    const handleAddCapacity = (event: React.MouseEvent<HTMLButtonElement>) => {
        event.preventDefault();
        // 检查是否还有可选的供应商
        const usedSupplierIds = new Set(capacities.map(c => c.supplierId));
        const availableSuppliers = suppliers.filter(s => !usedSupplierIds.has(s.id));

        if (availableSuppliers.length === 0) {
            toast.error("没有更多可选的供应商");
            return;
        }

        const firstAvailableSupplier = availableSuppliers[0];
        const newCapacity: SupplierCapacity = {
            supplierId: firstAvailableSupplier.id,
            supplierName: firstAvailableSupplier.name,
            totalCapacity: -1 // 默认无限大
        };

        const newCapacities = [...capacities, newCapacity];
        setCapacities(newCapacities);
        onChange?.(newCapacities);
    };

    const handleRemoveCapacity = (index: number) => {
        const newCapacities = capacities.filter((_, i) => i !== index);
        setCapacities(newCapacities);
        onChange?.(newCapacities);
    };

    const handleSupplierChange = (index: number, supplierId: string) => {
        const supplier = suppliers.find(s => s.id === supplierId);
        if (!supplier) return;

        const newCapacities = [...capacities];
        newCapacities[index] = {
            ...newCapacities[index],
            supplierId: supplier.id,
            supplierName: supplier.name
        };
        setCapacities(newCapacities);
        onChange?.(newCapacities);
    };

    const handleCapacityChange = (index: number, value: string) => {
        // 如果输入为空，或者输入"-"（尝试输入负数的第一步），使用-1作为默认值
        if (value === "" || value === "-") {
            const newCapacities = [...capacities];
            newCapacities[index] = {
                ...newCapacities[index],
                totalCapacity: -1
            };
            setCapacities(newCapacities);
            onChange?.(newCapacities);
            return;
        }

        const capacity = parseInt(value);
        if (isNaN(capacity)) return;

        const newCapacities = [...capacities];
        newCapacities[index] = {
            ...newCapacities[index],
            totalCapacity: capacity
        };
        setCapacities(newCapacities);
        onChange?.(newCapacities);
    };

    return (
        <Card>
            <CardHeader className="flex flex-row items-center justify-between">
                <CardTitle>供应商产能设置</CardTitle>
                <Button
                    variant="outline"
                    size="sm"
                    onClick={handleAddCapacity}
                    disabled={disabled}
                >
                    <Plus className="w-4 h-4 mr-2" />
                    添加供应商
                </Button>
            </CardHeader>
            <CardContent>
                <div className="space-y-4">
                    {capacities.map((capacity, index) => {
                        const usedSupplierIds = new Set(
                            capacities.map((c, i) => i === index ? "-1" : c.supplierId)
                        );
                        const availableSuppliers = suppliers.filter(
                            s => !usedSupplierIds.has(s.id)
                        );

                        // 确保当前选中的供应商在列表中
                        const currentSupplier = suppliers.find(s => s.id === capacity.supplierId);
                        if (currentSupplier && !availableSuppliers.includes(currentSupplier)) {
                            availableSuppliers.push(currentSupplier);
                        }

                        return (
                            <div
                                key={index}
                                className="flex items-center gap-4 p-4 border rounded-lg bg-card"
                            >
                                <div className="flex-1">
                                    <Select
                                        value={capacity.supplierId || ""}
                                        onValueChange={(value) => handleSupplierChange(index, value)}
                                        disabled={disabled}
                                    >
                                        <SelectTrigger>
                                            <SelectValue placeholder="选择供应商" />
                                        </SelectTrigger>
                                        <SelectContent>
                                            {availableSuppliers.map((supplier) => (
                                                <SelectItem
                                                    key={supplier.id}
                                                    value={supplier.id}
                                                >
                                                    {supplier.name}
                                                </SelectItem>
                                            ))}
                                        </SelectContent>
                                    </Select>
                                </div>
                                <div className="w-48 relative">
                                    <Input
                                        type="number"
                                        placeholder="产能 (-1表示无限)"
                                        value={capacity.totalCapacity === -1 ? "-1" : capacity.totalCapacity || ""}
                                        onChange={(e) => handleCapacityChange(index, e.target.value)}
                                        disabled={disabled}
                                        className={capacity.totalCapacity === -1 ? "pr-20" : ""}
                                    />
                                    {capacity.totalCapacity === -1 && (
                                        <span className="text-xs text-muted-foreground absolute -translate-y-1/2 top-1/2 right-10">
                                            无限产能
                                        </span>
                                    )}
                                </div>
                                <Button
                                    variant="ghost"
                                    size="icon"
                                    onClick={(event) => {
                                        event.preventDefault();
                                        handleRemoveCapacity(index)
                                    }}
                                    disabled={disabled}
                                >
                                    <Trash2 className="w-4 h-4 text-destructive" />
                                </Button>
                            </div>
                        );
                    })}
                    {capacities.length === 0 && (
                        <div className="text-center text-muted-foreground py-8">
                            暂无供应商产能设置
                        </div>
                    )}
                </div>
            </CardContent>
        </Card>
    );
}