// src/components/form/customer-spu/customer-spu-form.tsx
import {findCountries} from '@/api/common-api';
import {SupplierCapacity} from "@/api/inventory/customer/customer-product-api";
import {SkuCountryPrices, SkuPcsPrices} from '@/api/inventory/customer/types';
import {SkuResponse} from '@/api/inventory/sku/sku-model';
import {findSkus, selectCustomerSpu} from '@/api/inventory/spu/spu-api';
import {Button} from "@/components/ui/button";
import {Card, CardContent, CardHeader, CardTitle} from "@/components/ui/card";
import {Command, CommandEmpty, CommandGroup, CommandInput, CommandItem, CommandList} from "@/components/ui/command";
import {Form, FormControl, FormField, FormItem, FormLabel, FormMessage} from "@/components/ui/form";
import {Input} from "@/components/ui/input";
import {Popover, PopoverContent, PopoverTrigger} from "@/components/ui/popover";
import {ScrollArea} from "@/components/ui/scroll-area";
import {cn} from "@/lib/utils";
import {zodResolver} from "@hookform/resolvers/zod";
import {useRequest} from "ahooks";
import {ArrowLeft, Check, ChevronsUpDown, Info, Package2} from "lucide-react";
import {useEffect} from 'react';
import {useForm} from "react-hook-form";
import {Link} from "react-router-dom";
import {toast} from "sonner";
import * as z from "zod";
import CustomerSkuTable, {CustomerSKU} from './customer-sku-table';
import {SupplierCapacityCard} from './supplier-capacity-card';

const formSchema = z.object({
    id: z.string().optional(),
    systemSpuId: z.string().min(1, "System SPU is required"),
    customerSpuCode: z.string().min(1, "SPU Code is required"),
    systemSpuCode: z.string(),
    customerId: z.string(),
    triggerDiscountQuantity: z.number().min(1, "Trigger discount quantity must be at least 1"),
    discount: z.number().min(0),
    skus: z.array(z.object({
        id: z.string().optional(),
        systemSkuCode: z.string(),
        systemSpuId: z.string(),
        systemSkuId: z.string(),
        size: z.string(),
        color: z.string(),
        offlinePrice: z.number(),
        offlinePriceCurrency: z.string(),
        skuCountryPrices: z.record(z.string(), z.object({
            price: z.number(),
            countryName: z.string(),
            isoAlphaTwo: z.string(),
            skuPcsPrices: z.array(z.object({
                price: z.number(),
                atLeastPcs: z.number(),
            })).optional(),
        })).optional(),
        skuPcsPrices: z.array(z.object({
            price: z.number(),
            atLeastPcs: z.number(),
        })).optional(),
        discount: z.number().optional(),
        triggerDiscountQuantity: z.number().optional(),
    })),
    supplierCapacities: z.array(z.object({
        supplierId: z.string(),
        supplierName: z.string(),
        totalCapacity: z.number(),
    })).default([]),
});

export type CustomerSpuFormData = z.infer<typeof formSchema>;

interface Props {
    id?: string | null;
    customerId: string
    isCreate?: boolean;
    initialData?: CustomerSpuFormData;
    onSubmit: (data: CustomerSpuFormData) => Promise<void>;
}

export function CustomerSpuForm({ id, customerId, isCreate, initialData, onSubmit }: Props) {
    const { data: systemSpus } = useRequest(selectCustomerSpu, {
        defaultParams: [isCreate ? customerId : null],
    });

    const { data: countries } = useRequest(findCountries);

    const form = useForm<CustomerSpuFormData>({
        resolver: zodResolver(formSchema),
        defaultValues: {
            skus: [],
            systemSpuId: "",
            customerSpuCode: "",
            triggerDiscountQuantity: 1,
            discount: 0,
            supplierCapacities: []
        },
    });

    useEffect(() => {
        if (initialData) {
            form.reset(initialData);
        }
    }, [initialData, form]);

    const { setValue, watch } = form;
    //@ts-ignore
    const systemSpuId = watch('systemSpuId');
    const skus = watch('skus');

    const handleSkuChange = (index: number, field: keyof CustomerSKU, value: string | number | SkuCountryPrices | SkuPcsPrices) => {
        const updatedSkus = [...skus];
        updatedSkus[index] = { ...updatedSkus[index], [field]: value };
        setValue('skus', updatedSkus);
    };

    const handleBulkEdit = (field: keyof CustomerSKU, value: string | number) => {
        const updatedSkus = skus.map(sku => ({
            ...sku,
            [field]: value
        }));
        setValue('skus', updatedSkus);
    };

    const handleSystemSpuChange = async (newSystemSpuId: string) => {
        if (!id) {
            const skus = await findSkus(newSystemSpuId);
            try {
                setValue('systemSpuId', newSystemSpuId);
                setValue('customerSpuCode', systemSpus!!.find((spu: any) => spu.id === newSystemSpuId)!!.spuCode);
                setValue('customerId', customerId)
                setValue('systemSpuCode', systemSpus!!.find((spu: any) => spu.id === newSystemSpuId)!!.spuCode);
                setValue('skus', skus.map((sku: SkuResponse) => ({
                    ...sku,
                    systemSkuCode: sku.skuCode,
                    systemSpuId: sku.spuId,
                    systemSkuId: sku.id,
                    offlinePrice: 0,
                    offlinePriceCurrency: 'USD',
                })));
            } catch (error) {
                console.error("Error fetching system SPU details:", error);
                toast.error("Failed to fetch system SPU details. Please try again.");
            }
        }
    };

    async function handleSubmit(values: CustomerSpuFormData) {
        try {
            // 在提交前,将skuPcsPrices进行排序,根据atLeastPcs字段从大到小排序
            const clonedObject = structuredClone(values)
            clonedObject.skus.forEach(sku => {
                if (sku.skuPcsPrices) {
                    sku.skuPcsPrices = sku.skuPcsPrices.sort((a, b) => b.atLeastPcs - a.atLeastPcs);
                }
                if (sku.skuCountryPrices) {
                    const countryIds = Object.keys(sku.skuCountryPrices)
                    for (const countryId of countryIds) {
                        const countryPrice = sku.skuCountryPrices[countryId];
                        if (!countryPrice) {
                            continue
                        }
                        if (!countryPrice.skuPcsPrices) {
                            continue
                        }
                        countryPrice.skuPcsPrices = countryPrice.skuPcsPrices.sort((a, b) => b.atLeastPcs - a.atLeastPcs);
                    }
                }
            })

            await onSubmit(clonedObject);
        } catch (error) {
            console.error("Error submitting form:", error);
            toast.error("Failed to submit form. Please try again.");
        }
    }

    return (
        <div className="space-y-6">
            <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                    <Button
                      variant="ghost"
                      size="icon"
                      asChild
                    >
                        <Link to="/dashboard/inventory/customer/products">
                            <ArrowLeft className="h-4 w-4"/>
                        </Link>
                    </Button>
                    <h2 className="text-lg font-medium">{isCreate ? '创建客户SPU' : '编辑客户SPU'}</h2>
                </div>
            </div>

            <Form {...form}>
                <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-8">
                    <Card>
                        <CardHeader>
                            <CardTitle className="flex items-center gap-2">
                                <Info className="w-5 h-5" />
                                基本信息
                            </CardTitle>
                        </CardHeader>
                        <CardContent className="space-y-4">
                            <div className="grid grid-cols-2 gap-4">
                                <FormField
                                    control={form.control}
                                    name="systemSpuId"
                                    render={({ field }) => (
                                        <FormItem>
                                            <FormLabel>系统 SPU</FormLabel>
                                            <FormControl>
                                                <Popover>
                                                    <PopoverTrigger asChild>
                                                        <Button
                                                            variant="outline"
                                                            role="combobox"
                                                            disabled={!!id}
                                                            className="w-full justify-between"
                                                        >
                                                            {field.value
                                                                ? (systemSpus ?? []).find((spu: any) => spu.id === field.value)?.spuCode
                                                                : "选择系统 SPU"}
                                                            <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                                                        </Button>
                                                    </PopoverTrigger>
                                                    <PopoverContent className="w-[--radix-popover-trigger-width] p-0 min-w-[200px]">
                                                        <Command>
                                                            <CommandInput placeholder="搜索系统 SPU..." />
                                                            <CommandList>
                                                                <CommandEmpty>未找到匹配的 SPU</CommandEmpty>
                                                                <CommandGroup>
                                                                    <ScrollArea className="h-72">
                                                                        {(systemSpus ?? []).map((spu: any) => (
                                                                            <CommandItem
                                                                                key={spu.id}
                                                                                value={spu.spuCode}
                                                                                onSelect={() => {
                                                                                    handleSystemSpuChange(spu.id);
                                                                                }}
                                                                            >
                                                                                <Check
                                                                                    className={cn(
                                                                                        "mr-2 h-4 w-4",
                                                                                        field.value === spu.id ? "opacity-100" : "opacity-0"
                                                                                    )}
                                                                                />
                                                                                {spu.spuCode}
                                                                            </CommandItem>
                                                                        ))}
                                                                    </ScrollArea>
                                                                </CommandGroup>
                                                            </CommandList>
                                                        </Command>
                                                    </PopoverContent>
                                                </Popover>
                                            </FormControl>
                                            <FormMessage />
                                        </FormItem>
                                    )}
                                />

                                <FormField
                                    control={form.control}
                                    name="customerSpuCode"
                                    render={({ field }) => (
                                        <FormItem>
                                            <FormLabel>客户 SPU </FormLabel>
                                            <FormControl>
                                                <Input
                                                    placeholder="输入客户 SPU"
                                                    {...field}
                                                />
                                            </FormControl>
                                            <FormMessage />
                                        </FormItem>
                                    )}
                                />
                            </div>

                            <div className="grid grid-cols-2 gap-4">
                                <FormField
                                    control={form.control}
                                    name="triggerDiscountQuantity"
                                    render={({ field }) => (
                                        <FormItem>
                                            <FormLabel>触发折扣数量</FormLabel>
                                            <FormControl>
                                                <Input
                                                    type="number"
                                                    placeholder="输入触发折扣数量"
                                                    {...field}
                                                    onChange={e => field.onChange(Number(e.target.value))}
                                                />
                                            </FormControl>
                                            <FormMessage />
                                        </FormItem>
                                    )}
                                />

                                <FormField
                                    control={form.control}
                                    name="discount"
                                    render={({ field }) => (
                                        <FormItem>
                                            <FormLabel>折扣</FormLabel>
                                            <FormControl>
                                                <Input
                                                    type="number"
                                                    step="0.01"
                                                    placeholder="输入折扣 (0-1)"
                                                    {...field}
                                                    onChange={e => field.onChange(Number(e.target.value))}
                                                />
                                            </FormControl>
                                            <FormMessage />
                                        </FormItem>
                                    )}
                                />
                            </div>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader>
                            <CardTitle className="flex items-center gap-2">
                                <Package2 className="w-5 h-5" />
                                SKU 信息
                            </CardTitle>
                        </CardHeader>
                        <CardContent>
                            <FormField
                                control={form.control}
                                name="skus"
                                render={({ field }) => (
                                    <FormItem>
                                        <FormControl>
                                            <CustomerSkuTable
                                                skus={field.value}
                                                countries={countries || []}
                                                onSkuChange={handleSkuChange}
                                                onBulkEdit={handleBulkEdit}
                                                disabled={form.formState.isSubmitting}
                                            />
                                        </FormControl>
                                        <FormMessage />
                                    </FormItem>
                                )}
                            />
                        </CardContent>
                    </Card>

                    <FormField
                        control={form.control}
                        name="supplierCapacities"
                        render={({ field }) => (
                            <FormItem>
                                <FormControl>
                                    <SupplierCapacityCard
                                        value={field.value}
                                        onChange={(capacities: SupplierCapacity[]) => {
                                            field.onChange(capacities);
                                        }}
                                        disabled={form.formState.isSubmitting}
                                    />
                                </FormControl>
                                <FormMessage />
                            </FormItem>
                        )}
                    />

                    <div className="flex justify-end space-x-4">
                        <Button
                            type="submit"
                            disabled={form.formState.isSubmitting}
                        >
                            {isCreate ? '创建' : '保存'}
                        </Button>
                    </div>
                </form>
            </Form>
        </div>
    );
}