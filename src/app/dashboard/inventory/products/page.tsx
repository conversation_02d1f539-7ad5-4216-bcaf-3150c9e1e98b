import {DataTable} from "@/components/table/base-table";
import {DataTableToolbar, ToolbarItem, ToolbarLeft, ToolbarRight} from "@/components/table/toolbar/data-table-toolbar";
import {DataTableViewOptions} from "@/components/table/toolbar/data-table-view-options";
import {Button} from "@/components/ui/button";
import {Plus} from "lucide-react";
import {SpuColumns} from "./table/spu-colmun";
import {pageSpu} from "@/api/inventory/spu/spu-api";
import {Link} from "react-router-dom";
import {SelectWithInput} from "@/components/ui/select-with-input";
import {ProductExcelExport} from "./product-export-btn";
import useSearchParamsManager from "@/hooks/use-url-param";
import {CategoryCombobox} from "@/components/form/category-combox";
import {getDictValue} from "@/api/common-api";
import {useRequest} from "ahooks";

export default function ProductPage() {
    const {getParam, addParam} = useSearchParamsManager();

    // 获取类别选项
    const {data: categoryOptions = []} = useRequest<string[], any[]>(getDictValue, {
        defaultParams: ['spu-category'],
    });

    return (
        <div className="flex flex-col gap-4">
            <div className="flex items-center justify-between">
                <h1 className="text-2xl font-bold">产品管理</h1>
            </div>
            <div className="rounded-lg border shadow-sm p-4">
                <DataTable
                    columns={SpuColumns}
                    onFetch={param => pageSpu(param)}
                    isFixedHeader={false}
                    containerHeight="620px"
                    isNeedSelect={true}
                    toolbar={(table, tableId) => (
                        <DataTableToolbar table={table} tableId={tableId}>
                            <ToolbarLeft>
                                <ToolbarItem>
                                    <SelectWithInput
                                        options={[
                                            { label: "商品编码", value: "spuCode" },
                                            { label: "商品标题", value: "title" },
                                        ]}
                                        inputPlaceholder="请输入搜索内容"
                                    />
                                </ToolbarItem>
                                <ToolbarItem>
                                    <div className="w-48">
                                        <CategoryCombobox
                                            value={getParam('category') || ''}
                                            onChange={(value) => addParam('category', value)}
                                            categoryOptions={categoryOptions}
                                        />
                                    </div>
                                </ToolbarItem>
                            </ToolbarLeft>


                            <ToolbarRight>
                                <ToolbarItem>
                                    <ProductExcelExport selectedRows={table.getSelectedRowModel().rows} />
                                </ToolbarItem>
                                <ToolbarItem>
                                    <DataTableViewOptions table={table} tableId={tableId} />
                                </ToolbarItem>
                                <ToolbarItem>
                                    <Button className="h-8 text-green-600 bg-green-50 hover:bg-green-100"
                                            asChild
                                    >
                                      <Link to='/dashboard/inventory/products/details'>
                                        <Plus className="mr-2 h-4 w-4" />
                                        新增产品
                                      </Link>
                                    </Button>
                                </ToolbarItem>
                            </ToolbarRight>
                        </DataTableToolbar>
                    )}
                    dependencies={[]}
                />
            </div>
        </div>
    )
}