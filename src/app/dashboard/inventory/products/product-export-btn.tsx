import React, { useState } from 'react';
import { Button } from "@/components/ui/button";
import {
    Dialog,
    DialogContent,
    DialogDescription,
    DialogFooter,
    DialogHeader,
    DialogTitle,
    DialogTrigger,
} from "@/components/ui/dialog";
import { toast } from 'sonner';
import { downloadSpuExcel } from '@/api/inventory/spu/spu-api';
import { useExcelDownload } from '@/hooks/use-export-file';

interface ProductExcelExportProps {
    selectedRows: any[];
}

export const ProductExcelExport: React.FC<ProductExcelExportProps> = ({ selectedRows }) => {

    const [isDialogOpen, setIsDialogOpen] = useState(false);

    const selectedIds = selectedRows.map(row => row.original.id);

    const { downloadExcel } = useExcelDownload(downloadSpuExcel);

    const handleExport = async () => {
        setIsDialogOpen(false);
        toast.promise(downloadExcel({ ids: selectedIds }), {
            loading: '导出中...',
            success: '导出成功',
            error: '导出失败'
        });
    };

    if (selectedRows.length === 0) return null;

    return (
        <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
            <DialogTrigger asChild>
                <Button className='h-8' variant="outline">导出Excel</Button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-[425px]">
                <DialogHeader>
                    <DialogTitle>确认导出</DialogTitle>
                    <DialogDescription>
                        您确定要导出选中的 {selectedRows.length} 条记录吗？
                    </DialogDescription>
                </DialogHeader>
                <DialogFooter>
                    <Button variant="outline" onClick={() => setIsDialogOpen(false)}>
                        取消
                    </Button>
                    <Button onClick={handleExport}>确认</Button>
                </DialogFooter>
            </DialogContent>
        </Dialog>
    );
};
