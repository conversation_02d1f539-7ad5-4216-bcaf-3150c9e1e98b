import {zod<PERSON><PERSON>olver} from "@hookform/resolvers/zod"
import {useForm} from "react-hook-form"
import {z} from "zod"
import {Button} from "@/components/ui/button"
import {Form, FormControl, FormField, FormItem, FormLabel, FormMessage,} from "@/components/ui/form"
import {Input} from "@/components/ui/input"
import {Textarea} from "@/components/ui/textarea"
import {Checkbox} from "@/components/ui/checkbox"
import {useEffect, useMemo, useState} from 'react'
import ImageUpload from "@/components/buttons/image-upload";

import {useRequest} from "ahooks";
import {toast} from "sonner";
import {Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger} from "@/components/ui/dialog"
import {addDictValue, getDictValue} from "@/api/common-api"
import {SpuResponse, UpdateSpuRequest} from "@/api/inventory/spu/spu-model"
import {createSpu, updateSpu} from "@/api/inventory/spu/spu-api"
import {CategoryCombobox} from "@/components/form/category-combox"
import {uploadImage} from "@/api/file-api"
import MultiImageUploadField from "@/components/buttons/muti-img-upload"
import {
    MultiSelector,
    MultiSelectorContent,
    MultiSelectorInput,
    MultiSelectorItem,
    MultiSelectorList,
    MultiSelectorTrigger
} from "@/components/ui/muti-select"
import SkuTable, {SKU} from "./sku-table"
import {cn} from "@/lib/utils"
import {Link, useNavigate} from "react-router-dom"
import {Card, CardContent, CardHeader, CardTitle} from "@/components/ui/card"
import {ArrowLeft, Boxes, Image as ImageIcon, Info, Package2, Plus} from "lucide-react"

const formSchema = z.object({
    spuCode: z.string().min(1, "SPU Code is required"),
    title: z.string().min(1, "Title is required"),
    name: z.string().min(1, "Name is required"),
    cnName: z.string().min(1, "Chinese Name is required"),
    category: z.string().min(1, "Category is required"),
    productImage: z.string().min(1, "Product Image is required"),
    packageQuantity: z.number().int().positive("Package Quantity must be a positive integer"),
    showImages: z.array(z.string()).min(1, "At least one show image is required"),
    description: z.string().optional(),
    sizes: z.array(z.string()).min(1, "At least one size is required"),
    colors: z.array(z.string()).min(1, "At least one color is required"),
    hsCode: z.string().optional(),
    isSet: z.boolean().default(false),
    setQuantity: z.number().int().positive("Set quantity must be a positive integer").optional(),
    skus: z.array(z.object({
        id: z.string().optional(),
        skuCode: z.string(),
        size: z.string(),
        color: z.string(),
        purchaseCost: z.number(),
        purchaseCostCurrency: z.string(),
        weight: z.number(),
        volume: z.number(),
        salePrice: z.number(),
    })),
}).refine(data => {
    // If isSet is true, setQuantity must be provided
    return !data.isSet || (data.isSet && data.setQuantity && data.setQuantity > 0);
}, {
    message: "套装数量不能为空且必须大于0",
    path: ["setQuantity"]
})

interface Props {
    id?: string | undefined | null
    product?: SpuResponse
    onUpdated: () => void
}

const AddOptionDialog: React.FC<{
    title: string;
    onAdd: (value: string) => void;
}> = ({ title, onAdd }) => {
    const [newValue, setNewValue] = useState('');
    const [isOpen, setIsOpen] = useState(false);

    const handleAdd = () => {
        if (newValue.trim()) {
            onAdd(newValue.trim());
            setNewValue('');
            setIsOpen(false);
        }
    };

    return (
        <Dialog open={isOpen} onOpenChange={setIsOpen}>
            <DialogTrigger asChild>
                <Button variant="outline" size="sm" className="flex items-center gap-1">
                    <Plus className="w-4 h-4" />
                    新增{title}
                </Button>
            </DialogTrigger>
            <DialogContent>
                <DialogHeader>
                    <DialogTitle>新增{title}</DialogTitle>
                </DialogHeader>
                <Input
                    value={newValue}
                    onChange={(e) => setNewValue(e.target.value)}
                    placeholder={`输入新的${title.toLowerCase()}`}
                />
                <Button onClick={handleAdd}>新增</Button>
            </DialogContent>
        </Dialog>
    );
};


export function ProductForm({ id, product, onUpdated }: Props) {

    const { data: sizeOptions = [], mutate: mutateSize } = useRequest<string[], any[]>(getDictValue, {
        defaultParams: ['size'],
    });

    const { data: colorOptions = [], mutate: mutateColor } = useRequest<string[], any[]>(getDictValue, {
        defaultParams: ['color'],
    });

    const { data: categoryOptions = [], mutate: mutateCategory } = useRequest<string[], any[]>(getDictValue, {
        defaultParams: ['spu-category'],
    });

    const navigate = useNavigate();

    const handleAddOption = async (type: 'size' | 'color' | 'spu-category', value: string) => {
        try {
            await addDictValue(type, value);
            toast.success(`Added new ${type}: ${value}`);
            switch (type) {
                case 'size':
                    mutateSize([...sizeOptions, value])
                    break;
                case 'color':
                    mutateColor([...colorOptions, value])
                    break;
                case 'spu-category':
                    mutateCategory([...categoryOptions, value])
                    break;
            }
        } catch (error) {
            toast.error(`Failed to add new ${type}`);
        }
    };

    const isEdit = id != null

    const form = useForm<z.infer<typeof formSchema>>({
        resolver: zodResolver(formSchema),
        defaultValues: {
            spuCode: "",
            title: "",
            name: "",
            cnName: "",
            category: "",
            productImage: "",
            packageQuantity: 1,
            showImages: [],
            description: "",
            sizes: [],
            colors: [],
            hsCode: "",
            isSet: false,
            setQuantity: undefined,
            skus: [],
        },
    })

    useEffect(() => {
        if (product) {
            form.reset({
                spuCode: product.spuCode,
                title: product.title,
                name: product.name,
                cnName: product.cnName,
                category: product.category,
                productImage: product.productImage,
                packageQuantity: product.packageQuantity,
                showImages: product.showImages,
                description: product.description,
                sizes: product.sizes,
                colors: product.colors,
                hsCode: product.hsCode,
                isSet: product.isSet || false,
                setQuantity: product.setQuantity,
                skus: product.skus || [],
            });
        }
    }, [product, form]);

    const { watch, setValue } = form;

    const spuCode = watch('spuCode');
    const sizes = watch('sizes');
    const colors = watch('colors');
    const skus = watch('skus');
    const isSet = watch('isSet');

    const generateSkus = useMemo(() => {
        return sizes.flatMap((size: string) =>
            colors.map((color: string) => {
                const existingSku = skus.find(sku => sku.size === size && sku.color === color);
                return existingSku || {
                    skuCode: `${spuCode}-${size}-${color}`,
                    size,
                    color,
                    purchaseCost: 0,
                    purchaseCostCurrency: 'USD',
                    weight: 0,
                    volume: 0,
                    salePrice: 0,
                };
            })
        );
    }, [id, spuCode, sizes, colors, skus]);

    useEffect(() => {
        if (JSON.stringify(generateSkus) !== JSON.stringify(skus)) {
            setValue('skus', generateSkus);
        }
    }, [generateSkus, skus, setValue]);

    const handleSkuChange = (index: number, field: keyof SKU, value: string | number) => {
        const updatedSkus = [...skus];
        updatedSkus[index] = { ...updatedSkus[index], [field]: value };
        setValue('skus', updatedSkus);
    };

    const handleBulkEdit = (field: keyof SKU, value: string | number) => {
        const updatedSkus = skus.map(sku => ({
            ...sku,
            [field]: value
        }));
        setValue('skus', updatedSkus);
    };

    async function onSubmit(values: z.infer<typeof formSchema>) {
        try {
            const processedValues = {
                ...values,
                hsCode: values.hsCode?.trim() || null
            };

            const updateRequest: UpdateSpuRequest = {
                ...processedValues,
                spuCode: spuCode,
                packageQuantity: Number(values.packageQuantity),
                skus: values.skus.map(sku => {
                    return {
                        id: sku.id,
                        skuCode: sku.skuCode,
                        size: sku.size,
                        color: sku.color,
                        purchaseCost: sku.purchaseCost,
                        purchaseCostCurrency: sku.purchaseCostCurrency,
                        weight: sku.weight,
                        volume: sku.volume,
                        salePrice: sku.salePrice,
                    }
                })
            } as UpdateSpuRequest;
            console.log(JSON.stringify(updateRequest))
            if (id) {
                await updateSpu(id, updateRequest);
                toast.success("更新成功");
                onUpdated()
            } else {
                const res = await createSpu(updateRequest)
                navigate(`/dashboard/inventory/products/details?id=${res.id}`)
                toast.success("创建成功");
            }

        } catch (error) {
            console.error("Error updating product:", error);
            toast.error("Failed to update product. Please try again.");
        }
    }

    return (
        <Form {...form}>
            <div className="flex items-center gap-4 mb-6">
                <Button
                    variant="ghost"
                    size="icon"
                    className="hover:bg-muted"
                    asChild
                >
                    <Link to="/dashboard/inventory/products">
                        <ArrowLeft className="w-5 h-5" />
                    </Link>
                </Button>
                <div>
                    <h1 className="text-2xl font-semibold tracking-tight">
                        {isEdit ? "编辑商品" : "新增商品"}
                    </h1>
                    <p className="text-sm text-muted-foreground">
                        {isEdit
                            ? `正在编辑 ${product?.name || 'SPU'} (${product?.spuCode || 'Unknown'})`
                            : "创建一个新的商品并设置其属性"
                        }
                    </p>
                </div>
            </div>

            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
                <Card>
                    <CardHeader className="flex flex-row items-center space-y-0 gap-2">
                        <Info className="w-5 h-5 text-muted-foreground" />
                        <CardTitle>基本信息</CardTitle>
                    </CardHeader>
                    <CardContent className="grid grid-cols-2 gap-6">
                        <FormField
                            control={form.control}
                            name="spuCode"
                            render={({ field }) => (
                                <FormItem>
                                    <FormLabel>SPU 编码</FormLabel>
                                    <FormControl>
                                        <Input disabled={isEdit}
                                            className={cn(isEdit ? "bg-gray-100" : "", "")}
                                            placeholder="Enter SPU Code" {...field} />
                                    </FormControl>
                                    <FormMessage />
                                </FormItem>
                            )}
                        />

                        <FormField
                            control={form.control}
                            name="title"
                            render={({ field }) => (
                                <FormItem>
                                    <FormLabel>标题</FormLabel>
                                    <FormControl>
                                        <Input placeholder="Enter Title" {...field} />
                                    </FormControl>
                                    <FormMessage />
                                </FormItem>
                            )}
                        />

                        <FormField
                            control={form.control}
                            name="name"
                            render={({ field }) => (
                                <FormItem>
                                    <FormLabel>英文名称</FormLabel>
                                    <FormControl>
                                        <Input placeholder="Enter English Name" {...field} />
                                    </FormControl>
                                    <FormMessage />
                                </FormItem>
                            )}
                        />

                        <FormField
                            control={form.control}
                            name="cnName"
                            render={({ field }) => (
                                <FormItem>
                                    <FormLabel>中文名称</FormLabel>
                                    <FormControl>
                                        <Input placeholder="Enter Chinese Name" {...field} />
                                    </FormControl>
                                    <FormMessage />
                                </FormItem>
                            )}
                        />

                        <FormField
                            control={form.control}
                            name="category"
                            render={({ field }) => (
                                <FormItem className="col-span-2">
                                    <FormLabel>类别</FormLabel>
                                    <FormControl>
                                        <div className="flex items-center space-x-2">
                                            <CategoryCombobox
                                                value={field.value}
                                                onChange={field.onChange}
                                                categoryOptions={categoryOptions}
                                            />
                                            <AddOptionDialog
                                                title="分类"
                                                onAdd={(value) => handleAddOption('spu-category', value)}
                                            />
                                        </div>
                                    </FormControl>
                                    <FormMessage />
                                </FormItem>
                            )}
                        />
                    </CardContent>
                </Card>

                <Card>
                    <CardHeader className="flex flex-row items-center space-y-0 gap-2">
                        <ImageIcon className="w-5 h-5 text-muted-foreground" />
                        <CardTitle>图片信息</CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-6">
                        <FormField
                            control={form.control}
                            name="productImage"
                            render={({ field }) => (
                                <FormItem>
                                    <FormLabel>产品主图</FormLabel>
                                    <FormControl>
                                        <ImageUpload
                                            onImageUpload={async (file) => {
                                                const dummyUrl = await uploadImage(file);
                                                field.onChange(dummyUrl)
                                            }}
                                            onImageRemove={() => field.onChange("")}
                                            initialImage={field.value}
                                        />
                                    </FormControl>
                                    <FormMessage />
                                </FormItem>
                            )}
                        />

                        <MultiImageUploadField
                            form={form}
                            name="showImages"
                            label="展示图片 (最多三张)"
                            maxImages={3}
                            uploadImage={uploadImage}
                        />
                    </CardContent>
                </Card>

                <Card>
                    <CardHeader className="flex flex-row items-center space-y-0 gap-2">
                        <Package2 className="w-5 h-5 text-muted-foreground" />
                        <CardTitle>商品属性</CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-6">
                        <div className="grid grid-cols-2 gap-6">
                            <FormField
                                control={form.control}
                                name="packageQuantity"
                                render={({ field }) => (
                                    <FormItem>
                                        <FormLabel>包装数量</FormLabel>
                                        <FormControl>
                                            <Input type="number" {...field}
                                                onChange={e => field.onChange(parseInt(e.target.value))} />
                                        </FormControl>
                                        <FormMessage />
                                    </FormItem>
                                )}
                            />

                            <FormField
                                control={form.control}
                                name="hsCode"
                                render={({ field }) => (
                                    <FormItem>
                                        <FormLabel>HS编码</FormLabel>
                                        <FormControl>
                                            <Input
                                                placeholder="输入HS编码"
                                                {...field}
                                                value={field.value || ""}
                                            />
                                        </FormControl>
                                        <FormMessage />
                                    </FormItem>
                                )}
                            />
                        </div>

                        <div className="grid grid-cols-2 gap-6">
                            <FormField
                                control={form.control}
                                name="isSet"
                                render={({field}) => (
                                    <FormItem className="flex flex-row items-start space-x-3 space-y-0">
                                        <FormControl>
                                            <Checkbox
                                                checked={field.value}
                                                onCheckedChange={(checked) => {
                                                    field.onChange(checked);
                                                    if (!checked) {
                                                        setValue('setQuantity', undefined);
                                                    } else {
                                                        setValue('setQuantity', 2);
                                                    }
                                                }}
                                            />
                                        </FormControl>
                                        <div className="space-y-1 leading-none">
                                            <FormLabel>
                                                是否套装
                                            </FormLabel>
                                        </div>
                                        <FormMessage/>
                                    </FormItem>
                                )}
                            />

                            {isSet && (
                                <FormField
                                    control={form.control}
                                    name="setQuantity"
                                    render={({field}) => (
                                        <FormItem>
                                            <FormLabel>套装数量</FormLabel>
                                            <FormControl>
                                                <Input
                                                    type="number"
                                                    placeholder="输入套装数量"
                                                    {...field}
                                                    value={field.value || ""}
                                                    onChange={e => {
                                                        const value = e.target.value;
                                                        field.onChange(value ? parseInt(value) : undefined);
                                                    }}
                                                />
                                            </FormControl>
                                            <FormMessage/>
                                        </FormItem>
                                    )}
                                />
                            )}

                            <FormField
                                control={form.control}
                                name="description"
                                render={({ field }) => (
                                    <FormItem className="col-span-2">
                                        <FormLabel>描述</FormLabel>
                                        <FormControl>
                                            <Textarea placeholder="Enter Description" {...field} />
                                        </FormControl>
                                        <FormMessage />
                                    </FormItem>
                                )}
                            />
                        </div>
                    </CardContent>
                </Card>

                <Card>
                    <CardHeader className="flex flex-row items-center space-y-0 gap-2">
                        <Boxes className="w-5 h-5 text-muted-foreground" />
                        <CardTitle>SKU信息</CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-6">
                        <div className="grid grid-cols-2 gap-6">
                            <FormField
                                control={form.control}
                                name="sizes"
                                render={({ field }) => (
                                    <FormItem>
                                        <FormLabel>尺寸</FormLabel>
                                        <FormControl>
                                            <div className="flex items-center space-x-2">
                                                <MultiSelector
                                                    values={field.value.map(v => ({ value: v, label: v }))}
                                                    onValuesChange={(newValues) => {
                                                        field.onChange(newValues.map(item => item.value));
                                                    }}
                                                >
                                                    <MultiSelectorTrigger>
                                                        <MultiSelectorInput placeholder="选择size" />
                                                    </MultiSelectorTrigger>
                                                    <MultiSelectorContent>
                                                        <MultiSelectorList>
                                                            {sizeOptions.map((v: string) => (
                                                                <MultiSelectorItem
                                                                    key={v}
                                                                    prop={{ value: v, label: v }}
                                                                >
                                                                    {v}
                                                                </MultiSelectorItem>
                                                            ))}
                                                        </MultiSelectorList>
                                                    </MultiSelectorContent>
                                                </MultiSelector>
                                                <AddOptionDialog
                                                    title="尺寸"
                                                    onAdd={(value) => handleAddOption('size', value)}
                                                />
                                            </div>
                                        </FormControl>
                                        <FormMessage />
                                    </FormItem>
                                )}
                            />

                            <FormField
                                control={form.control}
                                name="colors"
                                render={({ field }) => (
                                    <FormItem>
                                        <FormLabel>颜色</FormLabel>
                                        <FormControl>
                                            <div className="flex items-center space-x-2">
                                                <MultiSelector
                                                    values={field.value.map(v => ({ value: v, label: v }))}
                                                    onValuesChange={(newValues) => {
                                                        field.onChange(newValues.map(item => item.value));
                                                    }}
                                                >
                                                    <MultiSelectorTrigger>
                                                        <MultiSelectorInput placeholder="选择颜色" />
                                                    </MultiSelectorTrigger>
                                                    <MultiSelectorContent>
                                                        <MultiSelectorList>
                                                            {colorOptions.map((v: string) => (
                                                                <MultiSelectorItem
                                                                    key={v}
                                                                    prop={{ value: v, label: v }}
                                                                >
                                                                    {v}
                                                                </MultiSelectorItem>
                                                            ))}
                                                        </MultiSelectorList>
                                                    </MultiSelectorContent>
                                                </MultiSelector>
                                                <AddOptionDialog
                                                    title="颜色"
                                                    onAdd={(value) => handleAddOption('color', value)}
                                                />
                                            </div>
                                        </FormControl>
                                        <FormMessage />
                                    </FormItem>
                                )}
                            />
                        </div>

                        <FormField
                            control={form.control}
                            name="skus"
                            render={({ field }) => (
                                <FormItem>
                                    <FormLabel>SKU列表</FormLabel>
                                    <FormControl>
                                        <SkuTable
                                            skus={field.value as SKU[]}
                                            onSkuChange={handleSkuChange}
                                            onBulkEdit={handleBulkEdit}
                                        />
                                    </FormControl>
                                    <FormMessage />
                                </FormItem>
                            )}
                        />
                    </CardContent>
                </Card>

                <div className="flex justify-end pt-4 border-t">
                    <Button
                        type="submit"
                        size="lg"
                        className="min-w-[120px]"
                    >
                        {isEdit ? "保存修改" : "创建商品"}
                    </Button>
                </div>
            </form>
        </Form>
    )
}