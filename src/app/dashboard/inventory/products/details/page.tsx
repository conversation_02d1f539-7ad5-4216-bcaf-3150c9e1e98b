import { findSpu } from "@/api/inventory/spu/spu-api";
import { useRequest } from "ahooks";
import { useSearchParams } from "react-router-dom";
import { ProductForm } from "./spu-detail";


export default function ProductDetail() {

    const [searchParams] = useSearchParams  ();
    const id = searchParams.get('id');

    const {data, refresh} = useRequest(findSpu, {
        defaultParams: [id ?? null]
    })


    return (
        <div className="max-w-5xl mx-auto pt-10">
            <ProductForm product={data} id={id} onUpdated={()=> refresh()}/>
            <div className={'pt-20'}>
            </div>
        </div>
    )
        ;
}
