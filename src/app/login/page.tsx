import { loginUser } from "@/api/user/user-api"
import { PasswordInput } from "@/components/inputs/password-input"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { token<PERSON>tom } from "@/state/common"
import { zodResolver } from "@hookform/resolvers/zod"
import { ChevronRightIcon } from "@radix-ui/react-icons"
import { useRequest } from "ahooks"
import { motion } from "framer-motion"
import { useSetAtom } from "jotai/react"
import { Loader2 } from "lucide-react"
import { useForm } from "react-hook-form"
import { useNavigate } from "react-router-dom"
import { toast } from "sonner"
import * as z from "zod"


const loginSchema = z.object({
    account: z.string().min(1, "账号不能为空"),
    password: z.string().min(6, "密码至少需要6个字符")
})

type LoginFormValues = z.infer<typeof loginSchema>

export default function LoginPage() {

    const navigate = useNavigate();

    const setToken = useSetAtom(tokenAtom)

    const {
        register,
        handleSubmit,
        formState: { errors },
    } = useForm<LoginFormValues>({
        resolver: zodResolver(loginSchema)
    })

    // 修改useRequest配置,添加防抖
    const { runAsync: loginRun, loading } = useRequest(loginUser, {
        manual: true,
        debounceWait: 300, // 添加300ms的防抖
        onSuccess: (res) => {
            toast.success("登录成功")
            setToken(res)
            navigate("/dashboard/order/main-order")
        },
        onError: (error) => {
            toast.error(error.message)
        }
    })

    const onSubmit = async (data: LoginFormValues) => {
        try {
            await loginRun(data.account, data.password)
        } catch (error) {
            // 处理错误
            console.error(error)
        }
    }

    return (
        <div className="min-h-screen bg-gray-100 flex flex-col items-center justify-center p-4 relative overflow-hidden">
            <svg
                className="absolute inset-0 w-full h-full"
                xmlns="http://www.w3.org/2000/svg"
                viewBox="0 0 100 100"
                preserveAspectRatio="none"
            >
                <defs>
                    <pattern
                        id="grid"
                        width="10"
                        height="10"
                        patternUnits="userSpaceOnUse"
                    >
                        <path
                            d="M 10 0 L 0 0 0 10"
                            fill="none"
                            stroke="rgba(0,0,0,0.05)"
                            strokeWidth="0.5"
                        />
                    </pattern>
                </defs>
                <rect width="100" height="100" fill="url(#grid)" />
            </svg>

            <motion.div
                initial={{ y: -20, opacity: 0 }}
                animate={{ y: 0, opacity: 1 }}
                transition={{ duration: 0.5 }}
                className="mb-8 relative z-10"
            >
                <h1 className="text-4xl font-bold text-gray-800 text-center flex items-center justify-center">
                    Luxury Pro INC.
                    <motion.span
                        initial={{ opacity: 0, x: -10 }}
                        animate={{ opacity: 1, x: 0 }}
                        transition={{ delay: 0.5, duration: 0.5 }}
                        className="ml-2 text-blue-600"
                    >
                        <ChevronRightIcon className="h-8 w-8" />
                    </motion.span>
                </h1>
            </motion.div>

            <motion.div
                initial={{ scale: 0.95, opacity: 0 }}
                animate={{ scale: 1, opacity: 1 }}
                transition={{ duration: 0.5 }}
                className="w-full max-w-md relative z-10"
            >
                <motion.div
                    className="bg-white bg-opacity-90 backdrop-blur-sm rounded-lg shadow-xl overflow-hidden"
                    whileHover={{ boxShadow: "0 10px 25px -5px rgba(59, 130, 246, 0.1), 0 8px 10px -6px rgba(59, 130, 246, 0.1)" }}
                    transition={{ type: "spring", stiffness: 300 }}
                >
                    <div className="p-8">
                        <motion.h2
                            className="text-2xl font-semibold text-gray-800 mb-6"
                            initial={{ y: -10, opacity: 0 }}
                            animate={{ y: 0, opacity: 1 }}
                            transition={{ delay: 0.2, duration: 0.5 }}
                        >
                            欢迎登录
                        </motion.h2>
                        <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
                            <div className="space-y-2">
                                <Label htmlFor="account" className="text-sm font-medium text-gray-700">
                                    账号
                                </Label>
                                <motion.div
                                    initial={{ x: -10, opacity: 0 }}
                                    animate={{ x: 0, opacity: 1 }}
                                    transition={{ delay: 0.3, duration: 0.5 }}
                                >
                                    <Input
                                        id="account"
                                        placeholder="请输入账号"
                                        type="text"
                                        {...register("account")}
                                        autoComplete="username"
                                        className="bg-white bg-opacity-75"
                                    />
                                    {errors.account && <p className="text-red-500 text-xs mt-1">{errors.account.message}</p>}
                                </motion.div>
                            </div>
                            <div className="space-y-2">
                                <Label htmlFor="password" className="text-sm font-medium text-gray-700">
                                    密码
                                </Label>
                                <motion.div
                                    initial={{ x: -10, opacity: 0 }}
                                    animate={{ x: 0, opacity: 1 }}
                                    transition={{ delay: 0.4, duration: 0.5 }}
                                >
                                    <PasswordInput
                                        id="password"
                                        register={register("password")}
                                        error={errors.password?.message}
                                    />
                                </motion.div>
                            </div>
                            <motion.div
                                initial={{ y: 10, opacity: 0 }}
                                animate={{ y: 0, opacity: 1 }}
                                transition={{ delay: 0.5, duration: 0.5 }}
                            >
                                <Button type="submit" className="w-full bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-md transition duration-300 ease-in-out" disabled={loading}>
                                    {loading ? <Loader2 className="mr-2 h-4 w-4 animate-spin" /> : "登录"}
                                </Button>
                            </motion.div>
                        </form>
                    </div>
                    <motion.div
                        className="px-8 py-4 bg-gray-50 bg-opacity-75 border-t border-gray-200 flex justify-between items-center"
                        initial={{ y: 10, opacity: 0 }}
                        animate={{ y: 0, opacity: 1 }}
                        transition={{ delay: 0.6, duration: 0.5 }}
                    >
                        <div></div>
                        {/* <Button variant="link" onClick={() => pushModal("RegisterModal")} className="text-sm text-blue-600 hover:text-blue-800 transition duration-300">注册账号</Button> */}
                    </motion.div>
                </motion.div>
                <motion.div
                    className="mt-6 text-center text-gray-600 text-sm"
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    transition={{ delay: 0.7, duration: 0.5 }}
                >
                    © 2023 Luxury PRO Inc. 创新科技，引领未来.
                </motion.div>
            </motion.div>
        </div>
    )
}