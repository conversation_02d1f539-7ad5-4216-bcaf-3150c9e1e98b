-- TrackingInfo表关键索引
-- 只包含最重要的索引，提高查询性能

-- ============================================================================
-- 核心索引（必需）
-- ============================================================================

-- 运单号唯一索引
CREATE UNIQUE INDEX IF NOT EXISTS idx_tracking_waybill_no
ON tracking_info(waybill_no);

-- 运单ID索引
CREATE INDEX IF NOT EXISTS idx_tracking_waybill_id
ON tracking_info(waybill_id);

-- 渠道索引
CREATE INDEX IF NOT EXISTS idx_tracking_channel
ON tracking_info(channel);

-- 当前状态索引
CREATE INDEX IF NOT EXISTS idx_tracking_current_status
ON tracking_info(current_status);

-- 最后更新时间索引
CREATE INDEX IF NOT EXISTS idx_tracking_last_updated_at
ON tracking_info(last_updated_at);

-- ============================================================================
-- 复合索引（高频查询优化）
-- ============================================================================

-- 定时任务查询优化索引
-- 用于: findTrackingInfoForUpdate
CREATE INDEX IF NOT EXISTS idx_tracking_update_query
ON tracking_info(last_updated_at, last_event_time);

-- 统计查询优化索引
-- 用于: countByStatusWithDateRange, countByChannelWithDateRange
CREATE INDEX IF NOT EXISTS idx_tracking_stats_query
ON tracking_info(created_at, current_status, channel);

-- 分页查询优化索引
-- 用于: pageQuery方法的ORDER BY last_updated_at DESC
CREATE INDEX IF NOT EXISTS idx_tracking_page_query
ON tracking_info(last_updated_at DESC, id);

-- ============================================================================
-- 特殊索引
-- ============================================================================

-- 订单号数组GIN索引（PostgreSQL特有）
-- 用于: findByOrderNos查询中的 ANY(order_nos) 操作
CREATE INDEX IF NOT EXISTS idx_tracking_order_nos_gin
ON tracking_info USING GIN(order_nos);

-- ============================================================================
-- 维护命令
-- ============================================================================

-- 更新表统计信息
ANALYZE tracking_info;
