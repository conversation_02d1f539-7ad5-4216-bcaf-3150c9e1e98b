package io.github.clive.luxomssystem.timer

import io.github.clive.luxomssystem.infrastructure.config.properties.WebHookType
import io.github.clive.luxomssystem.infrastructure.remote.LarkRemoteNotifyService
import io.github.clive.luxomssystem.infrastructure.repository.jpa.SupplierMainOrderRepository
import io.github.clive.luxomssystem.infrastructure.repository.jpa.SupplierRepository
import io.github.oshai.kotlinlogging.KotlinLogging
import org.springframework.data.repository.findByIdOrNull
import org.springframework.scheduling.annotation.Scheduled
import org.springframework.stereotype.Component
import java.time.Instant

@Component
class SupplierMainOrderRemindScheduler(
    private val supplierRepository: SupplierRepository,
    private val larkRemoteNotifyService: LarkRemoteNotifyService,
    private val supplierMainOrderRepository: SupplierMainOrderRepository,
) {
    @Scheduled(cron = "0 0/30 * * * ?")
    fun scan() {
        log.info { "开始扫描未接受的供应商订单" }
        val unacceptedOrders =
            supplierMainOrderRepository.findByCreatedAtLessThanAndAccepted(
                Instant.now().minus(1, java.time.temporal.ChronoUnit.DAYS).toEpochMilli(),
                false,
            )

        log.info { "未接受的供应商订单数量: ${unacceptedOrders.size}" }

        unacceptedOrders.forEach { supplierMainOrder ->
            log.info { "未接受的供应商订单| 供应商 ${supplierMainOrder.supplierId} | 订单号: ${supplierMainOrder.id}" }
            supplierRepository.findByIdOrNull(supplierMainOrder.supplierId)?.let {
                it.config.larkRobotWebhook?.let { webhook ->
                    // send lark message
                    larkRemoteNotifyService.sendNotification(
                        WebHookType.SUPPLIER_ORDER_ON_HOLD,
                        LarkRemoteNotifyService.LarkMessage(
                            atAll = true,
                            title = "订单未接受已超过24小时",
                            content = "供应商: ${it.name} \n未接受订单: ${supplierMainOrder.fileName}\n请及时处理",
                            link = "http://122.51.127.198/dashboard/order/supplier-main-order?mainOrderId=${supplierMainOrder.mainOrderId}",
                        ),
                        overrideWebhookUrl = webhook,
                    )
                } ?: run {
                    log.error { "未找到供应商飞书群配置 | 供应商 ${supplierMainOrder.supplierId}" }
                }
            } ?: run {
                log.error { "未找到供应商| 供应商 ${supplierMainOrder.supplierId}" }
            }
        }
    }

    companion object {
        private val log = KotlinLogging.logger { }
    }
}
