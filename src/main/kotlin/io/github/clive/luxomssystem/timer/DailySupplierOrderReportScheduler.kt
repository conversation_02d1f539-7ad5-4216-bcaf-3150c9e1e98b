package io.github.clive.luxomssystem.timer

import io.github.clive.luxomssystem.domain.SupplierMainOrder
import io.github.clive.luxomssystem.infrastructure.remote.LarkRemoteNotifyService
import io.github.clive.luxomssystem.infrastructure.repository.jpa.SupplierMainOrderRepository
import io.github.clive.luxomssystem.infrastructure.repository.jpa.SupplierRepository
import io.github.oshai.kotlinlogging.KotlinLogging
import org.springframework.data.repository.findByIdOrNull
import org.springframework.scheduling.annotation.Scheduled
import org.springframework.stereotype.Component
import java.time.LocalDate
import java.time.ZoneId

/**
 * 每日供应商订单报告定时任务
 * 每天23:59分查询当天所有供应商主订单，并通过飞书发送给机器人
 */
@Component
class DailySupplierOrderReportScheduler(
    private val supplierMainOrderRepository: SupplierMainOrderRepository,
    private val supplierRepository: SupplierRepository,
    private val larkRemoteNotifyService: LarkRemoteNotifyService,
) {
    /**
     * 每天23:59:00执行
     * cron表达式: 秒 分 时 日 月 周
     */
    @Scheduled(cron = "0 50 23 * * ?")
    fun sendDailyOrderReport() {
        log.info { "开始执行每日供应商订单报告任务" }

        try {
            val today = LocalDate.now()
            val todayStartMillis = today.atStartOfDay(ZoneId.of("Asia/Shanghai")).toInstant().toEpochMilli()
            val todayEndMillis =
                today
                    .plusDays(1)
                    .atStartOfDay(ZoneId.of("Asia/Shanghai"))
                    .toInstant()
                    .toEpochMilli()

            log.info { "查询今日订单范围: $today ($todayStartMillis - $todayEndMillis)" }

            // 查询今天的所有供应商主订单
            val todayOrders =
                supplierMainOrderRepository.findOrdersByCreatedAtRange(
                    createdAtFrom = todayStartMillis,
                    createdAtTo = todayEndMillis,
                )

            log.info { "查询到今日供应商主订单数量: ${todayOrders.size}" }

            // 按供应商ID分组
            val ordersBySupplierId = todayOrders.groupBy { it.supplierId }

            if (ordersBySupplierId.isEmpty()) {
                log.info { "今日暂无供应商主订单，跳过发送" }
                return
            }

            var successCount = 0
            var failureCount = 0

            // 为每个供应商单独发送通知
            ordersBySupplierId.forEach { (supplierId, orders) ->
                try {
                    sendReportToSupplier(supplierId, orders, today)
                    successCount++
                } catch (e: Exception) {
                    log.error(e) { "向供应商 $supplierId 发送订单报告失败: ${e.message}" }
                    failureCount++
                }
            }

            log.info { "每日供应商订单报告发送完成 | 成功: $successCount | 失败: $failureCount" }
        } catch (e: Exception) {
            log.error(e) { "执行每日供应商订单报告任务失败: ${e.message}" }
        }
    }

    /**
     * 向指定供应商发送订单报告
     */
    private fun sendReportToSupplier(
        supplierId: Long,
        orders: List<SupplierMainOrder>,
        date: LocalDate,
    ) {
        val supplier = supplierRepository.findByIdOrNull(supplierId)
        if (supplier == null) {
            log.warn { "未找到供应商 | ID: $supplierId" }
            return
        }

        val webhookUrl = supplier.config.larkRobotWebhook
        if (webhookUrl.isNullOrBlank()) {
            log.warn { "供应商未配置飞书机器人webhook | 供应商: ${supplier.name} | ID: $supplierId" }
            return
        }

        // 构建消息内容
        val contentBuilder = StringBuilder()
        contentBuilder.append("今日订单：\n")

        orders.forEach { order ->
            contentBuilder.append("- ${order.fileName}\n")
        }

        contentBuilder.append("\n统计信息：\n")
        contentBuilder.append("订单总数：${orders.size}个")

        // 发送飞书消息到供应商配置的webhook
        larkRemoteNotifyService.sendNotification(
            webhookUrl,
            LarkRemoteNotifyService.LarkMessage(
                title = "每日订单报告 - $date - ${supplier.name}",
                content = contentBuilder.toString(),
                atAll = true, // 不需要@所有人
            ),
        )

        log.info { "已向供应商发送订单报告 | 供应商: ${supplier.name} | 订单数: ${orders.size}" }
    }

    companion object {
        private val log = KotlinLogging.logger { }
    }
}
