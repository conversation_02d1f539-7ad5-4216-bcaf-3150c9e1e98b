package io.github.clive.luxomssystem.timer

import io.github.clive.luxomssystem.common.enums.order.MainOrderStatus
import io.github.clive.luxomssystem.facade.dto.OrderProgress
import io.github.clive.luxomssystem.infrastructure.repository.jpa.MainOrderRepository
import io.github.clive.luxomssystem.infrastructure.repository.jpa.SubOrderRepository
import io.github.oshai.kotlinlogging.KotlinLogging
import org.springframework.scheduling.annotation.Scheduled
import org.springframework.stereotype.Component

@Component
class MainOrderMatchSupplierProgressScheduler(
    private val subOrderRepository: SubOrderRepository,
    private val mainOrderRepository: MainOrderRepository,
) {
    @Scheduled(cron = "0 */1 * * * *")
    fun execute() {
        log.info { "SupplierMainOrderMatchSupplierProgressScheduler execute" }
        mainOrderRepository
            .findByStatusIn(listOf(MainOrderStatus.MATCHING, MainOrderStatus.PARTIALLY_MATCHED))
            .forEach { it ->
                val oldStaus = it.status
                var newStatus = it.status
                val result = subOrderRepository.getStatsByParentId(it.id)

                val progress =
                    OrderProgress(
                        total = result["total"]?.let { total -> (total as Number).toLong() } ?: 0,
                        failed = result["failed"]?.let { failed -> (result["failed"] as Number).toLong() } ?: 0,
                        pending = result["pending"]?.let { pending -> (result["pending"] as Number).toLong() } ?: 0,
                        success = result["success"]?.let { success -> (result["success"] as Number).toLong() } ?: 0,
                    )

                log.info { "主订单号: ${it.id} | 进度: $progress" }
                if (progress.onPartialSuccess()) {
                    if (oldStaus != MainOrderStatus.PARTIALLY_MATCHED) {
                        log.info { "主订单号: ${it.id} | 部分匹配" }
                        newStatus = MainOrderStatus.PARTIALLY_MATCHED
                    }
                }

                if (progress.onAllMatch()) {
                    log.info { "主订单号: ${it.id} | 全部匹配" }
                    newStatus = MainOrderStatus.COMPLETED
                }
                if (oldStaus != newStatus) {
                    if (mainOrderRepository.updateStatusCAS(it.id, oldStaus, newStatus) != 1) {
                        log.info { "定时任务尝试更新主订单号: ${it.id} 但是失败,可能其他任务在处理状态变更" }
                    }
                }
            }
    }

    companion object {
        private val log = KotlinLogging.logger { }
    }
}
