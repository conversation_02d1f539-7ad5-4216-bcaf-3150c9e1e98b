package io.github.clive.luxomssystem.application.waybill

import io.github.clive.luxomssystem.common.enums.WaybillChannel
import io.github.clive.luxomssystem.domain.waybill.event.WaybillPendingEvent
import io.github.clive.luxomssystem.infrastructure.channel.prediction.channel.*
import io.github.clive.luxomssystem.infrastructure.repository.jpa.MainOrderRepository
import io.github.clive.luxomssystem.infrastructure.repository.jpa.SubOrderRepository
import io.github.clive.luxomssystem.infrastructure.repository.jpa.WaybillRepository
import io.github.oshai.kotlinlogging.KotlinLogging
import org.springframework.data.repository.findByIdOrNull
import org.springframework.scheduling.annotation.Async
import org.springframework.stereotype.Component
import org.springframework.transaction.annotation.Propagation
import org.springframework.transaction.annotation.Transactional
import org.springframework.transaction.event.TransactionalEventListener
import kotlin.jvm.optionals.getOrNull

@Component
class WaybillEventHandler(
    private val waybillRepository: WaybillRepository,
    private val mainOrderRepository: MainOrderRepository,
    private val subOrderRepository: SubOrderRepository,
    private val sdhPredictionRemoteService: SdhPredictionRemoteService,
    private val ywbzPredictionRemoteService: YWBZPredictionRemoteService,
    private val yunTuPredictionRemoteService: YunTuPredictionRemoteService,
    private val shunfengPredictionRemoteService: ShunfengPredictionRemoteService,
    private val caiNiaoPredictionRemoteService: CaiNiaoPredictionRemoteService,
    private val yanWenPredictionRemoteService: YanWenPredictionRemoteService,
    private val fpxPredictionRemoteService: FpxPredictionRemoteService,
    private val zmPredictionRemoteService: ZmPredictionRemoteService,
    private val changLianPredictionRemoteService: ChangLianPredictionRemoteService,
) {
    @Async("waybillExecutor")
    @TransactionalEventListener(WaybillPendingEvent::class)
    @Transactional(rollbackFor = [Exception::class], propagation = Propagation.REQUIRES_NEW)
    fun handle(event: WaybillPendingEvent) {
        try {
            val waybill =
                waybillRepository.findByIdOrNull(event.waybillId) ?: run {
                    log.error { "运单不存在 | WaybillId: ${event.waybillId}" }
                    return
                }
            log.info { "运单预报开始 | WaybillId: ${event.waybillId} | 订单号: ${waybill.orderNos} | 渠道: ${waybill.shipping.channel}" }
            when (waybill.shipping.channel) {
                WaybillChannel.YUNTU -> yunTuPredictionRemoteService.createPredication(waybill)
                WaybillChannel.YWBZ -> ywbzPredictionRemoteService.createPredication(waybill)
                WaybillChannel.SF -> shunfengPredictionRemoteService.createPredication(waybill)
                WaybillChannel.CAINIAO -> caiNiaoPredictionRemoteService.createPredication(waybill)
                WaybillChannel.FOURPX -> fpxPredictionRemoteService.createPredication(waybill)
                WaybillChannel.YW_GZ, WaybillChannel.YW_HZ, WaybillChannel.YW_QZ, WaybillChannel.YW_YW ->
                    yanWenPredictionRemoteService.createPredication(waybill)

                WaybillChannel.SDH -> sdhPredictionRemoteService.createPredication(waybill)
                WaybillChannel.ZM -> zmPredictionRemoteService.createPredication(waybill)
                WaybillChannel.CHANGLIAN -> changLianPredictionRemoteService.createPredication(waybill)
            }

            // TODO 这一块的逻辑其实是存在并发问题的

            val mainOrder =
                mainOrderRepository.findById(waybill.mainOrderId).getOrNull() ?: run {
                    log.error { "运单对应的主订单不存在| 主订单ID: ${waybill.mainOrderId}" }
                    return
                }

            val orderList = waybill.orderNos.split(",")

            val subOrders = subOrderRepository.findByOrderNoIn(orderList)
            subOrders.forEach {
                it.waybillStatus = waybill.status
            }

            subOrderRepository.saveAllAndFlush(subOrders)

            if (!mainOrder.supplierPushed) {
                // 还没推送子订单,不用管
                // 业务逻辑成功完成后，设置Redis标志防止重复处理
                log.info { "运单预报成功完成 | WaybillId: ${event.waybillId}" }
                return
            }

//            val supplierMainOrder =
//                supplierMainOrderRepository.findByMainOrderIdAndSupplierId(
//                    waybill.mainOrderId,
//                    waybill.product.supplierId!!,
//                )
//
//            if (supplierMainOrder == null) {
//                // 主订单推送了,但是没有供应商主订单,可能是之前推送的时候没有有效子订单,这里需要重新去推送主订单
//                eventBus.publishEvent(MainOrderEvent.OrderPushSupplyEvent(waybill.mainOrderId))
//            } else {
//                subOrders.forEach {
//                    // 已经存在供应商主订单了,这里推送子订单
//                    eventBus.publishEvent(SubOrderPushSupplierEvent(it.id, it.orderNo!!))
//                }
//            }

            // 业务逻辑成功完成后，设置Redis标志防止重复处理
            log.info { "运单预报成功完成 | WaybillId: ${event.waybillId}" }
        } catch (e: Exception) {
            log.error(e) { "运单预报失败 | WaybillId: ${event.waybillId}" }
            // 预报失败时不设置标志，允许重新预报
            // 注意：这里不需要清除标志，因为我们只在成功时才设置标志
        }
    }

    companion object {
        private val log = KotlinLogging.logger { }
    }
}
