package io.github.clive.luxomssystem.application.inventory.customerSpu

import io.github.clive.luxomssystem.common.PageResponse
import io.github.clive.luxomssystem.common.enums.BaseStatus
import io.github.clive.luxomssystem.common.enums.RoleLimit
import io.github.clive.luxomssystem.common.toPageable
import io.github.clive.luxomssystem.common.toResponse
import io.github.clive.luxomssystem.domain.customerSku.model.CustomerSku
import io.github.clive.luxomssystem.domain.customerSpu.domainservice.CustomerSpuDomainService
import io.github.clive.luxomssystem.domain.customerSpu.event.CustomerSpuEvent
import io.github.clive.luxomssystem.domain.customerSpu.model.CustomerSpu
import io.github.clive.luxomssystem.domain.spu.event.SPUDeletedEvent
import io.github.clive.luxomssystem.domain.spu.event.SPUUpdatedEvent // Added import
import io.github.clive.luxomssystem.facade.customerSku.request.CreateCustomerSkuRequest
import io.github.clive.luxomssystem.facade.customerSku.request.UpdateCustomerSkuRequest
import io.github.clive.luxomssystem.facade.customerSku.response.CustomerSkuResponse
import io.github.clive.luxomssystem.facade.customerSpu.CustomerSpuSearchReq
import io.github.clive.luxomssystem.facade.customerSpu.request.CreateCustomerSpuRequest
import io.github.clive.luxomssystem.facade.customerSpu.request.UpdateCustomerSpuRequest
import io.github.clive.luxomssystem.facade.customerSpu.response.CustomerSpuPageResponse
import io.github.clive.luxomssystem.facade.customerSpu.response.CustomerSpuResponse
import io.github.clive.luxomssystem.infrastructure.config.auth.UserContextHolder
import io.github.clive.luxomssystem.infrastructure.repository.jpa.CustomerRepository
import io.github.clive.luxomssystem.infrastructure.repository.jpa.spu.CustomerSkuRepository
import io.github.clive.luxomssystem.infrastructure.repository.jpa.spu.CustomerSpuRepository
import io.github.clive.luxomssystem.infrastructure.repository.jpa.spu.SPURepository // Added import
import io.github.clive.luxomssystem.infrastructure.repository.jpa.spu.SkuRepository // Added import
import io.github.oshai.kotlinlogging.KotlinLogging
import org.springframework.context.ApplicationEventPublisher
import org.springframework.context.event.EventListener
import org.springframework.data.domain.Sort
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional

@Service
class CustomerSpuApplicationService(
    private val customerSpuRepository: CustomerSpuRepository,
    private val customerSpuDomainService: CustomerSpuDomainService,
    private val eventPublisher: ApplicationEventPublisher,
    private val customerSkuRepository: CustomerSkuRepository,
    private val customerRepository: CustomerRepository,
    private val spuRepository: SPURepository, // Added dependency
    private val skuRepository: SkuRepository, // Added dependency
) {
    companion object { // Added companion object for logging
        private val log = KotlinLogging.logger {}
    }

    @Transactional
    fun createCustomerSpu(request: CreateCustomerSpuRequest): CustomerSpuPageResponse {
        val customerSpu =
            CustomerSpu(
                bizId = UserContextHolder.user!!.bizId,
                customerId = request.customerId,
                systemSpuId = request.systemSpuId,
                systemSpuCode = request.systemSpuCode,
                customerSpuCode = request.customerSpuCode,
                triggerDiscountQuantity = request.triggerDiscountQuantity,
                discount = request.discount,
                status = BaseStatus.ENABLED,
            ).apply {
                supplierCapacities = request.supplierCapacities
                createdByName = UserContextHolder.user!!.name
                updatedByName = UserContextHolder.user!!.name
            }

        customerSpuDomainService.performInitialSetup(customerSpu)
        val savedCustomerSpu = customerSpuRepository.save(customerSpu)

        eventPublisher.publishEvent(
            CustomerSpuEvent.Created(
                savedCustomerSpu.id,
                request.skus.map {
                    CreateCustomerSkuRequest(
                        customerId = request.customerId,
                        systemSpuId = request.systemSpuId,
                        systemSkuId = it.systemSkuId,
                        systemSpuCode = request.systemSpuCode!!,
                        systemSkuCode = it.systemSkuCode,
                        customerSpuCode = request.customerSpuCode,
                        customerSpuId = savedCustomerSpu.id,
                        customerSkuCode = it.systemSkuCode,
                        size = it.size,
                        color = it.color,
                        offlinePrice = it.offlinePrice,
                        offlinePriceCurrency = it.offlinePriceCurrency,
                        skuCountryPrices = it.skuCountryPrices,
                        skuPcsPrices = it.skuPcsPrices,
                        discount = it.discount,
                        triggerDiscountQuantity = it.triggerDiscountQuantity,
                    )
                },
            ),
        )

        return CustomerSpuPageResponse.fromDomain(savedCustomerSpu)
    }

    @Transactional
    fun updateCustomerSpu(
        id: Long,
        request: UpdateCustomerSpuRequest,
    ): CustomerSpuPageResponse {
        val customerSpu =
            customerSpuRepository.findById(id).orElseThrow {
                NoSuchElementException("CustomerSpu not found")
            }

        customerSpu.apply {
            // Update fields
            request.triggerDiscountQuantity?.let { triggerDiscountQuantity = it }
            request.discount?.let { discount = it }
            supplierCapacities = request.supplierCapacities
            updatedByName = UserContextHolder.user!!.name
        }

        customerSpuDomainService.validateUpdate(customerSpu)
        val updatedCustomerSpu = customerSpuRepository.save(customerSpu)

        eventPublisher.publishEvent(
            CustomerSpuEvent.Updated(
                updatedCustomerSpu.id,
                request.skus.map {
                    UpdateCustomerSkuRequest(
                        id = it.id,
                        offlinePrice = it.offlinePrice,
                        offlinePriceCurrency = it.offlinePriceCurrency,
                        skuCountryPrices = it.skuCountryPrices,
                        skuPcsPrices = it.skuPcsPrices,
                        discount = it.discount,
                        triggerDiscountQuantity = it.triggerDiscountQuantity,
                    )
                },
            ),
        )

        return CustomerSpuPageResponse.fromDomain(updatedCustomerSpu)
    }

    @Transactional(readOnly = true)
    fun getCustomerSpu(id: Long): CustomerSpuResponse {
        val customerSpu =
            customerSpuRepository.findById(id).orElseThrow {
                NoSuchElementException("CustomerSpu not found")
            }
        return CustomerSpuResponse.fromDomain(
            customerSpu,
            customerSkuRepository.findByCustomerSpuId(id, sort = Sort.by("id")).map {
                CustomerSkuResponse.fromDomain(it)
            },
        )
    }

    @Transactional
    fun deleteCustomerSpu(id: Long) {
        if (!customerSpuRepository.existsById(id)) {
            throw NoSuchElementException("CustomerSpu not found")
        }
        customerSpuRepository.deleteById(id)
        eventPublisher.publishEvent(CustomerSpuEvent.Deleted(id))
    }

    @Transactional
    @EventListener(SPUDeletedEvent::class)
    fun handleSPUDeletedEvent(event: SPUDeletedEvent) {
        customerSpuRepository
            .findBySystemSpuId(event.spuId)
            .map { deleteCustomerSpu(it.id) }
    }

    fun page(req: CustomerSpuSearchReq): PageResponse<CustomerSpuPageResponse> {
        if (UserContextHolder.user!!.roleLimit == RoleLimit.SELF) {
            val customerIds = customerRepository.findByCreatedBy(UserContextHolder.user!!.id).map { it.id }
            return if (customerIds.isEmpty()) {
                PageResponse.empty()
            } else {
                customerSpuRepository
                    .pageQueryByCustomerIdForSelf(
                        UserContextHolder.user!!.bizId,
                        req.systemSpuCode?.split("\n")?.toList() ?: emptyList(),
                        req.customerId,
                        req.toPageable(),
                        customerIds,
                    ).map { CustomerSpuPageResponse.fromDomain(it) }
                    .toResponse()
            }
        } else {
            return customerSpuRepository
                .pageQueryByCustomerId(
                    UserContextHolder.user!!.bizId,
                    req.systemSpuCode?.split("\n")?.toList() ?: emptyList(),
                    req.customerId,
                    req.toPageable(),
                ).map { CustomerSpuPageResponse.fromDomain(it) }
                .toResponse()
        }
    }

    @Transactional
    fun changeStatus(
        id: Long,
        wantedStatus: String,
    ) {
        val customerSpu =
            customerSpuRepository.findById(id).orElseThrow {
                NoSuchElementException("CustomerSpu not found")
            }
        customerSpu.status = BaseStatus.valueOf(wantedStatus)
        customerSpuRepository.saveAndFlush(customerSpu)
    }

    @Transactional
    @EventListener(SPUUpdatedEvent::class)
    fun handleSystemSPUUpdatedEvent(event: SPUUpdatedEvent) {
        log.info { "Starting SPUUpdatedEvent handling for systemSpuId: ${event.spuId}" }

        val systemSpu = spuRepository.findById(event.spuId).orElse(null)
        if (systemSpu == null) {
            log.error { "System SPU with id ${event.spuId} not found for customer SPU sync." }
            return
        }

        val customerSpus = customerSpuRepository.findAllBySystemSpuId(event.spuId)
        if (customerSpus.isEmpty()) {
            log.warn { "No CustomerSpus found for systemSpuId ${event.spuId}. Skipping update." }
            return
        }

        val systemSkus = skuRepository.findBySpuId(event.spuId) // Fetched once

        for (customerSpu in customerSpus) {
            log.info { "Processing SPUUpdatedEvent for customerSpuId ${customerSpu.id}, systemSpuId ${event.spuId}" }

            // Update CustomerSpu Fields
            customerSpu.systemSpuCode = systemSpu.spuCode
            customerSpu.updatedByName = UserContextHolder.user?.name ?: "System"
            customerSpu.updatedAt = System.currentTimeMillis()

            // Fetch SKUs
            val existingCustomerSkus = customerSkuRepository.findByCustomerSpuId(customerSpu.id).toMutableList()

            // Synchronize CustomerSkus
            val skusToSave = mutableListOf<CustomerSku>()
            val skusToDelete = mutableListOf<CustomerSku>()
            val existingCustomerSkusMap = existingCustomerSkus.associateBy { it.systemSkuId }.toMutableMap()

            for (systemSku in systemSkus) {
                val customerSkuToProcess = existingCustomerSkusMap.remove(systemSku.id)

                if (customerSkuToProcess != null) { // Update
                    customerSkuToProcess.size = systemSku.size
                    customerSkuToProcess.color = systemSku.color
                    customerSkuToProcess.updatedByName = UserContextHolder.user?.name ?: "System"
                    customerSkuToProcess.updatedAt = System.currentTimeMillis()
                    customerSkuToProcess.status = customerSpu.status // Align with parent
                    skusToSave.add(customerSkuToProcess)
                } else { // Create New
                    val newCustomerSku =
                        CustomerSku(
                            // id = 0L by default from data class
                            bizId = customerSpu.bizId,
                            customerId = customerSpu.customerId,
                            systemSpuId = systemSpu.id,
                            systemSkuId = systemSku.id,
                            systemSpuCode = systemSpu.spuCode,
                            systemSkuCode = systemSku.skuCode,
                            customerSpuCode = customerSpu.customerSpuCode,
                            customerSpuId = customerSpu.id,
                            customerSkuCode = systemSku.skuCode, // Default as per requirement
                            size = systemSku.size,
                            color = systemSku.color,
                            offlinePrice = systemSku.salePrice, // Default as per requirement
                            offlinePriceCurrency = systemSku.purchaseCostCurrency, // Default as per requirement
                            status = customerSpu.status,
                            skuCountryPrices = emptyMap(),
                            skuPcsPrices = emptyList(),
                        ).apply {
                            createdByName = UserContextHolder.user?.name ?: "System"
                            updatedByName = UserContextHolder.user?.name ?: "System"
                        }
                    skusToSave.add(newCustomerSku)
                }
            }

            skusToDelete.addAll(existingCustomerSkusMap.values)

            // Save Changes for this CustomerSpu
            customerSpuRepository.saveAndFlush(customerSpu)
            customerSkuRepository.saveAllAndFlush(skusToSave)
            if (skusToDelete.isNotEmpty()) {
                customerSkuRepository.deleteAll(skusToDelete)
            }
            log.info { "CustomerSpu ${customerSpu.id} synced. SKUs saved: ${skusToSave.size}, SKUs deleted: ${skusToDelete.size}." }
        }
        log.info { "Finished processing SPUUpdatedEvent for systemSpuId ${event.spuId} for all relevant customers." }
    }
}
