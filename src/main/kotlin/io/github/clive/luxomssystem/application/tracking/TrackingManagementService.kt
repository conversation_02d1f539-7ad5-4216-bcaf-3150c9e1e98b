package io.github.clive.luxomssystem.application.tracking

import io.github.clive.luxomssystem.common.enums.WaybillChannel
import io.github.clive.luxomssystem.common.toPageable
import io.github.clive.luxomssystem.domain.tracking.model.*
import io.github.clive.luxomssystem.domain.tracking.service.SubOrderTrackingStatusUpdateService
import io.github.clive.luxomssystem.facade.tracking.TrackingInfoResponse
import io.github.clive.luxomssystem.facade.tracking.request.TrackingPageRequest
import io.github.clive.luxomssystem.infrastructure.repository.jpa.WaybillRepository
import io.github.clive.luxomssystem.infrastructure.tracking.remote.TrackingRemoteService
import io.github.clive.luxomssystem.infrastructure.tracking.repository.TrackingInfoRepository
import io.github.oshai.kotlinlogging.KotlinLogging
import org.springframework.data.domain.Page
import org.springframework.data.repository.findByIdOrNull
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import java.time.LocalDate
import java.time.ZoneId
import java.time.ZonedDateTime

/**
 * 轨迹管理服务
 * 提供手动触发轨迹更新、查询轨迹统计等功能
 */
@Service
@Transactional
class TrackingManagementService(
    private val trackingInfoRepository: TrackingInfoRepository,
    private val trackingRemoteService: TrackingRemoteService,
    private val trackingApplicationService: TrackingApplicationService,
    private val waybillRepository: WaybillRepository,
    private val subOrderTrackingStatusUpdateService: SubOrderTrackingStatusUpdateService
) {

    private val log = KotlinLogging.logger {}

    /**
     * 分页查询轨迹信息
     */
    fun pageTracking(request: TrackingPageRequest): Page<TrackingInfoResponse> {
        log.info { "分页查询轨迹信息 | 请求参数: $request" }

        // 处理批量查询参数
        val orderNosList = if (request.orderNos != null) {
            request.orderNos!!.lines().filter { it.isNotEmpty() }
        } else {
            emptyList()
        }

        val waybillNosList = if (request.waybillNos != null) {
            request.waybillNos!!.lines().filter { it.isNotEmpty() }
        } else {
            emptyList()
        }

        val trackingNumbersList = if (request.trackingNumbers != null) {
            request.trackingNumbers!!.lines().filter { it.isNotEmpty() }
        } else {
            emptyList()
        }

        return trackingInfoRepository.pageQuery(
            waybillNo = request.waybillNo,
            trackingNumber = request.trackingNumber,
            orderNo = request.orderNo,
            channel = request.channel,
            destinationCountry = request.destinationCountry,
            destinationCountryCode = request.destinationCountryCode,
            waybillId = request.waybillId,
            createdAtFrom = request.createdAtFromEpochMilli(),
            createdAtTo = request.createdAtToEpochMilli(),
            currentStatus = request.status.map { it.name },
            pageable = request.toPageable(),
            orderNos = orderNosList,
            waybillNos = waybillNosList,
            trackingNumbers = trackingNumbersList
        ).map { TrackingInfoResponse.fromDomain(it) }
    }

    /**
     * 手动更新指定运单的轨迹信息
     */
    fun updateTrackingByWaybillNo(waybillNo: String): TrackingUpdateResult {
        log.info { "手动更新运单轨迹 | 运单号: $waybillNo" }

        try {
            // 查找轨迹信息
            val trackingInfo = trackingInfoRepository.findByWaybillNo(waybillNo)
            if (trackingInfo == null) {
                return TrackingUpdateResult.failure("轨迹信息不存在: $waybillNo")
            }

            return updateTrackingInfo(trackingInfo)

        } catch (e: Exception) {
            log.error(e) { "手动更新运单轨迹异常 | 运单号: $waybillNo" }
            return TrackingUpdateResult.failure("更新异常: ${e.message}")
        }
    }

    /**
     * 手动更新指定渠道的所有轨迹信息
     */
    fun updateTrackingByChannel(channel: WaybillChannel): BatchTrackingUpdateResult {
        log.info { "手动更新渠道轨迹 | 渠道: ${channel.displayName}" }

        try {
            val converterChannel = trackingRemoteService.getConverterChannelName(channel)
            if (converterChannel == null) {
                return BatchTrackingUpdateResult(0, 1, 0, listOf("不支持的渠道: ${channel.displayName}"))
            }

            val trackingInfoList = trackingInfoRepository.findByChannel(converterChannel)
            log.info { "找到${channel.displayName}渠道的未完成轨迹数量: ${trackingInfoList.size}" }

            return batchUpdateTrackingInfo(trackingInfoList)

        } catch (e: Exception) {
            log.error(e) { "手动更新渠道轨迹异常 | 渠道: ${channel.displayName}" }
            return BatchTrackingUpdateResult(0, 1, 0, listOf("更新异常: ${e.message}"))
        }
    }

    /**
     * 强制更新指定运单的轨迹（忽略15天限制）
     */
    fun forceUpdateTracking(waybillNo: String): TrackingUpdateResult {
        log.info { "强制更新运单轨迹 | 运单号: $waybillNo" }

        try {
            val trackingInfo = trackingInfoRepository.findByWaybillNo(waybillNo)
            if (trackingInfo == null) {
                return TrackingUpdateResult.failure("轨迹信息不存在: $waybillNo")
            }

            return updateTrackingInfo(trackingInfo, force = true)

        } catch (e: Exception) {
            log.error(e) { "强制更新运单轨迹异常 | 运单号: $waybillNo" }
            return TrackingUpdateResult.failure("更新异常: ${e.message}")
        }
    }

    /**
     * 根据运单ID刷新轨迹信息
     * 如果轨迹信息不存在则创建新的，如果存在则更新
     */
    fun refreshTrackingByWaybillId(waybillId: Long): TrackingUpdateResult {
        log.info { "根据运单ID刷新轨迹信息 | 运单ID: $waybillId" }

        try {
            // 查找运单信息
            val waybill = waybillRepository.findByIdOrNull(waybillId)
            if (waybill == null) {
                return TrackingUpdateResult.failure("运单不存在: $waybillId")
            }

            // 检查运单号是否存在
            if (waybill.waybillNo.isNullOrBlank()) {
                return TrackingUpdateResult.failure("运单号为空，无法创建轨迹信息")
            }

            // 查找现有轨迹信息
            var trackingInfo = trackingInfoRepository.findByWaybillId(waybillId)

            if (trackingInfo == null) {
                // 创建新的轨迹信息
                trackingInfo = createTrackingInfoFromWaybill(waybill)
                if (trackingInfo == null) {
                    return TrackingUpdateResult.failure("不支持的渠道或创建轨迹信息失败")
                }
                trackingInfoRepository.save(trackingInfo)
                log.info { "创建新的轨迹信息 | 运单ID: $waybillId | 运单号: ${waybill.waybillNo}" }
            }

            // 更新轨迹信息
            return updateTrackingInfo(trackingInfo, force = true)

        } catch (e: Exception) {
            log.error(e) { "根据运单ID刷新轨迹信息异常 | 运单ID: $waybillId" }
            return TrackingUpdateResult.failure("刷新异常: ${e.message}")
        }
    }

    /**
     * 批量根据运单ID刷新轨迹信息
     */
    fun batchRefreshTrackingByWaybillIds(waybillIds: List<Long>): BatchTrackingUpdateResult {
        log.info { "批量根据运单ID刷新轨迹信息 | 运单ID数量: ${waybillIds.size}" }

        var successCount = 0
        var failureCount = 0
        var skippedCount = 0
        val errors = mutableListOf<String>()

        waybillIds.forEach { waybillId ->
            try {
                val result = refreshTrackingByWaybillId(waybillId)
                when {
                    result.success -> successCount++
                    result.skipped -> skippedCount++
                    else -> {
                        failureCount++
                        errors.add("运单ID $waybillId: ${result.message}")
                    }
                }
            } catch (e: Exception) {
                failureCount++
                errors.add("运单ID $waybillId: ${e.message}")
            }
        }

        return BatchTrackingUpdateResult(successCount, failureCount, skippedCount, errors)
    }

    /**
     * 批量更新轨迹信息
     */
    fun batchUpdateTrackingInfo(trackingInfoList: List<TrackingInfo>): BatchTrackingUpdateResult {
        var successCount = 0
        var failureCount = 0
        var skippedCount = 0
        val errors = mutableListOf<String>()

        trackingInfoList.forEach { trackingInfo ->
            try {
                val result = updateTrackingInfo(trackingInfo)
                when {
                    result.success -> successCount++
                    result.skipped -> skippedCount++
                    else -> {
                        failureCount++
                        errors.add("${trackingInfo.waybillNo}: ${result.message}")
                    }
                }
            } catch (e: Exception) {
                failureCount++
                errors.add("${trackingInfo.waybillNo}: ${e.message}")
            }
        }

        return BatchTrackingUpdateResult(successCount, failureCount, skippedCount, errors)
    }

    /**
     * 更新单个轨迹信息
     */
    private fun updateTrackingInfo(trackingInfo: TrackingInfo, force: Boolean = false): TrackingUpdateResult {
        val channel = getWaybillChannelByConverterName(trackingInfo.channel)

        if (channel == null) {
            return TrackingUpdateResult.failure("无法识别轨迹渠道: ${trackingInfo.channel}")
        }
        
        // 检查是否需要更新（除非强制更新）
        if (!force) {
            // 使用最后事件时间判断是否超过15天
            val lastEventTime = trackingInfo.lastEventTime
            if (lastEventTime != null && lastEventTime.isBefore(ZonedDateTime.now().minusDays(15))) {
                log.info { "轨迹最后事件超过15天，跳过更新 | 运单号: ${trackingInfo.waybillNo} | 最后事件时间: $lastEventTime" }
                return TrackingUpdateResult.skipped("轨迹最后事件超过15天")
            }
        }
        
        // 获取并转换轨迹信息
        val newTrackingData = fetchAndConvertTrackingInfo(trackingInfo)
        if (newTrackingData == null) {
            return TrackingUpdateResult.failure("获取或转换轨迹信息失败")
        }

        // 使用简化的参数更新轨迹信息
        updateSingleTrackingInfo(
            trackingInfo = trackingInfo,
            status = newTrackingData.currentStatus,
            events = newTrackingData.trackingEvents,
            deliveryDays = newTrackingData.deliveryDays,
            trackingNumber = newTrackingData.trackingNumber,
            destinationCountry = newTrackingData.destinationCountry,
            originCountry = newTrackingData.originCountry,
            lastMileProvider = newTrackingData.lastMileProvider,
            podLinks = newTrackingData.podLinks,
            rawData = newTrackingData.rawData

        )
        trackingInfoRepository.save(trackingInfo)

        return TrackingUpdateResult.success("轨迹更新成功", trackingInfo)
    }

    /**
     * 根据转换器名称获取运单渠道
     */
    private fun getWaybillChannelByConverterName(converterName: String): WaybillChannel? {
        return when (converterName) {
            "YANWEN" -> WaybillChannel.YW_HZ // 默认使用杭州燕文
            "YUNTU" -> WaybillChannel.YUNTU
            else -> null
        }
    }

    /**
     * 获取轨迹统计信息
     */
    fun getTrackingStatistics(startDate: String? = null, endDate: String? = null): TrackingStatistics {
        val startTimestamp = startDate?.let {
            LocalDate.parse(it).atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli()
        }
        val endTimestamp = endDate?.let {
            LocalDate.parse(it).atTime(23, 59, 59).atZone(ZoneId.systemDefault()).toInstant().toEpochMilli()
        }

        val statusCounts = if (startTimestamp != null && endTimestamp != null) {
            trackingInfoRepository.countByStatusWithDateRange(startTimestamp, endTimestamp)
                .associate { (it[0] as TrackingStatus) to (it[1] as Long) }
        } else {
            trackingInfoRepository.countByStatus()
                .associate { (it[0] as TrackingStatus) to (it[1] as Long) }
        }

        val channelCounts = if (startTimestamp != null && endTimestamp != null) {
            trackingInfoRepository.countByChannelWithDateRange(startTimestamp, endTimestamp)
                .associate { (it[0] as String) to (it[1] as Long) }
        } else {
            trackingInfoRepository.countByChannel()
                .associate { (it[0] as String) to (it[1] as Long) }
        }

        val totalTracking = if (startTimestamp != null && endTimestamp != null) {
            trackingInfoRepository.countByDateRange(startTimestamp, endTimestamp)
        } else {
            trackingInfoRepository.count()
        }

        val completedTracking = if (startTimestamp != null && endTimestamp != null) {
            trackingInfoRepository.countByStatusAndDateRange(TrackingStatus.DELIVERED, startTimestamp, endTimestamp)
        } else {
            trackingInfoRepository.findByIsCompleted(TrackingStatus.DELIVERED).size.toLong()
        }

        val pendingTracking = totalTracking - completedTracking

        return TrackingStatistics(
            totalTracking = totalTracking,
            completedTracking = completedTracking,
            pendingTracking = pendingTracking,
            statusCounts = statusCounts,
            channelCounts = channelCounts
        )
    }

    /**
     * 清理过期的轨迹信息（超过30天未更新的已完成轨迹）
     */
    fun cleanupStaleTracking(): CleanupResult {
        val cutoffTime = ZonedDateTime.now().minusDays(30)
        val staleTrackingList = trackingInfoRepository.findTrackingInfoOlderThan(cutoffTime)

        val deletedCount = staleTrackingList.size
        trackingInfoRepository.deleteAll(staleTrackingList)
        log.info { "清理过期轨迹信息完成 | 删除数量: $deletedCount" }
        return CleanupResult(deletedCount)
    }

    /**
     * 更新单个轨迹信息（简化参数版本）
     */
    private fun updateSingleTrackingInfo(
        trackingInfo: TrackingInfo,
        status: TrackingStatus,
        events: List<TrackingEvent>,
        deliveryDays: Int?,
        trackingNumber: String? = null,
        destinationCountry: String? = null,
        originCountry: String? = null,
        lastMileProvider: LastMileProvider? = null,
        podLinks: List<String>? = null,
        rawData: Map<String, Any>? = null
    ) {
        trackingInfo.updateTracking(
            status = status,
            events = events,
            deliveryDays = deliveryDays,
            trackingNumber = trackingNumber,
            destinationCountry = destinationCountry,
            originCountry = originCountry,
            lastMileProvider = lastMileProvider,
            podLinks = podLinks,
            rawData = rawData,
            subOrderTrackingStatusUpdateService = subOrderTrackingStatusUpdateService
        )
    }

    /**
     * 获取并转换轨迹信息
     */
    private fun fetchAndConvertTrackingInfo(trackingInfo: TrackingInfo): TrackingData? {
        val waybillNo = trackingInfo.waybillNo
        val channel = getWaybillChannelByConverterName(trackingInfo.channel)

        if (channel == null) {
            log.warn { "无法识别轨迹渠道 | 运单号: $waybillNo | 渠道: ${trackingInfo.channel}" }
            return null
        }

        // 调用远程API获取轨迹信息
        val remoteResponse = trackingRemoteService.getTrackingByChannel(waybillNo, channel)
        if (remoteResponse == null) {
            log.warn { "获取轨迹信息失败 | 运单号: $waybillNo | 渠道: ${trackingInfo.channel}" }
            return null
        }

        // 转换为统一格式
        val newTrackingData = when (trackingInfo.channel) {
            "YANWEN" -> trackingApplicationService.processYanWenTracking(
                remoteResponse as io.github.clive.luxomssystem.infrastructure.channel.prediction.channel.yanwen.YanWenTrackingResponse,
                waybillNo
            )
            "YUNTU" -> trackingApplicationService.processYuntuTracking(
                remoteResponse as io.github.clive.luxomssystem.infrastructure.channel.prediction.channel.yuntu.YuntuTrackingResponse,
                waybillNo
            )
            else -> null
        }

        if (newTrackingData == null) {
            log.warn { "轨迹信息转换失败 | 运单号: $waybillNo | 渠道: ${trackingInfo.channel}" }
            return null
        }

        return newTrackingData
    }

    /**
     * 根据运单信息创建轨迹信息
     */
    private fun createTrackingInfoFromWaybill(waybill: io.github.clive.luxomssystem.domain.waybill.model.Waybill): TrackingInfo? {
        val waybillNo = waybill.waybillNo ?: return null
        val channel = waybill.shipping.channel

        // 获取转换器渠道名称
        val converterChannel = trackingRemoteService.getConverterChannelName(channel)
        if (converterChannel == null) {
            log.debug { "渠道不支持轨迹查询，跳过创建轨迹记录 | 运单号: $waybillNo | 渠道: ${channel.displayName}" }
            return null
        }

        log.info { "根据运单创建轨迹信息 | 运单ID: ${waybill.id} | 运单号: $waybillNo | 渠道: ${channel.displayName}" }

        return TrackingInfo(
            orderNos = waybill.orderNos.split(",").filter { it.isNotBlank() },
            waybillId = waybill.id,
            mainOrderId = waybill.mainOrderId,
            waybillNo = waybillNo,
            channel = converterChannel,
            currentStatus = TrackingStatus.NOT_FOUND,
            trackingEvents = emptyList(),
            lastEventTime = null
        )
    }
}

/**
 * 轨迹更新结果
 */
data class TrackingUpdateResult(
    val success: Boolean,
    val skipped: Boolean,
    val message: String,
    val trackingInfo: TrackingInfo? = null
) {
    companion object {
        fun success(message: String, trackingInfo: TrackingInfo? = null) = 
            TrackingUpdateResult(true, false, message, trackingInfo)
        
        fun failure(message: String) = 
            TrackingUpdateResult(false, false, message)
        
        fun skipped(message: String) = 
            TrackingUpdateResult(false, true, message)
    }
}

/**
 * 批量轨迹更新结果
 */
data class BatchTrackingUpdateResult(
    val successCount: Int,
    val failureCount: Int,
    val skippedCount: Int,
    val errors: List<String>
)

/**
 * 轨迹统计信息
 */
data class TrackingStatistics(
    val totalTracking: Long,
    val completedTracking: Long,
    val pendingTracking: Long,
    val statusCounts: Map<TrackingStatus, Long>,
    val channelCounts: Map<String, Long>
)

/**
 * 清理结果
 */
data class CleanupResult(
    val deletedCount: Int
)
