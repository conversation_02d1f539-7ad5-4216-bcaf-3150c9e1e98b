package io.github.clive.luxomssystem.application

import io.github.clive.luxomssystem.common.PageReq
import io.github.clive.luxomssystem.domain.supplierOrder.model.SupplierOrderStatus
import io.github.clive.luxomssystem.domain.waybill.model.WayBillStatus
import io.github.clive.luxomssystem.infrastructure.config.auth.UserContextHolder
import io.github.clive.luxomssystem.infrastructure.repository.jpa.SupplierOrderRepository
import io.github.clive.luxomssystem.infrastructure.repository.jpa.WaybillRepository
import org.springframework.stereotype.Service

@Service
class StatisticApplicationService(
    private val waybillRepository: WaybillRepository,
    private val supplierOrderRepository: SupplierOrderRepository,
) {
    fun queryFailedWaybillCount(pageRequest: PageReq): Map<String, Int> =
        buildMap {
            put(
                "waybillAll",
                waybillRepository.countByStatusAndCreatedAtBetween(
                    null,
                    pageRequest.createdAtFromEpochMilli(),
                    pageRequest.createdAtToEpochMilli(),
                    UserContextHolder.user!!.bizId,
                ),
            )
            put(
                "waybillFailed",
                waybillRepository.countByStatusAndCreatedAtBetween(
                    WayBillStatus.FAILED,
                    pageRequest.createdAtFromEpochMilli(),
                    pageRequest.createdAtToEpochMilli(),
                    UserContextHolder.user!!.bizId,
                ),
            )

            put(
                "supplierOrderAll",
                supplierOrderRepository.countByStatusAndCreatedAtBetween(
                    null,
                    pageRequest.createdAtFromEpochMilli(),
                    pageRequest.createdAtToEpochMilli(),
                    UserContextHolder.user!!.bizId,
                ),
            )

            put(
                "supplierOrderFailed",
                supplierOrderRepository.countByStatusAndCreatedAtBetween(
                    SupplierOrderStatus.FAILED,
                    pageRequest.createdAtFromEpochMilli(),
                    pageRequest.createdAtToEpochMilli(),
                    UserContextHolder.user!!.bizId,
                ),
            )
        }
}
