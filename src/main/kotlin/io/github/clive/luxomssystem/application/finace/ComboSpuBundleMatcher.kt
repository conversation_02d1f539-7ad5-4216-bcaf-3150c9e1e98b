package io.github.clive.luxomssystem.application.finace

import io.github.clive.luxomssystem.domain.SubOrder
import io.github.clive.luxomssystem.domain.customerSpu.model.ComboCustomerSpu
import io.github.oshai.kotlinlogging.KotlinLogging
import kotlin.time.measureTimedValue

class ComboSpuBundleMatcher(
    comboCustomerSpus: List<ComboCustomerSpu>,
) {
    data class MatchResult(
        val matchedComboResult: List<MatchedComboResult>,
        val remainingOrders: List<SubOrder>,
        val isOptimal: Boolean,
        val algorithm: String,
        val executionTimeMs: Long,
        val totalSpusMatched: Int,
    )

    data class MatchedComboResult(
        val matchedComboSpu: ComboCustomerSpu,
        val matchedOrders: List<SubOrder>,
    )

    private data class DPState(
        val spuCounts: Map<String, Int>,
        val matchedComboSpus: List<Long>,
    ) {
        fun toKey(): String =
            spuCounts.entries
                .sortedBy { it.key }
                .joinToString("|") { "${it.key}:${it.value}" }

        fun isEmpty(): Boolean = spuCounts.isEmpty()

        fun totalSpus(): Int = spuCounts.values.sum()
    }

    private val comboSpuMap = comboCustomerSpus.associateBy { it.id }
    private val spu2ComboSpu: Map<String, Set<ComboCustomerSpu>>

    init {
        val spu2ComboCustomerSpuInit: MutableMap<String, MutableSet<ComboCustomerSpu>> = mutableMapOf()
        comboCustomerSpus.forEach { ccs ->
            ccs.systemSpuCodes.forEach { spuCode ->
                spu2ComboCustomerSpuInit.computeIfAbsent(spuCode) { mutableSetOf() }.add(ccs)
            }
        }
        spu2ComboSpu = spu2ComboCustomerSpuInit
    }

    private fun dynamicProgrammingMatch(orders: List<SubOrder>): MatchResult {
        val cost =
            measureTimedValue {
                val spuCounts = orders.groupBy { it.product.spu!! }.mapValues { it.value.size }

                // 预筛选可能的套装
                val candidateComboSpu = getRelevantComboSpus(orders)

                // DP状态管理: 状态Key -> (套装数量, 套装列表)
                val dp = mutableMapOf<String, Pair<Int, List<Long>>>()
                val initialState = DPState(spuCounts, emptyList())
                dp[initialState.toKey()] = Pair(0, emptyList())

                // 使用队列管理待处理状态
                val stateQueue = mutableListOf(initialState)
                var processed = 0
                val maxIterations = 10000000 // 防止无限循环

                while (stateQueue.isNotEmpty() && processed < maxIterations) {
                    val currentState = stateQueue.removeAt(0)
                    val currentKey = currentState.toKey()
                    val (currentComboSpuCount, currentSolution) = dp[currentKey] ?: continue

                    processed++

                    // 尝试应用每个候选套装
                    for (comboId in candidateComboSpu) {
                        val comboSpu = comboSpuMap[comboId] ?: continue

                        // 检查是否可以应用这个套装
                        if (canApplyComboSpu(comboSpu, currentState.spuCounts)) {
                            val newSpuCounts = applyComboSpu(comboSpu, currentState.spuCounts)
                            val newSolution = currentSolution + comboId
                            val newComboSpuCount = currentComboSpuCount + 1
                            val newState = DPState(newSpuCounts, newSolution)
                            val newKey = newState.toKey()

                            // 如果找到更好的解，更新DP表
                            val existingBest = dp[newKey]
                            if (existingBest == null || existingBest.first < newComboSpuCount) {
                                dp[newKey] = Pair(newComboSpuCount, newSolution)

                                // 只有非空状态才继续展开
                                if (!newState.isEmpty()) {
                                    stateQueue.add(newState)
                                }
                            }
                        }
                    }

                    // 优化：按剩余商品数排序，优先处理接近结束的状态
                    stateQueue.sortBy { it.totalSpus() }
                }

                // 找到最优解
                val bestEntry = dp.maxByOrNull { it.value.first }
                val (_, bestSolution) = bestEntry?.value ?: Pair(0, emptyList())

                buildMatchResult(
                    solution = bestSolution,
                    orders = orders,
                    isOptimal = processed < maxIterations,
                    algorithm = "DP",
                )
            }
        val result = cost.value
        val costTime = cost.duration
        return result.copy(executionTimeMs = costTime.inWholeMilliseconds)
    }

    /**
     * 从订单池中提取匹配套装的具体订单
     */
    private fun extractMatchedOrders(
        bundle: ComboCustomerSpu,
        ordersPool: MutableList<SubOrder>,
    ): List<SubOrder> {
        val requiredCounts = bundle.systemSpuCodes.groupBy { it }.mapValues { it.value.size }
        val matchedOrders = mutableListOf<SubOrder>()

        requiredCounts.forEach { (spuCode, requiredCount) ->
            var extracted = 0
            val iterator = ordersPool.iterator()

            while (iterator.hasNext() && extracted < requiredCount) {
                val order = iterator.next()
                if (order.product.spu == spuCode) {
                    matchedOrders.add(order)
                    iterator.remove()
                    extracted++
                }
            }
        }

        return matchedOrders
    }

    /**
     * 构造匹配结果
     */
    private fun buildMatchResult(
        solution: List<Long>,
        orders: List<SubOrder>,
        isOptimal: Boolean,
        algorithm: String,
    ): MatchResult {
        val ordersPool = orders.toMutableList()
        val matchedComboResultSpuses = mutableListOf<MatchedComboResult>()

        // 按解决方案顺序构造匹配的套装
        solution.forEach { bundleId ->
            val comboSpu = comboSpuMap[bundleId]!!
            val matchedOrdersForBundle = extractMatchedOrders(comboSpu, ordersPool)

            matchedComboResultSpuses.add(
                MatchedComboResult(
                    matchedComboSpu = comboSpu,
                    matchedOrders = matchedOrdersForBundle,
                ),
            )
        }

        val totalMatched = orders.size - ordersPool.size

        return MatchResult(
            matchedComboResult = matchedComboResultSpuses,
            remainingOrders = ordersPool,
            isOptimal = isOptimal,
            algorithm = algorithm,
            executionTimeMs = -1,
            totalSpusMatched = totalMatched,
        )
    }

    /**
     * 暴力遍历匹配算法
     * 简单贪心策略，不保证最优解
     */
    private fun bruteForceMatch(orders: List<SubOrder>): MatchResult {
        val cost =
            measureTimedValue {
                val spuCounts = orders.groupBy { it.product.spu!! }.mapValues { it.value.size }.toMutableMap()
                val matchedComboResultSpuses = mutableListOf<MatchedComboResult>()
                val ordersPool = orders.toMutableList() // 可用订单池

                // 获取相关套装并按优先级排序
                val candidateComboSpus = getRelevantComboSpus(orders)
                val sortedComboSpus =
                    candidateComboSpus.sortedWith(
                        compareByDescending { comboSpuId ->
                            val comboSpu = comboSpuMap[comboSpuId]!!
                            // 优先级：套装大小优先，然后按ID排序保证稳定性
                            comboSpu.systemSpuCodes.size * 1000 + (10000 - comboSpuId % 10000)
                        },
                    )

                // 贪心遍历：能匹配就匹配
                for (comboSpuId in sortedComboSpus) {
                    val comboSpu = comboSpuMap[comboSpuId] ?: continue

                    // 检查当前剩余商品是否足够组成这个套装
                    while (canApplyComboSpuToMutableMap(comboSpu, spuCounts)) {
                        // 应用套装：从剩余商品中扣除
                        applyComboSpuToMutableMap(comboSpu, spuCounts)

                        // 找到匹配的具体订单并从池中移除
                        val matchedOrdersForBundle = extractMatchedOrders(comboSpu, ordersPool)

                        // 记录匹配的套装
                        matchedComboResultSpuses.add(
                            MatchedComboResult(
                                matchedComboSpu = comboSpu,
                                matchedOrders = matchedOrdersForBundle,
                            ),
                        )
                    }
                }

                val totalMatched = orders.size - ordersPool.size

                MatchResult(
                    matchedComboResult = matchedComboResultSpuses,
                    remainingOrders = ordersPool,
                    isOptimal = false, // 暴力算法不保证最优(当然在不重叠的情况下是最优的,只是无法保证按照固定条件最优)
                    algorithm = "brute",
                    executionTimeMs = -1,
                    totalSpusMatched = totalMatched,
                )
            }
        val costTime = cost.duration.inWholeMilliseconds
        return cost.value.copy(
            executionTimeMs = costTime,
        )
    }

    /**
     * 快速暴力匹配
     * 按套装ID顺序遍历，完全不考虑优化
     */
    private fun simpleBruteForceMatch(orders: List<SubOrder>): MatchResult {
        val cost =
            measureTimedValue {
                val spuCounts = orders.groupBy { it.product.spu!! }.mapValues { it.value.size }.toMutableMap()
                val matchedComboResultSpuses = mutableListOf<MatchedComboResult>()
                val ordersPool = orders.toMutableList()

                // 最简单的遍历：按ID顺序
                val allComboSpuIds = comboSpuMap.keys.sorted()

                for (comboSpuId in allComboSpuIds) {
                    val comboSpu = comboSpuMap[comboSpuId] ?: continue

                    // 检查套装的所有商品是否在订单中
                    while (comboSpu.systemSpuCodes.all { it in spuCounts.keys }) {
                        // 检查数量是否足够
                        if (canApplyComboSpuToMutableMap(comboSpu, spuCounts)) {
                            // 直接应用
                            applyComboSpuToMutableMap(comboSpu, spuCounts)

                            // 找到匹配的具体订单
                            val matchedOrdersForBundle = extractMatchedOrders(comboSpu, ordersPool)

                            matchedComboResultSpuses.add(
                                MatchedComboResult(
                                    matchedComboSpu = comboSpu,
                                    matchedOrders = matchedOrdersForBundle,
                                ),
                            )
                        }
                    }
                }

                val totalMatched = orders.size - ordersPool.size

                MatchResult(
                    matchedComboResult = matchedComboResultSpuses,
                    remainingOrders = ordersPool,
                    isOptimal = false, // 暴力算法不保证最优(当然在不重叠的情况下是最优的,只是无法保证按照固定条件最优)
                    algorithm = "simple-brute",
                    executionTimeMs = -1,
                    totalSpusMatched = totalMatched,
                )
            }
        val costTime = cost.duration.inWholeMilliseconds
        return cost.value.copy(
            executionTimeMs = costTime,
        )
    }

    fun match(
        orderSpuCodes: List<SubOrder>,
        brute: Boolean = false,
    ): MatchResult {
        // 超过60种商品种类,可能计算复杂度过高会占用较多内存,降级到暴力遍历
        val useBrute = brute || orderSpuCodes.toSet().size > 60

        if (useBrute) {
            return bruteForceMatch(orderSpuCodes)
        }

        return kotlin
            .runCatching {
                dynamicProgrammingMatch(orderSpuCodes)
            }.recoverCatching {
                log.warn(it) { "dp match combospu error: $orderSpuCodes, fallback to brute" }
                bruteForceMatch(orderSpuCodes)
            }.getOrThrow()
    }

    /**
     * 快速预筛选相关套装
     */
    private fun getRelevantComboSpus(orders: List<SubOrder>): List<Long> {
        val availableSpuSet = orders.mapNotNull { it.product.spu }.toSet()
        val candidateComboSpus = mutableSetOf<Long>()

        // 通过单个spuCode索引找到候选套装
        orders.forEach { order ->
            spu2ComboSpu[order.product.spu]?.let { comboSpus ->
                candidateComboSpus.addAll(comboSpus.map { it.id })
            }
        }

        // 过滤出所有spuCode都在订单中的套装
        return candidateComboSpus.filter { comboSpuId ->
            val comboSpu = comboSpuMap[comboSpuId]!!
            comboSpu.systemSpuCodes.all { it in availableSpuSet }
        }
    }

    /**
     * 验证解的正确性
     */
    fun validateSolution(
        orders: List<SubOrder>,
        result: MatchResult,
    ): Boolean {
        val originalCounts = orders.groupBy { it.product.spu }.mapValues { it.value.size }
        val usedCounts = mutableMapOf<String, Int>()

        // 统计套装使用的商品
        result.matchedComboResult.forEach { comboSpu ->
            comboSpu.matchedComboSpu.systemSpuCodes.forEach { spuCode ->
                usedCounts[spuCode] = usedCounts.getOrDefault(spuCode, 0) + 1
            }
        }

        // 验证不超用
        return usedCounts.all { (spuCode, used) ->
            used <= originalCounts.getOrDefault(spuCode, 0)
        }
    }

    // 辅助方法
    private fun canApplyComboSpu(
        comboSpu: ComboCustomerSpu,
        spuCounts: Map<String, Int>,
    ): Boolean {
        val requiredCounts = comboSpu.systemSpuCodes.groupBy { it }.mapValues { it.value.size }
        return requiredCounts.all { (spuCode, requiredCount) ->
            spuCounts.getOrDefault(spuCode, 0) >= requiredCount
        }
    }

    private fun applyComboSpu(
        comboSpu: ComboCustomerSpu,
        spuCounts: Map<String, Int>,
    ): Map<String, Int> {
        val newCounts = spuCounts.toMutableMap()
        val requiredCounts = comboSpu.systemSpuCodes.groupBy { it }.mapValues { it.value.size }

        requiredCounts.forEach { (spuCode, requiredCount) ->
            val newCount = newCounts.getOrDefault(spuCode, 0) - requiredCount
            if (newCount > 0) {
                newCounts[spuCode] = newCount
            } else {
                newCounts.remove(spuCode)
            }
        }

        return newCounts
    }

    // 暴力算法专用的可变Map操作方法
    private fun canApplyComboSpuToMutableMap(
        comboSpu: ComboCustomerSpu,
        spuCounts: MutableMap<String, Int>,
    ): Boolean {
        val requiredCounts = comboSpu.systemSpuCodes.groupBy { it }.mapValues { it.value.size }
        return requiredCounts.all { (spuCode, requiredCount) ->
            spuCounts.getOrDefault(spuCode, 0) >= requiredCount
        }
    }

    private fun applyComboSpuToMutableMap(
        comboSpu: ComboCustomerSpu,
        spuCounts: MutableMap<String, Int>,
    ) {
        val requiredCounts = comboSpu.systemSpuCodes.groupBy { it }.mapValues { it.value.size }

        requiredCounts.forEach { (spuCode, requiredCount) ->
            val currentCount = spuCounts.getOrDefault(spuCode, 0)
            val newCount = currentCount - requiredCount
            if (newCount > 0) {
                spuCounts[spuCode] = newCount
            } else {
                spuCounts.remove(spuCode)
            }
        }
    }

    companion object {
        private val log = KotlinLogging.logger { }
    }
}
