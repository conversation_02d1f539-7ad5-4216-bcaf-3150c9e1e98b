package io.github.clive.luxomssystem.application.tracking

import io.github.clive.luxomssystem.domain.tracking.domainservice.TrackingService
import io.github.clive.luxomssystem.domain.tracking.model.TrackingData
import io.github.clive.luxomssystem.domain.tracking.model.TrackingInfo
import io.github.clive.luxomssystem.infrastructure.channel.prediction.channel.yanwen.YanWenTrackingResponse
import io.github.clive.luxomssystem.infrastructure.channel.prediction.channel.yuntu.YuntuTrackingResponse
import io.github.oshai.kotlinlogging.KotlinLogging
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional

/**
 * 轨迹应用服务
 * 提供轨迹查询和管理的应用层功能
 */
@Service
@Transactional
class TrackingApplicationService(
    private val trackingService: TrackingService
) {

    private val log = KotlinLogging.logger {}

    /**
     * 处理燕文轨迹响应
     *
     * @param response 燕文API响应
     * @param waybillNo 运单号
     * @return 转换后的轨迹数据
     */
    fun processYanWenTracking(response: YanWenTrackingResponse, waybillNo: String): TrackingData? {
        log.info { "处理燕文轨迹信息 | 运单号: $waybillNo" }

        return trackingService.convertChannelResponse(
            channelResponse = response,
            channel = "YANWEN",
            waybillNo = waybillNo
        )
    }

    /**
     * 处理云途轨迹响应
     *
     * @param response 云途API响应
     * @param waybillNo 运单号
     * @return 转换后的轨迹数据
     */
    fun processYuntuTracking(response: YuntuTrackingResponse, waybillNo: String): TrackingData? {
        log.info { "处理云途轨迹信息 | 运单号: $waybillNo" }

        return trackingService.convertChannelResponse(
            channelResponse = response,
            channel = "YUNTU",
            waybillNo = waybillNo
        )
    }

}

/**
 * 轨迹请求
 */
data class TrackingRequest(
    val waybillNo: String,
    val channel: String,
    val response: Any
)

/**
 * 批量处理结果
 */
data class BatchTrackingResult(
    val successCount: Int,
    val errorCount: Int,
    val results: List<TrackingInfo>,
    val errors: List<TrackingError>
)

/**
 * 轨迹错误
 */
data class TrackingError(
    val waybillNo: String,
    val errorMessage: String
)

/**
 * 验证结果
 */
data class ValidationResult(
    val isValid: Boolean,
    val issues: List<String>
)
