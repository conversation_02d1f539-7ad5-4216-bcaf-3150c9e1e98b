package io.github.clive.luxomssystem.application.inventory

import io.github.clive.luxomssystem.domain.CustomerCountryTaxConfig
import io.github.clive.luxomssystem.facade.customerSpu.BatchCustomerTaxRequest
import io.github.clive.luxomssystem.infrastructure.config.auth.UserContextHolder
import io.github.clive.luxomssystem.infrastructure.repository.jpa.CustomerCountryTaxConfigRepository
import io.github.clive.luxomssystem.nextId
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import java.math.BigDecimal

@Service
class CustomerTaxApplicationService(
    private val customerCountryTaxConfigRepository: CustomerCountryTaxConfigRepository,
) {
    @Transactional(rollbackFor = [Exception::class])
    fun deleteCountryTaxConfig(
        customerId: Long,
        countryId: Long,
    ) {
        customerCountryTaxConfigRepository.deleteByCustomerIdAndCountryId(customerId, countryId)
    }

    @Transactional(rollbackFor = [Exception::class])
    fun batchSave(taxReq: BatchCustomerTaxRequest) {
        taxReq.customerIds.forEach {
            val configsToSave =
                taxReq.customerTaxReq.map { req ->
                    CustomerCountryTaxConfig(
                        id = nextId(),
                        // 创建新配置
                        customerId = it,
                        countryId = req.countryId,
                        countryName = req.countryName,
                        additionalTax = req.additionalTax ?: BigDecimal.ZERO,
                        vatTax = req.vatTax ?: BigDecimal.ZERO,
                        stateTaxConfigs = req.stateTaxConfigs,
                    ).apply {
                        createdByName = UserContextHolder.user!!.name
                        updatedByName = UserContextHolder.user!!.name
                    }
                }
            customerCountryTaxConfigRepository.saveAll(configsToSave)
        }
    }

    @Transactional(rollbackFor = [Exception::class])
    fun batchUpdate(taxReq: BatchCustomerTaxRequest) {
        taxReq.customerIds.forEach {
            val existingConfigs = customerCountryTaxConfigRepository.findByCustomerId(it)
            val configsToSave =
                taxReq.customerTaxReq.map { req ->
                    existingConfigs.find { it.countryId == req.countryId }?.let {
                        it
                            .copy(
                                additionalTax = req.additionalTax ?: it.additionalTax,
                                vatTax = req.vatTax ?: it.vatTax,
                                countryName = req.countryName,
                                stateTaxConfigs = req.stateTaxConfigs,
                            ).apply {
                                updatedByName = UserContextHolder.user!!.name
                            }
                    }
                }
            customerCountryTaxConfigRepository.saveAll(configsToSave.filterNotNull())
        }
    }

    fun getCountryTaxConfig(id: Long): List<CustomerCountryTaxConfig> = customerCountryTaxConfigRepository.findByCustomerId(id)
}
