package io.github.clive.luxomssystem.application.openapi

import io.github.clive.luxomssystem.common.enums.MainOrderType
import io.github.clive.luxomssystem.common.exception.CognitionException
import io.github.clive.luxomssystem.common.exception.CognitionWebException
import io.github.clive.luxomssystem.common.exception.OmsBaseErrorCode
import io.github.clive.luxomssystem.domain.SubOrder
import io.github.clive.luxomssystem.domain.doanload.MainOrder
import io.github.clive.luxomssystem.domain.doanload.OrderSource
import io.github.clive.luxomssystem.facade.openapi.request.CreateOpenAPIOrderRequest
import io.github.clive.luxomssystem.infrastructure.config.auth.CustomerContextHolder
import io.github.clive.luxomssystem.infrastructure.repository.jpa.CustomerRepository
import io.github.clive.luxomssystem.infrastructure.repository.jpa.MainOrderRepository
import io.github.clive.luxomssystem.infrastructure.repository.jpa.SubOrderRepository
import io.github.clive.luxomssystem.infrastructure.repository.jpa.spu.SPURepository
import io.github.clive.luxomssystem.infrastructure.repository.jpa.spu.SkuRepository
import io.github.oshai.kotlinlogging.KotlinLogging
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.runBlocking
import org.springframework.data.repository.findByIdOrNull
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional

@Service
class OpenAPIApplicationService(
    private val spuRepository: SPURepository,
    private val skuRepository: SkuRepository,
    private val subOrderRepository: SubOrderRepository,
    private val mainOrderRepository: MainOrderRepository,
    private val customerRepository: CustomerRepository,
) {
    @Transactional(rollbackFor = [Exception::class])
    fun createOrder(req: CreateOpenAPIOrderRequest) {
        val customerId = CustomerContextHolder.user!!.id

        val customer =
            customerRepository.findByIdOrNull(customerId)
                ?: throw CognitionWebException(OmsBaseErrorCode.CUSTOMER_NOT_FOUND)

        val order = mainOrderRepository.findByUniqueKey(req.uniqueRequestId!!)

        if (order != null) {
            throw CognitionWebException(OmsBaseErrorCode.ORDER_REPEAT, req.uniqueRequestId)
        }

        val mainOrder =
            MainOrder(
                fileName = req.uniqueRequestId,
                uniqueKey = req.uniqueRequestId,
                type = MainOrderType.ORDER,
                bizId = customer.bizId,
                customerId = customer.id,
                customerName = customer.name,
                source = OrderSource.OPENAPI,
            ).apply {
                this.createdBy = customer.createdBy
                this.updatedBy = customer.createdBy
            }
        mainOrderRepository.saveAndFlush(mainOrder)

        val suborders =
            req.orderItems.map {
                val product = it.product
                val recipient = it.recipient
                SubOrder().apply {
                    parentId = mainOrder.id
                    fileName = req.uniqueRequestId
                    orderNo = it.orderNo
                    urls = it.imageUrls.joinToString { "," }
                    this.shipping.wayBillRelation =
                        java.util.UUID
                            .randomUUID()
                            .toString()
                    this.recipient.city = recipient?.city
                    this.recipient.state = recipient?.state
                    this.recipient.country = recipient?.country
                    this.recipient.address1 = recipient?.address1
                    this.recipient.postcode = recipient?.postcode
                    this.recipient.phone = recipient?.phone
                    this.recipient.receiverName = recipient?.receiverName
                    this.recipient.email = recipient?.email
                    this.remark = recipient?.remark ?: ""
                    this.product.qty = product?.quantity!!
                    this.product.color = product.color
                    this.product.size = product.size
                    this.product.spu = product.spu
                    this.source = OrderSource.OPENAPI
                    this.createdBy = customer.createdBy
                    this.updatedBy = customer.createdBy
                    this.bizId = customer.bizId
                }
            }

        processSubOrdersConcurrently(suborders)

        subOrderRepository.saveAllAndFlush(suborders)
    }

    fun processSubOrdersConcurrently(subOrders: List<SubOrder>) {
        // 创建一个协程作用域以管理并发
        runBlocking {
            val updateJobs =
                subOrders.map { subOrder ->
                    launch(Dispatchers.IO) {
                        // 逐个处理 subOrder
                        subOrder.apply {
                            val spu =
                                spuRepository.findBySpuCode(product.spu!!)
                                    ?: throw CognitionException("spu is not exist: ${product.spu}")
                            val sku =
                                if (product.onSet()) {
                                    skuRepository.findBySkuCode(product.firstSetSku())
                                        ?: throw CognitionException("sku is not exist: ${product.skuCode()}")
                                } else {
                                    skuRepository.findBySkuCode(product.skuCode())
                                        ?: throw CognitionException("sku is not exist: ${product.skuCode()}")
                                }
                            product.update(sku, spu)
                        }
                    }
                }

            // 等待所有协程完成
            updateJobs.forEach { it.join() }
        }
    }

    companion object {
        private val log = KotlinLogging.logger { }
    }
}
