package io.github.clive.luxomssystem.application.mainorder

import io.github.clive.luxomssystem.application.inventory.suppliersku.findIntersection
import io.github.clive.luxomssystem.common.enums.BaseStatus
import io.github.clive.luxomssystem.common.enums.SubOrderStatus
import io.github.clive.luxomssystem.common.enums.order.MainOrderStatus
import io.github.clive.luxomssystem.common.ext.TimeUtils
import io.github.clive.luxomssystem.domain.SubOrder
import io.github.clive.luxomssystem.domain.SupplierCustomerSpuCapacity
import io.github.clive.luxomssystem.domain.customerSpu.model.SupplierCapacity
import io.github.clive.luxomssystem.domain.doanload.event.MainOrderEvent
import io.github.clive.luxomssystem.domain.supplier.model.selectHighestPrioritySupplier
import io.github.clive.luxomssystem.infrastructure.config.properties.WebHookType
import io.github.clive.luxomssystem.infrastructure.remote.LarkRemoteNotifyService
import io.github.clive.luxomssystem.infrastructure.repository.jpa.MainOrderRepository
import io.github.clive.luxomssystem.infrastructure.repository.jpa.SubOrderRepository
import io.github.clive.luxomssystem.infrastructure.repository.jpa.SupplierCustomerSpuCapacityRepository
import io.github.clive.luxomssystem.infrastructure.repository.jpa.SupplierRepository
import io.github.clive.luxomssystem.infrastructure.repository.jpa.spu.*
import io.github.oshai.kotlinlogging.KotlinLogging
import org.springframework.data.repository.findByIdOrNull
import org.springframework.scheduling.annotation.Async
import org.springframework.stereotype.Component
import org.springframework.transaction.annotation.Propagation
import org.springframework.transaction.annotation.Transactional
import org.springframework.transaction.event.TransactionalEventListener

@Component
class MainOrderCreatedEventHandler(
    private val subOrderRepository: SubOrderRepository,
    private val supplierRepository: SupplierRepository,
    private val customerSpuRepository: CustomerSpuRepository,
    private val supplierSkuRepository: SupplierSkuRepository,
    private val supplierCustomerSpuCapacityRepository: SupplierCustomerSpuCapacityRepository,
    private val mainOrderRepository: MainOrderRepository,
    private val larkRemoteNotifyService: LarkRemoteNotifyService,
    private val skuRepository: SkuRepository,
    private val spuRepository: SPURepository,
    private val supplierSpuRepository: SupplierSpuRepository,
) {
    @Async
    @Transactional(rollbackFor = [Exception::class], propagation = Propagation.REQUIRES_NEW)
    @TransactionalEventListener(MainOrderEvent.MainOrderCreatedEvent::class)
    fun handle(event: MainOrderEvent.MainOrderCreatedEvent) {
        log.info { "主任务事件 | 类型: 创建 | 订单ID: ${event.id}" }
        val mainOrder =
            mainOrderRepository.findByIdOrNull(event.id) ?: run {
                log.error { "主任务事件 | 类型: 创建 | 订单ID: ${event.id} 不存在" }
                return
            }
        mainOrder.status = MainOrderStatus.MATCHING
        mainOrderRepository.saveAndFlush(mainOrder)
        val subOrders = subOrderRepository.findByParentIdOrderByIdAsc(event.id)
        subOrders.forEach(this::matchSupplier)

        log.info { "主任务事件 | 类型: 创建 | 订单ID: ${event.id} | 订单状态: ${mainOrder.status}" }

        subOrders
            .filter {
                !it.shipping.wayBillRelation.isNullOrBlank()
            }.groupBy { it.shipping.wayBillRelation }
            .forEach { (wayBillRelation, v) ->
                checkRelationShipOrder(wayBillRelation, null, v)
            }

        val failedSubOrderCount = subOrders.count { it.status == SubOrderStatus.FAILED }

        if (failedSubOrderCount > 0) {
            log.error { "主任务事件 | 类型: 创建 | 订单ID: ${event.id} | 订单状态: ${mainOrder.status} | 存在${failedSubOrderCount}个失败的子订单" }

            // TODO 文案瞎写的
            larkRemoteNotifyService.sendNotificationAsync(
                WebHookType.MAIN_ORDER_SOME_SUB_ORDER_FAILED,
                LarkRemoteNotifyService.LarkMessage(
                    title = "主订单的子订单发生异常",
                    content = "主订单 ${mainOrder.id} 存在有${failedSubOrderCount}个子订单发生异常, 请尽快处理",
                ),
            )
        }

        subOrderRepository.saveAll(subOrders)
    }

    private fun List<SubOrder>.failedAll(reason: String) {
        log.error { reason }
        forEach {
            it.failed(reason)
        }
    }

    private fun checkRelationShipOrder(
        wayBillRelation: String?,
        orderNo: String?,
        subOrders: List<SubOrder>,
    ) {
        if (subOrders.size > 1) {
            val expectOrder = subOrders[0]
            val expect = expectOrder.recipient
            for (i in 1..<subOrders.size) {
                val subOrder = subOrders[i]
                val recipient = subOrder.recipient
//                if (recipient.userName != expect.userName) {
//                    return subOrders.failedAll("关联订单号 [$wayBillRelation] 的关联订单[${expectOrder.orderNo},${subOrder.orderNo}]存在地址[客户]不一致")
//                }
                if (recipient.receiverName != expect.receiverName) {
                    return subOrders.failedAll("关联订单号 [$wayBillRelation] 的关联订单[${expectOrder.orderNo},${subOrder.orderNo}]存在地址[收件人]不一致")
                }
                if (recipient.country != expect.country) {
                    return subOrders.failedAll("关联订单号 [$wayBillRelation] 的关联订单[${expectOrder.orderNo},${subOrder.orderNo}]存在地址[国家]不一致")
                }
                if (recipient.state != expect.state) {
                    return subOrders.failedAll("关联订单号 [$wayBillRelation] 的关联订单[${expectOrder.orderNo},${subOrder.orderNo}]存在地址[州]不一致")
                }
                if (recipient.city != expect.city) {
                    return subOrders.failedAll("关联订单号 [$wayBillRelation] 的关联订单[${expectOrder.orderNo},${subOrder.orderNo}]存在地址[城市]不一致")
                }
                if (recipient.address1 != expect.address1) {
                    return subOrders.failedAll(
                        "关联订单号 [$wayBillRelation] 的关联订单[${expectOrder.orderNo},${subOrder.orderNo}]存在地址[address1]不一致",
                    )
                }
                if (recipient.address2 != expect.address2) {
                    return subOrders.failedAll(
                        "关联订单号 [$wayBillRelation] 的关联订单[${expectOrder.orderNo},${subOrder.orderNo}]存在地址[address2]不一致",
                    )
                }
                if (recipient.postcode != expect.postcode) {
                    return subOrders.failedAll("关联订单号 [$wayBillRelation] 的关联订单[${expectOrder.orderNo},${subOrder.orderNo}]存在地址[邮编]不一致")
                }
                if (recipient.phone != expect.phone) {
                    return subOrders.failedAll("关联订单号 [$wayBillRelation] 的关联订单[${expectOrder.orderNo},${subOrder.orderNo}]存在地址[号码]不一致")
                }
                if (recipient.email != expect.email) {
                    return subOrders.failedAll("关联订单号 [$wayBillRelation] 的关联订单[${expectOrder.orderNo},${subOrder.orderNo}]存在地址[邮箱]不一致")
                }
            }
        }

        val assignedSupplier = subOrders.map { it.product.supplierId }.toSet()

        val supplierMatchFailedOrders = subOrders.filter { it.product.supplierId == null }

        val errorMessage =
            if (supplierMatchFailedOrders.isNotEmpty()) {
                val failedOrderIds = supplierMatchFailedOrders.joinToString { it.orderNo ?: "null" }
                "关联订单号 [$wayBillRelation] 的关联的订单[$failedOrderIds]供应商匹配失败"
            } else {
                "关联订单号 [$wayBillRelation] 的关联的订单分配给了多个工厂: $assignedSupplier"
            }

        if (orderNo == null) {
            log.info { errorMessage }
        } else {
            log.info { "订单号:$orderNo $errorMessage" }
        }

        if (assignedSupplier.size > 1) {
            log.error { errorMessage }
            subOrders.forEach {
                if (it.status == SubOrderStatus.FAILED && it.product.supplierId == null) {
                    // 这里当supplierId为null的代表供应商匹配失败了,那么在上一个阶段就已经会设置好错误信息了,这里不应该去覆盖掉这个错误信息
                    return@forEach
                }
                it.failed(errorMessage)
            }
        }
    }

    fun assertRelationShipOrder(subOrder: SubOrder) {
        if (!subOrder.shipping.wayBillRelation.isNullOrBlank()) {
            log.info { "订单号:${subOrder.orderNo} 关联订单号 [${subOrder.shipping.wayBillRelation}] 的关联的订单对于工厂的数量审查与地址核验" }
            val subOrders =
                subOrderRepository.findByShipping_WayBillRelationAndParentId(
                    subOrder.shipping.wayBillRelation!!,
                    subOrder.parentId,
                )
            checkRelationShipOrder(subOrder.shipping.wayBillRelation, subOrder.orderNo, subOrders)
        }
    }

    fun matchSupplier(subOrder: SubOrder): Boolean {
        try {
            log.info { "子任务事件 | 类型: 匹配工厂  | 订单号: ${subOrder.orderNo}" }

            val cannotDetermineSkuCodeReason: String? = subOrder.product.cannotDetermineSkuCodeReason()
            if (cannotDetermineSkuCodeReason != null) {
                log.info { "供应商匹配失败  | 订单号: ${subOrder.orderNo} | 原因: 无法确定SKU代码: ($cannotDetermineSkuCodeReason)" }
                subOrder.failed("无法确定SKU代码, 请确认$cannotDetermineSkuCodeReason")
                return false
            }

            if (subOrder.product.skuId == null) {
                // 这里没有skuId,尝试立即进行一次sku重匹配

                val skuCode = subOrder.product.determineSkuCode()
                if (skuCode != null) {
                    // 尝试重新匹配sku
                    skuRepository.findBySkuCode(skuCode)?.let {
                        log.info { "子订单 ${subOrder.orderNo} 重试,尝试重新匹配sku: $skuCode" }
                        val spu = spuRepository.findByIdOrNull(it.spuId) ?: throw Exception("未知的spu：${it.spuId}")
                        subOrder.product.update(it, spu)
                    } ?: run {
                        log.info { "子订单 ${subOrder.orderNo} 重试,尝试重匹配sku,但无法找到对应的sku: $skuCode" }
                    }
                } else {
                    log.info { "子订单 ${subOrder.orderNo} 重试,尝试重匹配sku,但仍然无法构建sku code" }
                }

                log.info { "供应商匹配失败  | 订单号: ${subOrder.orderNo} | 原因: 无法确定SKUID" }
                subOrder.failed("无法确定SKU: ${subOrder.product.skuCode()}, 请确认SKU是否正确")
                return false
            }

            val matchResult = tryMatchSupplierFromCustomerSpu(subOrder)

            if (matchResult) {
                log.info { "供应商匹配成功  | 订单号: ${subOrder.orderNo}" }
                return true
            }

            if (subOrder.product.skuCode().contains("/")) {
                log.info { "处理组合SKU  | 订单号: ${subOrder.orderNo} | SKU代码: ${subOrder.product.skuCode()}" }
                if (!handleComboSku(subOrder)) {
                    return false
                }
            } else {
                log.info { "处理单个SKU  | 订单号: ${subOrder.orderNo} | SKU代码: ${subOrder.product.skuCode()}" }
                if (!handleSingleSku(subOrder)) {
                    return false
                }
            }

            log.info { "供应商订单处理完成  | 订单号: ${subOrder.orderNo} | 状态: 成功" }
            return true
        } catch (e: Exception) {
            log.error { "供应商订单处理失败  | 订单号: ${subOrder.orderNo} | 错误类型: ${e.javaClass.simpleName} | 错误信息: ${e.message ?: "未知错误"}" }
            subOrder.failed(e.message ?: "处理SupplierOrderEvent创建事件失败")
            log.info { "供应商订单状态已更新为失败  | 订单号: ${subOrder.orderNo}" }
            return false
        }
    }

    private fun tryMatchSupplierFromCustomerSpu(subOrder: SubOrder): Boolean {
        log.info { "开始尝试匹配客户SPU特定供应商  | 订单号: ${subOrder.orderNo}" }

        val supplier = selectCustomerSpuSpecificSupplier(subOrder)

        if (supplier == null) {
            log.info { "未找到匹配的客户SPU特定供应商  | 订单号: ${subOrder.orderNo}" }
            return false
        }
        assignSupplierToOrder(subOrder, supplier.supplierId, supplier.supplierName)

        log.info { "成功匹配供应商  | 订单号: ${subOrder.orderNo} | 供应商ID: ${supplier.supplierId} | 供应商名称: ${supplier.supplierName}" }

        log.info { "供应商匹配完成  | 订单号: ${subOrder.orderNo}" }

        return true
    }

    private fun handleComboSku(subOrder: SubOrder): Boolean {
        val skuCode = subOrder.product.skuCode()

        log.info { "处理套装SKU开始  | 订单号: ${subOrder.orderNo} | SKU: $skuCode | 业务ID: ${subOrder.bizId}" }

        try {
            val sizes = subOrder.product.size?.split("/") ?: emptyList()

            if (sizes.isEmpty()) {
                log.warn { "套装SKU尺寸为空  | 订单号: ${subOrder.orderNo} | SKU: $skuCode" }
                subOrder.failed("套装尺寸为空: $skuCode")
                return false
            }

            val supplierIds =
                sizes
                    .map { size ->
                        val systemSkuCode = "${subOrder.product.spu}-$size-${subOrder.product.color}"
                        val suppliers =
                            supplierSkuRepository.findBySystemSkuCodeAndBizIdAndStatus(
                                systemSkuCode,
                                subOrder.bizId,
                                BaseStatus.ENABLED,
                            )

                        log.debug { "套装子SKU查询  | 订单号: ${subOrder.orderNo} | 子SKU: $systemSkuCode | 供应商数量: ${suppliers.size}" }
                        suppliers.map { it.supplierId }
                    }.findIntersection()

            log.info { "套装SKU匹配结果  | 订单号: ${subOrder.orderNo} | SKU: $skuCode | 匹配供应商数量: ${supplierIds.size}" }

            val supplier = supplierRepository.findAllById(supplierIds).selectHighestPrioritySupplier()

            if (supplier != null) {
                assignSupplierToOrder(subOrder, supplier.id, supplier.name)
                log.info { "套装SKU分配成功  | 订单号: ${subOrder.orderNo} | SKU: $skuCode | 供应商ID: ${supplier.id} | 供应商名称: ${supplier.name}" }
                return true
            } else {
                subOrder.failed("套装匹配不到对应的供应商: $skuCode")
                log.warn { "套装SKU分配失败  | 订单号: ${subOrder.orderNo} | SKU: $skuCode | 原因: 无匹配供应商" }
                return false
            }
        } catch (e: Exception) {
            log.error(e) { "套装SKU处理异常  | 订单号: ${subOrder.orderNo} | SKU: $skuCode | 异常信息: ${e.message}" }
            subOrder.failed("套装处理异常: ${e.message}")
            return false
        }
    }

    private fun handleSingleSku(subOrder: SubOrder): Boolean {
        val startTime = System.currentTimeMillis()
        log.info { "开始处理单个SKU订单  | 订单号: ${subOrder.orderNo} | SKU编码: ${subOrder.product.skuCode()} | 业务ID: ${subOrder.bizId}" }

        try {
            val supplierIds =
                supplierSkuRepository
                    .findBySystemSkuCodeAndBizIdAndStatus(
                        subOrder.product.skuCode(),
                        subOrder.bizId,
                        BaseStatus.ENABLED,
                    ).map { it.supplierId }

            val spuSupplierIds =
                supplierSpuRepository.findBySystemSpuIdAndStatus(subOrder.product.spuId!!, BaseStatus.ENABLED)
                    .map { it.supplierId }

            val realSupplierIds = supplierIds.intersect(spuSupplierIds)

            log.info {
                "查询到符合条件的供应商  | 订单号: ${subOrder.orderNo} | SKU编码: ${subOrder.product.skuCode()} | 供应商数量: ${realSupplierIds.size} | 供应商IDs: ${realSupplierIds.joinToString()}"
            }

            val supplier = supplierRepository.findAllById(realSupplierIds).selectHighestPrioritySupplier()

            if (supplier != null) {
                log.info { "为订单分配供应商  | 订单号: ${subOrder.orderNo} | 供应商ID: ${supplier.id} | 供应商名称: ${supplier.name}" }
                assignSupplierToOrder(subOrder, supplier.id, supplier.name)
                return true
            } else {
                log.warn { "订单匹配供应商失败  | 订单号: ${subOrder.orderNo} | SKU编码: ${subOrder.product.skuCode()} | 业务ID: ${subOrder.bizId}" }
                subOrder.failed("SKU匹配不到符合的供应商: ${subOrder.product.skuCode()}")
                return false
            }
        } catch (e: Exception) {
            log.error(e) { "处理单个SKU订单异常  | 订单号: ${subOrder.orderNo} | 异常信息: ${e.message}" }
            subOrder.failed("处理订单异常: ${e.message}")
            return false
        } finally {
            val processingTime = System.currentTimeMillis() - startTime
            log.info { "完成处理单个SKU订单  | 订单号: ${subOrder.orderNo} | 处理时间: ${processingTime}ms | 处理结果: ${subOrder.status}" }
        }
    }

    private fun assignSupplierToOrder(
        subOrder: SubOrder,
        supplierId: Long,
        supplierName: String,
    ) {
        subOrder.product.supplierId = supplierId
        subOrder.product.supplierName = supplierName
        subOrder.status = SubOrderStatus.SUPPLIER_MATCHED
        subOrder.failedReason = null
    }

    private fun selectCustomerSpuSpecificSupplier(subOrder: SubOrder): SupplierCapacity? {
        log.info { "根据客户SPU上供应商配置的产品容量匹配  | 订单号: ${subOrder.orderNo} | 产品ID: ${subOrder.product.spuId} | 客户ID: ${subOrder.customerId}" }

        val customerSpu =
            customerSpuRepository.findBySystemSpuIdAndCustomerId(
                subOrder.product.spuId!!,
                subOrder.customerId,
            )

        if (customerSpu == null) {
            log.warn { "客户SPU未找到  | 订单号: ${subOrder.orderNo} | 系统SPUID: ${subOrder.product.spuId} | 客户ID: ${subOrder.customerId}" }
            return null
        }

        if (customerSpu.supplierCapacities.isEmpty()) {
            log.warn { "供应商容量未配置  | 订单号: ${subOrder.orderNo} | 客户SPUID: ${customerSpu.id}" }
            return null
        }

        val capacityUsed =
            supplierCustomerSpuCapacityRepository
                .findByCustomerSpuIdAndDate(customerSpu.id, TimeUtils.yyMMdd())
                .associateBy { it.supplierId }

        log.info {
            "容量使用记录 |  订单号: ${subOrder.orderNo} | 记录数: ${capacityUsed.size} | 客户SPUID: ${customerSpu.id} | 日期: ${TimeUtils.yyMMdd()}"
        }

        for (capacity in customerSpu.supplierCapacities) {
            val supplier = supplierRepository.findByIdOrNull(capacity.supplierId)
            if (supplier == null || supplier.status == BaseStatus.DISABLED) {
                log.info { "供应商不存在或已禁用  | 订单号: ${subOrder.orderNo} | 供应商ID: ${capacity.supplierId} | 往下匹配" }
                continue
            }

            val statistic = capacityUsed[capacity.supplierId]
            if (statistic == null) {
                log.info {
                    "供应商容量检查 |  订单号: ${subOrder.orderNo} | 供应商ID: ${capacity.supplierId} | 需求量: ${subOrder.product.qty} | 总容量: ${capacity.totalCapacity}"
                }

                if (capacity.canSupport(subOrder.product.qty)) {
                    val supplierCustomerSpuCapacity =
                        SupplierCustomerSpuCapacity(
                            date = TimeUtils.yyMMdd(),
                            supplierId = capacity.supplierId,
                            customerSpuId = customerSpu.id,
                            usedCapacity = subOrder.product.qty.toLong(),
                            totalCapacity = capacity.totalCapacity.toLong(),
                        )
                    supplierCustomerSpuCapacityRepository.saveAndFlush(supplierCustomerSpuCapacity)

                    log.info {
                        "供应商选择成功 |  订单号: ${subOrder.orderNo} | 供应商ID: ${capacity.supplierId} | 客户SPUID: ${customerSpu.id} | 分配容量: ${subOrder.product.qty}"
                    }
                    return capacity
                } else {
                    log.info {
                        "供应商容量不足 |  订单号: ${subOrder.orderNo} | 供应商ID: ${capacity.supplierId} | 需求量: ${subOrder.product.qty} | 总容量: ${capacity.totalCapacity}"
                    }
                }
            } else {
                if (statistic.canSupport(subOrder.product.qty.toLong())) {
                    statistic.usedCapacity += subOrder.product.qty.toLong()
                    supplierCustomerSpuCapacityRepository.saveAndFlush(statistic)

                    log.info {
                        "供应商选择成功 |  订单号: ${subOrder.orderNo} | 供应商ID: ${capacity.supplierId} | 客户SPUID: ${customerSpu.id} | 分配容量: ${subOrder.product.qty}"
                    }
                    return capacity
                } else {
                    log.info {
                        "供应商容量不足 |  订单号: ${subOrder.orderNo} | 供应商ID: ${capacity.supplierId} | 需求量: ${subOrder.product.qty} | 剩余可分配容量: ${statistic.remainCapacity()}"
                    }
                }
            }
        }

        log.warn { "供应商选择失败 | 订单号: ${subOrder.orderNo} | 客户SPUID: ${customerSpu.id} | 需求量: ${subOrder.product.qty} " }
        return null
    }

    companion object {
        private val log = KotlinLogging.logger { }
    }
}
