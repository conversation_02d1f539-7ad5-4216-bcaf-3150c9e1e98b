package io.github.clive.luxomssystem.application.inventory.customerSku

import io.github.clive.luxomssystem.common.enums.BaseStatus
import io.github.clive.luxomssystem.domain.customerSku.domainservice.CustomerSkuDomainService
import io.github.clive.luxomssystem.domain.customerSku.event.CustomerSkuCreatedEvent
import io.github.clive.luxomssystem.domain.customerSku.event.CustomerSkuDeletedEvent
import io.github.clive.luxomssystem.domain.customerSku.event.CustomerSkuUpdatedEvent
import io.github.clive.luxomssystem.domain.customerSku.model.CustomerSku
import io.github.clive.luxomssystem.domain.customerSpu.event.CustomerSpuEvent
import io.github.clive.luxomssystem.facade.customerSku.request.CreateCustomerSkuRequest
import io.github.clive.luxomssystem.facade.customerSku.request.UpdateCustomerSkuRequest
import io.github.clive.luxomssystem.facade.customerSku.response.CustomerSkuResponse
import io.github.clive.luxomssystem.infrastructure.config.auth.UserContextHolder
import io.github.clive.luxomssystem.infrastructure.repository.jpa.spu.CustomerSkuRepository
import io.github.clive.luxomssystem.infrastructure.repository.jpa.spu.SkuRepository // Added import
import io.github.oshai.kotlinlogging.KotlinLogging // Added import
import org.springframework.context.ApplicationEventPublisher
import org.springframework.context.event.EventListener
import org.springframework.data.domain.Page
import org.springframework.data.domain.Pageable
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional

/**
 * Application service for CustomerSku operations.
 * This service orchestrates the flow of data and coordinates domain logic.
 */
@Service
class CustomerSkuApplicationService(
    private val customerSkuRepository: CustomerSkuRepository,
    private val customerSkuDomainService: CustomerSkuDomainService,
    private val eventPublisher: ApplicationEventPublisher,
    private val skuRepository: SkuRepository, // Added SkuRepository
) {
    companion object { // Added companion object for logging
        private val log = KotlinLogging.logger {}
    }

    /**
     * Creates a new CustomerSku.
     * This method coordinates the creation process, including:
     * 1. Creating the CustomerSku instance
     * 2. Persisting it via the repository
     * 3. Publishing a creation event
     */
    @Transactional
    fun createCustomerSku(request: CreateCustomerSkuRequest): CustomerSkuResponse {
        val customerSku =
            customerSkuRepository.save(
                CustomerSku(
                    bizId = UserContextHolder.user!!.bizId,
                    customerId = request.customerId,
                    systemSpuId = request.systemSpuId,
                    systemSkuId = request.systemSkuId,
                    systemSpuCode = request.systemSpuCode,
                    systemSkuCode = request.systemSkuCode,
                    customerSpuCode = request.customerSpuCode,
                    customerSkuCode = request.customerSkuCode,
                    customerSpuId = request.customerSpuId,
                    size = request.size,
                    color = request.color,
                    offlinePrice = request.offlinePrice,
                    offlinePriceCurrency = request.offlinePriceCurrency,
                    status = BaseStatus.ENABLED,
                    skuCountryPrices = request.skuCountryPrices,
                    skuPcsPrices = request.skuPcsPrices,
                    discount = request.discount,
                    triggerDiscountQuantity = request.triggerDiscountQuantity,
                ).apply {
                    // Apply audit fields
                    createdByName = UserContextHolder.user!!.name
                    updatedByName = UserContextHolder.user!!.name
                },
            )

        customerSkuDomainService.performInitialSetup(customerSku)

        eventPublisher.publishEvent(CustomerSkuCreatedEvent(customerSku.id))

        return CustomerSkuResponse.fromDomain(customerSku)
    }

    /**
     * Retrieves a CustomerSku by its ID.
     */
    fun getCustomerSkuById(id: Long): CustomerSkuResponse {
        val customerSku =
            customerSkuRepository
                .findById(id)
                .orElseThrow { NoSuchElementException("CustomerSku not found with id: $id") }
        return CustomerSkuResponse.fromDomain(customerSku)
    }

    /**
     * Updates an existing CustomerSku.
     */
    @Transactional
    fun updateCustomerSku(
        id: Long,
        request: UpdateCustomerSkuRequest,
    ): CustomerSkuResponse {
        val customerSku =
            customerSkuRepository
                .findById(id)
                .orElseThrow { NoSuchElementException("CustomerSku not found with id: $id") }

        customerSku.updateDetails(
            offlinePrice = request.offlinePrice,
            offlinePriceCurrency = request.offlinePriceCurrency,
            skuCountryPrices = request.skuCountryPrices,
            skuPcsPrices = request.skuPcsPrices,
            discount = request.discount,
            triggerDiscountQuantity = request.triggerDiscountQuantity,
        )
        // Apply audit fields
        customerSku.updatedByName = UserContextHolder.user!!.name
        customerSku.updatedAt = System.currentTimeMillis()

        val updatedCustomerSku = customerSkuRepository.save(customerSku)
        eventPublisher.publishEvent(CustomerSkuUpdatedEvent(updatedCustomerSku.id))

        return CustomerSkuResponse.fromDomain(updatedCustomerSku)
    }

    /**
     * Deletes a CustomerSku by its ID.
     */
    @Transactional
    fun deleteCustomerSku(id: Long) {
        if (!customerSkuRepository.existsById(id)) {
            throw NoSuchElementException("CustomerSku not found with id: $id")
        }
        customerSkuRepository.deleteById(id)
        eventPublisher.publishEvent(CustomerSkuDeletedEvent(id))
    }

    /**
     * Retrieves a page of CustomerSku entities.
     */
    fun getAllCustomerSkus(pageable: Pageable): Page<CustomerSkuResponse> =
        customerSkuRepository.findAll(pageable).map { CustomerSkuResponse.fromDomain(it) }

    @Transactional
    @EventListener(CustomerSpuEvent.Deleted::class)
    fun handleCustomerSpuEvent(event: CustomerSpuEvent.Deleted) {
        customerSkuRepository.deleteByCustomerSpuId(event.customerSpuId)
    }

    @Transactional
    @EventListener(CustomerSpuEvent.Created::class)
    fun handleCustomerSpuEvent(event: CustomerSpuEvent.Created) {
        event.skus.map {
            createCustomerSku(it)
        }
    }

    @Transactional
    @EventListener(CustomerSpuEvent.Updated::class)
    fun handleCustomerSpuEvent(event: CustomerSpuEvent.Updated) {
        event.cmds.map {
            updateCustomerSku(it.id!!, it)
        }
    }
}
