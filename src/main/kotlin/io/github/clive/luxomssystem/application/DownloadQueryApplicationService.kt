package io.github.clive.luxomssystem.application

import io.github.clive.luxomssystem.domain.doanload.OrderImageTask
import io.github.clive.luxomssystem.domain.doanload.OrderImageTaskStatus
import io.github.clive.luxomssystem.domain.doanload.event.MainOrderEvent
import io.github.clive.luxomssystem.infrastructure.config.auth.UserContextHolder
import io.github.clive.luxomssystem.infrastructure.repository.jpa.OrderImageTaskRepository
import org.springframework.context.event.EventListener
import org.springframework.data.domain.Page
import org.springframework.data.domain.PageRequest
import org.springframework.jdbc.core.JdbcTemplate
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional

@Service
class DownloadQueryApplicationService(
    private val jdbcTemplate: JdbcTemplate,
    private val orderImageTaskRepository: OrderImageTaskRepository,
) {
    fun findDownloadOrderImageTask(
        pageIndex: Int,
        pageSize: Int,
        status: OrderImageTaskStatus?,
        orderNo: String?,
        orderId: Long?,
    ): Page<OrderImageTask> {
        val nos = orderNo?.split(",")?.map { it.trim() }
        return orderImageTaskRepository.findByStatus(
            status,
            nos,
            orderId,
            UserContextHolder.user!!.bizId,
            PageRequest.of(pageIndex, pageSize),
        )
    }

    @Transactional
    fun retryOrderImageTask(id: Long) {
        orderImageTaskRepository.findById(id).ifPresent {
            it.status = OrderImageTaskStatus.CREATED
            it.errorMessage = ""
            orderImageTaskRepository.saveAndFlush(it)
        }
    }

    @Transactional
    @EventListener(MainOrderEvent.RetryOrderImageTaskEvent::class)
    fun retryMainOrderFailedTaskEvent(event: MainOrderEvent.RetryOrderImageTaskEvent) {
        val task =
            orderImageTaskRepository.findByOrderIdAndStatus(event.id, OrderImageTaskStatus.FAILED).map {
                it.status = OrderImageTaskStatus.CREATED
                it.errorMessage = ""
                it
            }
        orderImageTaskRepository.saveAllAndFlush(task)
    }

    //
    //    fun findUsers(page: Int, size: Int): Page<OrderImageTask> {
    //        val pageable: Pageable = PageRequest.of(page, size)
    //        return userRepository.findAll(pageable)    }
    //
    //
    //    fun findPersons(name: String?, age: Int?, page: Int, size: Int): Page<OrderImageTask> {
    //        val pageable: Pageable = PageRequest.of(page, size)
    //        val booleanBuilder = BooleanBuilder()
    //
    //        // 动态添加查询条件
    //        name?.let { booleanBuilder.and(QOrderImageTaskEntity.orderImageTask.orderId
    // .name.containsIgnoreCase(it)) }
    //        age?.let { booleanBuilder.and(QOrderImageTaskEntity.orderImageTask.person.age.eq(it))
    // }
    //
    //        return orderImageTaskRepository.findAll(booleanBuilder, pageable)
    //    }
}
