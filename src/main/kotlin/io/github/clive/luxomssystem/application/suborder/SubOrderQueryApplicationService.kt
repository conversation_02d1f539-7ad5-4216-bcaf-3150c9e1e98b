package io.github.clive.luxomssystem.application.suborder

import io.github.clive.luxomssystem.domain.SubOrder
import io.github.clive.luxomssystem.infrastructure.repository.jpa.SubOrderRepository
import io.github.oshai.kotlinlogging.KotlinLogging
import org.springframework.data.repository.findByIdOrNull
import org.springframework.stereotype.Service

@Service
class SubOrderQueryApplicationService(
    private val subOrderRepository: SubOrderRepository,
) {
    fun findMergedSubOrders(subOrderId: Long): List<SubOrder> {
        val subOrder = subOrderRepository.findByIdOrNull(subOrderId) ?: throw IllegalArgumentException("子订单不存在")
        return subOrderRepository.findByShipping_WayBillRelationAndParentId(
            subOrder.shipping.wayBillRelation!!,
            subOrder.parentId,
        )
    }

    fun findSplitSubOrders(id: Long): List<SubOrder> = subOrderRepository.findByOriginId(id)

    companion object {
        private val log = KotlinLogging.logger {}
    }
}
