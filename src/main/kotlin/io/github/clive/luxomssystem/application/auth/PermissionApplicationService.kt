package io.github.clive.luxomssystem.application.auth

import io.github.clive.luxomssystem.common.enums.BaseStatus
import io.github.clive.luxomssystem.domain.doanload.auth.Permission
import io.github.clive.luxomssystem.infrastructure.repository.jpa.auth.PermissionRepository
import io.github.oshai.kotlinlogging.KotlinLogging
import org.springframework.cache.annotation.Cacheable
import org.springframework.data.domain.Page
import org.springframework.data.domain.PageRequest
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import kotlin.math.log

@Service
class PermissionApplicationService(
    private val permissionRepository: PermissionRepository,
) {
    fun findById(id: Long): Permission? = permissionRepository.findById(id).orElse(null)

    fun findAll(pageable: PageRequest): Page<Permission> = permissionRepository.findAll(pageable)

    fun findByParentId(parentId: Long): List<Permission> = permissionRepository.findByParentId(parentId)

    fun findByNameContaining(): List<Permission> = permissionRepository.findAll()

    @Cacheable(value = ["permissions"], key = "'all_permissions'", unless = "#result.isEmpty()")
    fun findAll(): List<Permission> {
        log.info("Fetching from database or cache miss.")
        return permissionRepository.findAll()
    }

    @Transactional
    fun create(permission: Permission): Permission {
        permission.status = BaseStatus.ENABLED
        return permissionRepository.save(permission)
    }

    @Transactional
    fun update(
        id: Long,
        permission: Permission,
    ): Permission {
        val existingPermission = findById(id) ?: throw IllegalArgumentException("Permission not found")
        existingPermission.apply {
            name = permission.name
            description = permission.description
            resource = permission.resource
            parentId = permission.parentId
            type = permission.type
            icon = permission.icon
        }
        return permissionRepository.save(existingPermission)
    }

    @Transactional
    fun delete(id: Long) {
        val permission = findById(id) ?: throw IllegalArgumentException("Permission not found")
        permission.status = BaseStatus.DISABLED
        permissionRepository.save(permission)
    }

    companion object {
        private val log = KotlinLogging.logger {}
    }
}
