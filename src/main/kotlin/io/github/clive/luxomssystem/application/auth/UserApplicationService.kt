package io.github.clive.luxomssystem.application.auth

import io.github.clive.luxomssystem.application.supplier.SupplierApplicationService
import io.github.clive.luxomssystem.common.PageResponse
import io.github.clive.luxomssystem.common.enums.BaseStatus
import io.github.clive.luxomssystem.common.enums.BusinessType
import io.github.clive.luxomssystem.common.enums.PricingType
import io.github.clive.luxomssystem.common.exception.UnverifiedException
import io.github.clive.luxomssystem.common.toResponse
import io.github.clive.luxomssystem.common.utils.Jwt
import io.github.clive.luxomssystem.domain.doanload.auth.Business
import io.github.clive.luxomssystem.domain.doanload.auth.User
import io.github.clive.luxomssystem.facade.auth.UserController
import io.github.clive.luxomssystem.facade.supplier.request.CreateSupplierRequest
import io.github.clive.luxomssystem.infrastructure.config.auth.UserContextHolder
import io.github.clive.luxomssystem.infrastructure.repository.jpa.UserRepository
import io.github.clive.luxomssystem.infrastructure.repository.jpa.auth.BusinessRepository
import io.github.clive.luxomssystem.infrastructure.repository.jpa.auth.RoleRepository
import io.github.clive.luxomssystem.infrastructure.repository.redis.UserRedisRepository
import io.github.clive.luxomssystem.nextId
import org.springframework.data.domain.Pageable
import org.springframework.data.repository.findByIdOrNull
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional

@Service
class UserApplicationService(
    private val repository: UserRepository,
    private val roleRepository: RoleRepository,
    private val businessRepository: BusinessRepository,
    private val userRedisRepository: UserRedisRepository,
    private val permissionApplicationService: PermissionApplicationService,
    private val supplierApplicationService: SupplierApplicationService,
) {
    data class UserInfo(
        val id: Long,
        val name: String,
        val account: String,
        val roleId: Long?,
        val permissionNames: List<String>,
        val pricingType: PricingType,
        val supplierId: Long? = null,
        val bizName: String? = null,
        val bizType: BusinessType,
        val subscribeFrom: Long? = null,
        val subscribeTo: Long? = null,
    ) {
        companion object {
            fun of(
                user: User,
                permissionNames: List<String>,
                supplierId: Long?,
                business: Business,
            ) = UserInfo(
                id = user.id,
                name = user.name,
                account = user.account,
                roleId = user.roleId,
                permissionNames = permissionNames,
                supplierId = supplierId,
                pricingType = business.pricingType,
                bizName = business.name,
                bizType = business.type,
                subscribeFrom = business.subscribeFrom,
                subscribeTo = business.subscribeTo,
            )
        }
    }

    fun login(
        account: String,
        password: String,
    ): String {
        val user =
            repository.findByAccountAndPassword(account, password)
                ?: throw IllegalArgumentException("账号密码不正确")
        val token = Jwt.createToken(user.id)
        userRedisRepository.saveUser(user, token)
        return token
    }

    fun logout() {
        val id = UserContextHolder.user!!.id
        val user = repository.findByIdOrNull(id) ?: throw IllegalArgumentException("用户不存在")
        userRedisRepository.deleteUserByToken(user, UserContextHolder.user!!.token)
    }

    fun userInfo() =
        repository.findByIdOrNull(UserContextHolder.user!!.id)?.let {
            val business = businessRepository.findByIdOrNull(it.bizId) ?: throw UnverifiedException
            if (business.status == BaseStatus.DISABLED) {
                throw UnverifiedException
            }
            if (it.status == BaseStatus.DISABLED) {
                throw UnverifiedException
            }
            it.roleId?.let { roleId ->
                val role = roleRepository.findByIdOrNull(roleId) ?: throw IllegalArgumentException("角色不存在")
                val map = permissionApplicationService.findAll().associateBy { it.id }
                UserInfo.of(
                    user = it,
                    permissionNames = role.permissions.map { map[it]!!.resource },
                    UserContextHolder.user!!.supplierId,
                    business,
                )
            }
        }

    @Transactional(rollbackFor = [Exception::class])
    fun createUser(request: UserController.CreateUserCmd): User {
        // name and account should be unique
        if (repository.existsByName(request.name)) {
            throw IllegalArgumentException("用户名已存在")
        }
        if (repository.existsByAccount(request.account)) {
            throw IllegalArgumentException("账号已存在")
        }
        var supplierId: Long? = null

        if (request.supplierName != null) {
            val res =
                supplierApplicationService.createSupplier(
                    CreateSupplierRequest(
                        name = request.supplierName,
                        phone = request.supplierPhone ?: "",
                        description = request.supplierDescription ?: "",
                    ),
                )
            supplierId = res.id
        }

        val role = roleRepository.findByIdOrNull(request.roleId) ?: throw IllegalArgumentException("角色不存在")
        return User(
            nextId(),
            request.name,
            request.account,
            request.password,
            roleId = request.roleId,
            roleName = role.name,
            roleLimit = role.limit,
            supplierId = supplierId,
            bizId = request.bizId ?: UserContextHolder.user!!.bizId,
        ).also {
            repository.saveAndFlush(it)
        }
    }

    @Transactional(rollbackFor = [Exception::class])
    fun changePassword(
        oldPasswd: String,
        newPasswd: String,
    ) {
        val id = UserContextHolder.user!!.id
        val user = repository.findByIdOrNull(id) ?: throw IllegalArgumentException("用户不存在")
        if (user.password != oldPasswd) {
            throw IllegalArgumentException("原密码不正确")
        }
        user.password = newPasswd
        repository.saveAndFlush(user)
        userRedisRepository.deleteAllUserByToken(user)
    }

    fun batchDelete(ids: List<Long>) {
        val users = repository.findAllById(ids)
        users.mapNotNull { it.supplierId }.map {
            // todo
//            supplierApplicationService.deleteSupplier(it)
        }
        repository.deleteAll(users)
    }

    fun changeRole(
        userId: Long,
        roleId: Long,
    ) {
        repository.findByIdOrNull(userId)?.apply {
            this.roleId = roleId
            repository.saveAndFlush(this)
        } ?: throw IllegalArgumentException("用户不存在")
    }

    fun page(withPage: Pageable): PageResponse<User> =
        repository
            .findByBizId(UserContextHolder.user!!.bizId, withPage)
            .map {
                it.password = ""
                it
            }.toResponse()
}
