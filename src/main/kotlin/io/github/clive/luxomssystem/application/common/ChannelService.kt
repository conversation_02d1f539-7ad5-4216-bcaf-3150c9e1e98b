package io.github.clive.luxomssystem.application.common

import io.github.clive.luxomssystem.domain.channel.Channel
import io.github.clive.luxomssystem.infrastructure.repository.jpa.ChannelRepository
import org.springframework.data.repository.findByIdOrNull
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional

@Service
class ChannelService(
    private val channelRepository: ChannelRepository,
) {
    fun list() = channelRepository.findByEnabled(true).sortedBy { it.name }

    fun findById(id: Long): Channel? = channelRepository.findByIdOrNull(id)

    @Transactional
    fun updateChannel(id: Long, channel: Channel): Channel {
        val existingChannel = findById(id) ?: throw NoSuchElementException("Channel not found with id: $id")
        existingChannel.apply {
            displayName = channel.displayName
            methodCode = channel.methodCode
            methodName = channel.methodName
            enabled = channel.enabled
            iossNumber = channel.iossNumber
        }
        return channelRepository.save(existingChannel)
    }
}
