package io.github.clive.luxomssystem.application.inventory.supplierSpu

import io.github.clive.luxomssystem.application.inventory.suppliersku.SupplierSkuApplicationService
import io.github.clive.luxomssystem.common.PageResponse
import io.github.clive.luxomssystem.common.enums.BaseStatus
import io.github.clive.luxomssystem.common.toPageable
import io.github.clive.luxomssystem.common.toResponse
import io.github.clive.luxomssystem.domain.spu.event.SPUDeletedEvent
import io.github.clive.luxomssystem.domain.spu.event.SPUUpdatedEvent
import io.github.clive.luxomssystem.domain.supplier.event.SupplierDeletedEvent
import io.github.clive.luxomssystem.domain.supplierSpu.domainservice.SupplierSpuDomainService
import io.github.clive.luxomssystem.domain.supplierSpu.event.SupplierSpuCreatedEvent
import io.github.clive.luxomssystem.domain.supplierSpu.event.SupplierSpuDeletedEvent
import io.github.clive.luxomssystem.domain.supplierSpu.model.SupplierSpu
import io.github.clive.luxomssystem.domain.suppliersku.model.SupplierSku // Added import
import io.github.clive.luxomssystem.facade.supplierSpu.request.CreateSupplierSpuRequest
import io.github.clive.luxomssystem.facade.supplierSpu.request.PageSupplierSpuRequest
import io.github.clive.luxomssystem.facade.supplierSpu.request.UpdateSupplierSpuRequest
import io.github.clive.luxomssystem.facade.supplierSpu.response.SupplierSpuAndSkuResponse
import io.github.clive.luxomssystem.facade.supplierSpu.response.SupplierSpuResponse
import io.github.clive.luxomssystem.facade.supplierSpu.response.SupplierSpuWithSpuDTO
import io.github.clive.luxomssystem.facade.suppliersku.request.CreateSupplierSkuRequest
import io.github.clive.luxomssystem.facade.suppliersku.request.UpdateSupplierSkuRequest
import io.github.clive.luxomssystem.facade.suppliersku.response.SupplierSkuResponse
import io.github.clive.luxomssystem.infrastructure.config.auth.UserContextHolder
import io.github.clive.luxomssystem.infrastructure.repository.jpa.spu.SPURepository
import io.github.clive.luxomssystem.infrastructure.repository.jpa.spu.SkuRepository
import io.github.clive.luxomssystem.infrastructure.repository.jpa.spu.SupplierSkuRepository
import io.github.clive.luxomssystem.infrastructure.repository.jpa.spu.SupplierSpuRepository
import io.github.clive.luxomssystem.nextId
import io.github.oshai.kotlinlogging.KotlinLogging
import org.springframework.context.ApplicationEventPublisher
import org.springframework.context.event.EventListener
import org.springframework.scheduling.annotation.Async
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Propagation
import org.springframework.transaction.annotation.Transactional

/**
 * Application service for SupplierSpu operations.
 * This service orchestrates the flow of data and coordinates domain logic.
 */
@Service
class SupplierSpuApplicationService(
    private val spuRepository: SPURepository,
    private val supplierSpuRepository: SupplierSpuRepository,
    private val supplierSpuDomainService: SupplierSpuDomainService,
    private val eventPublisher: ApplicationEventPublisher,
    private val supplierSkuRepository: SupplierSkuRepository,
    private val skuRepository: SkuRepository,
    private val supplierSkuApplicationService: SupplierSkuApplicationService,
) {
    companion object { // Added companion object for logging
        private val log = KotlinLogging.logger {}
    }

    /**
     * Creates a new SupplierSpu.
     */
    @Transactional
    fun createSupplierSpu(request: CreateSupplierSpuRequest): SupplierSpuResponse {
        val supplierSpu =
            SupplierSpu(
                id = nextId(),
                bizId = UserContextHolder.user!!.bizId,
                supplierId = request.supplierId,
                systemSpuId = request.systemSpuId,
                systemSpuCode =
                    spuRepository
                        .findById(request.systemSpuId)
                        .orElseThrow { NoSuchElementException("SPU not found with id: ${request.systemSpuId}") }
                        .spuCode,
                supplierSpuCode = request.supplierSpuCode,
                status = BaseStatus.ENABLED,
                title = request.title,
                name = request.name,
                cnName = request.cnName,
                category = request.category,
            ).apply {
                createdByName = UserContextHolder.user!!.name
                updatedByName = UserContextHolder.user!!.name
            }

        supplierSpuDomainService.performInitialSetup(supplierSpu)
        supplierSpuDomainService.validate(supplierSpu)

        val savedSupplierSpu = supplierSpuRepository.saveAndFlush(supplierSpu)
        eventPublisher.publishEvent(SupplierSpuCreatedEvent(savedSupplierSpu.id!!))
        request.skus.map {
            val req =
                CreateSupplierSkuRequest(
                    supplierId = request.supplierId,
                    supplierSpuId = savedSupplierSpu.id,
                    systemSpuId = request.systemSpuId,
                    systemSkuId = it.systemSkuId,
                    systemSkuCode = it.systemSkuCode,
                    purchaseCost = it.purchaseCost,
                    purchaseCostCurrency = it.purchaseCostCurrency,
                )
            supplierSkuApplicationService.createSupplierSku(req)
        }
        return SupplierSpuResponse.fromDomain(savedSupplierSpu)
    }

    /**
     * Retrieves a SupplierSpu by its ID.
     */
    fun getSupplierSpuById(id: Long): SupplierSpuAndSkuResponse {
        val skus =
            supplierSkuRepository
                .findBySupplierSpuId(id)
                .map {
                    val sku =
                        skuRepository
                            .findById(it.systemSkuId!!)
                            .orElseThrow { NoSuchElementException("SKU not found with id: ${it.systemSkuId}") }
                    SupplierSkuResponse.fromDomain(it, sku.size, sku.color)
                }
        val supplierSpu =
            supplierSpuRepository
                .findById(id)
                .orElseThrow { NoSuchElementException("SupplierSpu not found with id: $id") }
        return SupplierSpuAndSkuResponse.fromDomain(supplierSpu, skus)
    }

    /**
     * Updates an existing SupplierSpu.
     */
    @Transactional
    fun updateSupplierSpu(
        id: Long,
        request: UpdateSupplierSpuRequest,
    ) {
        val spu =
            supplierSpuRepository.findById(id).orElseThrow { NoSuchElementException("SupplierSpu not found with id: $id") }
        if (spu.supplierSpuCode != request.supplierSpuCode) {
            spu.supplierSpuCode = request.supplierSpuCode
            spu.updatedByName = UserContextHolder.user!!.name
            spu.updatedAt = System.currentTimeMillis()
            supplierSpuRepository.save(spu)
        }
        request.skus.map {
            val cmd =
                UpdateSupplierSkuRequest(
                    it.purchaseCost,
                    it.purchaseCostCurrency,
                )
            supplierSkuApplicationService.updateSupplierSku(it.id, cmd)
        }
    }

    /**
     * Deletes a SupplierSpu by its ID.
     */
    @Transactional
    fun deleteSupplierSpu(id: Long) {
        if (supplierSpuRepository.existsById(id)) {
            supplierSpuRepository.deleteById(id)
            eventPublisher.publishEvent(SupplierSpuDeletedEvent(id))
            supplierSkuApplicationService.deleteBySupplierSpuId(id)
        } else {
            throw NoSuchElementException("SupplierSpu not found with id: $id")
        }
    }

    @Transactional
    fun deleteSupplierSpuBySupplierId(supplierId: Long) {
        supplierSpuRepository.deleteBySupplierId(supplierId)
        supplierSkuRepository.deleteBySupplierId(supplierId)
    }

    fun page(page: PageSupplierSpuRequest): PageResponse<SupplierSpuWithSpuDTO> {
        if (UserContextHolder.user?.supplierId == null) {
            return supplierSpuRepository
                .findSupplierSpuWithSpuDetails(
                    page.supplierId,
                    page.supplierSpuCode,
                    page.systemSpuCode,
                    null,
                    page.title,
                    page.toPageable(),
                ).toResponse()
        } else {
            return supplierSpuRepository
                .findSupplierSpuWithSpuDetails(
                    UserContextHolder.user?.supplierId,
                    page.supplierSpuCode,
                    page.systemSpuCode,
                    BaseStatus.ENABLED,
                    page.title,
                    page.toPageable(),
                ).toResponse()
        }
    }

    @Transactional
    @EventListener(SPUDeletedEvent::class)
    fun handleSPUDeletedEvent(event: SPUDeletedEvent) {
        supplierSpuRepository.findBySystemSpuId(event.spuId).forEach {
            deleteSupplierSpu(it.id)
        }
    }

    @Transactional
    fun changeStatus(
        id: Long,
        wantedStatus: String,
    ) {
        val supplierSpu =
            supplierSpuRepository.findById(id).orElseThrow { NoSuchElementException("SPU not found with id: $id") }
        supplierSpu.status = BaseStatus.valueOf(wantedStatus)
        supplierSpuRepository.save(supplierSpu)
        if (supplierSpu.status == BaseStatus.DISABLED) {
            supplierSkuRepository.findBySupplierSpuId(supplierSpu.id).map {
                it.status = BaseStatus.DISABLED
                supplierSkuRepository.saveAndFlush(it)
            }
        }
        if (supplierSpu.status == BaseStatus.ENABLED) {
            supplierSkuRepository.findBySupplierSpuId(supplierSpu.id).map {
                it.status = BaseStatus.ENABLED
                supplierSkuRepository.saveAndFlush(it)
            }
        }
    }

    @Async
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    @EventListener(SupplierDeletedEvent::class)
    fun handleSupplierDeletedEvent(event: SupplierDeletedEvent) {
        deleteSupplierSpuBySupplierId(event.supplierId)
    }

    fun syncPlatformSpu(id: Long) {
        val supplierSpu =
            supplierSpuRepository
                .findById(id)
                .orElseThrow { NoSuchElementException("SupplierSpu not found with id: $id") }
        val spu =
            spuRepository
                .findById(supplierSpu.systemSpuId)
                .orElseThrow { NoSuchElementException("SPU not found with id: ${supplierSpu.systemSpuId}") }
        skuRepository
            .findBySpuId(spuId = spu.id)
            .filter {
                !supplierSkuRepository.existsBySystemSkuIdAndSupplierId(it.id, supplierSpu.supplierId)
            }.map {
                val req =
                    CreateSupplierSkuRequest(
                        supplierId = supplierSpu.supplierId,
                        supplierSpuId = supplierSpu.id!!,
                        systemSpuId = supplierSpu.systemSpuId,
                        systemSkuId = it.id,
                        systemSkuCode = it.skuCode,
                        purchaseCost = it.purchaseCost,
                        purchaseCostCurrency = it.purchaseCostCurrency,
                    )
                supplierSkuApplicationService.createSupplierSku(req)
            }
    }

    @Transactional
    @EventListener(SPUUpdatedEvent::class)
    fun handleSPUUpdatedEvent(event: SPUUpdatedEvent) {
        log.info { "Starting SPUUpdatedEvent handling for systemSpuId: ${event.spuId}" }

        val systemSpu = spuRepository.findById(event.spuId).orElse(null)
        if (systemSpu == null) {
            log.error { "System SPU with id ${event.spuId} not found." }
            return
        }

        val supplierSpus = supplierSpuRepository.findBySystemSpuId(event.spuId)
        if (supplierSpus.isEmpty()) {
            log.warn { "No SupplierSpus found for systemSpuId ${event.spuId}. Skipping update." }
            return
        }

        // Fetch SKUs once for all supplier SPUs
        val systemSkus = skuRepository.findBySpuId(event.spuId)

        for (supplierSpu in supplierSpus) {
            log.info { "Processing SPUUpdatedEvent for supplierSpuId ${supplierSpu.id}, systemSpuId ${event.spuId}" }

            // Update SupplierSpu Fields
            supplierSpu.systemSpuCode = systemSpu.spuCode
            supplierSpu.title = systemSpu.title
            supplierSpu.name = systemSpu.name
            supplierSpu.cnName = systemSpu.cnName
            supplierSpu.category = systemSpu.category
            supplierSpu.updatedByName = UserContextHolder.user?.name ?: "System"
            supplierSpu.updatedAt = System.currentTimeMillis()

            val existingSupplierSkus = supplierSkuRepository.findBySupplierSpuId(supplierSpu.id!!).toMutableList()

            // Synchronize SKUs
            val skusToSave = mutableListOf<SupplierSku>()
            val skusToDelete = mutableListOf<SupplierSku>()
            val existingSupplierSkusMap = existingSupplierSkus.associateBy { it.systemSkuId }.toMutableMap()

            for (systemSku in systemSkus) {
                val supplierSkuToProcess = existingSupplierSkusMap.remove(systemSku.id)

                if (supplierSkuToProcess != null) {
                    // Update existing SupplierSku
                    supplierSkuToProcess.systemSkuCode = systemSku.skuCode
                    supplierSkuToProcess.purchaseCost = systemSku.purchaseCost // Defaulting
                    supplierSkuToProcess.purchaseCostCurrency = systemSku.purchaseCostCurrency // Defaulting
                    supplierSkuToProcess.updatedByName = UserContextHolder.user?.name ?: "System"
                    supplierSkuToProcess.updatedAt = System.currentTimeMillis()
                    supplierSkuToProcess.status = BaseStatus.ENABLED // Ensure it's enabled
                    skusToSave.add(supplierSkuToProcess)
                } else {
                    // Create new SupplierSku
                    val newSupplierSku =
                        SupplierSku(
                            id = null, // Let DB generate ID
                            bizId = supplierSpu.bizId,
                            supplierId = supplierSpu.supplierId,
                            supplierSpuId = supplierSpu.id,
                            systemSpuId = systemSpu.id,
                            systemSkuId = systemSku.id,
                            systemSkuCode = systemSku.skuCode,
                            purchaseCost = systemSku.purchaseCost,
                            purchaseCostCurrency = systemSku.purchaseCostCurrency,
                            status = BaseStatus.ENABLED,
                        ).apply {
                            createdByName = UserContextHolder.user?.name ?: "System"
                            updatedByName = UserContextHolder.user?.name ?: "System"
                        }
                    skusToSave.add(newSupplierSku)
                }
            }

            // Identify SKUs to delete
            skusToDelete.addAll(existingSupplierSkusMap.values)

            // Save Changes for this supplier SPU
            supplierSpuRepository.save(supplierSpu)
            supplierSkuRepository.saveAll(skusToSave)
            if (skusToDelete.isNotEmpty()) {
                supplierSkuRepository.deleteAll(skusToDelete)
            }

            log.info {
                "Successfully processed SPUUpdatedEvent for supplierSpuId ${supplierSpu.id}, systemSpuId ${event.spuId}. SKUs saved: ${skusToSave.size}, SKUs deleted: ${skusToDelete.size}."
            }
        }
    }
}
