package io.github.clive.luxomssystem.application.supplier

import io.github.clive.luxomssystem.common.PageReq
import io.github.clive.luxomssystem.common.PageResponse
import io.github.clive.luxomssystem.common.enums.BaseStatus
import io.github.clive.luxomssystem.common.toPageable
import io.github.clive.luxomssystem.common.toResponse
import io.github.clive.luxomssystem.domain.supplier.domainservice.SupplierDomainService
import io.github.clive.luxomssystem.domain.supplier.event.SupplierCreatedEvent
import io.github.clive.luxomssystem.domain.supplier.event.SupplierDeletedEvent
import io.github.clive.luxomssystem.domain.supplier.event.SupplierUpdatedEvent
import io.github.clive.luxomssystem.domain.supplier.model.Supplier
import io.github.clive.luxomssystem.facade.supplier.ChangePriorityRequest
import io.github.clive.luxomssystem.facade.supplier.request.CreateSupplierRequest
import io.github.clive.luxomssystem.facade.supplier.request.UpdateSupplierRequest
import io.github.clive.luxomssystem.facade.supplier.response.SupplierResponse
import io.github.clive.luxomssystem.facade.supplier.response.SupplierSelectResponse
import io.github.clive.luxomssystem.infrastructure.config.auth.UserContextHolder
import io.github.clive.luxomssystem.infrastructure.repository.jpa.SupplierRepository
import io.github.clive.luxomssystem.nextId
import org.springframework.context.ApplicationEventPublisher
import org.springframework.data.repository.findByIdOrNull
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional

@Service
class SupplierApplicationService(
    private val supplierRepository: SupplierRepository,
    private val eventPublisher: ApplicationEventPublisher,
    private val supplierDomainService: SupplierDomainService,
) {
    @Transactional
    fun createSupplier(request: CreateSupplierRequest): SupplierResponse {
        val id = nextId()
        val supplier =
            Supplier(
                id = id,
                bizId = UserContextHolder.user!!.bizId,
                name = request.name,
                phone = request.phone,
                description = request.description,
                status = BaseStatus.ENABLED,
            )
        supplierDomainService.validateSupplier(supplier)
        val savedSupplier = supplierRepository.save(supplier)
        eventPublisher.publishEvent(SupplierCreatedEvent(savedSupplier.id))
        return SupplierResponse.fromDomain(savedSupplier)
    }

    @Transactional
    fun updateSupplier(
        id: Long,
        request: UpdateSupplierRequest,
    ): SupplierResponse {
        val supplier =
            supplierRepository.findByIdOrNull(id)
                ?: throw IllegalArgumentException("Supplier not found")

        supplier.apply {
            name = request.name
            phone = request.phone
            description = request.description
            config = request.config
        }

        supplierDomainService.validateSupplier(supplier)
        val updatedSupplier = supplierRepository.save(supplier)
        eventPublisher.publishEvent(SupplierUpdatedEvent(updatedSupplier.id))
        return SupplierResponse.fromDomain(updatedSupplier)
    }

    @Transactional(readOnly = true)
    fun getSupplier(id: Long): SupplierResponse {
        val supplier =
            supplierRepository.findByIdOrNull(id)
                ?: throw IllegalArgumentException("Supplier not found")
        return SupplierResponse.fromDomain(supplier)
    }

    @Transactional
    fun deleteSupplier(id: Long) {
        val supplier =
            supplierRepository.findByIdOrNull(id)
                ?: throw IllegalArgumentException("Supplier not found")
        supplierRepository.delete(supplier)
        eventPublisher.publishEvent(SupplierDeletedEvent(id))
    }

    @Transactional
    fun enableSupplier(id: Long): SupplierResponse {
        val supplier =
            supplierRepository.findByIdOrNull(id)
                ?: throw IllegalArgumentException("Supplier not found")
        supplierDomainService.enableSupplier(supplier)
        val updatedSupplier = supplierRepository.save(supplier)
        eventPublisher.publishEvent(SupplierUpdatedEvent(updatedSupplier.id))
        return SupplierResponse.fromDomain(updatedSupplier)
    }

    @Transactional
    fun disableSupplier(id: Long): SupplierResponse {
        val supplier =
            supplierRepository.findByIdOrNull(id)
                ?: throw IllegalArgumentException("Supplier not found")
        supplierDomainService.disableSupplier(supplier)
        val updatedSupplier = supplierRepository.save(supplier)
        eventPublisher.publishEvent(SupplierUpdatedEvent(updatedSupplier.id))
        return SupplierResponse.fromDomain(updatedSupplier)
    }

    fun page(page: PageReq): PageResponse<SupplierResponse> =
        supplierRepository
            .pageQuery(
                UserContextHolder.user!!.bizId,
                null,
                page.toPageable(),
            ).map { SupplierResponse.fromDomain(it) }
            .toResponse()

    fun list(): List<SupplierSelectResponse> {
        if (UserContextHolder.user?.supplierId != null) {
            return supplierRepository.findSelectsByBizId(
                UserContextHolder.user!!.bizId,
                UserContextHolder.user?.supplierId,
            )
        } else {
            return supplierRepository.findSelectsByBizId(UserContextHolder.user!!.bizId)
        }
    }

    @Transactional
    fun changePriority(request: ChangePriorityRequest) {
        supplierRepository.findByIdOrNull(request.id)?.let {
            it.priority = request.priority
            supplierRepository.saveAndFlush(it)
        }
    }
}
