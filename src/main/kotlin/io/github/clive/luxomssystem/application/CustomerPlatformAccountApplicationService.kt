package io.github.clive.luxomssystem.application

import io.github.clive.luxomssystem.common.PageReq
import io.github.clive.luxomssystem.common.PageResponse
import io.github.clive.luxomssystem.common.enums.BaseStatus
import io.github.clive.luxomssystem.common.exception.CognitionWebException
import io.github.clive.luxomssystem.common.exception.OmsBaseErrorCode
import io.github.clive.luxomssystem.common.toPageable
import io.github.clive.luxomssystem.common.toResponse
import io.github.clive.luxomssystem.common.utils.PasswordValidator
import io.github.clive.luxomssystem.domain.CustomerPlatformAccount
import io.github.clive.luxomssystem.infrastructure.config.auth.UserContextHolder
import io.github.clive.luxomssystem.infrastructure.repository.jpa.CustomerPlatformAccountRepository
import io.github.clive.luxomssystem.infrastructure.repository.jpa.CustomerRepository
import org.springframework.data.repository.findByIdOrNull
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional

@Service
class CustomerPlatformAccountApplicationService(
    private val customerPlatformAccountRepository: CustomerPlatformAccountRepository,
    private val customerRepository: CustomerRepository,
) {

    data class CreateCustomerPlatformAccountRequest(
        val customerId: Long,
        val email: String,
        val password: String,
        val accountName: String,
        val description: String? = null,
    )

    data class UpdateCustomerPlatformAccountRequest(
        val email: String,
        val password: String?,
        val accountName: String,
        val description: String? = null,
        val status: BaseStatus,
    )

    data class CustomerPlatformAccountResponse(
        val id: Long,
        val customerId: Long,
        val customerName: String,
        val email: String,
        val accountName: String,
        val description: String,
        val status: BaseStatus,
        val lastLoginAt: Long?,
        val createdAt: Long,
        val updatedAt: Long,
    )

    data class CustomerPlatformAccountPageRequest(
        val email: String? = null,
        val accountName: String? = null,
        val customerId: Long? = null,
        val statuses: List<BaseStatus> = listOf(BaseStatus.ENABLED),
    )

    @Transactional(rollbackFor = [Exception::class])
    fun createCustomerPlatformAccount(request: CreateCustomerPlatformAccountRequest): CustomerPlatformAccountResponse {
        val userContext = UserContextHolder.user!!

        // 验证客户是否存在
        val customer = customerRepository.findByIdAndBizId(request.customerId, userContext.bizId)
            ?: throw CognitionWebException(OmsBaseErrorCode.CUSTOMER_NOT_FOUND)

        // 验证邮箱格式
        if (!PasswordValidator.validateEmail(request.email)) {
            throw IllegalArgumentException("邮箱格式不正确")
        }

        // 验证邮箱是否已存在
        if (customerPlatformAccountRepository.existsByEmailAndBizId(request.email, userContext.bizId)) {
            throw IllegalArgumentException("该邮箱已被使用")
        }

        // 验证密码强度
        val passwordValidation = PasswordValidator.validatePassword(request.password)
        if (!passwordValidation.isValid) {
            throw IllegalArgumentException("密码不符合要求：${passwordValidation.errors.joinToString(", ")}")
        }

        val account = CustomerPlatformAccount().apply {
            customerId = request.customerId
            email = request.email
            password = request.password
            accountName = request.accountName
            description = request.description ?: ""
            status = BaseStatus.ENABLED
            bizId = userContext.bizId
        }

        val savedAccount = customerPlatformAccountRepository.save(account)

        return CustomerPlatformAccountResponse(
            id = savedAccount.id,
            customerId = savedAccount.customerId,
            customerName = customer.name,
            email = savedAccount.email,
            accountName = savedAccount.accountName,
            description = savedAccount.description,
            status = savedAccount.status,
            lastLoginAt = savedAccount.lastLoginAt,
            createdAt = savedAccount.createdAt,
            updatedAt = savedAccount.updatedAt,
        )
    }

    @Transactional(rollbackFor = [Exception::class])
    fun updateCustomerPlatformAccount(
        id: Long,
        request: UpdateCustomerPlatformAccountRequest,
    ): CustomerPlatformAccountResponse {
        val userContext = UserContextHolder.user!!

        val account = customerPlatformAccountRepository.findByIdOrNull(id)
            ?: throw CognitionWebException(OmsBaseErrorCode.CUSTOMER_PLATFORM_ACCOUNT_NOT_FOUND)

        // 验证邮箱格式
        if (!PasswordValidator.validateEmail(request.email)) {
            throw IllegalArgumentException("邮箱格式不正确")
        }

        // 验证邮箱是否已被其他账户使用
        val existingAccount = customerPlatformAccountRepository.findByEmailAndBizId(request.email, userContext.bizId)
        if (existingAccount != null && existingAccount.id != id) {
            throw IllegalArgumentException("该邮箱已被其他账户使用")
        }

        // 如果提供了新密码，验证密码强度
        if (!request.password.isNullOrBlank()) {
            val passwordValidation = PasswordValidator.validatePassword(request.password)
            if (!passwordValidation.isValid) {
                throw IllegalArgumentException("密码不符合要求：${passwordValidation.errors.joinToString(", ")}")
            }
            account.password = request.password
        }

        account.email = request.email
        account.accountName = request.accountName
        account.description = request.description ?: ""
        account.status = request.status

        val savedAccount = customerPlatformAccountRepository.save(account)

        val customer = customerRepository.findByIdOrNull(savedAccount.customerId)!!

        return CustomerPlatformAccountResponse(
            id = savedAccount.id,
            customerId = savedAccount.customerId,
            customerName = customer.name,
            email = savedAccount.email,
            accountName = savedAccount.accountName,
            description = savedAccount.description,
            status = savedAccount.status,
            lastLoginAt = savedAccount.lastLoginAt,
            createdAt = savedAccount.createdAt,
            updatedAt = savedAccount.updatedAt,
        )
    }

    fun getCustomerPlatformAccount(id: Long): CustomerPlatformAccountResponse {
        val account = customerPlatformAccountRepository.findByIdOrNull(id)
            ?: throw CognitionWebException(OmsBaseErrorCode.CUSTOMER_PLATFORM_ACCOUNT_NOT_FOUND)

        val customer = customerRepository.findByIdOrNull(account.customerId)!!

        return CustomerPlatformAccountResponse(
            id = account.id,
            customerId = account.customerId,
            customerName = customer.name,
            email = account.email,
            accountName = account.accountName,
            description = account.description,
            status = account.status,
            lastLoginAt = account.lastLoginAt,
            createdAt = account.createdAt,
            updatedAt = account.updatedAt,
        )
    }

    fun pageQuery(
        pageReq: PageReq,
        searchReq: CustomerPlatformAccountPageRequest,
    ): PageResponse<CustomerPlatformAccountResponse> {
        val userContext = UserContextHolder.user!!

        val page = customerPlatformAccountRepository.pageQuery(
            email = searchReq.email,
            accountName = searchReq.accountName,
            customerId = searchReq.customerId,
            statuses = searchReq.statuses,
            bizId = userContext.bizId,
            pageable = pageReq.toPageable(),
        )

        val customerIds = page.content.map { it.customerId }.distinct()
        val customerMap = customerRepository.findAllById(customerIds).associateBy { it.id }

        return page.map { account ->
            val customer = customerMap[account.customerId]!!
            CustomerPlatformAccountResponse(
                id = account.id,
                customerId = account.customerId,
                customerName = customer.name,
                email = account.email,
                accountName = account.accountName,
                description = account.description,
                status = account.status,
                lastLoginAt = account.lastLoginAt,
                createdAt = account.createdAt,
                updatedAt = account.updatedAt,
            )
        }.toResponse()
    }

    fun getCustomerPlatformAccounts(customerId: Long): List<CustomerPlatformAccountResponse> {
        val userContext = UserContextHolder.user!!

        val accounts = customerPlatformAccountRepository.findByCustomerIdAndBizId(customerId, userContext.bizId)
        val customer = customerRepository.findByIdOrNull(customerId)!!

        return accounts.map { account ->
            CustomerPlatformAccountResponse(
                id = account.id,
                customerId = account.customerId,
                customerName = customer.name,
                email = account.email,
                accountName = account.accountName,
                description = account.description,
                status = account.status,
                lastLoginAt = account.lastLoginAt,
                createdAt = account.createdAt,
                updatedAt = account.updatedAt,
            )
        }
    }

    @Transactional(rollbackFor = [Exception::class])
    fun deleteCustomerPlatformAccount(id: Long) {
        val account = customerPlatformAccountRepository.findByIdOrNull(id)
            ?: throw CognitionWebException(OmsBaseErrorCode.CUSTOMER_PLATFORM_ACCOUNT_NOT_FOUND)

        customerPlatformAccountRepository.delete(account)
    }

    @Transactional(rollbackFor = [Exception::class])
    fun changeStatus(id: Long, status: BaseStatus) {
        val account = customerPlatformAccountRepository.findByIdOrNull(id)
            ?: throw CognitionWebException(OmsBaseErrorCode.CUSTOMER_PLATFORM_ACCOUNT_NOT_FOUND)

        account.status = status
        customerPlatformAccountRepository.save(account)
    }
}
