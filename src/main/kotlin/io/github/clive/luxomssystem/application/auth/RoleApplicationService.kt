package io.github.clive.luxomssystem.application.auth

import io.github.clive.luxomssystem.common.enums.BaseStatus
import io.github.clive.luxomssystem.domain.doanload.auth.Role
import io.github.clive.luxomssystem.facade.auth.dto.UpdateRoleRequest
import io.github.clive.luxomssystem.infrastructure.config.auth.UserContextHolder
import io.github.clive.luxomssystem.infrastructure.repository.jpa.auth.RoleRepository
import io.github.clive.luxomssystem.nextId
import org.springframework.data.domain.Page
import org.springframework.data.domain.Pageable
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional

@Service
class RoleApplicationService(
    private val roleRepository: RoleRepository,
) {
    fun findById(id: Long): Role? = roleRepository.findById(id).orElse(null)

    fun findAll(pageable: Pageable): Page<Role> {
        val bizId = UserContextHolder.user!!.bizId
        return roleRepository.findByBizId(bizId, pageable)
    }

    fun findAll(): List<Role> {
        val bizId = UserContextHolder.user!!.bizId
        return roleRepository.findByBizId(bizId)
    }

    fun findByBizId(bizId: Long): List<Role> = roleRepository.findByBizId(bizId)

    fun findByNameContaining(name: String): List<Role> = roleRepository.findByNameContaining(name)

    @Transactional
    fun create(role: Role): Role {
        role.id = nextId()
        role.status = BaseStatus.ENABLED
        return roleRepository.saveAndFlush(role)
    }

    @Transactional
    fun update(
        id: Long,
        req: UpdateRoleRequest,
    ): Role {
        val existingRole = findById(id) ?: throw IllegalArgumentException("Role not found")
        existingRole.apply {
            name = req.name
            permissions = req.permissions
        }
        return roleRepository.save(existingRole)
    }

    @Transactional
    fun delete(id: Long) {
        val role = findById(id) ?: throw IllegalArgumentException("Role not found")
        role.status = BaseStatus.DISABLED
        roleRepository.save(role)
    }

    @Transactional
    fun assignPermissions(
        id: Long,
        permissionIds: List<Long>,
    ): Role {
        val role = findById(id) ?: throw IllegalArgumentException("Role not found")
        role.permissions = permissionIds
        return roleRepository.save(role)
    }
}
