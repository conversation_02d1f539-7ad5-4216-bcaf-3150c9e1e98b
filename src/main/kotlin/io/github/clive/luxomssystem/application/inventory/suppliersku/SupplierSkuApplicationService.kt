package io.github.clive.luxomssystem.application.inventory.suppliersku

import io.github.clive.luxomssystem.common.PageReq
import io.github.clive.luxomssystem.common.PageResponse
import io.github.clive.luxomssystem.common.enums.BaseStatus
import io.github.clive.luxomssystem.common.toPageable
import io.github.clive.luxomssystem.domain.suppliersku.domainservice.SupplierSkuDomainService
import io.github.clive.luxomssystem.domain.suppliersku.event.SupplierSkuCreatedEvent
import io.github.clive.luxomssystem.domain.suppliersku.event.SupplierSkuDeletedEvent
import io.github.clive.luxomssystem.domain.suppliersku.event.SupplierSkuUpdatedEvent
import io.github.clive.luxomssystem.domain.suppliersku.model.SupplierSku
import io.github.clive.luxomssystem.facade.suppliersku.request.CreateSupplierSkuRequest
import io.github.clive.luxomssystem.facade.suppliersku.request.UpdateSupplierSkuRequest
import io.github.clive.luxomssystem.facade.suppliersku.response.SupplierSkuResponse
import io.github.clive.luxomssystem.infrastructure.config.auth.UserContextHolder // Added import
import io.github.clive.luxomssystem.infrastructure.repository.jpa.spu.SkuRepository // Added import
import io.github.clive.luxomssystem.infrastructure.repository.jpa.spu.SupplierSkuRepository
import io.github.clive.luxomssystem.nextId
import io.github.oshai.kotlinlogging.KotlinLogging
import org.springframework.context.ApplicationEventPublisher
import org.springframework.data.repository.findByIdOrNull
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional

@Service
class SupplierSkuApplicationService(
    private val supplierSkuRepository: SupplierSkuRepository,
    private val supplierSkuDomainService: SupplierSkuDomainService,
    private val eventPublisher: ApplicationEventPublisher,
    private val skuRepository: SkuRepository, // Added SkuRepository
) {
    @Transactional
    fun createSupplierSku(request: CreateSupplierSkuRequest): SupplierSkuResponse {
        val supplierSku =
            SupplierSku(
                id = nextId(),
                bizId = UserContextHolder.user!!.bizId,
                supplierId = request.supplierId,
                supplierSpuId = request.supplierSpuId,
                systemSpuId = request.systemSpuId,
                systemSkuId = request.systemSkuId,
                purchaseCost = request.purchaseCost,
                systemSkuCode = request.systemSkuCode,
                purchaseCostCurrency = request.purchaseCostCurrency,
                status = BaseStatus.ENABLED,
            ).apply {
                // Apply user context for audit fields
                createdByName = UserContextHolder.user!!.name
                updatedByName = UserContextHolder.user!!.name
            }

        supplierSkuDomainService.performInitialSetup(supplierSku)
        val savedSupplierSku = supplierSkuRepository.saveAndFlush(supplierSku)
        eventPublisher.publishEvent(SupplierSkuCreatedEvent(savedSupplierSku.id!!))

        return SupplierSkuResponse.fromDomain(savedSupplierSku)
    }

    @Transactional
    fun updateSupplierSku(
        id: Long,
        request: UpdateSupplierSkuRequest,
    ): SupplierSkuResponse {
        val supplierSku =
            supplierSkuRepository.findByIdOrNull(id)
                ?: throw IllegalArgumentException("SupplierSku not found with id: $id")

        supplierSku.apply {
            purchaseCost = request.purchaseCost ?: purchaseCost
            purchaseCostCurrency = request.purchaseCostCurrency ?: purchaseCostCurrency
            updatedByName = UserContextHolder.user!!.name // Update audit field
            updatedAt = System.currentTimeMillis() // Update audit field
        }

        supplierSkuDomainService.validateSupplierSku(supplierSku)
        val updatedSupplierSku = supplierSkuRepository.saveAndFlush(supplierSku)
        eventPublisher.publishEvent(SupplierSkuUpdatedEvent(updatedSupplierSku.id!!))

        return SupplierSkuResponse.fromDomain(updatedSupplierSku)
    }

    @Transactional(readOnly = true)
    fun getSupplierSku(id: Long): SupplierSkuResponse {
        val supplierSku =
            supplierSkuRepository.findByIdOrNull(id)
                ?: throw IllegalArgumentException("SupplierSku not found with id: $id")
        return SupplierSkuResponse.fromDomain(supplierSku)
    }

    @Transactional
    fun deleteSupplierSku(id: Long) {
        val supplierSku =
            supplierSkuRepository.findByIdOrNull(id)
                ?: throw IllegalArgumentException("SupplierSku not found with id: $id")
        supplierSkuRepository.delete(supplierSku)
        eventPublisher.publishEvent(SupplierSkuDeletedEvent(id))
    }

    fun deleteBySupplierSpuId(supplierSpuId: Long) {
        supplierSkuRepository.deleteBySupplierSpuId(supplierSpuId)
    }

    @Transactional(readOnly = true)
    fun pageQuery(req: PageReq): PageResponse<SupplierSkuResponse> {
        val page = supplierSkuRepository.findAll(req.toPageable())
        return PageResponse(
            content = page.content.map { SupplierSkuResponse.fromDomain(it) },
            total = page.totalElements,
            totalPages = page.totalPages,
        )
    }

    companion object {
        private val log = KotlinLogging.logger { }
    }
}

fun <T> List<List<T>>.findIntersection(): Set<T> {
    if (isEmpty()) return emptySet()

    return this
        .reduce { acc, list ->
            acc.intersect(list.toSet()).toList()
        }.toSet()
}
