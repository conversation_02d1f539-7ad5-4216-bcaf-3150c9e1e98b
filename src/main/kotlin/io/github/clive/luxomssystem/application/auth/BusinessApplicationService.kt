package io.github.clive.luxomssystem.application.auth

import io.github.clive.luxomssystem.common.exception.UnverifiedException
import io.github.clive.luxomssystem.domain.doanload.DownloadExcelColumn
import io.github.clive.luxomssystem.domain.doanload.DownloadTemplate
import io.github.clive.luxomssystem.domain.doanload.auth.Business
import io.github.clive.luxomssystem.domain.doanload.auth.Role
import io.github.clive.luxomssystem.domain.doanload.auth.SystemConfig
import io.github.clive.luxomssystem.facade.auth.BusinessController
import io.github.clive.luxomssystem.facade.auth.UserController
import io.github.clive.luxomssystem.infrastructure.channel.cos.CosRemoteService
import io.github.clive.luxomssystem.infrastructure.config.auth.UserContextHolder
import io.github.clive.luxomssystem.infrastructure.repository.jpa.DownloadTemplateRepository
import io.github.clive.luxomssystem.infrastructure.repository.jpa.SystemConfigRepository
import io.github.clive.luxomssystem.infrastructure.repository.jpa.auth.BusinessRepository
import io.github.clive.luxomssystem.nextId
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import java.time.Instant

@Service
class BusinessApplicationService(
    private val cosRemoteService: CosRemoteService,
    private val businessRepository: BusinessRepository,
    private val templateRepository: DownloadTemplateRepository,
    private val systemConfigRepository: SystemConfigRepository,
    private val roleApplicationService: RoleApplicationService,
    private val userApplicationService: UserApplicationService,
) {
    @Transactional
    fun create(req: BusinessController.CreateBusinessRequest) {
        if (UserContextHolder.user!!.name == "tao") {
            val business =
                Business(
                    name = req.businessName,
                    subscribeFrom = System.currentTimeMillis(),
                    subscribeTo = Instant.now().plus(2, java.time.temporal.ChronoUnit.DAYS).toEpochMilli(),
                ).apply {
                    createdByName = UserContextHolder.user!!.name
                    updatedByName = UserContextHolder.user!!.name
                }

            val savedBusiness = businessRepository.saveAndFlush(business)
            val role =
                roleApplicationService.create(
                    Role().apply {
                        name = "admin"
                        bizId = savedBusiness.id
                        permissions = listOf(1, 2, 3, 4, 15)
                    },
                )

            val user =
                userApplicationService.createUser(
                    UserController.CreateUserCmd(
                        name = req.userName,
                        account = req.accountName,
                        password = req.accountPassword,
                        roleId = role.id,
                        bizId = savedBusiness.id,
                    ),
                )

            systemConfigRepository.saveAndFlush(
                SystemConfig(
                    id = nextId(),
                    bizId = savedBusiness.id,
                    type = "COS",
                    createAt = System.currentTimeMillis(),
                    updateAt = System.currentTimeMillis(),
                    content =
                        SystemConfig.OssConfig(
                            domain = "https://${req.bucketName}.cos.ap-hongkong.myqcloud.com",
                            bucketName = req.bucketName,
                            regionName = "ap-hongkong",
                            tmpSecretId = "AKIDKWWdqGvYkswvwsTE2w5ZzHmYKzogO6xl",
                            tmpSecretKey = "9HZwqjyFSarPydGGTl3IYm3MjLuMyb6Y",
                        ),
                ),
            )
            templateRepository.saveAndFlush(
                DownloadTemplate(
                    id = nextId(),
                    name = "单链接模版",
                    bizId = savedBusiness.id,
                    content =
                        listOf(
                            DownloadExcelColumn("orderNo", "orderNo"),
                            DownloadExcelColumn("效果图", "url"),
                        ),
                    onDefault = true,
                ),
            )
            templateRepository.saveAndFlush(
                DownloadTemplate(
                    id = nextId(),
                    name = "双链接模版",
                    bizId = savedBusiness.id,
                    content =
                        listOf(
                            DownloadExcelColumn("orderNo", "orderNo"),
                            DownloadExcelColumn("效果图", "url"),
                            DownloadExcelColumn("设计图", "url"),
                        ),
                    onDefault = false,
                ),
            )
            cosRemoteService.flushOssClient()
        } else {
            throw UnverifiedException
        }
    }
}
