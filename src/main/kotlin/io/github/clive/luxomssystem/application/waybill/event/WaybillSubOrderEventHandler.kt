package io.github.clive.luxomssystem.application.waybill.event

import io.github.clive.luxomssystem.domain.suborder.SubOrderSplitEvent
import io.github.clive.luxomssystem.domain.waybill.model.WayBillStatus
import io.github.clive.luxomssystem.infrastructure.repository.jpa.WaybillRepository
import io.github.oshai.kotlinlogging.KotlinLogging
import org.springframework.context.event.EventListener
import org.springframework.stereotype.Component

@Component
class WaybillSubOrderEventHandler(
    private val waybillRepository: WaybillRepository,
) {
    @EventListener(SubOrderSplitEvent::class)
    fun handleSubOrderSplitEvent(event: SubOrderSplitEvent) {
        val startTime = System.currentTimeMillis()

        log.info {
            """处理子订单拆分事件开始 | OrderNo: ${event.orderNo} 
            | EventId: ${event.id} """.trimMargin()
        }

        try {
            waybillRepository.findByOrderNo(event.orderNo)?.let { waybill ->
                val oldStatus = waybill.status
                waybill.status = WayBillStatus.CANCELLED
                waybill.cancelReason = "订单${event.orderNo}拆分子单"
                waybillRepository.saveAndFlush(waybill)

                log.info {
                    """运单状态更新成功 | OrderNo: ${event.orderNo} 
                    | WaybillId: ${waybill.id}
                    | OldStatus: $oldStatus 
                    | NewStatus: ${waybill.status}
                    | ProcessingTime: ${System.currentTimeMillis() - startTime}ms
                    """.trimMargin()
                }
            } ?: run {
                log.warn {
                    """未找到对应运单 | OrderNo: ${event.orderNo} 
                    | ProcessingTime: ${System.currentTimeMillis() - startTime}ms
                    """.trimMargin()
                }
            }
        } catch (e: Exception) {
            log.error {
                """运单处理异常 | OrderNo: ${event.orderNo} 
                | ErrorMessage: ${e.message}
                | ProcessingTime: ${System.currentTimeMillis() - startTime}ms
                """.trimMargin()
            }
            throw e
        }
    }

    companion object {
        private val log = KotlinLogging.logger {}
    }
}
