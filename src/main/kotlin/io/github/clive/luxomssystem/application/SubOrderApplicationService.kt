package io.github.clive.luxomssystem.application

import io.github.clive.luxomssystem.application.mainorder.MainOrderCreatedEventHandler
import io.github.clive.luxomssystem.application.waybill.WaybillApplicationService
import io.github.clive.luxomssystem.common.PageResponse
import io.github.clive.luxomssystem.common.enums.SubOrderStatus
import io.github.clive.luxomssystem.common.toPageable
import io.github.clive.luxomssystem.common.toResponse
import io.github.clive.luxomssystem.domain.SubOrder
import io.github.clive.luxomssystem.domain.doanload.event.MainOrderEvent
import io.github.clive.luxomssystem.domain.suborder.SubOrderSplitEvent
import io.github.clive.luxomssystem.domain.supplierOrder.event.SupplierOrderEvent
import io.github.clive.luxomssystem.domain.supplierOrder.model.SupplierOrderStatus
import io.github.clive.luxomssystem.domain.waybill.event.WaybillCancelEvent
import io.github.clive.luxomssystem.domain.waybill.event.WaybillCompletedEvent
import io.github.clive.luxomssystem.domain.waybill.event.WaybillPendingEvent
import io.github.clive.luxomssystem.domain.waybill.model.WayBillStatus
import io.github.clive.luxomssystem.facade.BatchUpdateOrdersChannelRequest
import io.github.clive.luxomssystem.facade.MergeSubOrderRequest
import io.github.clive.luxomssystem.facade.SubOrderController
import io.github.clive.luxomssystem.facade.SubOrderSelectSupplierRequest
import io.github.clive.luxomssystem.facade.order.req.PageQuerySubOrderRequest
import io.github.clive.luxomssystem.infrastructure.config.auth.UserContextHolder
import io.github.clive.luxomssystem.infrastructure.repository.jpa.ChannelRepository
import io.github.clive.luxomssystem.infrastructure.repository.jpa.MainOrderRepository
import io.github.clive.luxomssystem.infrastructure.repository.jpa.OrderImageTaskRepository
import io.github.clive.luxomssystem.infrastructure.repository.jpa.SubOrderRepository
import io.github.clive.luxomssystem.infrastructure.repository.jpa.SupplierRepository
import io.github.clive.luxomssystem.infrastructure.repository.jpa.getSubOrderStatusCounts
import io.github.clive.luxomssystem.infrastructure.repository.jpa.spu.SPURepository
import io.github.clive.luxomssystem.infrastructure.repository.jpa.spu.SkuRepository
import io.github.oshai.kotlinlogging.KotlinLogging
import org.springframework.context.ApplicationEventPublisher
import org.springframework.context.event.EventListener
import org.springframework.data.repository.findByIdOrNull
import org.springframework.scheduling.annotation.Async
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Propagation
import org.springframework.transaction.annotation.Transactional
import org.springframework.transaction.event.TransactionalEventListener
import java.time.ZoneOffset
import java.util.UUID

@Service
class SubOrderApplicationService(
    private val eventBus: ApplicationEventPublisher,
    private val subOrderRepository: SubOrderRepository,
    private val downTaskRepository: OrderImageTaskRepository,
    private val supplierRepository: SupplierRepository,
    private val channelRepository: ChannelRepository,
    private val mainOrderRepository: MainOrderRepository,
    private val waybillApplicationService: WaybillApplicationService,
    private val mainOrderCreatedEventHandler: MainOrderCreatedEventHandler,
    private val skuRepository: SkuRepository,
    private val sPURepository: SPURepository,
) {
    fun pageQuery(req: PageQuerySubOrderRequest): PageResponse<SubOrder> {
        val orderNos =
            if (req.orderNos != null) {
                req.orderNos!!.lines()
            } else {
                emptyList()
            }
        val customerNos =
            if (req.customerOrderNos != null) {
                req.customerOrderNos!!.lines()
            } else {
                emptyList()
            }
        if (req.hasAssignedChannel == null) {
            return subOrderRepository
                .pageQuery(
                    req.orderNo,
                    req.customerId,
                    req.status,
                    req.orderId,
                    req.fileName,
                    req.createdBy,
                    req.createdAtFrom
                        ?.atStartOfDay()
                        ?.toInstant(ZoneOffset.UTC)
                        ?.toEpochMilli(),
                    req.createdAtTo
                        ?.plusDays(1)
                        ?.atStartOfDay()
                        ?.toInstant(ZoneOffset.UTC)
                        ?.toEpochMilli(),
                    req.toPageable(),
                    orderNos,
                    req.country,
                    req.customerOrderNo,
                    customerNos,
                ).toResponse()
        } else {
            if (req.hasAssignedChannel!!) {
                return subOrderRepository
                    .pageQueryWithSelectChannel(
                        req.orderNo,
                        req.customerId,
                        req.status,
                        req.orderId,
                        req.fileName,
                        req.createdBy,
                        req.createdAtFrom
                            ?.atStartOfDay()
                            ?.toInstant(ZoneOffset.UTC)
                            ?.toEpochMilli(),
                        req.createdAtTo
                            ?.plusDays(1)
                            ?.atStartOfDay()
                            ?.toInstant(ZoneOffset.UTC)
                            ?.toEpochMilli(),
                        req.toPageable(),
                        orderNos,
                        req.country,
                        req.customerOrderNo,
                        customerNos,
                    ).toResponse()
            } else {
                return subOrderRepository
                    .pageQueryWithNoSelectChannel(
                        req.orderNo,
                        req.customerId,
                        req.status,
                        req.orderId,
                        req.fileName,
                        req.createdBy,
                        req.createdAtFrom
                            ?.atStartOfDay()
                            ?.toInstant(ZoneOffset.UTC)
                            ?.toEpochMilli(),
                        req.createdAtTo
                            ?.plusDays(1)
                            ?.atStartOfDay()
                            ?.toInstant(ZoneOffset.UTC)
                            ?.toEpochMilli(),
                        req.toPageable(),
                        orderNos,
                        req.country,
                        req.customerOrderNo,
                        customerNos,
                    ).toResponse()
            }
        }
    }

//    fun simplePageQuery(req: PageQuerySubOrderRequest): PageResponse<SubOrderDTO> {
//        return pageQuery(req).apply {
//           content = content.map { SubOrderDTO(it.id,it.orderNo)}
//        }
//    }

    @Transactional
    fun update(req: SubOrderController.UpdateSubOrderRequest) {
        subOrderRepository.findByIdOrNull(req.id)?.apply {
            log.info {
                "更新子订单 $orderNo (status:$status) designUrl: ${req.designUrl} effectUrl: ${req.effectUrl} size: ${req.size} color: ${req.color}"
            }

            this.designUrl = req.designUrl
            this.effectUrl = req.effectUrl

            if (status == SubOrderStatus.FAILED) {
                // 失败状态,可以编辑产品信息

                this.product.size = req.size
                this.product.color = req.color

                // 尝试重新匹配sku
                val skuCode = product.determineSkuCode()
                if (skuCode != null) {
                    skuRepository.findBySkuCode(skuCode)?.let {
                        log.info { "子订单 $orderNo 更新,并重新匹配到sku: $skuCode" }
                        val spu = sPURepository.findByIdOrNull(it.spuId) ?: throw Exception("未知的spu：${it.spuId}")
                        product.update(it, spu)
                    } ?: run {
                        log.info { "子订单 $orderNo 更新,但无法找到对应的sku: $skuCode" }
                    }
                } else {
                    log.debug { "子订单 $orderNo 更新,但仍然无法构建sku code" }
                }
            }

            this.updatedBy = UserContextHolder.user!!.id
            this.updatedByName = UserContextHolder.user!!.name
            this.updatedAt = System.currentTimeMillis()

            subOrderRepository.saveAndFlush(this)
        }
    }

    @Transactional
    fun retryDownload(id: Long) {
    }

    fun deleteSubOrder(id: Long) {
        subOrderRepository.deleteById(id)
    }

    @Transactional(rollbackFor = [Exception::class])
    fun cancelSubOrder(id: Long) {
        val subOrder = subOrderRepository.findByIdOrNull(id) ?: throw IllegalArgumentException("子订单不存在")
        if (subOrder.status == SubOrderStatus.FAILED ||
            (subOrder.status == SubOrderStatus.SUPPLIER_MATCHED && subOrder.waybillStatus == WayBillStatus.FAILED)
        ) {
            subOrder.status = SubOrderStatus.CANCELLED

            subOrder.orderNo?.let { waybillApplicationService.cancelWaybillByOrderNo(it, "子订单取消", errorOnNotFound = false) }

            subOrderRepository.saveAndFlush(subOrder)
        } else {
            throw IllegalStateException("现在仅在匹配失败或面单生成失败时可以取消")
        }
    }

    @Transactional
    @EventListener(MainOrderEvent.MainOrderDeletedEvent::class)
    fun handle(event: MainOrderEvent.MainOrderDeletedEvent) {
        log.info { "Main order deleted, deleting sub orders: ${event.id}" }
        subOrderRepository.deleteByParentId(event.id)
    }

    @Async
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    @TransactionalEventListener(SupplierOrderEvent.MatchedSupplier::class)
    fun handle(event: SupplierOrderEvent.MatchedSupplier) {
        subOrderRepository.findByOrderNo(event.orderNo)?.let {
            it.supplierOrderStatus = SupplierOrderStatus.MATCHED_SUPPLY
            subOrderRepository.saveAndFlush(it)
        }
    }

    fun deleteSubOrderBatch(ids: List<Long>) {
        subOrderRepository.deleteAllById(ids)
    }

    fun getSubOrderStatusCounts(selection: Int): List<SubOrderRepository.SubOrderStatusCount> {
        val now = System.currentTimeMillis()
        val startDate =
            when (selection) {
                0 -> now - 24 * 60 * 60 * 1000 // 1 day ago
                1 -> now - 3 * 24 * 60 * 60 * 1000 // 3 days ago
                2 -> now - 7 * 24 * 60 * 60 * 1000 // 7 days ago
                else -> throw IllegalArgumentException("Invalid selection. Must be 0, 1, or 2.")
            }

        val queryResults = subOrderRepository.getSubOrderStatusCounts(startDate, UserContextHolder.user!!.bizId)
        // 创建一个包含所有状态的 Map，初始计数为 0
        val allStatusCounts = SubOrderStatus.entries.associateWith { 0L }.toMutableMap()

        // 用查询结果更新 Map
        queryResults.forEach { (status, count) ->
            allStatusCounts[status] = count
        }

        // 将 Map 转换为 List<StatusCount>
        return allStatusCounts.map { (status, count) ->
            SubOrderRepository.SubOrderStatusCount(status, count)
        }
    }

    @Transactional
    fun selectSupplierForSubOrder(req: SubOrderSelectSupplierRequest) {
        val supplier =
            supplierRepository.findByIdOrNull(req.supplierId) ?: throw IllegalArgumentException("不存在的供应商")
        subOrderRepository.findAllById(req.subOrderIds).forEach {
            it.product.supplierId = req.supplierId
            it.product.supplierName = supplier.name
            subOrderRepository.saveAndFlush(it)
        }
    }

    @Transactional
    fun removeImg(
        id: Long,
        imgUrl: String,
    ) {
        subOrderRepository.findByIdOrNull(id)?.apply {
            subOrderRepository.saveAndFlush(this)
        }
    }

    fun getImgUrls(orderNo: String): List<String> = downTaskRepository.findByOrderNo(orderNo).map { it.uploadUrl }

    fun merge(req: MergeSubOrderRequest) {
        val subOrders = subOrderRepository.findAllById(req.ids)
        // check if all suborders have the same supplier

        if (subOrders.map { it.product.supplierId }.toSet().size > 1) {
            throw IllegalArgumentException("不允许合并不同供应商的子订单")
        }

        val relationShip = UUID.randomUUID().toString()
        val allOrder =
            subOrders.map {
                it.shipping =
                    it.shipping.apply {
                        this.wayBillRelation = relationShip
                    }
                it
            }
        subOrderRepository.saveAll(allOrder)
    }

    @EventListener(WaybillPendingEvent::class)
    @Transactional
    fun handle(event: WaybillPendingEvent) {
        log.info { "Waybill pending event received: ${event.orderNos}" }
        val orderList = event.orderNos.split(",")

        subOrderRepository.findByOrderNoIn(orderList).forEach {
            if (it.status == SubOrderStatus.CANCELLED) {
                // 子订单取消的状态下收到运单pending是运单在发起重试,这里要恢复到正常状态
                it.status = SubOrderStatus.SUPPLIER_MATCHED
            }

            it.waybillStatus = WayBillStatus.PENDING
            subOrderRepository.saveAndFlush(it)
        }
    }

    @EventListener(WaybillCompletedEvent::class)
    @Transactional
    fun handle(event: WaybillCompletedEvent) {
        log.info { "Waybill complete event received: ${event.orderNos}" }
        val orderList = event.orderNos.split(",")

        subOrderRepository.findByOrderNoIn(orderList).forEach {
            it.waybillStatus = WayBillStatus.COMPLETED
            subOrderRepository.saveAndFlush(it)
        }
    }

    @EventListener(WaybillCancelEvent::class)
    @Transactional
    fun handle(event: WaybillCancelEvent) {
        log.info { "Waybill cancel event received: ${event.orderNos}" }
        val orderList = event.orderNos.split(",")

        subOrderRepository.findByOrderNoIn(orderList).forEach {
            if (it.status == SubOrderStatus.CANCELLED) {
                return@forEach
            }

            it.waybillStatus = WayBillStatus.CANCELLED

            // 运单取消的时候连带子订单一起取消
            it.status = SubOrderStatus.CANCELLED

            subOrderRepository.saveAndFlush(it)
        }
    }

    fun batchUpdateOrdersChannel(req: BatchUpdateOrdersChannelRequest) {
        val subOrders =
            subOrderRepository.findAllById(req.ids).map { subOrder ->
                subOrder.shipping.shipMethod = req.method
                subOrder.shipping.channel = req.channel
                subOrder
            }
        subOrderRepository.saveAllAndFlush(subOrders)
    }

    @Transactional
    fun split(reqs: List<Map<Long, Int>>) {
        // 1. 在方法开始添加入参日志
        log.info { "开始订单拆分处理 | 请求数量: ${reqs.size} | 请求详情: $reqs" }

        val oldMergedKSubOrders = reqs.flatMap { it.keys }.toSet()
        val subOrders = subOrderRepository.findAllById(oldMergedKSubOrders)

        // 2. 添加查询结果日志
        log.debug { "查询子订单结果 | 预期数量: ${oldMergedKSubOrders.size} | 实际数量: ${subOrders.size}" }

        if (subOrders.size != oldMergedKSubOrders.size) {
            log.error { "子订单数量不匹配 | 预期数量: ${oldMergedKSubOrders.size} | 实际数量: ${subOrders.size} | 请求订单IDs: $oldMergedKSubOrders" }
            throw IllegalArgumentException("子订单数量不匹配")
        }

        // 3. 记录合并订单校验
        val wayBillRelations = subOrders.map { it.shipping.wayBillRelation }.toSet()
        val totalQty = subOrders.sumOf { it.product.qty }
        log.debug { "合并订单校验 | 运单关系数: ${wayBillRelations.size} | 总数量: $totalQty" }

        if (wayBillRelations.size == 1 && totalQty == 1) {
            log.warn { "非法拆分操作 | 运单关系: ${wayBillRelations.first()} | 总数量: $totalQty" }
            throw IllegalArgumentException("不允许非合并订单进行拆分")
        }

        // 4. 记录数据库查询结果
        val first = subOrders.first()
        val dbMergedSubOrders =
            subOrderRepository.findByShipping_WayBillRelationAndParentId(
                first.shipping.wayBillRelation!!,
                first.parentId,
            )
        log.debug {
            "查询合并订单 | WayBillRelation: ${first.shipping.wayBillRelation} | BizId: ${first.bizId} | 结果数量: ${dbMergedSubOrders.size}"
        }

        // 5. ID匹配检查日志
        val dbMergedSubOrderIds = dbMergedSubOrders.map { it.id }.toSet()
        val subOrderIds = subOrders.map { it.id }.toSet()

        if (dbMergedSubOrderIds != subOrderIds) {
            val missingIds = dbMergedSubOrderIds - subOrderIds
            val extraIds = subOrderIds - dbMergedSubOrderIds
            log.error {
                """订单ID不匹配 | 
                    |缺少ID: $missingIds 
                    |多余ID: $extraIds 
                    |数据库ID集合: $dbMergedSubOrderIds 
                    |请求ID集合: $subOrderIds
                """.trimMargin()
            }
            throw IllegalArgumentException("子订单ID不匹配: 缺少ID $missingIds, 多余ID $extraIds")
        }

        // 6. 主订单查询日志
        val mainOrder = mainOrderRepository.findByIdOrNull(first.parentId)
        log.debug { "查询主订单 | ParentId: ${first.parentId} | 查询结果: ${mainOrder != null}" }

        if (mainOrder == null) {
            log.error { "主订单不存在 | ParentId: ${first.parentId}" }
            throw IllegalArgumentException("主订单不存在 ${first.parentId}")
        }

//        if (mainOrder.supplierPushed){
//            throw IllegalArgumentException("主订单已推送供应商，无法拆分")
//        }

        // 7. 数量校验日志
        val totalDbQty = dbMergedSubOrders.sumOf { it.product.qty }
        val totalReqQty = reqs.flatMap { req -> req.map { (_, count) -> count } }.sum()

        log.info {
            """数量校验 | 
            |原始订单总数量: $totalDbQty 
            |拆分后总数量: $totalReqQty
            """.trimMargin()
        }

        if (totalDbQty != totalReqQty) {
            throw IllegalArgumentException("拆分后的总数量 $totalReqQty 与原始合并订单总数量 $totalDbQty 不匹配")
        }

        val subOrderLongMap: Map<Long, SubOrder> = dbMergedSubOrders.associateBy { it.id }

        val indexMap = mutableMapOf<Long, Int>()

        val newSplitOrders: List<SubOrder> =
            reqs.flatMap {
                val waybillRelationShip = UUID.randomUUID().toString()
                it.map { (id, count) ->
                    val oldSubOrder: SubOrder = subOrderLongMap[id]!!
                    val newSubOrder =
                        oldSubOrder.splitNewOrder(count, indexMap.getOrPut(oldSubOrder.id) { 0 }, waybillRelationShip)
                    indexMap[oldSubOrder.id] = indexMap.getOrDefault(oldSubOrder.id, 0) + 1
                    newSubOrder
                }
            }
        log.info {
            """订单拆分完成 | 
            |拆分订单数量: ${newSplitOrders.size} 
            |主订单ID: ${mainOrder.id}
            """.trimMargin()
        }
        try {
            subOrderRepository.saveAllAndFlush(newSplitOrders)
            log.debug { "保存拆分订单成功 | 数量: ${newSplitOrders.size}" }
            dbMergedSubOrders.map { it.status = SubOrderStatus.SPLIT }
            subOrderRepository.saveAllAndFlush(dbMergedSubOrders)

            if (mainOrder.wayBillPushed) {
                waybillApplicationService.createWayBills(newSplitOrders, mainOrder.id)
                log.debug { "推送运单成功 | 主订单ID: ${mainOrder.id}" }

                dbMergedSubOrders.forEach {
                    eventBus.publishEvent(SubOrderSplitEvent(it.id, it.orderNo!!))
                }
            }
            log.info { "订单拆分事件发布完成 | 事件数量: ${newSplitOrders.size}" }
        } catch (e: Exception) {
            log.error(e) { "订单拆分处理异常 | 异常信息: ${e.message}" }
            throw e
        }
    }

    @Transactional(rollbackFor = [Exception::class])
    fun matchSupplier(id: Long) {
        subOrderRepository.findById(id).ifPresent {
            if (it.status.onSplitStatus()) {
                throw IllegalArgumentException("订单状态是已拆分状态 无法进行匹配供应商")
            }

            val mainOrder = mainOrderRepository.findByIdOrNull(it.parentId)
            if (mainOrder?.wayBillPushed == true) {
                throw IllegalArgumentException("订单已推送运单 无法进行匹配供应商")
            }

            log.info { "匹配供应商 | 订单ID: $id | 订单号: ${it.orderNo}" }
            val matchSuccess = mainOrderCreatedEventHandler.matchSupplier(it)
            subOrderRepository.saveAndFlush(it)
            if (matchSuccess) {
                mainOrderCreatedEventHandler.assertRelationShipOrder(it)
                subOrderRepository.saveAndFlush(it)
            }
        }
    }

    companion object {
        private val log = KotlinLogging.logger { }
    }
}
