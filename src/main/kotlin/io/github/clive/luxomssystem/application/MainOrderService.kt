package io.github.clive.luxomssystem.application

import io.github.clive.luxomssystem.common.PageResponse
import io.github.clive.luxomssystem.common.enums.MainOrderType
import io.github.clive.luxomssystem.common.enums.order.MainOrderStatus
import io.github.clive.luxomssystem.common.toResponse
import io.github.clive.luxomssystem.domain.doanload.MainOrder
import io.github.clive.luxomssystem.domain.doanload.OrderImageTaskStatus
import io.github.clive.luxomssystem.domain.doanload.event.MainOrderEvent
import io.github.clive.luxomssystem.facade.download.dto.CreateMainOrderRequest
import io.github.clive.luxomssystem.facade.download.dto.MainOrderDownTaskOverView
import io.github.clive.luxomssystem.facade.download.dto.UpdateMainOrderRequest
import io.github.clive.luxomssystem.facade.download.dto.toEntity
import io.github.clive.luxomssystem.facade.download.response.MainOrderFinancialResponse
import io.github.clive.luxomssystem.facade.download.response.MainOrderStatisticsResponse
import io.github.clive.luxomssystem.common.enums.SubOrderStatus
import io.github.clive.luxomssystem.infrastructure.config.auth.UserContextHolder
import io.github.clive.luxomssystem.infrastructure.repository.jpa.MainOrderRepository
import io.github.clive.luxomssystem.infrastructure.repository.jpa.OrderImageTaskRepository
import io.github.clive.luxomssystem.infrastructure.repository.jpa.SupplierMainOrderRepository
import io.github.clive.luxomssystem.infrastructure.repository.jpa.SupplierOrderRepository
import io.github.clive.luxomssystem.infrastructure.repository.jpa.WaybillRepository
import io.github.clive.luxomssystem.infrastructure.repository.redis.RedisRateLimiter
import io.github.oshai.kotlinlogging.KotlinLogging
import org.springframework.context.ApplicationContext
import org.springframework.data.domain.PageRequest
import org.springframework.data.repository.findByIdOrNull
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import java.math.BigDecimal
import java.time.LocalDate
import java.time.ZoneOffset
import java.util.concurrent.CompletableFuture

@Service
class MainOrderService(
    private val eventBus: ApplicationContext,
    private val waybillRepository: WaybillRepository,
    private val mainOrderRepository: MainOrderRepository,
    private val orderImageTaskRepository: OrderImageTaskRepository,
    private val supplierOrderRepository: SupplierOrderRepository,
    private val supplierMainOrderRepository: SupplierMainOrderRepository,
    private val redisRateLimiter: RedisRateLimiter,
) {
    fun findAll(
        pageable: Int,
        pageSize: Int,
        fileName: String?,
        id: Long?,
        createdAtFrom: LocalDate? = null,
        createdAtTo: LocalDate? = null,
        status: List<MainOrderStatus>?,
        wayBillPushed: Boolean? = null,
        supplierPushed: Boolean? = null,
        type: MainOrderType?,
    ): PageResponse<MainOrder> =
        mainOrderRepository
            .pageQuery(
                fileName,
                id,
                createdAtFrom?.atStartOfDay()?.toInstant(ZoneOffset.UTC)?.toEpochMilli(),
                createdAtTo
                    ?.plusDays(1)
                    ?.atStartOfDay()
                    ?.toInstant(ZoneOffset.UTC)
                    ?.toEpochMilli(),
                status,
                PageRequest.of(pageable, pageSize),
                wayBillPushed,
                supplierPushed,
                UserContextHolder.user!!.bizId,
                type,
            ).toResponse()

    fun findById(id: Long): MainOrder =
        mainOrderRepository.findById(id).orElseThrow {
            NoSuchElementException("主订单未找到: $id")
        }

    @Transactional
    fun create(request: CreateMainOrderRequest): MainOrder = mainOrderRepository.save(request.toEntity())

    @Transactional
    fun update(
        id: Long,
        request: UpdateMainOrderRequest,
    ): MainOrder {
        val mainOrder = findById(id)
        return mainOrderRepository.save(
            mainOrder.copy(
                fileName = request.fileName ?: mainOrder.fileName,
                status = request.status ?: mainOrder.status,
                imgDownloadStatus = request.imgDownloadStatus ?: mainOrder.imgDownloadStatus,
            ),
        )
    }

    @Transactional
    fun deleteById(id: Long) {
        mainOrderRepository.deleteById(id)
        orderImageTaskRepository.deleteByOrderId(id)
        waybillRepository.deleteByMainOrderId(id)
        supplierOrderRepository.deleteByMainOrderId(id)
        supplierMainOrderRepository.deleteByMainOrderId(id)
        eventBus.publishEvent(MainOrderEvent.MainOrderDeletedEvent(id))
    }

    fun findTaskOverview(id: Long): MainOrderDownTaskOverView {
        val countFuture =
            CompletableFuture.supplyAsync { orderImageTaskRepository.countByMainOrderId(id) }

        val failedTasksFuture =
            CompletableFuture.supplyAsync {
                orderImageTaskRepository.findFailedTasksByOrderId(id)
            }

        val statusMap = mutableMapOf<String, Long>()
        val get = countFuture.get()
        get.forEach { statusMap[(it["status"] as OrderImageTaskStatus).name] = it["count"] as Long }

        return MainOrderDownTaskOverView(statusMap, failedTasksFuture.get())
    }

    fun retryTask(id: Long) {
        eventBus.publishEvent(MainOrderEvent.RetryOrderImageTaskEvent(id))
    }

    fun compressTask(id: Long) {
        eventBus.publishEvent(MainOrderEvent.CompressOrderImageTaskEvent(id))
    }

    @Transactional
    fun pushSupply(id: Long) {
        log.info { "推送供应商订单 | 订单号: $id" }
        val order = mainOrderRepository.findByIdOrNull(id) ?: throw IllegalArgumentException("order not found")
        order.supplierPushed = true
        if (order.wayBillPushed) {
            order.status = MainOrderStatus.COMPLETED
        }
        mainOrderRepository.save(order)
        eventBus.publishEvent(MainOrderEvent.OrderPushSupplyEvent(id))
    }

    @Transactional
    fun ntfySupply(id: Long) {
        log.info { "通知供应商订单 | 订单号: $id" }
        if (!redisRateLimiter.allowRequest("NTFY_SUPPLY:$id", 1)) {
            throw IllegalStateException("通知供应商太频繁, 请稍后再试")
        }
        // FIXME 限制先去除,等流程理通
//        val order = mainOrderRepository.findByIdOrNull(id) ?: throw IllegalArgumentException("order not found")
//        if (!order.wayBillPushed) {
//            throw IllegalArgumentException("运单未推送, 无法通知供应商")
//        }
        eventBus.publishEvent(MainOrderEvent.OrderNtfySupplyEvent(id))
    }

    fun pageForFinancial(
        customerId: Long,
        pageIndex: Int,
        pageSize: Int,
    ): PageResponse<MainOrderFinancialResponse> =
        mainOrderRepository
            .pageQueryFinancial(
                customerId = customerId,
                type = MainOrderType.ORDER,
                wayBillPushed = true,
                pageable = PageRequest.of(pageIndex, pageSize),
            ).map {
                val static = waybillRepository.getFinanceStatistic(it.id, it.bizId)
                MainOrderFinancialResponse(
                    id = it.id,
                    fileName = it.fileName,
                    createdAt = it.createdAt,
                    totalWaybillCost = static?.totalWaybillCost ?: BigDecimal.ZERO,
                    totalQuickChannelTax = static?.totalQuickChannelTax ?: BigDecimal.ZERO,
                    totalCustomerNeedPayTax = static?.totalCustomerNeedPayTax ?: BigDecimal.ZERO,
                    totalCustomerNeedPayCost = static?.totalCustomerNeedPayCost ?: BigDecimal.ZERO,
                )
            }.toResponse()

    fun getStatistics(id: Long): MainOrderStatisticsResponse {
        val bizId = UserContextHolder.user?.bizId ?: 1L

        // 查询主订单状态
        val mainOrderStatus = mainOrderRepository.getMainOrderStatus(id, bizId) ?: "UNKNOWN"

        // 查询子订单总数
        val totalSubOrders = mainOrderRepository.getTotalSubOrders(id, bizId)

        // 查询已完成子订单数
        val completedSubOrders = mainOrderRepository.getCompletedSubOrders(id, bizId, SubOrderStatus.TRACKING_DELIVERED)

        // 查询子订单状态分布
        val subOrderStatusResults = mainOrderRepository.getSubOrderStatusCounts(id, bizId)
        val subOrderStatusCounts = subOrderStatusResults.associate {
            (it[0] as SubOrderStatus).name to (it[1] as Long)
        }

        return MainOrderStatisticsResponse(
            mainOrderStatus = mainOrderStatus,
            totalSubOrders = totalSubOrders,
            completedSubOrders = completedSubOrders,
            subOrderStatusCounts = subOrderStatusCounts
        )
    }



    companion object {
        private val log = KotlinLogging.logger {}
    }
}
