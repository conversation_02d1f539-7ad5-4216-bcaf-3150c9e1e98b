package io.github.clive.luxomssystem.application.common

import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.module.kotlin.readValue
import io.github.clive.luxomssystem.domain.base.Country
import io.github.clive.luxomssystem.facade.common.SystemNameContainer
import io.github.clive.luxomssystem.infrastructure.config.auth.UserContextHolder
import io.github.clive.luxomssystem.infrastructure.repository.jpa.*
import org.springframework.stereotype.Service

@Service
class CommonApplicationService(
    private val mapper: ObjectMapper,
    private val dictRepository: DictRepository,
    private val userRepository: UserRepository,
    private val supplierRepository: SupplierRepository,
    private val customerRepository: CustomerRepository,
    private val countryRepository: CountryRepository,
) {
    fun getDictValues(type: String): String =
        dictRepository.findByType(type)?.value ?: throw NoSuchElementException("Dict not found with type: $type")

    fun getDictNames(): SystemNameContainer {
        val bizId = UserContextHolder.user!!.bizId
        return SystemNameContainer(
            userRepository.findNameByBizId(bizId).associateBy({ it.id.toString() }, { it.name }),
            supplierRepository.findNameByBizId(bizId).associateBy({ it.id.toString() }, { it.name }),
            customerRepository.findNameByBizId(bizId).associateBy({ it.id.toString() }, { it.name }),
        )
    }

    fun addDictValue(
        type: String,
        value: String,
    ) {
        val dictType =
            dictRepository.findByType(type) ?: throw NoSuchElementException("Dict not found with type: $type")

        mapper
            .readValue<List<String>>(dictType.value)
            .toMutableSet()
            .apply {
                add(value)
            }.also {
                dictType
                    .apply {
                        this.value = mapper.writeValueAsString(it)
                    }.also {
                        dictRepository.saveAndFlush(it)
                    }
            }
    }

    fun getCountries(): List<Country> = countryRepository.findAll()
}
