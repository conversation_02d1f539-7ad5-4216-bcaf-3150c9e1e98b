package io.github.clive.luxomssystem.application.inventory.customerSpu

import io.github.clive.luxomssystem.application.inventory.spu.ComboSkuDeleteEvent
import io.github.clive.luxomssystem.application.inventory.spu.ComboSkuUpdateEvent
import io.github.clive.luxomssystem.common.enums.BaseStatus
import io.github.clive.luxomssystem.common.enums.RoleLimit
import io.github.clive.luxomssystem.common.toPageable
import io.github.clive.luxomssystem.domain.customerSku.model.ComboCustomerSku
import io.github.clive.luxomssystem.domain.customerSpu.model.ComboCustomerSpu
import io.github.clive.luxomssystem.domain.spu.event.SPUDeletedEvent
import io.github.clive.luxomssystem.facade.customerSpu.ComboCustomerSpuController
import io.github.clive.luxomssystem.facade.customerSpu.ComboCustomerSpuController.ComboCustomerSpuSearchReq
import io.github.clive.luxomssystem.infrastructure.config.auth.UserContextHolder
import io.github.clive.luxomssystem.infrastructure.repository.jpa.CustomerRepository
import io.github.clive.luxomssystem.infrastructure.repository.jpa.spu.ComboCustomerSkuRepository
import io.github.clive.luxomssystem.infrastructure.repository.jpa.spu.ComboCustomerSpuRepository
import io.github.clive.luxomssystem.infrastructure.repository.jpa.spu.ComboSkuRepository
import io.github.clive.luxomssystem.infrastructure.repository.jpa.spu.SkuRepository
import io.github.clive.luxomssystem.nextId
import io.github.oshai.kotlinlogging.KotlinLogging
import org.springframework.context.event.EventListener
import org.springframework.data.domain.Page
import org.springframework.data.repository.findByIdOrNull
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import java.math.BigDecimal

@Service
class ComboCustomerSpuApplicationService(
    private val skuRepository: SkuRepository,
    private val comboSkuRepository: ComboSkuRepository,
    private val comboCustomerSpuRepository: ComboCustomerSpuRepository,
    private val comboCustomerSkuRepository: ComboCustomerSkuRepository,
    private val customerRepository: CustomerRepository,
) {
    fun pageComboCustomerCpu(req: ComboCustomerSpuSearchReq): Page<ComboCustomerSpu> =
        if (UserContextHolder.user!!.roleLimit == RoleLimit.SELF) {
            val customerIds = customerRepository.findByCreatedBy(UserContextHolder.user!!.id).map { it.id }
            if (customerIds.isEmpty()) {
                Page.empty()
            } else {
                comboCustomerSpuRepository
                    .pageQueryByCustomerIdForSelf(
                        UserContextHolder.user!!.bizId,
                        req.systemComboSpuId,
                        req.systemSpuCode?.split("\n")?.toList() ?: emptyList(),
                        req.customerId,
                        req.toPageable(),
                        customerIds,
                    )
            }
        } else {
            comboCustomerSpuRepository
                .pageQueryByCustomerId(
                    UserContextHolder.user!!.bizId,
                    req.systemComboSpuId,
                    req.systemSpuCode?.split("\n")?.toList() ?: emptyList(),
                    req.customerId,
                    req.toPageable(),
                )
        }

    fun getComboCustomerSpu(id: Long): Pair<ComboCustomerSpu, List<ComboCustomerSku>> {
        val spu =
            comboCustomerSpuRepository.findByIdOrNull(id) ?: throw IllegalArgumentException("combo customer spu id \"${id}\" not found")
        val skus = comboCustomerSkuRepository.findAllByComboCustomerSpuId(id)
        return spu to skus
    }

    @Transactional(rollbackFor = [Throwable::class])
    fun createComboCustomerSpu(req: ComboCustomerSpuController.ComboCustomerSpuCreateRequest) {
        val user = UserContextHolder.user!!
        val generateComboCustomerSpuId = nextId()
        val ccSpu =
            ComboCustomerSpu(
                id = generateComboCustomerSpuId,
                bizId = user.bizId,
                customerId = req.customerId,
                systemSpuIds = req.systemSpuIds,
                systemSpuCodes = req.systemSpuCodes,
                systemComboSpuId = req.systemComboSpuId,
                triggerDiscountQuantity = req.triggerDiscountQuantity,
                discount = req.discount,
                status = BaseStatus.ENABLED,
            ).apply {
                createdBy = user.id
                createdByName = user.name
            }
        comboCustomerSpuRepository.saveAndFlush(ccSpu)

        val ccSkus =
            req.skus.map {
                ComboCustomerSku(
                    id = nextId(),
                    bizId = user.bizId,
                    customerId = it.customerId,
                    comboCustomerSpuId = generateComboCustomerSpuId,
                    systemSpuIdsOfCombo = it.systemSpuIdsOfCombo,
                    systemSpuCodesOfCombo = it.systemSpuCodesOfCombo,
                    comboSpuId = it.comboSpuId,
                    comboSkuId = it.comboSkuId,
                    systemSpuId = it.systemSpuId,
                    systemSpuCode = it.systemSpuCode,
                    systemSkuId = it.systemSkuId,
                    systemSkuCode = it.systemSkuCode,
                    size = it.size,
                    color = it.color,
                    offlinePrice = it.offlinePrice,
                    offlinePriceCurrency = it.offlinePriceCurrency,
                    status = BaseStatus.ENABLED,
                    skuCountryPrices = it.skuCountryPrices,
                    skuPcsPrices = it.skuPcsPrices,
                    triggerDiscountQuantity = it.triggerDiscountQuantity,
                    discount = it.discount,
                ).apply {
                    createdBy = user.id
                    createdByName = user.name
                }
            }

        comboCustomerSkuRepository.saveAllAndFlush(ccSkus)
    }

    @Transactional(rollbackFor = [Throwable::class])
    fun updateComboCustomerSpu(
        id: Long,
        req: ComboCustomerSpuController.ComboCustomerSpuCreateRequest,
    ) {
        val comboCustomerSpu =
            comboCustomerSpuRepository.findByIdOrNull(id) ?: throw IllegalArgumentException("combo customer id \"${id}\" not found.")
        val user = UserContextHolder.user!!
        comboCustomerSpuRepository.saveAndFlush(
            comboCustomerSpu
                .copy(
                    triggerDiscountQuantity = req.triggerDiscountQuantity,
                    discount = req.discount,
                ).apply {
                    updatedBy = user.id
                    updatedByName = user.name
                },
        )

        val requestSkus = req.skus.associateBy { it.systemSkuId }

        val skus = comboCustomerSkuRepository.findAllByComboCustomerSpuId(id)
        val newSkus =
            skus.map {
                val newReq = requestSkus[it.systemSkuId] ?: return@map it
                it.copy(
                    offlinePrice = newReq.offlinePrice,
                    offlinePriceCurrency = newReq.offlinePriceCurrency,
                    skuCountryPrices = newReq.skuCountryPrices,
                    skuPcsPrices = newReq.skuPcsPrices,
                    triggerDiscountQuantity = newReq.triggerDiscountQuantity,
                    discount = newReq.discount,
                )
            }

        comboCustomerSkuRepository.saveAllAndFlush(newSkus)
    }

    @Transactional(rollbackFor = [Throwable::class])
    fun changeComboCustomerSpuStatus(
        id: Long,
        wantedStatus: BaseStatus,
    ) {
        comboCustomerSpuRepository.switchStatus(id, wantedStatus)
    }

    @EventListener
    @Transactional(rollbackFor = [Throwable::class])
    fun onComboSkuUpdateEvent(event: ComboSkuUpdateEvent) {
        val allSku = skuRepository.findBySpuId(event.fromSpuId)

        // 所有有的sku
        val skuCodes = allSku.map { it.skuCode }

        // 删除已经不在的sku
        comboCustomerSkuRepository.deleteAllBySystemSpuIdAndSystemSkuCodeNotIn(event.fromSpuId, skuCodes)

        // 先把受到影响的spu都查询出来
        val effectComboSpus = comboCustomerSpuRepository.findAllBySystemSpuIdsContains(event.fromSpuId)
        effectComboSpus.forEach { comboCustomerSpu ->
            val existsSkus = comboCustomerSkuRepository.findAllByComboCustomerSpuId(comboCustomerSpu.id)
            val missedSkus = skuCodes.toSet() - existsSkus.map { it.systemSkuCode }.toSet()

            val missedComboSkus = comboSkuRepository.findAllByComboSpuIdAndSkuCodeIn(comboCustomerSpu.systemComboSpuId, missedSkus)

            val newCustomerComboSku =
                missedComboSkus.map { comboSku ->
                    ComboCustomerSku(
                        id = nextId(),
                        bizId = comboCustomerSpu.bizId,
                        customerId = comboCustomerSpu.customerId,
                        comboCustomerSpuId = comboCustomerSpu.id,
                        systemSpuIdsOfCombo = comboCustomerSpu.systemSpuIds,
                        systemSpuCodesOfCombo = comboCustomerSpu.systemSpuCodes,
                        comboSpuId = comboCustomerSpu.systemComboSpuId,
                        comboSkuId = comboSku.id,
                        systemSpuId = comboSku.spuId,
                        systemSpuCode = comboSku.spuCode,
                        systemSkuId = comboSku.systemSkuId,
                        systemSkuCode = comboSku.skuCode,
                        size = comboSku.size,
                        color = comboSku.color,
                        offlinePrice = BigDecimal.ZERO,
                        offlinePriceCurrency = "USD",
                        status = BaseStatus.ENABLED,
                        skuCountryPrices = emptyMap(),
                        skuPcsPrices = emptyList(),
                        triggerDiscountQuantity = null,
                        discount = null,
                    )
                }
            log.info { "generated new ${newCustomerComboSku.size} comboCustomerSku for comboCustomerSpu[${comboCustomerSpu.id}]" }
            comboCustomerSkuRepository.saveAllAndFlush(newCustomerComboSku)
        }
    }

    @EventListener
    @Transactional(rollbackFor = [Throwable::class])
    fun onComboSkuDeleteEvent(event: ComboSkuDeleteEvent) {
        comboCustomerSpuRepository.deleteBySystemComboSpuId(event.comboSpuId)
        comboCustomerSkuRepository.deleteAllByComboSpuId(event.comboSpuId)
    }

    @EventListener
    @Transactional(rollbackFor = [Throwable::class])
    fun onSpuDelete(event: SPUDeletedEvent) {
        comboCustomerSpuRepository.deleteAllBySystemSpuIdsContains(event.spuId)
        comboCustomerSkuRepository.deleteAllBySystemSpuId(event.spuId)
    }

    @Transactional(rollbackFor = [Throwable::class])
    fun deleteComboCustomerSpu(id: Long) {
        comboCustomerSpuRepository.deleteById(id)
        comboCustomerSkuRepository.deleteAllByComboCustomerSpuId(id)
    }

    companion object {
        private val log = KotlinLogging.logger {}
    }
}
