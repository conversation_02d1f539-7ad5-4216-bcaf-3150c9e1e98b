package io.github.clive.luxomssystem.application.waybill

import com.alibaba.excel.EasyExcel
import com.alibaba.excel.annotation.ExcelProperty
import com.alibaba.excel.annotation.write.style.ColumnWidth
import io.github.clive.luxomssystem.application.SupplierOrderApplicationService
import io.github.clive.luxomssystem.application.converter.SupplierOrderConverter.toExcelModel
import io.github.clive.luxomssystem.common.PageResponse
import io.github.clive.luxomssystem.common.enums.SubOrderStatus
import io.github.clive.luxomssystem.common.enums.WaybillChannel
import io.github.clive.luxomssystem.common.enums.order.MainOrderStatus
import io.github.clive.luxomssystem.common.exception.CognitionException
import io.github.clive.luxomssystem.common.toPageable
import io.github.clive.luxomssystem.common.toResponse
import io.github.clive.luxomssystem.domain.CorrectMainOrderFinance
import io.github.clive.luxomssystem.domain.SubOrder
import io.github.clive.luxomssystem.domain.convertor.SupplierOrderConverter
import io.github.clive.luxomssystem.domain.waybill.domainservice.WaybillDomainService
import io.github.clive.luxomssystem.domain.waybill.event.WaybillCancelEvent
import io.github.clive.luxomssystem.domain.waybill.event.WaybillCompletedEvent
import io.github.clive.luxomssystem.domain.waybill.event.WaybillPendingEvent
import io.github.clive.luxomssystem.domain.waybill.model.WayBillStatus
import io.github.clive.luxomssystem.domain.waybill.model.Waybill
import io.github.clive.luxomssystem.facade.order.supplierorder.response.SupplierOrderExcelModel
import io.github.clive.luxomssystem.facade.order.waybill.*
import io.github.clive.luxomssystem.facade.order.waybill.request.UpdateWaybillRequest
import io.github.clive.luxomssystem.facade.order.waybill.request.WaybillPageRequest
import io.github.clive.luxomssystem.facade.order.waybill.response.WaybillResponse
import io.github.clive.luxomssystem.infrastructure.channel.prediction.channel.*
import io.github.clive.luxomssystem.infrastructure.channel.prediction.channel.request.WaybillRequest
import io.github.clive.luxomssystem.infrastructure.channel.prediction.channel.request.totalPrice
import io.github.clive.luxomssystem.infrastructure.config.auth.UserContextHolder
import io.github.clive.luxomssystem.infrastructure.repository.jpa.*
import io.github.clive.luxomssystem.infrastructure.repository.redis.WaybillPushRedisRepository
import io.github.oshai.kotlinlogging.KotlinLogging
import org.springframework.context.ApplicationEventPublisher
import org.springframework.data.repository.findByIdOrNull
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import java.io.ByteArrayOutputStream
import java.math.BigDecimal
import java.time.ZoneOffset

@Service
class WaybillApplicationService(
    private val waybillRepository: WaybillRepository,
    private val waybillDomainService: WaybillDomainService,
    private val eventPublisher: ApplicationEventPublisher,
    private val subOrderRepository: SubOrderRepository,
    private val ywbzPredictionRemoteService: YWBZPredictionRemoteService,
    private val yunTuPredictionRemoteService: YunTuPredictionRemoteService,
    private val shunfengPredictionRemoteService: ShunfengPredictionRemoteService,
    private val caiNiaoPredictionRemoteService: CaiNiaoPredictionRemoteService,
    private val yanWenPredictionRemoteService: YanWenPredictionRemoteService,
    private val sdhPredictionRemoteService: SdhPredictionRemoteService,
    private val fpxPredictionRemoteService: FpxPredictionRemoteService,
    private val zmPredictionRemoteService: ZmPredictionRemoteService,
    private val mainOrderRepository: MainOrderRepository,
    private val supplierOrderApplicationService: SupplierOrderApplicationService,
    private val supplierOrderRepository: SupplierOrderRepository,
    private val waybillPushRedisRepository: WaybillPushRedisRepository,
    private val channelRepository: ChannelRepository,
    private val changLianPredictionRemoteService: ChangLianPredictionRemoteService,
    private val correctMainOrderFinanceRepository: CorrectMainOrderFinanceRepository,
) {
    /** Update an existing Waybill */
    @Transactional
    fun updateWaybill(
        id: Long,
        request: UpdateWaybillRequest,
    ): WaybillResponse {
        val waybill =
            waybillRepository.findByIdOrNull(id)
                ?: throw NoSuchElementException("Waybill not found with id: $id")

        waybill.apply {
            recipient.apply {
                receiverName = request.receiverName
                country = request.country
                state = request.state
                city = request.city
                address1 = request.street
                postcode = request.postcode
                phone = request.phone
                address2 = request.street2
            }

            shipping.apply {
                waybillNo = null
                waybillLabelUrl = null
            }

            product.apply {
                hsCode = request.hsCode
                material = request.material
                weight = request.weight
                request.name?.let { this.name = it }
            }

            totalPrice = request.totalPrice
            taxNumber = request.taxNumber
            iossNumber = request.iossNumber
        }

        waybillDomainService.validateWaybill(waybill)
        val updatedWaybill = waybillRepository.saveAndFlush(waybill)

        return WaybillResponse.fromDomain(updatedWaybill)
    }

    /** Get a Waybill by ID */
    fun getWaybillById(id: Long): WaybillResponse {
        val waybill =
            waybillRepository.findByIdOrNull(id)
                ?: throw NoSuchElementException("Waybill not found with id: $id")
        return WaybillResponse.fromDomain(waybill)
    }

    /** Delete a Waybill by ID */
    @Transactional
    fun deleteWaybill(id: Long) {
        if (!waybillRepository.existsById(id)) {
            throw NoSuchElementException("Waybill not found with id: $id")
        }
        waybillRepository.deleteById(id)
    }

    /** Update Waybill status */
    @Transactional
    fun updateStatus(
        id: Long,
        newStatus: String,
    ) {
        val waybill =
            waybillRepository.findByIdOrNull(id)
                ?: throw NoSuchElementException("Waybill not found with id: $id")

        waybill.status = WayBillStatus.fromString(newStatus)
        waybillRepository.save(waybill)
    }

    fun pageQuery(req: WaybillPageRequest): PageResponse<WaybillResponse> =
        waybillRepository
            .findByBizId(
                UserContextHolder.user!!.bizId,
                req.orderNo,
                req.status.map { it.name },
                req.toPageable(),
                req.spu,
                req.mainOrderId,
                req.createdAtFrom
                    ?.atStartOfDay()
                    ?.toInstant(ZoneOffset.UTC)
                    ?.toEpochMilli(),
                req.createdAtTo
                    ?.plusDays(1)
                    ?.atStartOfDay()
                    ?.toInstant(ZoneOffset.UTC)
                    ?.toEpochMilli(),
                req.fileName,
                req.waybillNo,
                req.orderNos
                    ?.split("\n")
                    ?.filter { it.isNotEmpty() }
                    ?.joinToString(","),
                req.waybillNos?.split("\n") ?: emptyList(),
                req.country,
                req.countryCode,
            ).map { WaybillResponse.fromDomain(it) }
            .toResponse()

    @Transactional
    fun retryPush(id: Long) {
        // 重试时先清除Redis标志，允许重新推送
        log.info { "重试运单推送，已清除Redis标志 | 订单ID: $id" }
        pushWaybill(id)
    }

    @Transactional
    fun retryBatch(ids: List<Long>) {
        for (id in ids) {
            try {
                retry(id)
            } catch (e: Exception) {
                log.error(e) { "批量重试中运单失败 | 运单ID: $id | 错误信息: ${e.message}" }
                // 继续处理下一个运单，不中断批量操作
            }
        }
        log.info { "批量重试完成 | 总数: ${ids.size}" }
    }

    data class SupplierScannedCount(
        val scanned: Int,
        val total: Int,
    )

    fun countSupplierOrderScanned(ids: List<Long>): Map<Long, SupplierScannedCount> {
        val waybills = waybillRepository.findAllById(ids)
        return waybills.associate { waybill ->
            val orderNos = waybill.orderNos.split(",")
            val supplierOrders = supplierOrderRepository.findByOrderNoIn(orderNos)
            val scanned = supplierOrders.count { it.scanAt != null }
            waybill.id to SupplierScannedCount(scanned, supplierOrders.size)
        }
    }

    @Transactional
    fun retry(id: Long) {
        val waybill =
            waybillRepository.findByIdOrNull(id)
                ?: throw NoSuchElementException("Waybill not found with id: $id")
        // 检查Redis标志，防止重复处理
        if (waybillPushRedisRepository.isPendingFlagExists(id)) {
            log.warn { "运单预报被阻止，已存在处理标志 | 运单ID: $id" }
            throw CognitionException("运单执行成功30s等待中, 请勿重复操作")
        }

        // FIXME 这里限制条件先干掉
        // 这里需要检查主订单是否已经被供应商接受了,如果接受了,运单重试不应该成功,而是需要发起一个新的主订单
//        val supplierMainOrder =
//            supplierMainOrderRepository.findByMainOrderIdAndSupplierId(
//                waybill.mainOrderId,
//                waybill.product.supplierId!!,
//            )
//        if (supplierMainOrder?.accepted == true && waybill.status != WayBillStatus.COMPLETED) {
//            throw IllegalArgumentException("供应商已经接受了主订单,无法重推运单,需要重新创建主订单")
//        }

        log.info { "重推运单 | 运单ID: $id | 订单号: ${waybill.orderNos}" }
        callDeletePredication(waybill)
        eventPublisher.publishEvent(WaybillPendingEvent(id, waybill.orderNos))
    }

    fun getWayBillStatusCounts(selection: Int): List<WaybillRepository.WayBillStatusCount> {
        val now = System.currentTimeMillis()
        val startDate =
            when (selection) {
                0 -> now - 24 * 60 * 60 * 1000 // 1 day ago
                1 -> now - 3 * 24 * 60 * 60 * 1000 // 3 days ago
                2 -> now - 7 * 24 * 60 * 60 * 1000 // 7 days ago
                else -> throw IllegalArgumentException("Invalid selection. Must be 0, 1, or 2.")
            }

        val queryResults =
            waybillRepository.getWaybillStatusCounts(startDate, UserContextHolder.user!!.bizId)
        // 创建一个包含所有状态的 Map，初始计数为 0
        val allStatusCounts = WayBillStatus.entries.associateWith { 0L }.toMutableMap()

        // 用查询结果更新 Map
        queryResults.forEach { (status, count) -> allStatusCounts[status] = count }

        // 将 Map 转换为 List<StatusCount>
        return allStatusCounts.map { (status, count) ->
            WaybillRepository.WayBillStatusCount(status, count)
        }
    }

    fun deleteWaybillBatch(ids: List<Long>) {
        waybillRepository.deleteAllById(ids)
    }

    @Transactional(rollbackFor = [Exception::class])
    fun cancelWaybillByOrderNo(
        orderNo: String,
        reason: String,
        errorOnNotFound: Boolean = true,
    ) {
        val waybill =
            waybillRepository.findByOrderNo(orderNo) ?: run {
                if (errorOnNotFound) {
                    throw NoSuchElementException("Waybill not found")
                } else {
                    return
                }
            }

        waybill.status = WayBillStatus.CANCELLED
        waybill.cancelReason = reason
        waybillRepository.save(waybill)
        eventPublisher.publishEvent(WaybillCancelEvent(waybill.id, waybill.orderNos))
    }

    @Transactional(rollbackFor = [Exception::class])
    fun cancelWaybill(
        id: Long,
        reason: String,
    ) {
        val waybill =
            waybillRepository.findByIdOrNull(id)
                ?: throw NoSuchElementException("Waybill not found with id: $id")

        waybill.status = WayBillStatus.CANCELLED
        waybill.cancelReason = reason
        waybillRepository.save(waybill)

        eventPublisher.publishEvent(WaybillCancelEvent(id, waybill.orderNos))
    }

    @Transactional
    fun pushWaybill(mainOrderId: Long) {
        // 检查Redis标志，防止重复推送
        if (!waybillPushRedisRepository.trySetPushFlag(mainOrderId)) {
            log.warn { "运单推送被阻止，已存在推送标志 | 订单ID: $mainOrderId" }
            throw IllegalStateException("运单正在推送中，请勿重复操作")
        }

        try {
            val mainOrder =
                mainOrderRepository.findByIdOrNull(mainOrderId) ?: throw IllegalArgumentException("Order not found")
            mainOrder.wayBillPushed = true
            if (mainOrder.supplierPushed) {
                mainOrder.status = MainOrderStatus.COMPLETED
            }
            mainOrderRepository.save(mainOrder)

            log.info { "运单推送开始 | 订单ID: $mainOrderId | 订单状态: ${mainOrder.status} | 供应商已推送: ${mainOrder.supplierPushed}" }

            val allSubOrders =
                subOrderRepository
                    .findByParentIdOrderByIdAsc(mainOrderId)

            // FIXME 限制先去除,等流程理通
//        if (allSubOrders.any { it.status == SubOrderStatus.FAILED }) {
//            throw IllegalArgumentException("存在失败的子订单, 无法推送运单")
//        }

            val needCreateWaybillSubOrders = allSubOrders.filter { it.status == SubOrderStatus.SUPPLIER_MATCHED }

            createWayBills(needCreateWaybillSubOrders, mainOrderId)

            log.info { "运单推送成功完成 | 订单ID: $mainOrderId" }
        } catch (e: Exception) {
            log.error(e) { "运单推送失败 | 订单ID: $mainOrderId | 错误信息: ${e.message}" }
            // 推送失败时清除Redis标志，允许重试
            waybillPushRedisRepository.clearPushFlag(mainOrderId)
            throw e
        }
    }

    fun createWayBills(
        subOrders: List<SubOrder>,
        mainOrderId: Long,
    ) {
        val alreadyWaybills = waybillRepository.findByMainOrderId(mainOrderId).map { it.shipping.wayBillRelation }
        log.info { "订单分组处理 | 订单ID: $mainOrderId | 子订单数量: ${subOrders.size} | 已有运单数: ${alreadyWaybills.size}" }

        val res =
            subOrders
                .groupBy { it.shipping.wayBillRelation }
                .map { (_, v) ->
                    val waybill = SupplierOrderConverter.toWaybill(v)
                    // 创建运单时自动计算totalPrice
                    waybill.totalPrice = calculateTotalPriceForSubOrders(v, waybill)
                    // 从Channel获取iossNumber并设置到Waybill
                    if (waybill.shipping.shipMethod == null) {
                        throw CognitionException("渠道没有选择")
                    }

                    val channel =
                        channelRepository.findByNameAndMethodCode(
                            waybill.shipping.channel,
                            waybill.shipping.shipMethod!!,
                        )
                    waybill.iossNumber = channel?.iossNumber
                    waybill
                }.filterNot {
                    alreadyWaybills.contains(it.shipping.wayBillRelation)
                }

        val savedWaybills = waybillRepository.saveAllAndFlush(res)

        savedWaybills.forEach {
            log.info { "运单创建 | 订单ID: $mainOrderId | 运单ID: ${it.id}  | 订单号: ${it.orderNos}" }
            eventPublisher.publishEvent(
                WaybillPendingEvent(waybillId = it.id, orderNos = it.orderNos),
            )
        }

        // 运单的处理是异步的,这里并不能直接立刻拿到处理结果,如果需要做失败通知,要去waybillEventHandler中处理

        log.info {
            "运单推送完成 | 订单ID: $mainOrderId | 新增运单数: ${savedWaybills.size} | 总运单数: ${alreadyWaybills.size + savedWaybills.size} | 事件发布数: ${savedWaybills.size}"
        }
    }

    fun failedPage(req: WaybillPageRequest): Any =
        waybillRepository
            .findByBizId(
                UserContextHolder.user!!.bizId,
                req.orderNo,
                listOf(WayBillStatus.FAILED).map { it.name },
                req.toPageable(),
                req.spu,
                req.mainOrderId,
                req.createdAtFrom
                    ?.atStartOfDay()
                    ?.toInstant(ZoneOffset.UTC)
                    ?.toEpochMilli(),
                req.createdAtTo
                    ?.plusDays(1)
                    ?.atStartOfDay()
                    ?.toInstant(ZoneOffset.UTC)
                    ?.toEpochMilli(),
                req.fileName,
                req.waybillNo,
                req.orderNos
                    ?.split("\n")
                    ?.filter { it.isNotEmpty() }
                    ?.joinToString(","),
                req.waybillNos?.split("\n") ?: emptyList(),
                req.country,
                req.countryCode,
            ).map { WaybillResponse.fromDomain(it) }
            .toResponse()

    fun statisticFinance(mainOrderId: Long): FinanceStatisticResponse {
        log.info { "Calculating finance statistics for mainOrderId: $mainOrderId" }
//        val mainOrder =
//                mainOrderRepository.findByIdOrNull(mainOrderId)
//                        ?: throw NoSuchElementException("MainOrder not found")

        val correct = correctMainOrderFinanceRepository.findByMainOrderId(mainOrderId)

        val fsr =
            waybillRepository.getFinanceStatistic(mainOrderId, UserContextHolder.user!!.bizId)
                ?: FinanceStatisticResponse()
        if (correct != null) {
            fsr.correctWaybillCost = correct.correctFinanceCost
        }
        return fsr
    }

    @Transactional(rollbackFor = [Throwable::class])
    fun correctStatisticFinance(
        mainOrderId: Long,
        correctRequest: CorrectRequest,
    ) {
        val mainOrder = mainOrderRepository.findByIdOrNull(mainOrderId) ?: throw NoSuchElementException("找不到该主订单编号")
        val userInfo = UserContextHolder.user
        val correct =
            correctMainOrderFinanceRepository.findByMainOrderId(mainOrderId) ?: run {
                CorrectMainOrderFinance(mainOrderId, BigDecimal.ZERO, mainOrder.customerId, mainOrder.bizId).apply {
                    userInfo?.let {
                        createdBy = it.id
                        updatedBy = it.id
                    }
                }
            }
        userInfo?.let {
            correct.createdByName = it.name
            correct.updatedByName = it.name
        }
        val newFsr = correct.copy(correctFinanceCost = correctRequest.correctFinanceCost)
        correctMainOrderFinanceRepository.saveAndFlush(newFsr)
    }

    @ColumnWidth(35)
    data class WaybillBillExcelModel(
        @ExcelProperty("唯一码") var uniqueCode: String? = null,
        @ExcelProperty("品名") var productName: String? = null,
        @ExcelProperty("SPU") var spu: String? = null,
        @ExcelProperty("数量") var quantity: Int? = null,
        @ExcelProperty("金额") var priceSum: BigDecimal? = null,
        @ExcelProperty("VAT") var vatTax: BigDecimal? = null,
        @ExcelProperty("附加税") var additionalTax: BigDecimal? = null,
        @ExcelProperty("折扣") var discounted: BigDecimal? = null,
        @ExcelProperty("备注") var comment: String? = null,
    )

    fun exportFinance(mainOrderId: Long): ByteArray {
        val waybills =
            waybillRepository.findByMainOrderIdAndStatusIn(
                mainOrderId,
                listOf(
                    WayBillStatus.COMPLETED,
                    WayBillStatus.FAILED,
                    WayBillStatus.CANCELLED,
                ),
            )

        val models =
            waybills.flatMap { waybill ->
                waybill.calculateCustomerNeedPayCostData?.orders?.map {
                    WaybillBillExcelModel(
                        uniqueCode = it.uniqueCode,
                        productName = it.productName,
                        spu = it.spu,
                        quantity = it.quantity,
                        priceSum = it.priceSum,
                        vatTax = it.vatTax,
                        additionalTax = it.additionalTax,
                        discounted = it.discounted,
                        comment = it.comment,
                    )
                } ?: run {
                    val subOrders = subOrderRepository.findByOrderNoIn(waybill.orderNos.split(","))
                    subOrders.map {
                        WaybillBillExcelModel(
                            uniqueCode = it.orderNo,
                            productName = it.product.cnName,
                            spu = it.product.spu,
                            quantity = it.product.qty,
                            priceSum = null,
                            vatTax = null,
                            additionalTax = null,
                            discounted = null,
                            comment =
                                when (it.status) {
                                    SubOrderStatus.COMPLETED -> "未记录数据"
                                    SubOrderStatus.FAILED -> "订单失败"
                                    SubOrderStatus.CANCELLED -> "订单取消"
                                    else -> "未记录数据"
                                },
                        )
                    }
                }
            }
        val data = ByteArrayOutputStream()
        val writer =
            EasyExcel
                .write(data, WaybillBillExcelModel::class.java)
                .build()
        writer.write(models, EasyExcel.writerSheet(0, "financial_$mainOrderId").build())
        writer.finish()
        return data.toByteArray()
    }

    @Transactional
    fun setQuickChannelTax(req: SetQuickChannelTaxRequest) {
        val waybills = waybillRepository.findAllById(req.waybillIds)
        waybills.forEach {
            it.quickChannelTax = req.tax
        }
        waybillRepository.saveAll(waybills)
    }

    @Transactional
    fun changeChannel(request: BatchChangeWayBillChannelRequest) {
        for (id in request.ids) {
            if (waybillPushRedisRepository.isPendingFlagExists(id)) {
                log.warn { "运单预报被阻止，已存在处理标志 | 运单ID: $id" }
                throw CognitionException("运单执行成功30s等待中, 请勿重复操作")
            }
        }
        val waybills =
            waybillRepository.findAllById(request.ids).map {
                it.shipping.channel = request.channel
                it.shipping.shipMethod = request.method
                // 渠道切换时重新计算totalPrice
                it.totalPrice = calculateTotalPrice(it)
                it
            }
        waybillRepository.saveAll(waybills)
        waybills.forEach { waybill ->
            callDeletePredication(waybill)

            eventPublisher.publishEvent(
                WaybillPendingEvent(waybill.id, waybill.orderNos),
            )
        }
    }

    @Transactional
    fun stopPrint(req: StopWaybillPrintRequest) {
        val startTime = System.currentTimeMillis()

        log.info {
            """停止打印运单操作开始 | Operation: stopPrint 
        | WaybillIds: ${req.ids.joinToString(",")}
        | Warning: ${req.warning}
            """.trimMargin()
        }

        try {
            waybillRepository.findAllById(req.ids).forEach { waybills ->
                val orderIds = waybills.orderNos.split(",")

                log.info {
                    """处理运单订单 | WaybillId: ${waybills.id} 
                | OrderCount: ${orderIds.size}
                | OrderIds: ${orderIds.joinToString(",")}
                    """.trimMargin()
                }

                supplierOrderApplicationService.stopPrint(
                    orderIds,
                    req.warning,
                )
            }

            val processingTime = System.currentTimeMillis() - startTime
            log.info {
                """停止打印运单操作完成 | Operation: stopPrint 
            | WaybillCount: ${req.ids.size}
            | ProcessingTime: ${processingTime}ms
                """.trimMargin()
            }
        } catch (e: Exception) {
            log.error {
                """停止打印运单操作失败 | Operation: stopPrint 
            | WaybillIds: ${req.ids.joinToString(",")}
            | ErrorMessage: ${e.message}
                """.trimMargin()
            }
            throw e
        }
    }

    fun stopPrintCancel(req: CancelWaybillStopPrintRequest) {
        log.info {
            """取消停止打印运单操作开始 | Operation: cancelStopPrint 
        | WaybillIds: ${req.ids.joinToString(",")}
            """.trimMargin()
        }

        try {
            waybillRepository.findAllById(req.ids).forEach { waybills ->
                val orderIds = waybills.orderNos.split(",")

                log.info {
                    """处理运单订单 | WaybillId: ${waybills.id} 
                | OrderCount: ${orderIds.size}
                | OrderIds: ${orderIds.joinToString(",")}
                    """.trimMargin()
                }

                supplierOrderApplicationService.cancelStopPrint(orderIds)
            }

            log.info {
                """取消停止打印运单操作完成 | Operation: cancelStopPrint 
            | WaybillCount: ${req.ids.size}
                """.trimMargin()
            }
        } catch (e: Exception) {
            log.error {
                """取消停止打印运单操作失败 | Operation: cancelStopPrint 
            | WaybillIds: ${req.ids.joinToString(",")}
            | ErrorMessage: ${e.message}
                """.trimMargin()
            }
            throw e
        }
    }

    fun exportExcel(ids: List<Long>): ByteArray {
        val startTime = System.currentTimeMillis()

        log.info { "导出Excel开始 | 运单ID: $ids" }

        val waybillsStartTime = System.currentTimeMillis()
        val waybills = waybillRepository.findAllById(ids)
        val waybillsEndTime = System.currentTimeMillis()
        log.info { "查询运单完成 | 运单数量: ${waybills.size} | 耗时: ${waybillsEndTime - waybillsStartTime}ms" }

        val waybillMappingStartTime = System.currentTimeMillis()
        val waybillMapping = mutableMapOf<String, Waybill>()
        for (waybill in waybills) {
            waybill.orderNos.split(",").forEach {
                waybillMapping[it] = waybill
            }
        }
        val waybillMappingEndTime = System.currentTimeMillis()
        log.info { "创建运单映射完成 | 订单号数量: ${waybillMapping.size} | 耗时: ${waybillMappingEndTime - waybillMappingStartTime}ms" }

        val subOrdersStartTime = System.currentTimeMillis()
        val orderNos = waybills.flatMap { it.orderNos.split(",") }
        val subOrders = subOrderRepository.findByOrderNoIn(orderNos)
        val subOrdersEndTime = System.currentTimeMillis()
        log.info { "查询子订单完成 | 子订单数量: ${subOrders.size} | 订单号数量: ${orderNos.size} | 耗时: ${subOrdersEndTime - subOrdersStartTime}ms" }

        val excelModelsStartTime = System.currentTimeMillis()
        val excelModels =
            subOrders
                .map { subOrder ->
                    subOrder.toExcelModel(waybillMapping[subOrder.orderNo])
                }.groupBy { it.waybillNo }
                .toSortedMap(compareBy { it })
                .flatMap { (_, group) -> group }
        val excelModelsEndTime = System.currentTimeMillis()
        log.info { "创建Excel模型完成 | Excel模型数量: ${excelModels.size} | 耗时: ${excelModelsEndTime - excelModelsStartTime}ms" }

        val outputStreamStartTime = System.currentTimeMillis()
        val outputStream = ByteArrayOutputStream()

        EasyExcel
            .write(outputStream, SupplierOrderExcelModel::class.java)
            .sheet("供应商订单")
            .doWrite(excelModels)
        val outputStreamEndTime = System.currentTimeMillis()
        log.info { "EasyExcel写入完成 | 耗时: ${outputStreamEndTime - outputStreamStartTime}ms" }

        val byteArray = outputStream.toByteArray()
        val endTime = System.currentTimeMillis()

        log.info { "导出Excel完成 | 运单ID: $ids | 总耗时: ${endTime - startTime}ms | Excel文件大小: ${byteArray.size} bytes" }
        return byteArray
    }

    @Transactional
    fun extend(request: ExtendWaybillRequest): String {
        log.info { "开始扩展运单 | 运单ID: ${request.id} | 新增订单号: ${request.newOrderNo}" }
        val originalWaybill =
            waybillRepository.findByIdOrNull(request.id)
                ?: throw NoSuchElementException("Waybill not found with id: ${request.id}")

        // 创建新的运单实例，复制原运单的所有数据
        val newWaybill =
            Waybill().apply {
                // 复制基本信息
                mainOrderId = originalWaybill.mainOrderId
                subOrderId = originalWaybill.subOrderId
                bizId = originalWaybill.bizId
                fileName = originalWaybill.fileName
                customerId = originalWaybill.customerId
                googleSearch = originalWaybill.googleSearch
                totalPrice = originalWaybill.totalPrice
                trackingNumber = originalWaybill.trackingNumber
                status = WayBillStatus.CREATED // 重置状态为创建状态
                quickChannelTax = originalWaybill.quickChannelTax
                customerNeedPayCost = originalWaybill.customerNeedPayCost
                customerNeedPayTax = originalWaybill.customerNeedPayTax
                calculateCustomerNeedPayCostFlow = originalWaybill.calculateCustomerNeedPayCostFlow
                taxNumber = originalWaybill.taxNumber

                // 设置扩展运单特有的字段
                originalId = request.id
                val originOrderNo = originalWaybill.orderNo
                orderNo = request.newOrderNo ?: ("$originOrderNo-TJ")
                packageNo = "BG-$orderNo"

                // 重置运单相关字段
                waybillNo = null
                errorMsg = null
                failAtStatus = null
                cancelReason = null

                // 复制嵌入对象
                product = originalWaybill.product
                recipient = originalWaybill.recipient
                shipping =
                    originalWaybill.shipping.apply {
                        waybillLabelUrl = null // 重置运单标签URL
                    }

                // 更新订单号列表
                orderNos = originalWaybill.orderNos.replace(originOrderNo, orderNo)
            }

        waybillRepository.saveAndFlush(newWaybill)
        log.info { "扩展运单完成 | 原运单ID: ${request.id} | 新增运单ID: ${newWaybill.id} | 新增运单号: ${newWaybill.orderNo}" }
        return newWaybill.orderNo
    }

    @Transactional
    fun submitWaybill(
        id: Long,
        waybillNo: String,
    ) {
        log.info { "开始提交运单 | 运单ID: $id | 运单号: $waybillNo" }

        val waybill =
            waybillRepository.findByIdOrNull(id)
                ?: throw NoSuchElementException("Waybill not found with id: $id")

        // 更新运单号
        waybill.waybillNo = waybillNo

        // 根据渠道获取面单URL
        val labelUrl = fetchLabelUrlByChannel(waybill)
        waybill.shipping.waybillLabelUrl = labelUrl

        // 更新状态为已完成
        waybill.status = WayBillStatus.COMPLETED
        waybill.clearErrorMsg()

        waybillRepository.saveAndFlush(waybill)

        // 发布运单完成事件
        eventPublisher.publishEvent(
            WaybillCompletedEvent(
                waybillId = waybill.id,
                subOrderId = waybill.subOrderId,
                orderNos = waybill.orderNos,
            ),
        )

        log.info { "运单提交完成 | 运单ID: $id | 运单号: $waybillNo | 面单URL: $labelUrl" }
    }

    private fun fetchLabelUrlByChannel(waybill: Waybill): String? =
        when (waybill.shipping.channel) {
            WaybillChannel.CAINIAO -> caiNiaoPredictionRemoteService.getPrintUrl(waybill)
            WaybillChannel.YUNTU -> yunTuPredictionRemoteService.getPrintUrl(waybill)
            WaybillChannel.SF -> shunfengPredictionRemoteService.getPrintUrl(waybill)
            WaybillChannel.YW_HZ, WaybillChannel.YW_QZ, WaybillChannel.YW_GZ, WaybillChannel.YW_YW ->
                yanWenPredictionRemoteService.getPrintUrl(
                    waybill,
                )

            WaybillChannel.SDH -> sdhPredictionRemoteService.getPrintUrl(waybill)
            WaybillChannel.YWBZ -> ywbzPredictionRemoteService.getPrintUrl(waybill)
            WaybillChannel.FOURPX -> fpxPredictionRemoteService.getPrintUrl(waybill)
            WaybillChannel.ZM -> zmPredictionRemoteService.getPrintUrl(waybill)
            WaybillChannel.CHANGLIAN -> changLianPredictionRemoteService.getPrintUrl(waybill)
        }

    /**
     * 计算运单总价（用于创建时，已有subOrders）
     * 总价 = 多个WaybillRequest的price总和
     */
    private fun calculateTotalPriceForSubOrders(
        subOrders: List<SubOrder>,
        waybill: Waybill,
    ): BigDecimal {
        if (subOrders.isEmpty()) {
            log.warn { "运单没有对应的子订单，使用默认价格" }
            return BigDecimal.ZERO
        }

        // 生成WaybillRequest列表
        val waybillRequests = subOrders.flatMap { WaybillRequest.of(it, waybill) }
        val totalQty = waybillRequests.sumOf { it.qty }

        // 计算总价：每个WaybillRequest的单价 × 数量，然后求和
        val totalPrice = totalPrice(waybill.recipient.country!!, totalQty, waybill.shipping.channel)

        log.info { "运单创建时计算总价: $totalPrice | 渠道: ${waybill.shipping.channel} | 运输方式: ${waybill.shipping.shipMethod}" }
        return totalPrice
    }

    /**
     * 计算运单总价（用于渠道切换时，需要查询subOrders）
     * 总价 = 多个WaybillRequest的price总和
     */
    private fun calculateTotalPrice(waybill: Waybill): BigDecimal {
        // 获取运单对应的子订单
        val orderNos = waybill.orderNos.split(",")
        val subOrders = subOrderRepository.findByOrderNoIn(orderNos)

        if (subOrders.isEmpty()) {
            log.warn { "运单 ${waybill.id} 没有找到对应的子订单，使用默认价格" }
            return BigDecimal.ZERO
        }

        // 生成WaybillRequest列表，渠道切换时不使用旧的totalPrice，强制重新计算
        waybill.totalPrice = null // 临时清空，强制重新计算

        val waybillRequests = subOrders.flatMap { WaybillRequest.of(it, waybill) }
        val totalQty = waybillRequests.sumOf { it.qty }

        // 计算总价：每个WaybillRequest的单价 × 数量，然后求和
        val totalPrice = totalPrice(waybill.recipient.country!!, totalQty, waybill.shipping.channel)

        log.info { "运单 ${waybill.id} 渠道切换时计算总价: $totalPrice | 渠道: ${waybill.shipping.channel} | 运输方式: ${waybill.shipping.shipMethod}" }
        return totalPrice
    }

    /**
     * 调用物流商取消预报接口
     */
    private fun callDeletePredication(waybill: Waybill) {
        try {
            when (waybill.shipping.channel) {
                WaybillChannel.YUNTU -> yunTuPredictionRemoteService.deletePredication(waybill)
                WaybillChannel.YWBZ -> ywbzPredictionRemoteService.deletePredication(waybill)
                WaybillChannel.SF -> shunfengPredictionRemoteService.deletePredication(waybill)
                WaybillChannel.CAINIAO -> caiNiaoPredictionRemoteService.deletePredication(waybill)
                WaybillChannel.FOURPX -> {}
                WaybillChannel.YW_GZ,
                WaybillChannel.YW_HZ,
                WaybillChannel.YW_QZ,
                WaybillChannel.YW_YW,
                -> yanWenPredictionRemoteService.deletePredication(waybill)

                WaybillChannel.SDH -> sdhPredictionRemoteService.deletePredication(waybill)
                WaybillChannel.ZM -> {}
                WaybillChannel.CHANGLIAN -> changLianPredictionRemoteService.deletePredication(waybill)
            }
        } catch (e: Exception) {
            log.error(e) { "调用物流商取消预报接口失败 | 运单ID: ${waybill.id} | 订单号: ${waybill.orderNo} | 渠道: ${waybill.shipping.channel}" }
        }
    }

    companion object {
        private val log = KotlinLogging.logger {}
    }
}
