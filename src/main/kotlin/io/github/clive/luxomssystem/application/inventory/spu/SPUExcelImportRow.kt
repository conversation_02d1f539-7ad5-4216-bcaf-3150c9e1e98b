package io.github.clive.luxomssystem.application.inventory.spu

import com.alibaba.excel.annotation.ExcelProperty
import java.math.BigDecimal

data class SPUExcelImportRow(
    @ExcelProperty("SPU代码") var spuCode: String = "",
    @ExcelProperty("标题") var title: String = "",
    @ExcelProperty("名称") var name: String = "",
    @ExcelProperty("中文名称") var cnName: String = "",
    @ExcelProperty("类别") var category: String = "",
    @ExcelProperty("产品图片") var productImage: String = "",
    @ExcelProperty("包装数量") var packageQuantity: Int = 0,
    @ExcelProperty("展示图片") var showImages: String = "",
    @ExcelProperty("描述") var description: String = "",
    @ExcelProperty("尺寸") var sizes: String = "",
    @ExcelProperty("颜色") var colors: String = "",
    @ExcelProperty("HS编码") var hsCode: String = "",
    @ExcelProperty("重量") var weight: BigDecimal = BigDecimal.ZERO,
    @ExcelProperty("容量")var volume: BigDecimal = BigDecimal.ZERO,
    @ExcelProperty("成本价") var purchaseCost: BigDecimal = BigDecimal.ZERO,
    @ExcelProperty("成本价币种") var purchaseCostCurrency: String = "cny",
    @ExcelProperty("销售价") var salePrice: BigDecimal = BigDecimal.ZERO,
)
