package io.github.clive.luxomssystem.application.converter

import io.github.clive.luxomssystem.common.utils.generateQRCodeImage
import io.github.clive.luxomssystem.domain.SubOrder
import io.github.clive.luxomssystem.domain.supplierOrder.model.SupplierOrder
import io.github.clive.luxomssystem.domain.waybill.model.Waybill
import io.github.clive.luxomssystem.facade.order.supplierorder.response.SupplierOrderExcelModel

object SupplierOrderConverter {
    fun SubOrder.toExcelModel(waybill: Waybill?) =
        SupplierOrderExcelModel(
            no = orderNo,
            uniqueCode = "$orderNo-${product.skuCode()}",
            sku = product.skuCode(),
            urls = urls,
            designUrl = designUrl,
            effectUrl = effectUrl,
            googleSearchField = googleSearch,
            productName = product.title,
            customName = product.customName,
            waybillNo = waybill?.waybillNo,
            quantity = product.qty,
            name = recipient.receiverName,
            address = recipient.fullAddress(),
            city = recipient.city,
            state = recipient.state,
            country = recipient.country,
            postcode = recipient.postcode,
            phone = recipient.phone,
            qrCode = generateQRCodeImage("$orderNo-${product.skuCode()}", 300, "$orderNo-${product.skuCode()}"),
            detail = remark,
        )

    fun SupplierOrder.toExcelModel(waybill: Waybill?) =
        SupplierOrderExcelModel(
            no = orderNo,
            uniqueCode = "$orderNo-${product.skuCode()}",
            sku = product.skuCode(),
            urls = urls,
            designUrl = designUrl,
            effectUrl = effectUrl,
            googleSearchField = googleSearch,
            productName = product.title,
            customName = product.customName,
            waybillNo = waybill?.waybillNo,
            quantity = product.qty,
            name = recipient.receiverName,
            address = recipient.fullAddress(),
            city = recipient.city,
            state = recipient.state,
            country = recipient.country,
            postcode = recipient.postcode,
            phone = recipient.phone,
            qrCode = generateQRCodeImage("$orderNo-${product.skuCode()}", 300, "$orderNo-${product.skuCode()}"),
            detail = remark,
        )
}
