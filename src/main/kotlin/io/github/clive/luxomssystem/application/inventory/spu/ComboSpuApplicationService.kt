package io.github.clive.luxomssystem.application.inventory.spu

import io.github.clive.luxomssystem.common.toPageable
import io.github.clive.luxomssystem.common.utils.generatorSpuRefId
import io.github.clive.luxomssystem.domain.sku.model.ComboSku
import io.github.clive.luxomssystem.domain.sku.model.Sku
import io.github.clive.luxomssystem.domain.spu.event.SPUDeletedEvent
import io.github.clive.luxomssystem.domain.spu.event.SPUUpdatedEvent
import io.github.clive.luxomssystem.domain.spu.model.ComboSpu
import io.github.clive.luxomssystem.domain.spu.model.Spu
import io.github.clive.luxomssystem.facade.spu.ComboSpuController
import io.github.clive.luxomssystem.infrastructure.config.auth.UserContextHolder
import io.github.clive.luxomssystem.infrastructure.repository.jpa.spu.ComboCustomerSpuRepository
import io.github.clive.luxomssystem.infrastructure.repository.jpa.spu.ComboSkuRepository
import io.github.clive.luxomssystem.infrastructure.repository.jpa.spu.ComboSpuRepository
import io.github.clive.luxomssystem.infrastructure.repository.jpa.spu.SPURepository
import io.github.clive.luxomssystem.infrastructure.repository.jpa.spu.SkuRepository
import io.github.clive.luxomssystem.nextId
import io.github.oshai.kotlinlogging.KotlinLogging
import org.springframework.context.ApplicationContext
import org.springframework.context.event.EventListener
import org.springframework.data.domain.Page
import org.springframework.data.repository.findByIdOrNull
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional

@Service
class ComboSpuApplicationService(
    private val spuRepository: SPURepository,
    private val skuRepository: SkuRepository,
    private val comboSpuRepository: ComboSpuRepository,
    private val comboSkuRepository: ComboSkuRepository,
    private val comboCustomerSpuRepository: ComboCustomerSpuRepository,
    private val applicationContext: ApplicationContext,
) {
    // flat spu/sku 用于在套装创建/编辑 的时候在有哪些平台spu中显示
    // 一个flat spu就是平台中的产品,这个spu相对于直接使用spu,同时把sku返回回去了
    fun fetchFlatSpu(spuCodes: List<String>): Pair<List<Spu>, List<Sku>> {
        val spus = spuRepository.findAllBySpuCodeIn(spuCodes)
        val skus = skuRepository.findAllBySpuIdIn(spus.map { it.id })
        return spus to skus
    }

    fun searchFlatSpu(search: String): Pair<List<Spu>, List<Sku>> {
        val spus = spuRepository.searchSpu(search)
        val skus = skuRepository.findAllBySpuIdIn(spus.map { it.id })
        return spus to skus
    }

    @Transactional(rollbackFor = [Throwable::class])
    fun createComboSpu(request: ComboSpuController.ComboSpuCreateRequest): Long {
        val user = UserContextHolder.user!!
        if (request.spuCodes.size < 2) {
            throw IllegalArgumentException("至少两个spu才能算作套装")
        }
        val spus = spuRepository.findAllBySpuCodeIn(request.spuCodes)
        if (spus.size != request.spuCodes.size) {
            throw IllegalArgumentException("找不到至少一位spuCodes")
        }
        val spuIds = spus.map { it.id }
        val spuRefId = generatorSpuRefId(request.spuCodes)
        if (comboSpuRepository.existsBySpuRefId(spuRefId)) {
            throw IllegalStateException("该套装组合已存在")
        }
        val generatedSpuId = nextId()
        val comboSpu =
            ComboSpu(
                id = generatedSpuId,
                bizId = user.bizId,
                spuIds = spuIds,
                spuCodes = request.spuCodes,
                spuRefId = spuRefId,
                title = request.title,
                name = request.name,
                cnName = request.cnName,
                category = request.category,
                productImage = request.productImage,
                packageQuantity = request.packageQuantity,
                showImages = request.showImages,
                description = request.description,
                status = request.status,
                hsCode = request.hsCode,
            ).apply {
                createdBy = user.id
                createdByName = user.name
            }
        comboSpuRepository.saveAndFlush(comboSpu)

        val skus =
            request.skus.map {
                ComboSku(
                    id = nextId(),
                    comboSpuId = generatedSpuId,
                    spuId = it.spuId,
                    spuCode = it.spuCode,
                    skuCode = it.skuCode,
                    spuRefId = spuRefId,
                    systemSkuId = it.systemSkuId,
                    size = it.size,
                    color = it.color,
                    purchaseCost = it.purchaseCost,
                    purchaseCostCurrency = it.purchaseCostCurrency,
                    salePrice = it.salePrice,
                    weight = it.weight,
                    volume = it.volume,
                    status = "created",
                ).apply {
                    createdBy = user.id
                    createdByName = user.name
                }
            }

        comboSkuRepository.saveAllAndFlush(skus)

        return generatedSpuId
    }

    @Transactional
    fun updateComboSpu(
        id: Long,
        request: ComboSpuController.ComboSpuCreateRequest,
    ) {
        val user = UserContextHolder.user!!
        val comboSpu = comboSpuRepository.findByIdOrNull(id) ?: throw IllegalArgumentException("combo spu \"${id}\" not found.")

        comboSpuRepository.saveAndFlush(
            comboSpu
                .copy(
                    title = request.title,
                    name = request.name,
                    cnName = request.cnName,
                    category = request.category,
                    productImage = request.productImage,
                    packageQuantity = request.packageQuantity,
                    showImages = request.showImages,
                    description = request.description,
                    status = request.status,
                    hsCode = request.hsCode,
                ).apply {
                    updatedBy = user.id
                    updatedByName = user.name
                },
        )

        val updateSkus = request.skus.associateBy { it.systemSkuId }

        val skus = comboSkuRepository.findAllByComboSpuId(id)
        val newSkus =
            skus.map {
                val newReq = updateSkus[it.systemSkuId] ?: return@map it
                it
                    .copy(
                        purchaseCost = newReq.purchaseCost,
                        purchaseCostCurrency = newReq.purchaseCostCurrency,
                        salePrice = newReq.salePrice,
                        weight = newReq.weight,
                        volume = newReq.volume,
                    ).apply {
                        updatedBy = user.id
                        updatedByName = user.name
                    }
            }

        comboSkuRepository.saveAllAndFlush(newSkus)
    }

    fun getComboSpuById(id: Long): Pair<ComboSpu, List<ComboSku>> {
        val comboSpu = comboSpuRepository.findByIdOrNull(id) ?: throw IllegalArgumentException("combo spu id not found")
        val comboSkus = comboSkuRepository.findAllByComboSpuId(comboSpu.id)
        return comboSpu to comboSkus
    }

    fun selectComboSpu(customerId: Long?): List<ComboSpu> =
        if (customerId != null) {
            val existsIds = comboCustomerSpuRepository.findAllComboSpuIdByCustomerId(customerId)
            if (existsIds.isNotEmpty()) {
                comboSpuRepository.findAllByBizIdAndIdNotIn(UserContextHolder.user!!.bizId, existsIds)
            } else {
                comboSpuRepository.findAllByBizId(UserContextHolder.user!!.bizId)
            }
        } else {
            comboSpuRepository.findAllByBizId(UserContextHolder.user!!.bizId)
        }

    fun pageReq(req: ComboSpuController.PageComboSpuRequest): Page<ComboSpu> {
        val pageable = req.toPageable()
        return comboSpuRepository
            .pageQuery(
                req.spuCode?.let { "%$it%" },
                req.category,
                UserContextHolder.user!!.bizId,
                req.title?.let { "%$it%" },
                pageable,
            )
    }

    @EventListener
    @Transactional(rollbackFor = [Throwable::class])
    fun onSpuUpdate(event: SPUUpdatedEvent) {
        val rawSpu = spuRepository.findByIdOrNull(event.spuId) ?: throw IllegalArgumentException("找不到spuid,这不应该出现")
        val allSku = skuRepository.findBySpuId(event.spuId)

        // 所有有的sku
        val skuCodes = allSku.map { it.skuCode }
        val skuCodesMap = allSku.associateBy { it.skuCode }

        // 删除已经不在的sku
        comboSkuRepository.deleteAllBySpuIdAndSkuCodeNotIn(event.spuId, skuCodes)

        // 先把受到影响的spu都查询出来
        val effectComboSpus = comboSpuRepository.findAllBySpuIdsContains(event.spuId)
        effectComboSpus.forEach { comboSpu ->
            val existsSkus = comboSkuRepository.findAllByComboSpuId(comboSpu.id)
            val missedSkus = skuCodes.toSet() - existsSkus.map { it.skuCode }.toSet()

            val newComboSku =
                missedSkus.map { skuCode ->
                    val rawSku = skuCodesMap[skuCode] ?: throw IllegalArgumentException("找不到sku id,这不应该出现")
                    ComboSku(
                        id = nextId(),
                        comboSpuId = comboSpu.id,
                        spuId = rawSku.spuId,
                        spuCode = rawSpu.spuCode,
                        skuCode = skuCode,
                        spuRefId = comboSpu.spuRefId,
                        systemSkuId = rawSku.id,
                        size = rawSku.size,
                        color = rawSku.color,
                        purchaseCost = rawSku.purchaseCost,
                        purchaseCostCurrency = rawSku.purchaseCostCurrency,
                        salePrice = rawSku.salePrice,
                        weight = rawSku.weight,
                        volume = rawSku.volume,
                        status = rawSku.status,
                    )
                }
            log.info { "generated new ${newComboSku.size} comboSku for comboSpu[${comboSpu.id}]" }
            comboSkuRepository.saveAllAndFlush(newComboSku)
        }

        applicationContext.publishEvent(ComboSkuUpdateEvent(event.spuId, effectComboSpus.map { it.id }))
    }

    @Transactional(rollbackFor = [Throwable::class])
    fun deleteComboSpu(id: Long) {
        comboSpuRepository.deleteById(id)
        comboSkuRepository.deleteAllByComboSpuId(id)
        applicationContext.publishEvent(ComboSkuDeleteEvent(id))
    }

    @EventListener
    @Transactional(rollbackFor = [Throwable::class])
    fun onSpuDelete(event: SPUDeletedEvent) {
        comboSpuRepository.deleteAllBySpuIdsContains(event.spuId)
        comboSkuRepository.deleteAllBySpuId(event.spuId)
    }

    companion object {
        private val log = KotlinLogging.logger { }
    }
}

data class ComboSkuUpdateEvent(
    val fromSpuId: Long,
    val effectComboSpuId: Collection<Long>,
)

data class ComboSkuDeleteEvent(
    val comboSpuId: Long,
)
