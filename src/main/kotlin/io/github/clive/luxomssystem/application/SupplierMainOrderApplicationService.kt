package io.github.clive.luxomssystem.application

import com.alibaba.excel.EasyExcel
import com.alibaba.excel.annotation.ExcelProperty
import com.alibaba.excel.annotation.write.style.ColumnWidth
import io.github.clive.luxomssystem.common.PageResponse
import io.github.clive.luxomssystem.common.exception.CognitionException
import io.github.clive.luxomssystem.common.toPageable
import io.github.clive.luxomssystem.common.toResponse
import io.github.clive.luxomssystem.domain.SupplierMainOrder
import io.github.clive.luxomssystem.domain.UserExcelTask
import io.github.clive.luxomssystem.domain.UserExcelTaskStatus
import io.github.clive.luxomssystem.facade.order.req.SupplierMainOrderPageRequest
import io.github.clive.luxomssystem.facade.order.suppliermainorder.ChangeSupplierRequest
import io.github.clive.luxomssystem.facade.order.suppliermainorder.SupplierMainOrderExportBillRequest
import io.github.clive.luxomssystem.facade.order.suppliermainorder.SupplierMainOrderExportBillRequest2
import io.github.clive.luxomssystem.infrastructure.channel.cos.CosInnerRemoteService
import io.github.clive.luxomssystem.infrastructure.config.auth.UserContextHolder
import io.github.clive.luxomssystem.infrastructure.config.auth.withContext
import io.github.clive.luxomssystem.infrastructure.repository.jpa.SupplierMainOrderRepository
import io.github.clive.luxomssystem.infrastructure.repository.jpa.SupplierOrderRepository
import io.github.clive.luxomssystem.infrastructure.repository.jpa.SupplierRepository
import io.github.clive.luxomssystem.infrastructure.repository.jpa.UserExcelTaskRepository
import io.github.clive.luxomssystem.infrastructure.repository.jpa.spu.SupplierSkuRepository
import io.github.clive.luxomssystem.nextId
import io.github.oshai.kotlinlogging.KotlinLogging
import jakarta.transaction.Transactional
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.runBlocking
import org.ehcache.impl.internal.concurrent.ConcurrentHashMap
import org.springframework.data.repository.findByIdOrNull
import org.springframework.stereotype.Service
import org.springframework.web.multipart.MultipartFile
import java.io.ByteArrayInputStream
import java.io.ByteArrayOutputStream
import java.math.BigDecimal
import java.time.Instant
import java.time.ZoneId
import java.time.format.DateTimeFormatter
import java.util.*
import kotlin.concurrent.thread

@Service
class SupplierMainOrderApplicationService(
    private val supplierMainOrderRepository: SupplierMainOrderRepository,
    private val supplierRepository: SupplierRepository,
    private val supplierOrderRepository: SupplierOrderRepository,
    private val supplierSkuRepository: SupplierSkuRepository,
    private val userExcelTaskRepository: UserExcelTaskRepository,
    private val ossClient: CosInnerRemoteService,
) {
    fun page(req: SupplierMainOrderPageRequest): PageResponse<SupplierMainOrder> {
        if (UserContextHolder.user!!.supplierId == null) {
            return supplierMainOrderRepository
                .pageNoSupplier(
                    UserContextHolder.user!!.bizId,
                    req.toPageable(),
                    req.mainOrderId,
                    req.createdAtFromEpochMilli(),
                    req.createdAtToEpochMilli(),
                    req.fileName,
                    req.supplierIds,
                ).toResponse()
        } else {
            return supplierMainOrderRepository
                .page(
                    UserContextHolder.user!!.supplierId,
                    UserContextHolder.user!!.bizId,
                    req.toPageable(),
                    req.mainOrderId,
                    req.createdAtFromEpochMilli(),
                    req.createdAtToEpochMilli(),
                    req.fileName,
                ).toResponse()
        }
    }

    @Transactional(rollbackOn = [Exception::class])
    fun changeSupplier(req: ChangeSupplierRequest) {
        val startTime = System.currentTimeMillis()

        val mainOrder =
            supplierMainOrderRepository.findByIdOrNull(req.orderId) ?: throw CognitionException("不存在的订单")

        val oldSupplierId = mainOrder.supplierId

        val oldSupplierName = mainOrder.supplierName

        val newSupplier =
            supplierRepository.findByIdOrNull(req.newSupplierId) ?: throw CognitionException("不存在的供应商")

        val targetSupplierMainOrder =
            supplierMainOrderRepository.findByMainOrderIdAndSupplierId(mainOrder.mainOrderId, req.newSupplierId)
        if (targetSupplierMainOrder != null) {
            log.info {
                "供应商变更 | 订单ID: ${mainOrder.mainOrderId} | 新供应商ID: ${req.newSupplierId} | 旧供应商名称: $oldSupplierName | 新供应商名称: ${newSupplier.name} | 新供应商当前订单的主订单已存在"
            }
            supplierMainOrderRepository.deleteById(req.orderId)
        } else {
            mainOrder.supplierId = req.newSupplierId
            mainOrder.supplierName = newSupplier.name
            supplierMainOrderRepository.saveAndFlush(mainOrder)
        }

        val orders =
            supplierOrderRepository.findByMainOrderIdAndProduct_SupplierId(mainOrder.mainOrderId, oldSupplierId)

        orders.forEach {
            it.product.supplierId = req.newSupplierId
            it.product.supplierName = newSupplier.name
        }

        supplierOrderRepository.saveAll(orders)

        log.info {
            """供应商变更 | 订单ID: ${mainOrder.mainOrderId} | 新供应商ID: ${req.newSupplierId} | 旧供应商名称: $oldSupplierName | 新供应商名称: ${newSupplier.name} | Key-处理时间: ${System.currentTimeMillis() - startTime}ms"""
                .trimMargin()
        }
    }

    fun createExportBillTask(req: SupplierMainOrderExportBillRequest): Pair<Long, String> {
        val dateFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd")
        val nextId = nextId()
        val fileName =
            buildString {
                append("供应商账单-")
                append(req.createdAtFrom.format(dateFormatter))
                append("至")
                append(req.createdAtTo.format(dateFormatter))
            }
        userExcelTaskRepository.saveAndFlush(
            UserExcelTask().apply {
                id = nextId
                type = "供应商账单"
                this.fileName = "$fileName.xlsx"
                bizId = UserContextHolder.user!!.bizId
                userId = UserContextHolder.user!!.id
                createdByName = UserContextHolder.user!!.name
                updatedByName = UserContextHolder.user!!.name
            },
        )
        return nextId to fileName
    }

    fun createExportBillTask(req: SupplierMainOrderExportBillRequest2): Pair<Long, String> {
        val nextId = nextId()
        val fileName =
            buildString {
                append("供应商账单")
            }
        userExcelTaskRepository.saveAndFlush(
            UserExcelTask().apply {
                id = nextId
                type = "供应商账单"
                this.fileName = "$fileName.xlsx"
                bizId = UserContextHolder.user!!.bizId
                userId = UserContextHolder.user!!.id
                createdByName = UserContextHolder.user!!.name
                updatedByName = UserContextHolder.user!!.name
            },
        )
        return nextId to fileName
    }

    @ColumnWidth(35)
    data class SupplierProductBillExcelModel(
        @ExcelProperty("时间") var time: String? = null,
        @ExcelProperty("唯一码") var uniqueCode: String? = null,
        @ExcelProperty("品名") var productName: String? = null,
        @ExcelProperty("SPU") var spu: String? = null,
        @ExcelProperty("数量") var quantity: Int? = null,
        @ExcelProperty("单价") var unitPrice: BigDecimal? = null,
        @ExcelProperty("金额") var price: BigDecimal? = null,
        @ExcelProperty("备注") var comment: String? = null,
    )

    fun exportBill(req: SupplierMainOrderExportBillRequest): Long {
        val (taskId, fileName) = createExportBillTask(req)
        val userContext = UserContextHolder.user
        thread {
            UserContextHolder.withContext(userContext) {
                doExportBill(req, taskId, fileName)
            }
        }
        return taskId
    }

    fun exportBill(req: SupplierMainOrderExportBillRequest2): Long {
        val (taskId, fileName) = createExportBillTask(req)
        val userContext = UserContextHolder.user
        thread {
            UserContextHolder.withContext(userContext) {
                doExportBill(req, taskId, fileName)
            }
        }
        return taskId
    }

    private fun doExportBill(
        req: SupplierMainOrderExportBillRequest2,
        taskId: Long,
        fileName: String,
    ) {
        doExportBill(taskId, fileName) {
            supplierMainOrderRepository.findAllById(req.supplierMainOrderIds)
        }
    }

    private fun doExportBill(
        req: SupplierMainOrderExportBillRequest,
        taskId: Long,
        fileName: String,
    ) {
        doExportBill(taskId, fileName) {
            if (UserContextHolder.user!!.supplierId == null) {
                supplierMainOrderRepository
                    .listNoSupplierForExport(
                        UserContextHolder.user!!.bizId,
                        req.createdAtFromEpochMilli(),
                        req.createdAtToEpochMilli(),
                        req.supplierIds,
                    )
            } else {
                supplierMainOrderRepository
                    .listForExport(
                        UserContextHolder.user!!.supplierId,
                        UserContextHolder.user!!.bizId,
                        req.createdAtFromEpochMilli(),
                        req.createdAtToEpochMilli(),
                    )
            }
        }
    }

    private fun doExportBill(
        taskId: Long,
        fileName: String,
        mainOrderProvider: () -> List<SupplierMainOrder>,
    ) {
        kotlin
            .runCatching {
                log.info { "导出账单: task:$taskId 开始" }
                val supplierMainOrders = mainOrderProvider()

                log.info { "导出账单: task:$taskId 涉及:${supplierMainOrders.size}条供应商主订单" }
                val supplierMainOrdersGroup = supplierMainOrders.associateBy { it.mainOrderId to it.supplierId }
                val supplierSubOrders =
                    supplierMainOrders
                        .asSequence()
                        .map { it.mainOrderId to it.supplierId }
                        .flatMap { (mainOrderId, supplierId) ->
                            supplierOrderRepository.findByMainOrderIdAndProduct_SupplierId(mainOrderId, supplierId)
                        }.toList()

                log.info { "导出账单: task:$taskId 共${supplierSubOrders.size}条供应商子订单数据" }

                val skus =
                    supplierSubOrders
                        .asSequence()
                        .mapNotNull { it.product.skuId }
                        .distinct()
                        .chunked(1000)
                        .flatMap {
                            supplierSkuRepository.findAllBySystemSkuIdIn(it)
                        }.associateBy { it.systemSkuId!! }

                log.info { "导出账单: task:$taskId 共${skus.size}条相关sku数据" }

                val supplierMainOrderSkus = ConcurrentHashMap<Long, Pair<String, MutableList<SupplierProductBillExcelModel>>>()

                val dateFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.SSS")

                runBlocking {
                    supplierSubOrders.forEach {
                        launch(Dispatchers.IO) {
                            val refMainOrder =
                                supplierMainOrdersGroup[it.mainOrderId to it.product.supplierId] ?: run {
                                    log.warn {
                                        "导出账单: task:$taskId 不应该出现的异常, 无法找到供应商订单: 主订单ID ${it.mainOrderId}|供应商ID ${it.product.supplierId}"
                                    }
                                    return@launch
                                }
                            val time =
                                Instant
                                    .ofEpochMilli(it.createdAt!!)
                                    .atZone(ZoneId.systemDefault())
                                    .toLocalDateTime()
                                    .format(dateFormatter)
                            val uniqueCode = it.orderNo

                            val productName = it.product.title.ifBlank { it.product.cnName }
                            val spu = it.product.spu
                            val quantity = it.product.qty

                            val (_, aces) =
                                supplierMainOrderSkus.computeIfAbsent(refMainOrder.supplierId) {
                                    refMainOrder.supplierName to Collections.synchronizedList(ArrayList())
                                }

                            val sku =
                                skus[it.product.skuId] ?: run {
                                    aces.add(
                                        SupplierProductBillExcelModel(
                                            time = time,
                                            uniqueCode = uniqueCode,
                                            productName = productName,
                                            spu = spu,
                                            quantity = quantity,
                                            unitPrice = null,
                                            price = null,
                                            comment = "无法找到关联的供应商SKU,金额无法计算",
                                        ),
                                    )
                                    return@launch
                                }

                            val unitPrice = sku.purchaseCost
                            val price = sku.purchaseCost!!.multiply(quantity.toBigDecimal())
                            aces.add(
                                SupplierProductBillExcelModel(
                                    time = time,
                                    uniqueCode = uniqueCode,
                                    productName = productName,
                                    spu = spu,
                                    quantity = quantity,
                                    unitPrice = unitPrice,
                                    price = price,
                                ),
                            )
                        }
                    }
                }
                log.info { "导出账单: task:$taskId 处理完毕数据,写入excel" }

                val data = ByteArrayOutputStream()
                val writer =
                    EasyExcel
                        .write(data, SupplierProductBillExcelModel::class.java)
                        .build()

                if (supplierMainOrderSkus.isEmpty()) {
                    writer.write(emptyList<SupplierProductBillExcelModel>(), EasyExcel.writerSheet(0, "无供应商").build())
                } else {
                    supplierMainOrderSkus.values.mapIndexed { index, (name, values) ->
                        writer.write(values, EasyExcel.writerSheet(index, name).build())
                    }
                }

                writer.finish()
                log.info { "导出账单: task:$taskId 写出完毕" }

                val excelUrl =
                    ossClient.uploadFile(
                        "/excel/supplier_bills_${fileName}_${System.currentTimeMillis()}.xlsx",
                        ByteArrayInputStream(data.toByteArray()),
                    )

                log.info { "导出账单: taskId: $taskId Excel文件上传完成,URL: $excelUrl" }

                excelUrl
            }.onSuccess { excelUrl ->
                userExcelTaskRepository.findByIdOrNull(taskId)?.let {
                    log.info { "导出账单: 更新导出任务状态为成功，taskId: $taskId" }
                    it.zipUrl = ""
                    it.fileUrl = excelUrl
                    it.wayBillPdfMergeUrl = ""
                    it.status = UserExcelTaskStatus.SUCCESS
                    it.updatedAt = System.currentTimeMillis()
                    userExcelTaskRepository.saveAndFlush(it)
                }
            }.onFailure {
                log.error(it) { "导出账单: 导出供应商订单失败，taskId: $taskId" }
                userExcelTaskRepository.findByIdOrNull(taskId)?.let {
                    log.info { "导出账单: 更新导出任务状态为失败，taskId: $taskId" }
                    it.status = UserExcelTaskStatus.FAILED
                    it.updatedAt = System.currentTimeMillis()
                    userExcelTaskRepository.saveAndFlush(it)
                }
            }
    }

    @Transactional(rollbackOn = [Exception::class])
    fun accept(id: Long) {
        val order = supplierMainOrderRepository.findByIdOrNull(id) ?: throw CognitionException("没有找到订单")
        log.info { "开始接受供应商主订单 | 订单ID: ${order.id} | 订单号: ${order.mainOrderId}" }
        order.accepted = true
        supplierMainOrderRepository.saveAndFlush(order)
        log.info { "接受供应商主订单成功 | 订单ID: ${order.id} | 订单号: ${order.mainOrderId}" }
        val supplierOrders =
            supplierOrderRepository.findByMainOrderIdAndProduct_SupplierId(order.mainOrderId, order.supplierId)
        supplierOrders.forEach {
            it.accepted = true
        }
        supplierOrderRepository.saveAll(supplierOrders)
    }

    @Transactional(rollbackOn = [Exception::class])
    fun uploadAttachment(id: Long, file: MultipartFile): String {
        val order = supplierMainOrderRepository.findByIdOrNull(id) ?: throw CognitionException("没有找到订单")

        // 验证文件
        check(file.size > 0) { "文件为空" }
        check(!file.originalFilename.isNullOrBlank()) { "文件名为空" }
//        check(file.size <= 5000 * 1024 * 1024) { "文件大小超过50MB限制" }

        val user = UserContextHolder.user!!

        // 上传文件到OSS
        val attachmentUrl = ossClient.uploadFile(
            "/supplier-main-order-attachments/${user.bizId}/${UUID.randomUUID()}.${getFileExtension(file.originalFilename)}",
            file.inputStream
        )

        // 更新订单的附件列表
        val updatedAttachmentUrls = order.attachmentUrls.toMutableList()
        updatedAttachmentUrls.add(attachmentUrl)
        order.attachmentUrls = updatedAttachmentUrls

        supplierMainOrderRepository.saveAndFlush(order)

        log.info { "成功为供应商主订单添加附件 | 订单ID: ${order.id} | 附件URL: $attachmentUrl" }

        return attachmentUrl
    }

    private fun getFileExtension(filename: String?): String {
        if (filename.isNullOrBlank()) return "unknown"
        val lastDotIndex = filename.lastIndexOf('.')
        return if (lastDotIndex > 0 && lastDotIndex < filename.length - 1) {
            filename.substring(lastDotIndex + 1)
        } else {
            "unknown"
        }
    }

    companion object {
        private val log = KotlinLogging.logger { }
    }
}
