package io.github.clive.luxomssystem.application.inventory.sku

import io.github.clive.luxomssystem.common.PageReq
import io.github.clive.luxomssystem.common.PageResponse
import io.github.clive.luxomssystem.common.toPageable
import io.github.clive.luxomssystem.common.toResponse
import io.github.clive.luxomssystem.domain.sku.domainservice.SkuDomainService
import io.github.clive.luxomssystem.domain.sku.event.SkuCreatedEvent
import io.github.clive.luxomssystem.domain.sku.event.SkuDeletedEvent
import io.github.clive.luxomssystem.domain.sku.event.SkuUpdatedEvent
import io.github.clive.luxomssystem.domain.sku.model.Sku
import io.github.clive.luxomssystem.facade.sku.request.UpdateSkuRequest
import io.github.clive.luxomssystem.facade.sku.response.SkuResponse
import io.github.clive.luxomssystem.infrastructure.repository.jpa.spu.SkuRepository
import io.github.clive.luxomssystem.infrastructure.repository.jpa.spu.SupplierSkuRepository
import io.github.clive.luxomssystem.nextId
import org.springframework.context.ApplicationEventPublisher
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional

@Service
class SkuApplicationService(
    private val skuRepository: SkuRepository,
    private val skuDomainService: SkuDomainService,
    private val eventPublisher: ApplicationEventPublisher,
    private val supplierSkuRepository: SupplierSkuRepository,
) {
    @Transactional
    fun createSku(request: UpdateSkuRequest): SkuResponse {
        val sku =
            Sku(
                id = nextId(),
                spuId = request.spuId!!,
                skuCode = request.skuCode.trim(),
                size = request.size,
                color = request.color,
                purchaseCost = request.purchaseCost,
                purchaseCostCurrency = request.purchaseCostCurrency,
                weight = request.weight,
                volume = request.volume,
                salePrice = request.salePrice,
            )

        skuDomainService.validateSku(sku)
        val savedSku = skuRepository.saveAndFlush(sku)
        eventPublisher.publishEvent(SkuCreatedEvent(savedSku.id))

        return SkuResponse.fromDomain(savedSku)
    }

    @Transactional
    fun updateSku(
        id: Long,
        request: UpdateSkuRequest,
    ): SkuResponse {
        val sku = skuRepository.findById(id).orElseThrow { NoSuchElementException("Sku not found") }

        sku.apply {
            size = request.size
            color = request.color
            purchaseCost = request.purchaseCost
            purchaseCostCurrency = request.purchaseCostCurrency
            salePrice = request.salePrice
            weight = request.weight
            volume = request.volume
        }

        skuDomainService.validateSku(sku)
        val updatedSku = skuRepository.saveAndFlush(sku)
        eventPublisher.publishEvent(SkuUpdatedEvent(updatedSku.id))

        return SkuResponse.fromDomain(updatedSku)
    }

    @Transactional(readOnly = true)
    fun getSku(id: Long): SkuResponse {
        val sku = skuRepository.findById(id).orElseThrow { NoSuchElementException("Sku not found") }
        return SkuResponse.fromDomain(sku)
    }

    @Transactional
    fun deleteSku(id: Long) {
        val sku = skuRepository.findById(id).orElseThrow { NoSuchElementException("Sku not found") }
        skuRepository.delete(sku)
        eventPublisher.publishEvent(SkuDeletedEvent(id))
        supplierSkuRepository.deleteBySystemSkuId(id)
    }

    fun pageQuery(req: PageReq): PageResponse<SkuResponse> =
        skuRepository.findAll(req.toPageable()).map { SkuResponse.fromDomain(it) }.toResponse()
}
