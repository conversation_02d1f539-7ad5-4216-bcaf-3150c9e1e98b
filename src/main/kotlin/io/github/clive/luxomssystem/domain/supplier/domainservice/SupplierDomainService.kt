
package io.github.clive.luxomssystem.domain.supplier.domainservice

import io.github.clive.luxomssystem.common.enums.BaseStatus
import io.github.clive.luxomssystem.domain.supplier.model.Supplier
import org.springframework.stereotype.Service

@Service
class SupplierDomainService {
    fun validateSupplier(supplier: Supplier) {
        require(supplier.name.isNotBlank()) { "Supplier name cannot be blank" }
        require(supplier.phone.isNotBlank()) { "Supplier phone cannot be blank" }
    }

    fun enableSupplier(supplier: Supplier) {
        supplier.status = BaseStatus.ENABLED
    }

    fun disableSupplier(supplier: Supplier) {
        supplier.status = BaseStatus.DISABLED
    }
}
