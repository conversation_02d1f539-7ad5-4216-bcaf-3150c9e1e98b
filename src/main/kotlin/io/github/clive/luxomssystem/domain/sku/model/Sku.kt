
package io.github.clive.luxomssystem.domain.sku.model

import io.github.clive.luxomssystem.domain.doanload.base.AbstractBaseEntity
import io.github.clive.luxomssystem.nextId
import jakarta.persistence.*
import org.hibernate.annotations.ColumnDefault
import java.math.BigDecimal

@Entity
@Table(name = "skus")
class Sku(
    @Id
    val id: Long = nextId(),
    @Column(name = "spu_id", nullable = false)
    val spuId: Long,
    @Column(name = "sku_code", nullable = false, unique = true)
    val skuCode: String,
    @Column(nullable = false)
    var size: String,
    @Column(nullable = false)
    var color: String,
    @Column(name = "purchase_cost", nullable = false)
    var purchaseCost: BigDecimal,
    @Column(name = "purchase_cost_currency", nullable = false)
    var purchaseCostCurrency: String = "cny",
    @Column(name = "sale_price", nullable = false)
    @ColumnDefault("0")
    var salePrice: BigDecimal,
    @Column(nullable = false)
    var weight: BigDecimal,
    @Column(nullable = false)
    var volume: BigDecimal,
    @Column(nullable = false)
    var status: String = "created",
) : AbstractBaseEntity() {
    // Add any domain logic methods here
}
