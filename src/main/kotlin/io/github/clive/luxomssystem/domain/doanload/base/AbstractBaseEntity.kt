package io.github.clive.luxomssystem.domain.doanload.base

import jakarta.persistence.Column
import jakarta.persistence.EntityListeners
import jakarta.persistence.MappedSuperclass
import org.springframework.data.annotation.CreatedBy
import org.springframework.data.annotation.CreatedDate
import org.springframework.data.annotation.LastModifiedBy
import org.springframework.data.annotation.LastModifiedDate
import org.springframework.data.jpa.domain.support.AuditingEntityListener

@MappedSuperclass
@EntityListeners(value = [AuditingEntityListener::class])
abstract class AbstractBaseEntity {
    @CreatedDate
    @Column(name = "created_at", nullable = false, updatable = false)
    var createdAt: Long = -1

    @Column
    @LastModifiedDate
    var updatedAt: Long = -1

    var createdByName = ""

    var updatedByName = ""

    @CreatedBy
    var createdBy: Long = -1

    @LastModifiedBy
    var updatedBy: Long = -1
}
