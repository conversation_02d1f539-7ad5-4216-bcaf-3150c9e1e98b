package io.github.clive.luxomssystem.domain.doanload

import io.github.clive.luxomssystem.common.enums.MainOrderType
import io.github.clive.luxomssystem.common.enums.order.MainOrderImageDownloadStatus
import io.github.clive.luxomssystem.common.enums.order.MainOrderImageDownloadTask
import io.github.clive.luxomssystem.common.enums.order.MainOrderStatus
import io.github.clive.luxomssystem.domain.doanload.base.AbstractBaseEntity
import io.github.clive.luxomssystem.nextId
import io.hypersistence.utils.hibernate.type.array.ListArrayType
import io.hypersistence.utils.hibernate.type.json.JsonType
import jakarta.persistence.*
import org.hibernate.annotations.ColumnDefault
import org.hibernate.annotations.Type
import java.util.*

@Entity
@Table(
    name = "main_orders",
    uniqueConstraints = [
        UniqueConstraint(name = "uk_main_orders_unique_key", columnNames = ["unique_key"]),
    ],
)
data class MainOrder(
    @Id
    val id: Long = nextId(),
    @Column(name = "unique_key", nullable = false, unique = true)
    val uniqueKey: String = UUID.randomUUID().toString(),
    val fileName: String,
    @ColumnDefault("'IMAGE'")
    @Enumerated(EnumType.STRING)
    @Column(name = "type", nullable = false, columnDefinition = "text")
    var type: MainOrderType = MainOrderType.IMAGE,
    @Enumerated
    var status: MainOrderStatus = MainOrderStatus.CREATED,
    @Enumerated
    var imgDownloadStatus: MainOrderImageDownloadStatus = MainOrderImageDownloadStatus.IMAGE_DOWNING,
    var imgZipDownloadUrl: String = "",
    @Type(value = JsonType::class)
    @Column(name = "download_task", nullable = true, columnDefinition = "jsonb")
    var downloadTask: MainOrderImageDownloadTask = MainOrderImageDownloadTask(0, 0, 0, 0),
    @Column(name = "customer_id", nullable = false, columnDefinition = "bigint")
    var customerId: Long = 0,
    @Column(name = "customer_name", nullable = false, columnDefinition = "text")
    var customerName: String = "",
    var compressJobId: String = "",
    @ColumnDefault("false")
    @Column(name = "way_bill_pushed", nullable = false)
    var wayBillPushed: Boolean = false,
    @ColumnDefault("false")
    @Column(name = "supplier_pushed", nullable = false)
    var supplierPushed: Boolean = false,
    @ColumnDefault("1")
    @Column(name = "biz_id", nullable = false, columnDefinition = "bigint")
    var bizId: Long = 0,
    @ColumnDefault("'LUXOMS'")
    @Enumerated(EnumType.STRING)
    @Column(name = "source", nullable = false)
    var source: OrderSource = OrderSource.LUXOMS,
    var subOrderMatchSupplierTaskStatus: String = "",
    @ColumnDefault("false")
    @Column(name = "supplier_notified", nullable = false)
    var supplierNotified: Boolean = false,

    @Type(value = ListArrayType::class)
    @ColumnDefault("'{}'")
    @Column(name = "attachment_urls", nullable = false, columnDefinition = "text[]")
    var attachmentUrls: List<String> = emptyList(),
) : AbstractBaseEntity()

enum class OrderSource {
    LUXOMS,
    OPENAPI,
}
