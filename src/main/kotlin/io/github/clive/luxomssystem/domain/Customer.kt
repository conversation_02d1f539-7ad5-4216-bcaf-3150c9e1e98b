package io.github.clive.luxomssystem.domain

import com.fasterxml.jackson.databind.annotation.JsonSerialize
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer
import io.github.clive.luxomssystem.common.enums.BaseStatus
import io.github.clive.luxomssystem.domain.doanload.base.AbstractBaseEntity
import io.github.clive.luxomssystem.nextId
import jakarta.persistence.*
import org.hibernate.annotations.ColumnDefault

@Entity(name = "customers")
@Table(
    indexes = [
        Index(name = "idx_customers_name", columnList = "name", unique = true),
    ],
)
class Customer : AbstractBaseEntity() {
    @Id
    @Column(name = "id", nullable = false)
    @JsonSerialize(using = ToStringSerializer::class)
    var id: Long = nextId()

    @Column(name = "name", nullable = false, columnDefinition = "text")
    var name: String = ""

    var email: String = ""

    var token = ""

    @Enumerated(EnumType.STRING)
    var status = BaseStatus.ENABLED

    @ColumnDefault("1")
    @Column(name = "biz_id", nullable = false, columnDefinition = "bigint")
    var bizId: Long = 1

    var openapiKey: String? = null
}
