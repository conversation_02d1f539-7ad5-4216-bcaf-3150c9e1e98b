package io.github.clive.luxomssystem.domain.customerSpu.model

import io.github.clive.luxomssystem.domain.CountryStateTaxConfig
import io.github.clive.luxomssystem.domain.CountryStateTaxConfigMatcher
import io.github.clive.luxomssystem.domain.doanload.base.AbstractBaseEntity
import io.github.clive.luxomssystem.nextId
import io.hypersistence.utils.hibernate.type.json.JsonType
import jakarta.persistence.Column
import jakarta.persistence.Entity
import jakarta.persistence.Id
import jakarta.persistence.Table
import org.hibernate.annotations.ColumnDefault
import org.hibernate.annotations.Type
import java.math.BigDecimal

@Deprecated("税率不应该跟spu挂钩", level = DeprecationLevel.ERROR)
@Entity
@Table(name = "customer_spu_country_tax_config")
data class CustomerSpuCountryTaxConfig(
    @Id
    val id: Long = nextId(),
    var customerSpuId: Long,
    /**
     *
     * null 表示所有国家的默认税率
     *
     */
    @Column(name = "country_id", nullable = true)
    var countryId: Long,
    var countryName: String,
    @ColumnDefault("0")
    @Column(name = "additional_tax", precision = 10, scale = 2, nullable = false)
    var additionalTax: BigDecimal = BigDecimal.ZERO,
    @ColumnDefault("0")
    @Column(name = "vat_tax", precision = 10, scale = 2, nullable = false)
    var vatTax: BigDecimal = BigDecimal.ZERO,
    @Type(value = JsonType::class)
    @Column(
        name = "state_tax_configs",
        nullable = false,
        columnDefinition = "jsonb",
    )
    @ColumnDefault("'[]'")
    override var stateTaxConfigs: List<CountryStateTaxConfig>,
) : AbstractBaseEntity(),
    CountryStateTaxConfigMatcher
