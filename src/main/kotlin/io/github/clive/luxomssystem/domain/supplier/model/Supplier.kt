package io.github.clive.luxomssystem.domain.supplier.model

import io.github.clive.luxomssystem.common.enums.BaseStatus
import io.github.clive.luxomssystem.domain.doanload.base.AbstractBaseEntity
import io.github.clive.luxomssystem.nextId
import io.hypersistence.utils.hibernate.type.json.JsonType
import jakarta.persistence.*
import org.hibernate.annotations.ColumnDefault
import org.hibernate.annotations.Type

@Entity(name = "supplier")
@Table(name = "suppliers")
class Supplier(
    @Id
    val id: Long = nextId(),
    @Column(name = "biz_id", nullable = false)
    val bizId: Long,
    @Column(nullable = false)
    var name: String,
    @Column(nullable = false)
    var phone: String,
    @Column(nullable = true)
    var description: String? = null,
    @ColumnDefault("0")
    @Column(nullable = false)
    var priority: Int = 0,
    @Column(nullable = false)
    @Enumerated(EnumType.STRING)
    var status: BaseStatus = BaseStatus.DISABLED,
    @Type(value = JsonType::class)
    @Column(
        name = "config",
        nullable = false,
        columnDefinition = "jsonb",
    )
    @ColumnDefault("'{}'")
    var config: SupplierConfig = SupplierConfig(),
) : AbstractBaseEntity()

fun List<Supplier>.selectHighestPrioritySupplier(): Supplier? = this.filter { it.status == BaseStatus.ENABLED }.minByOrNull { it.priority }
