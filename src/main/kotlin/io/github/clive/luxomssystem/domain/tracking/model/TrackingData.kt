package io.github.clive.luxomssystem.domain.tracking.model

/**
 * 轨迹数据
 * 用于表示从外部API转换后的轨迹信息，不包含实体相关的字段
 */
data class TrackingData(
    /**
     * 跟踪号
     */
    val trackingNumber: String? = null,
    /**
     * 当前状态
     */
    val currentStatus: TrackingStatus,
    /**
     * 目的地国家代码
     */
    val destinationCountry: String? = null,
    /**
     * 起始国家代码
     */
    val originCountry: String? = null,
    /**
     * 末端服务商信息
     */
    val lastMileProvider: LastMileProvider? = null,
    /**
     * 轨迹事件列表
     */
    val trackingEvents: List<TrackingEvent>,
    /**
     * 签收天数
     */
    val deliveryDays: Int? = null,
    /**
     * 妥投证明链接
     */
    val podLinks: List<String> = emptyList(),
    /**
     * 原始响应数据（用于调试和数据恢复）
     */
    val rawData: Map<String, Any> = emptyMap()
)
