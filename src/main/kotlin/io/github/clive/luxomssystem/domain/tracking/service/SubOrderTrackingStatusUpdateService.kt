package io.github.clive.luxomssystem.domain.tracking.service

import io.github.clive.luxomssystem.domain.SubOrder
import io.github.clive.luxomssystem.domain.tracking.model.TrackingStatus
import io.github.clive.luxomssystem.infrastructure.repository.jpa.SubOrderRepository
import io.github.oshai.kotlinlogging.KotlinLogging
import org.jetbrains.annotations.Async
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Propagation
import org.springframework.transaction.annotation.Transactional

/**
 * SubOrder轨迹状态更新服务
 * 负责根据轨迹状态更新相关的SubOrder状态
 */
@Service
class SubOrderTrackingStatusUpdateService(
    private val subOrderRepository: SubOrderRepository,
    private val trackingStatusMappingService: TrackingStatusMappingService
) {

    private val log = KotlinLogging.logger {}

    /**
     * 根据轨迹状态更新相关的SubOrder状态
     * 
     * @param orderNos 订单号列表
     * @param newTrackingStatus 新的轨迹状态
     */
    @org.springframework.scheduling.annotation.Async
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    fun updateSubOrderStatusByTracking(
        orderNos: List<String>,
        newTrackingStatus: TrackingStatus
    ) {
        if (orderNos.isEmpty()) {
            log.debug { "订单号列表为空，跳过SubOrder状态更新" }
            return
        }

        try {
            log.info { "开始更新SubOrder状态 | 订单数量: ${orderNos.size} | 轨迹状态: $newTrackingStatus" }

            // 查找相关的SubOrder
            val subOrders = subOrderRepository.findByOrderNoIn(orderNos)
            if (subOrders.isEmpty()) {
                log.warn { "未找到相关的SubOrder | 订单号: $orderNos" }
                return
            }

            log.debug { "找到${subOrders.size}个SubOrder需要更新状态" }

            // 获取映射的SubOrder状态
            val mappedSubOrderStatus = trackingStatusMappingService.mapTrackingStatusToSubOrderStatus(newTrackingStatus)
            if (mappedSubOrderStatus == null) {
                log.debug { "轨迹状态$newTrackingStatus 不需要映射到SubOrder状态" }
                return
            }

            // 批量更新SubOrder状态
            var updatedCount = 0
            subOrders.forEach { subOrder ->
                if (shouldUpdateSubOrder(subOrder, newTrackingStatus)) {
                    val oldStatus = subOrder.status
                    subOrder.status = mappedSubOrderStatus
                    log.debug { "更新SubOrder状态 | 订单号: ${subOrder.orderNo} | 旧状态: $oldStatus | 新状态: $mappedSubOrderStatus" }
                    updatedCount++
                } else {
                    log.debug { "跳过SubOrder状态更新 | 订单号: ${subOrder.orderNo} | 当前状态: ${subOrder.status}" }
                }
            }

            if (updatedCount > 0) {
                subOrderRepository.saveAll(subOrders)
                log.info { "成功更新${updatedCount}个SubOrder的状态为: $mappedSubOrderStatus" }
            } else {
                log.info { "没有SubOrder需要更新状态" }
            }

        } catch (e: Exception) {
            log.error(e) { "更新SubOrder状态失败 | 订单号: $orderNos | 轨迹状态: $newTrackingStatus" }
            throw e
        }
    }

    /**
     * 判断是否应该更新指定的SubOrder
     */
    private fun shouldUpdateSubOrder(subOrder: SubOrder, newTrackingStatus: TrackingStatus): Boolean {
        return trackingStatusMappingService.shouldUpdateSubOrderStatus(
            currentSubOrderStatus = subOrder.status,
            newTrackingStatus = newTrackingStatus
        )
    }

    /**
     * 批量更新SubOrder状态（异步处理）
     * 
     * @param orderNosList 多个订单号列表
     * @param trackingStatus 轨迹状态
     */
    fun batchUpdateSubOrderStatus(
        orderNosList: List<List<String>>,
        trackingStatus: TrackingStatus
    ) {
        orderNosList.forEach { orderNos ->
            try {
                updateSubOrderStatusByTracking(orderNos, trackingStatus)
            } catch (e: Exception) {
                log.error(e) { "批量更新SubOrder状态失败 | 订单号: $orderNos" }
                // 继续处理其他订单，不中断整个批量处理
            }
        }
    }
}
