
package io.github.clive.luxomssystem.domain.suppliersku.model

import io.github.clive.luxomssystem.common.enums.BaseStatus
import io.github.clive.luxomssystem.domain.doanload.base.AbstractBaseEntity
import jakarta.persistence.*
import java.math.BigDecimal

@Entity
@Table(name = "supplier_sku")
class SupplierSku(
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    val id: Long? = null,
    @Column(name = "biz_id", nullable = false)
    val bizId: Long,
    @Column(name = "supplier_id", nullable = false)
    val supplierId: Long,
    @Column(name = "supplier_spu_id", nullable = false)
    val supplierSpuId: Long,
    @Column(name = "system_spu_id", nullable = false)
    val systemSpuId: Long,
    @Column(name = "system_sku_id")
    val systemSkuId: Long? = null,
    @Column(name = "purchase_cost", precision = 19, scale = 4)
    var purchaseCost: BigDecimal? = null,
    @Column(name = "purchase_cost_currency", length = 3)
    var purchaseCostCurrency: String? = null,
    var systemSkuCode: String? = null,
    @Enumerated(EnumType.STRING)
    @Column(name = "status", nullable = false)
    var status: BaseStatus = BaseStatus.DISABLED,
) : AbstractBaseEntity()
