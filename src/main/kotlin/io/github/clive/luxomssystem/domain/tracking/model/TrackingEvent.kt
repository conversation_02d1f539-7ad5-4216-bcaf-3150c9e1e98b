package io.github.clive.luxomssystem.domain.tracking.model

import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import java.time.ZonedDateTime

/**
 * OMS系统统一的轨迹事件模型
 * 表示包裹在物流过程中的一个具体事件
 */
@JsonIgnoreProperties(ignoreUnknown = true)
data class TrackingEvent(
    /**
     * 事件发生时间
     */
    val eventTime: ZonedDateTime,

    /**
     * 轨迹状态
     */
    val status: TrackingStatus,

    /**
     * 事件描述信息
     */
    val description: String,

    /**
     * 事件发生地点
     */
    val location: String? = null,

    /**
     * 详细地址信息
     */
    val locationDetail: LocationDetail? = null,

    /**
     * 是否为末端配送事件
     */
    val isLastMileEvent: Boolean = false,

    /**
     * 扩展信息
     */
    val extraInfo: Map<String, String> = emptyMap(),

    /**
     * 原始渠道状态码
     */
    val originalStatusCode: String? = null,

    /**
     * 原始渠道状态描述
     */
    val originalStatusDescription: String? = null
) {
    /**
     * 获取完整的地址描述
     */
    fun getFullLocation(): String {
        return when {
            locationDetail != null -> locationDetail.getFullAddress()
            location != null -> location
            else -> ""
        }
    }
}

/**
 * 地址详细信息
 */
@JsonIgnoreProperties(ignoreUnknown = true)
data class LocationDetail(
    /**
     * 国家
     */
    val country: String? = null,

    /**
     * 省/州
     */
    val province: String? = null,

    /**
     * 城市
     */
    val city: String? = null,

    /**
     * 邮编
     */
    val postCode: String? = null,

    /**
     * 详细地址
     */
    val address: String? = null
) {
    /**
     * 获取完整地址
     */
    fun getFullAddress(): String {
        val parts = listOfNotNull(country, province, city, address).filter { it.isNotBlank() }
        return parts.joinToString(", ")
    }
}
