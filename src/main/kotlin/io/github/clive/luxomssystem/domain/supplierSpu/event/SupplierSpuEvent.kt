
package io.github.clive.luxomssystem.domain.supplierSpu.event

import org.springframework.context.ApplicationEvent

/**
 * Base class for SupplierSpu related events.
 */
sealed class SupplierSpuEvent(
    source: Any,
) : ApplicationEvent(source)

/**
 * Event published when a new SupplierSpu is created.
 */
class SupplierSpuCreatedEvent(
    val supplierSpuId: Long,
) : SupplierSpuEvent(supplierSpuId)

/**
 * Event published when a SupplierSpu is updated.
 */
class SupplierSpuUpdatedEvent(
    val supplierSpuId: Long,
) : SupplierSpuEvent(supplierSpuId)

/**
 * Event published when a SupplierSpu is deleted.
 */
class SupplierSpuDeletedEvent(
    val supplierSpuId: Long,
) : SupplierSpuEvent(supplierSpuId)
