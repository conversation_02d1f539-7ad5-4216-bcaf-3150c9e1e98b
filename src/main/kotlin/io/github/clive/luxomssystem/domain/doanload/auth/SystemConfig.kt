package io.github.clive.luxomssystem.domain.doanload.auth

import com.fasterxml.jackson.annotation.JsonIgnore
import com.qcloud.cos.COSClient
import com.qcloud.cos.region.Region
import io.github.clive.luxomssystem.infrastructure.config.converter.OssConfigConverter
import jakarta.persistence.*
import org.hibernate.annotations.ColumnDefault

@Entity
@Table(name = "system_config")
data class SystemConfig(
    @Id
    val id: Long = 0,
    @Column(name = "biz_id", nullable = false)
    val bizId: Long,
    @Convert(converter = OssConfigConverter::class)
    @Column(name = "content", nullable = false, columnDefinition = "text")
    var content: OssConfig = OssConfig(),
    @Column(name = "type", nullable = false, length = 20)
    val type: String,
    @Column(name = "update_at", nullable = false)
    val updateAt: Long,
    @Column(name = "create_at", nullable = false)
    val createAt: Long,
    @Column(name = "status", nullable = false)
    @ColumnDefault("1")
    val status: Int = 1,
) {
    class OssConfig(
        var domain: String = "",
        var regionName: String = "",
        var bucketName: String = "",
        var tmpSecretId: String = "",
        var tmpSecretKey: String = "",
    ) {
        @JsonIgnore
        var cosClient: COSClient? = null

        @JsonIgnore
        var region: Region = Region(regionName)
    }
}
