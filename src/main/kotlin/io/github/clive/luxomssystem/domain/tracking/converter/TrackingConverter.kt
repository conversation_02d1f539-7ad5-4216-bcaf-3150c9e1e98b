package io.github.clive.luxomssystem.domain.tracking.converter

import io.github.clive.luxomssystem.domain.tracking.model.TrackingData

/**
 * 轨迹转换器接口
 * 定义将不同渠道的轨迹响应转换为OMS统一格式的规范
 */
interface TrackingConverter<T> {
    
    /**
     * 获取支持的渠道名称
     */
    fun getSupportedChannel(): String
    
    /**
     * 将渠道特定的轨迹响应转换为OMS统一的轨迹数据
     *
     * @param channelResponse 渠道原始响应数据
     * @param waybillNo 运单号
     * @return 转换后的轨迹数据，如果转换失败返回null
     */
    fun convert(channelResponse: T, waybillNo: String): TrackingData?
    
    /**
     * 验证响应数据是否有效
     * 
     * @param channelResponse 渠道原始响应数据
     * @return 是否为有效的响应数据
     */
    fun isValidResponse(channelResponse: T): Boolean
}
