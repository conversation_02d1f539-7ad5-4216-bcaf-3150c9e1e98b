package io.github.clive.luxomssystem.domain.doanload

import io.github.clive.luxomssystem.domain.doanload.base.AbstractBaseEntity
import jakarta.persistence.*
import org.hibernate.annotations.ColumnDefault

@Entity
@Table(name = "order_image_tasks")
data class OrderImageTask(
    @Id @GeneratedValue(strategy = GenerationType.IDENTITY) val id: Long = 0,
    @Column(name = "order_id", nullable = false) var orderId: Long = 1,
    @ColumnDefault("''") @Column(name = "order_no", nullable = false) val orderNo: String,
    @Column(name = "download_url", nullable = false, columnDefinition = "text")
    val downloadUrl: String = "",
    @Column(name = "tag", nullable = false, columnDefinition = "text ") val tag: String = "",
    @Column(name = "error_message", nullable = false, columnDefinition = "text ")
    var errorMessage: String = "",
    @Column(name = "file_id", nullable = false, columnDefinition = "text ")
    val fileId: String = "",
    @Column(name = "upload_url", nullable = false, columnDefinition = "text ")
    val uploadUrl: String = "",
    @Enumerated(EnumType.STRING)
    @Column(name = "status", nullable = false)
    var status: OrderImageTaskStatus = OrderImageTaskStatus.CREATED,
    @Column(name = "biz_id", nullable = false, columnDefinition = "bigint")
    @ColumnDefault("1")
    val bizId: Long = 0,
    @ColumnDefault("'default'")
    @Column(name = "naming_type", nullable = false, columnDefinition = "text")
    var namingType: String = "",
) : AbstractBaseEntity()

enum class OrderImageTaskStatus {
    CREATED,
    PROCESSING,
    COMPLETED,
    FAILED,
}
