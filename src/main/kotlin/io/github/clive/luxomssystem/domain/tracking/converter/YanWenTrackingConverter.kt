package io.github.clive.luxomssystem.domain.tracking.converter

import io.github.clive.luxomssystem.common.utils.JSON
import io.github.clive.luxomssystem.domain.tracking.model.*
import io.github.clive.luxomssystem.infrastructure.channel.prediction.channel.yanwen.YanWenTrackingResponse
import io.github.oshai.kotlinlogging.KotlinLogging
import org.springframework.stereotype.Component
import java.time.LocalDateTime
import java.time.ZoneId
import java.time.ZonedDateTime
import java.time.format.DateTimeFormatter
import java.time.format.DateTimeParseException

/**
 * 燕文物流轨迹转换器
 * 将燕文API响应转换为OMS统一格式
 */
@Component
class YanWenTrackingConverter : TrackingConverter<YanWenTrackingResponse> {
    override fun getSupportedChannel(): String = "YANWEN"

    override fun isValidResponse(channelResponse: YanWenTrackingResponse): Boolean =
        channelResponse.code == 0 &&
            !channelResponse.result.isNullOrEmpty()

    override fun convert(
        channelResponse: YanWenTrackingResponse,
        waybillNo: String,
    ): TrackingData? {
        if (!isValidResponse(channelResponse)) {
            return null
        }

        val trackingResult =
            channelResponse.result?.firstOrNull {
                it.waybillNumber == waybillNo || it.trackingNumber == waybillNo
            } ?: return null

        // 转换轨迹事件
        val events =
            trackingResult.checkpoints
                ?.mapNotNull { checkpoint ->
                    convertCheckpointToEvent(checkpoint)
                }?.sortedByDescending { it.eventTime } ?: emptyList()

        // 确定当前状态，优先使用trackingStatusWaybill中的状态信息
        val currentStatus = determineCurrentStatus(trackingResult)

        // 构建末端服务商信息
        val lastMileProvider =
            if (!trackingResult.lastMileCarrier.isNullOrBlank()) {
                LastMileProvider(
                    name = trackingResult.lastMileCarrier ?: "",
                    telephone = trackingResult.lastMileCarrierContactNumber,
                    website = trackingResult.lastMileCarrierWebsite,
                )
            } else {
                null
            }

        return TrackingData(
            trackingNumber = trackingResult.trackingNumber,
            currentStatus = currentStatus,
            destinationCountry = trackingResult.destinationCountry,
            originCountry = trackingResult.originCountry,
            lastMileProvider = lastMileProvider,
            trackingEvents = events,
            rawData = mapOf("meta" to  channelResponse)
        )
    }

    /**
     * 转换燕文检查点为轨迹事件
     */
    private fun convertCheckpointToEvent(checkpoint: YanWenTrackingResponse.Checkpoint): TrackingEvent? {
        val timeStamp = checkpoint.timeStamp ?: return null

        try {
            // 解析时间，支持多种燕文时间格式，使用Checkpoint的timeZone
            val eventTime = parseYanWenTimestamp(timeStamp, checkpoint.timeZone)

            val status = convertYanWenStatus(checkpoint.trackingStatus)

            val locationDetail =
                LocationDetail(
                    country = checkpoint.extraProperties?.country,
                    province = checkpoint.extraProperties?.state,
                    city = checkpoint.extraProperties?.city,
                    postCode = checkpoint.extraProperties?.postCode,
                    address = checkpoint.location,
                )

            val extraInfo = mutableMapOf<String, String>()
            checkpoint.extraProperties?.let { extra ->
                extra.flightNumber?.let { extraInfo["flightNumber"] = it }
                extra.trackingStatusDetail?.let { extraInfo["statusDetail"] = it }
                extra.attached?.let { extraInfo["attached"] = it }
            }

            return TrackingEvent(
                eventTime = eventTime,
                status = status,
                description = checkpoint.message ?: "",
                location = checkpoint.location,
                locationDetail = locationDetail,
                isLastMileEvent = checkpoint.isLastMileCheckpoint == 1,
                extraInfo = extraInfo,
                originalStatusCode = checkpoint.trackingStatus,
                originalStatusDescription = checkpoint.message,
            )
        } catch (e: Exception) {
            log.error(e) { "燕文轨迹时间解析失败 | 时间戳: $timeStamp" }
            // 时间解析失败，跳过该事件
            return null
        }
    }

    /**
     * 解析燕文时间戳，支持多种格式，并使用指定时区
     */
    private fun parseYanWenTimestamp(timeStamp: String, timeZone: String?): ZonedDateTime {
        val formatters = listOf(
            DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss"),     // ISO格式: 2025-04-08T09:51:04
            DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"),       // 标准格式: 2025-04-08 09:51:04
            DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss.SSS"), // 带毫秒的ISO格式
            DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.SSS")    // 带毫秒的标准格式
        )

        // 解析时区，如果没有提供则默认使用UTC
        val zoneId = try {
            if (!timeZone.isNullOrBlank()) {
                ZoneId.of(timeZone)
            } else {
                ZoneId.of("UTC")
            }
        } catch (e: Exception) {
            log.warn { "无法解析时区: $timeZone，使用UTC作为默认时区" }
            ZoneId.of("UTC")
        }

        for (formatter in formatters) {
            try {
                val localDateTime = LocalDateTime.parse(timeStamp, formatter)
                return localDateTime.atZone(zoneId)
            } catch (e: DateTimeParseException) {
                // 继续尝试下一个格式
            }
        }

        // 如果所有格式都失败，抛出异常
        throw DateTimeParseException("无法解析燕文时间戳格式: $timeStamp", timeStamp, 0)
    }

    /**
     * 确定当前状态，优先使用trackingStatusWaybill中的状态信息
     */
    private fun determineCurrentStatus(trackingResult: YanWenTrackingResponse.TrackingResult): TrackingStatus {
        // 1. 优先使用trackingStatusWaybill中的一级和二级状态
        trackingResult.trackingStatusWaybill?.let { statusWaybill ->
            // 优先使用二级状态，因为它更具体
            statusWaybill.level2?.let { level2 ->
                val level2Status = convertLevel2Status(level2)
                if (level2Status != TrackingStatus.UNKNOWN) {
                    return level2Status
                }
            }

            // 其次使用一级状态
            statusWaybill.level1?.let { level1 ->
                val level1Status = convertLevel1Status(level1)
                if (level1Status != TrackingStatus.UNKNOWN) {
                    return level1Status
                }
            }
        }

        // 2. 使用trackingStatus字段
        val trackingStatus = convertYanWenStatus(trackingResult.trackingStatus)
        if (trackingStatus != TrackingStatus.UNKNOWN) {
            return trackingStatus
        }

        // 3. 如果都没有，尝试从最新的checkpoint获取状态
        val latestCheckpoint = trackingResult.checkpoints?.maxByOrNull { checkpoint ->
            try {
                parseYanWenTimestamp(checkpoint.timeStamp ?: "", checkpoint.timeZone)
            } catch (e: Exception) {
                // 如果时间解析失败，使用最小时间
                ZonedDateTime.now().minusYears(100)
            }
        }

        latestCheckpoint?.trackingStatus?.let { checkpointStatus ->
            val status = convertYanWenStatus(checkpointStatus)
            if (status != TrackingStatus.UNKNOWN) {
                return status
            }
        }

        return TrackingStatus.UNKNOWN
    }

    /**
     * 转换燕文状态为OMS统一状态
     * 支持操作状态码、一级状态码、二级状态码以及英文状态名称
     */
    private fun convertYanWenStatus(yanwenStatus: String?): TrackingStatus {
        if (yanwenStatus.isNullOrBlank()) {
            return TrackingStatus.UNKNOWN
        }

        val status = yanwenStatus.uppercase().trim()

        // 1. 优先匹配操作状态码（如OR10、PU10、SC00等）
        val operationCodeStatus = convertOperationCode(status)
        if (operationCodeStatus != TrackingStatus.UNKNOWN) {
            return operationCodeStatus
        }

        // 2. 匹配一级状态编码（1-9、0）
        val level1Status = convertLevel1Status(status)
        if (level1Status != TrackingStatus.UNKNOWN) {
            return level1Status
        }

        // 3. 匹配二级状态编码（0-15）
        val level2Status = convertLevel2Status(status)
        if (level2Status != TrackingStatus.UNKNOWN) {
            return level2Status
        }
        return TrackingStatus.UNKNOWN
    }

    /**
     * 转换燕文操作状态码为OMS状态
     * 根据燕文官方文档的操作状态码映射表
     */
    private fun convertOperationCode(operationCode: String): TrackingStatus =
        when (operationCode) {
            // 订单相关状态
            "OR10" -> TrackingStatus.PRE_ADVICE_RECEIVED  // 订单已生成
            "OR14" -> TrackingStatus.PRE_ADVICE_RECEIVED  // 尾程派送订单确认
            "OR16" -> TrackingStatus.PRE_ADVICE_RECEIVED  // 尾程创建订单成功
            "OR30" -> TrackingStatus.CANCELLED            // 订单已取消
            "OR35" -> TrackingStatus.EXCEPTION            // 发货截留成功

            // 揽收相关状态
            "PU10" -> TrackingStatus.PICKED_UP            // 燕文已揽收
            "PU30" -> TrackingStatus.EXCEPTION            // 揽收失败

            // 仓库处理相关状态
            "SC00", "SC10", "SC11", "SC12", "SC13", "SC15", "SC16", "SC17", "SC18", "SC19", "SC20",
            "SC26", "SC27", "SC63", "S201", "S202", "S203", "S204", "S205" -> TrackingStatus.IN_TRANSIT

            // 异常处理状态
            "SC21", "SC36" -> TrackingStatus.EXCEPTION    // 异常件录入、异常件完结

            // 退件相关状态
            "SC34", "SC37", "SC38", "SC80", "SC85" -> TrackingStatus.RETURNED  // 退件相关

            // 预录单状态
            "SC40", "SC45", "SC45A", "SC45B" -> TrackingStatus.IN_TRANSIT  // 预录单电子信息

            // 航空运输状态
            "S206", "S207", "S208", "S209", "S210", "S211", "S212", "S213" -> TrackingStatus.IN_TRANSIT

            // 海外处理状态
            "S301", "S302", "S303", "S304", "S305", "S306" -> TrackingStatus.ARRIVED_DESTINATION_COUNTRY

            // 出口报关状态
            "EC10", "EC20" -> TrackingStatus.IN_TRANSIT   // 出口报关信息、出口报关放行
            "EC30" -> TrackingStatus.EXCEPTION            // 出口报关异常

            // 进口清关状态
            "IC50" -> TrackingStatus.IN_CUSTOMS           // 开始进口清关
            "IC51", "IC54", "IC55", "IC70" -> TrackingStatus.EXCEPTION  // 进口清关异常
            "IC60", "IC61" -> TrackingStatus.CUSTOMS_CLEARED  // 进口清关完成
            "IC80", "IC81", "IC82" -> TrackingStatus.IN_CUSTOMS  // 税务相关

            // 航空/运输状态
            "LH07", "LH08", "LH09", "LH10", "LH11", "LH12", "LH13", "LH15", "LH16", "LH20",
            "LH21", "LH22", "LH23", "LH24", "LH25", "LH26", "LH30", "LH31", "LH32", "LH33",
            "LH34", "LH38", "LH39", "LH40", "LH45", "LH47", "LH48", "LH49" -> TrackingStatus.IN_TRANSIT

            "LH14" -> TrackingStatus.EXCEPTION            // 场站查验扣留
            "LH35" -> TrackingStatus.EXCEPTION            // 国际运输-延误
            "LH36" -> TrackingStatus.EXCEPTION            // 货物损坏
            "LH37" -> TrackingStatus.EXCEPTION            // 航站/港区堆场拆板异常
            "LH55" -> TrackingStatus.EXCEPTION            // 确认干线丢件

            // 末端派送状态
            "LM03", "LM09" -> TrackingStatus.ARRIVED_DESTINATION_COUNTRY  // EP小件操作扫描、境外干线到达EP仓库
            "LM10" -> TrackingStatus.ARRIVED_DESTINATION_COUNTRY  // 到达派送目的国
            "LM11", "LM12", "LM13", "LM15" -> TrackingStatus.IN_TRANSIT  // 目的国内转运
            "LM20" -> TrackingStatus.ARRIVED_FOR_PICKUP   // 到达目的国最后派送点
            "LM25" -> TrackingStatus.OUT_FOR_DELIVERY     // 离开目的国最后派送点
            "LM30" -> TrackingStatus.ARRIVED_FOR_PICKUP   // 到达待取
            "LM35" -> TrackingStatus.OUT_FOR_DELIVERY     // 尝试投递
            "LM40" -> TrackingStatus.DELIVERED            // 妥投成功/签收成功/完成提货
            "LM45" -> TrackingStatus.RETURNED             // 海外退件签收成功
            "LM40E" -> TrackingStatus.DELIVERED           // 预计妥投
            "LM50" -> TrackingStatus.DELIVERY_FAILED      // 派送失败
            "LM54" -> TrackingStatus.RETURNED             // 海外退件入库确认
            "LM55" -> TrackingStatus.OUT_FOR_DELIVERY     // 重派转单获取成功
            "LM56" -> TrackingStatus.EXCEPTION            // 确认海外销毁
            "LM57" -> TrackingStatus.OUT_FOR_DELIVERY     // 确认海外重派
            "LM60" -> TrackingStatus.DELIVERY_FAILED      // 派送延迟
            "LM70" -> TrackingStatus.EXCEPTION            // 需要进一步确认收件人信息
            "LM75" -> TrackingStatus.DELIVERY_FAILED      // 收件人拒绝签收
            "LM85" -> TrackingStatus.EXCEPTION            // 包裹丢失
            "LM90" -> TrackingStatus.RETURNED             // 包裹退回

            // 通知状态
            "NT10" -> TrackingStatus.EXCEPTION            // 通知客户确认信息

            // 其他信息
            "OTHER" -> TrackingStatus.UNKNOWN             // 其他信息

            else -> TrackingStatus.UNKNOWN
        }

    /**
     * 转换燕文一级状态编码为OMS状态
     */
    private fun convertLevel1Status(level1Code: String): TrackingStatus =
        when (level1Code) {
            "1" -> TrackingStatus.NOT_FOUND              // 查询不到
            "2" -> TrackingStatus.PRE_ADVICE_RECEIVED    // 制单完成
            "3" -> TrackingStatus.IN_TRANSIT             // 运输途中
            "4" -> TrackingStatus.OUT_FOR_DELIVERY       // 正在派送
            "5" -> TrackingStatus.ARRIVED_FOR_PICKUP     // 到达待取
            "6" -> TrackingStatus.DELIVERED              // 投递成功
            "7" -> TrackingStatus.UNKNOWN                // 追踪结束（停止轨迹更新）
            "8" -> TrackingStatus.DELIVERY_FAILED        // 投递失败
            "9" -> TrackingStatus.EXCEPTION              // 包裹异常
            "0" -> TrackingStatus.RETURNED               // 包裹退回
            else -> TrackingStatus.UNKNOWN
        }

    /**
     * 转换燕文二级状态编码为OMS状态
     */
    private fun convertLevel2Status(level2Code: String): TrackingStatus =
        when (level2Code) {
            "0" -> TrackingStatus.PRE_ADVICE_RECEIVED    // 已制单
            "1" -> TrackingStatus.PRE_ADVICE_RECEIVED    // 已发货
            "2" -> TrackingStatus.PICKED_UP              // 已收货
            "3" -> TrackingStatus.IN_TRANSIT             // 运输中
            "4" -> TrackingStatus.DELIVERED              // 已妥投
            "5" -> TrackingStatus.CANCELLED              // 已取消
            "6" -> TrackingStatus.EXCEPTION              // 已截留
            "7" -> TrackingStatus.DELIVERY_FAILED        // 投递失败
            "8" -> TrackingStatus.EXCEPTION              // 处理异常
            "9" -> TrackingStatus.RETURNED               // 退回中
            "10" -> TrackingStatus.RETURNED              // 退件签收
            "11" -> TrackingStatus.EXCEPTION             // 转运异常
            "12" -> TrackingStatus.OUT_FOR_DELIVERY      // 派送中
            "13" -> TrackingStatus.ARRIVED_FOR_PICKUP    // 待提取
            "14" -> TrackingStatus.EXCEPTION             // 未制单
            "15" -> TrackingStatus.UNKNOWN               // 追踪结束
            else -> TrackingStatus.UNKNOWN
        }

    companion object{
        private val log = KotlinLogging.logger {}
    }
}
