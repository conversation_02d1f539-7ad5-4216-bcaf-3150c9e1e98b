package io.github.clive.luxomssystem.domain.valueobject

import io.github.clive.luxomssystem.common.enums.DeliveryMethod
import io.github.clive.luxomssystem.common.enums.WaybillChannel
import jakarta.persistence.Column
import jakarta.persistence.Embeddable
import jakarta.persistence.EnumType
import jakarta.persistence.Enumerated

@Embeddable
class ShippingInfo {
    @Enumerated(EnumType.STRING)
    @Column(name = "channel", nullable = false)
    var channel: WaybillChannel = WaybillChannel.SF

    @Column(name = "ship_method", columnDefinition = "text")
    var shipMethod: String? = null

    @Column(name = "way_bill_relation", columnDefinition = "text")
    var wayBillRelation: String? = null

    @Enumerated(EnumType.STRING)
    @Column(name = "delivery_method")
    var deliveryMethod: DeliveryMethod? = null

    @Column(name = "waybill_label_url")
    var waybillLabelUrl: String? = null
}
