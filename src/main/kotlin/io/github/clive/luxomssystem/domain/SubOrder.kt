package io.github.clive.luxomssystem.domain

import io.github.clive.luxomssystem.common.enums.SubOrderStatus
import io.github.clive.luxomssystem.common.utils.JSON
import io.github.clive.luxomssystem.domain.doanload.OrderSource
import io.github.clive.luxomssystem.domain.doanload.base.AbstractBaseEntity
import io.github.clive.luxomssystem.domain.supplierOrder.model.SupplierOrderStatus
import io.github.clive.luxomssystem.domain.valueobject.ProductInfo
import io.github.clive.luxomssystem.domain.valueobject.RecipientInfo
import io.github.clive.luxomssystem.domain.valueobject.ShippingInfo
import io.github.clive.luxomssystem.domain.waybill.model.WayBillStatus
import io.github.clive.luxomssystem.nextId
import io.hypersistence.utils.hibernate.type.array.ListArrayType
import jakarta.persistence.*
import org.hibernate.annotations.ColumnDefault
import org.hibernate.annotations.Type
import java.math.BigDecimal

@Entity(name = "sub_orders")
class SubOrder : AbstractBaseEntity() {
    @Id
    @Column(name = "id", nullable = false)
    var id: Long = nextId()

    var parentId: Long = 0

    var originId: Long? = null

    var fileName: String? = null

    @Column(columnDefinition = "text", unique = true)
    var orderNo: String? = null

    var googleSearch: String? = null

    @Column(columnDefinition = "text")
    var urls: String? = null

    @Column(columnDefinition = "text")
    var effectUrl: String? = null

    @Column(columnDefinition = "text")
    var designUrl: String? = null

    @Embedded
    var recipient: RecipientInfo = RecipientInfo()

    @Embedded
    var product: ProductInfo = ProductInfo()

    @Embedded
    var shipping: ShippingInfo = ShippingInfo()

    @Column(name = "customer_id", nullable = false, columnDefinition = "bigint")
    var customerId: Long = 0

    @Column(name = "customer_name", nullable = false, columnDefinition = "text")
    var customerName: String = ""

    @Column(name = "biz_id", nullable = false, columnDefinition = "bigint")
    @ColumnDefault("1")
    var bizId: Long = 1

    @Enumerated(EnumType.STRING)
    var status = SubOrderStatus.CREATED

    @Column(nullable = true)
    @Enumerated(EnumType.STRING)
    var waybillStatus: WayBillStatus? = null

    @Column(nullable = true)
    @Enumerated(EnumType.STRING)
    var supplierOrderStatus: SupplierOrderStatus? = null

    @Type(ListArrayType::class)
    @Column(
        name = "order_download_task_ids",
        columnDefinition = "bigint[]",
    )
    var orderDownloadTaskIds: List<Long> = listOf()

    @ColumnDefault("'LUXOMS'")
    @Enumerated(EnumType.STRING)
    @Column(name = "source", nullable = false)
    var source: OrderSource = OrderSource.LUXOMS

    @ColumnDefault("''")
    @Column(name = "remark", nullable = false, columnDefinition = "text")
    var remark: String = ""

    @Column(name = "failed_message", nullable = true, columnDefinition = "text")
    var failedReason: String? = null

    @ColumnDefault("''")
    @Column(name = "customer_order_no", nullable = false, columnDefinition = "text")
    var customerOrderNo: String = ""

    fun splitNewOrder(
        qty: Int,
        index: Int,
        wayBillRelation: String,
    ): SubOrder {
        val subOrder = JSON.parseObject<SubOrder>(JSON.toJSONString(this))
        subOrder.id = nextId()
        subOrder.orderNo = "$orderNo-$index"
        subOrder.product.qty = qty
        subOrder.originId = this.id
        subOrder.shipping.wayBillRelation = wayBillRelation
        return subOrder
    }

    fun canPushSupplier(): Boolean =
        this.status != SubOrderStatus.SPLIT &&
            this.status != SubOrderStatus.FAILED &&
            this.status != SubOrderStatus.CANCELLED
//            && this.waybillStatus == WayBillStatus.COMPLETED

    fun failed(message: String) {
        this.status = SubOrderStatus.FAILED
        this.failedReason = message
    }


}

// 扩展函数保持不变
fun List<SubOrder>.wayBillPrice(): BigDecimal =
    if (size == 1) {
        BigDecimal.valueOf(3)
    } else {
        BigDecimal.valueOf(5)
    }

fun List<SubOrder>.weight(): BigDecimal = sumOf { it.product.weight }
