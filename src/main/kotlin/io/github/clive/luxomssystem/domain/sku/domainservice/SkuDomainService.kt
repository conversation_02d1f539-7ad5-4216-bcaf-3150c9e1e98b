
package io.github.clive.luxomssystem.domain.sku.domainservice

import io.github.clive.luxomssystem.domain.sku.model.Sku
import org.springframework.stereotype.Service

@Service
class SkuDomainService {
    fun validateSku(sku: Sku) {
        // Implement domain-specific validation logic
    }

    fun generateSkuCode(
        spuCode: String,
        color: String,
        size: String,
    ): String = "$spuCode-$color-$size"
}
