package io.github.clive.luxomssystem.domain.waybill.domainservice

import io.github.clive.luxomssystem.domain.waybill.model.Waybill
import org.springframework.stereotype.Service

@Service
class WaybillDomainService {
    /**
     * Perform initial setup for a new Waybill
     */
    fun performInitialSetup(waybill: Waybill) {
        // Add any domain-specific logic for initial setup
    }

    /**
     * Validate Waybill data
     */
    fun validateWaybill(waybill: Waybill) {
//        require(waybill.weight > BigDecimal.ZERO) { "Weight must be greater than zero" }
        // Add more validation rules as needed
    }
}
