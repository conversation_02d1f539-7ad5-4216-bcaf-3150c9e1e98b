package io.github.clive.luxomssystem.domain

import io.github.clive.luxomssystem.domain.doanload.base.AbstractBaseEntity
import jakarta.persistence.Column
import jakarta.persistence.Entity
import jakarta.persistence.Id
import jakarta.persistence.Table

@Entity(name = "user_excel_tasks")
@Table(name = "user_excel_tasks")
class UserExcelTask : AbstractBaseEntity() {
    @Id
    var id: Long = 0

    var fileName = ""

    var type = ""

    var zipUrl = ""

    @Column(name = "file_url", nullable = false)
    var fileUrl: String = ""

    @Column(name = "waybill_pdf_merge_url", nullable = true)
    var wayBillPdfMergeUrl: String? = ""

    @Column(name = "biz_id", nullable = false)
    var bizId: Long = 0

    @Column(name = "user_id", nullable = false)
    var userId: Long = 0

    var status: UserExcelTaskStatus = UserExcelTaskStatus.PROCESSING
}

enum class UserExcelTaskStatus {
    PROCESSING,
    SUCCESS,
    FAILED,
}
