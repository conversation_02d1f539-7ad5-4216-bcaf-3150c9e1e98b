# OMS轨迹系统

## 概述

OMS轨迹系统提供了统一的物流轨迹信息管理功能，将不同渠道（燕文、云途等）的轨迹响应转换为平台标准格式，方便在OMS系统中统一显示和管理。

## 核心功能

### 1. 统一的轨迹模型
- **TrackingInfo**: 统一的轨迹信息实体
- **TrackingEvent**: 轨迹事件模型
- **TrackingStatus**: 标准化的轨迹状态枚举

### 2. 渠道转换器
- **YanWenTrackingConverter**: 燕文轨迹转换器
- **YuntuTrackingConverter**: 云途轨迹转换器
- **TrackingConverter**: 转换器接口，支持扩展新渠道

### 3. 服务层
- **TrackingService**: 轨迹领域服务
- **TrackingApplicationService**: 轨迹应用服务

## 使用方法

### 1. 处理燕文轨迹

```kotlin
@Autowired
private lateinit var trackingApplicationService: TrackingApplicationService

fun processYanWenTracking(response: YanWenTrackingResponse, waybillNo: String) {
    val trackingInfo = trackingApplicationService.processYanWenTracking(response, waybillNo)
    
    if (trackingInfo != null) {
        println("运单号: ${trackingInfo.waybillNo}")
        println("当前状态: ${trackingInfo.currentStatus.displayName}")
        println("轨迹事件数: ${trackingInfo.trackingEvents.size}")
        
        // 保存到数据库或进行其他业务处理
        saveTrackingInfo(trackingInfo)
    }
}
```

### 2. 处理云途轨迹

```kotlin
fun processYuntuTracking(response: YuntuTrackingResponse, waybillNo: String) {
    val trackingInfo = trackingApplicationService.processYuntuTracking(response, waybillNo)
    
    if (trackingInfo != null) {
        println("运单号: ${trackingInfo.waybillNo}")
        println("签收天数: ${trackingInfo.deliveryDays}")
        println("POD链接: ${trackingInfo.podLinks}")
        
        // 处理轨迹事件
        trackingInfo.trackingEvents.forEach { event ->
            println("${event.eventTime} - ${event.status.displayName}: ${event.description}")
        }
    }
}
```

### 3. 批量处理

```kotlin
fun batchProcessTracking() {
    val requests = listOf(
        TrackingRequest("UK242854650YP", "YANWEN", yanwenResponse),
        TrackingRequest("YT2509821272328511", "YUNTU", yuntuResponse)
    )
    
    val result = trackingApplicationService.batchProcessTracking(requests)
    
    println("成功处理: ${result.successCount}")
    println("失败处理: ${result.errorCount}")
}
```

## 状态映射

### 统一状态枚举

| 状态 | 代码 | 描述 | 显示名称 |
|------|------|------|----------|
| NOT_FOUND | NOT_FOUND | 未找到轨迹信息 | 未找到 |
| PRE_ADVICE_RECEIVED | PRE_ADVICE_RECEIVED | 电子预报信息已接收 | 电子预报 |
| PICKED_UP | PICKED_UP | 已收件/已揽收 | 已揽收 |
| IN_TRANSIT | IN_TRANSIT | 运输途中 | 运输途中 |
| ARRIVED_DESTINATION_COUNTRY | ARRIVED_DESTINATION_COUNTRY | 到达目的地国家 | 到达目的地国家 |
| IN_CUSTOMS | IN_CUSTOMS | 清关中 | 清关中 |
| CUSTOMS_CLEARED | CUSTOMS_CLEARED | 清关完成 | 清关完成 |
| ARRIVED_FOR_PICKUP | ARRIVED_FOR_PICKUP | 到达待取 | 到达待取 |
| OUT_FOR_DELIVERY | OUT_FOR_DELIVERY | 派送中 | 派送中 |
| DELIVERY_FAILED | DELIVERY_FAILED | 投递失败 | 投递失败 |
| DELIVERED | DELIVERED | 已签收/已投递 | 已签收 |
| EXCEPTION | EXCEPTION | 异常 | 异常 |
| RETURNED | RETURNED | 已退回 | 已退回 |
| CANCELLED | CANCELLED | 已取消 | 已取消 |
| UNKNOWN | UNKNOWN | 未知状态 | 未知状态 |

### 燕文状态映射

| 燕文状态 | OMS状态 |
|----------|---------|
| NOTFOUND | NOT_FOUND |
| INFORECEIVED | PRE_ADVICE_RECEIVED |
| INTRANSIT | IN_TRANSIT |
| DELIVERED | DELIVERED |
| EXCEPTION | EXCEPTION |
| RETURNED | RETURNED |
| PICKUP | PICKED_UP |
| OUTFORDELIVERY | OUT_FOR_DELIVERY |
| DELIVERYFAILED | DELIVERY_FAILED |

### 云途状态映射

| 云途状态码 | 云途状态 | OMS状态 |
|------------|----------|---------|
| 0 | NOT_FOUND | NOT_FOUND |
| 10 | PRE_ADVICE_RECEIVED | PRE_ADVICE_RECEIVED |
| 20 | IN_TRANSIT | IN_TRANSIT |
| 30 | ARRIVED_FOR_PICKUP | ARRIVED_FOR_PICKUP |
| 40 | DELIVERY_FAILED | DELIVERY_FAILED |
| 50 | DELIVERED | DELIVERED |
| 60 | EXCEPTION | EXCEPTION |
| 80 | UNKNOWN | UNKNOWN |
| 90 | RETURNED | RETURNED |
| 100 | CANCELLED | CANCELLED |

## 扩展新渠道

要添加新的物流渠道支持，需要：

1. 实现 `TrackingConverter<T>` 接口
2. 添加 `@Component` 注解
3. 实现状态映射逻辑

```kotlin
@Component
class NewChannelTrackingConverter : TrackingConverter<NewChannelResponse> {
    
    override fun getSupportedChannel(): String = "NEW_CHANNEL"
    
    override fun isValidResponse(channelResponse: NewChannelResponse): Boolean {
        // 验证响应有效性
    }
    
    override fun convert(channelResponse: NewChannelResponse, waybillNo: String): TrackingInfo? {
        // 实现转换逻辑
    }
}
```

## 数据库表结构

```sql
CREATE TABLE tracking_info (
    id BIGINT PRIMARY KEY,
    waybill_no VARCHAR(100) NOT NULL UNIQUE,
    tracking_number VARCHAR(100),
    channel VARCHAR(50) NOT NULL,
    current_status VARCHAR(50) NOT NULL,
    destination_country VARCHAR(10),
    origin_country VARCHAR(10),
    last_mile_provider JSONB,
    tracking_events JSONB,
    delivery_days INTEGER,
    pod_links JSONB,
    last_updated_at TIMESTAMP,
    is_completed BOOLEAN DEFAULT FALSE,
    raw_data JSONB,
    created_at BIGINT,
    updated_at BIGINT,
    created_by_name VARCHAR(100),
    updated_by_name VARCHAR(100)
);

CREATE INDEX idx_tracking_info_waybill_no ON tracking_info(waybill_no);
CREATE INDEX idx_tracking_info_channel ON tracking_info(channel);
CREATE INDEX idx_tracking_info_status ON tracking_info(current_status);
```

## 注意事项

1. **时间格式**: 不同渠道的时间格式可能不同，转换器会自动处理
2. **状态映射**: 某些渠道特有的状态会映射到最接近的标准状态
3. **异常处理**: 转换失败时会返回null，不会抛出异常
4. **数据完整性**: 原始响应数据会保存在rawData字段中，便于调试和数据恢复
5. **性能考虑**: 批量处理时建议分批进行，避免内存占用过大
