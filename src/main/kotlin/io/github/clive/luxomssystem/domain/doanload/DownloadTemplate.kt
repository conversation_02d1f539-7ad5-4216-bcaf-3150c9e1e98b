package io.github.clive.luxomssystem.domain.doanload

import com.alibaba.excel.EasyExcel
import com.alibaba.excel.context.AnalysisContext
import com.alibaba.excel.event.AnalysisEventListener
import com.alibaba.excel.write.style.column.SimpleColumnWidthStyleStrategy
import io.github.clive.luxomssystem.common.enums.BaseStatus
import io.github.clive.luxomssystem.common.enums.DeliveryMethod
import io.github.clive.luxomssystem.common.enums.DownloadTemplateType
import io.github.clive.luxomssystem.common.enums.WaybillChannel
import io.github.clive.luxomssystem.common.ext.isValidUrl
import io.github.clive.luxomssystem.common.ext.removeAddressSpecialChars
import io.github.clive.luxomssystem.common.ext.removeNameSpecialChars
import io.github.clive.luxomssystem.domain.SubOrder
import io.github.clive.luxomssystem.infrastructure.config.auth.UserContextHolder
import io.github.clive.luxomssystem.nextId
import io.github.oshai.kotlinlogging.KotlinLogging
import io.hypersistence.utils.hibernate.type.json.JsonType
import jakarta.persistence.*
import org.hibernate.annotations.ColumnDefault
import org.hibernate.annotations.Type
import java.io.ByteArrayOutputStream
import java.time.Instant

@Entity
@Table(name = "download_templates")
data class DownloadTemplate(
    @Id
    val id: Long = 0,
    @Column(name = "name", nullable = false)
    val name: String,
    @Type(value = JsonType::class)
    @Column(name = "content", nullable = false, columnDefinition = "jsonb")
    val content: List<DownloadExcelColumn>,
    @ColumnDefault("1")
    @Column(name = "biz_id", nullable = false)
    val bizId: Long = 0,
    @Column(name = "type", nullable = true)
    @ColumnDefault("'IMAGE'")
    @Enumerated(EnumType.STRING)
    val type: DownloadTemplateType = DownloadTemplateType.IMAGE,
    @Enumerated
    val status: BaseStatus = BaseStatus.ENABLED,
    @ColumnDefault("false")
    var onDefault: Boolean = false,
    @Column(name = "created_by", nullable = false)
    val createdBy: String = "admin",
    val updatedBy: String = "admin",
    val updatedAt: Long = Instant.now().toEpochMilli(),
    @Column(name = "created_at", nullable = false)
    val createdAt: Long = Instant.now().toEpochMilli(),
) {
    fun generateExcelTemplate(): ByteArray {
        val outputStream = ByteArrayOutputStream()
        EasyExcel
            .write(outputStream)
            .head(createHead())
            .registerWriteHandler(SimpleColumnWidthStyleStrategy(30))
            .sheet("Template")
            .doWrite(emptyList<List<String>>())
        return outputStream.toByteArray()
    }

    private fun createHead(): List<List<String>> = content.map { listOf(it.name) }

    /**
     * 验证并解析上传的Excel文件
     * @param excelBytes Excel文件的字节数组
     * @return 解析后的数据列表，每个元素是一行数据的Map
     * @throws IllegalArgumentException 当Excel的表头与模板不匹配时抛出
     */
    fun parseExcelContent(excelBytes: ByteArray): List<Map<String, String>> {
        val headerList = content.map { it.name }
        val result = mutableListOf<Map<String, String>>()

        // 使用EasyExcel读取Excel文件
        EasyExcel
            .read(excelBytes.inputStream())
            .sheet()
            .registerReadListener(
                object : AnalysisEventListener<Map<Int, String>>() {
                    private var headMap = mutableMapOf<Int, String>()

                    override fun doAfterAllAnalysed(p0: AnalysisContext?) {}

                    override fun invokeHeadMap(
                        headMap: Map<Int, String>,
                        context: AnalysisContext,
                    ) {
                        // 验证表头是否匹配
                        val excelHeaders = headMap.values.toList()
                        if (!headerList.containsAll(excelHeaders) ||
                            !excelHeaders.containsAll(headerList)
                        ) {
                            throw IllegalArgumentException(
                                "Excel表头与模板不匹配。期望的表头: $headerList, 实际的表头: $excelHeaders",
                            )
                        }
                        this.headMap =
                            headMap.entries
                                .associate { it.key to it.value }
                                .toMutableMap()
                    }

                    override fun invoke(
                        data: Map<Int, String>,
                        context: AnalysisContext,
                    ) {
                        // 将数据转换为Map<列名, 值>的格式
                        try {
                            val rowData =
                                data.entries.filter { it.value != null }.associate { (index, value) ->
                                    headMap[index]!! to value
                                }
                            result.add(rowData)
                        } catch (e: Exception) {
                            log.error { "解析Excel文件失败: ${e.message}" }
                        }
                    }
                },
            ).doRead()

        return result
    }

    /**
     * 验证单个数据行
     * @param rowData 行数据
     * @return 验证结果，包含错误信息（如果有）
     */
    fun validateCompressTaskRow(
        rowData: Map<String, String>,
        urlSplitToken: String,
    ): ValidationResult {
        val errors = mutableListOf<String>()
        val tasks = mutableListOf<OrderImageTask>()
        var orderNo = ""

        // 先解析 orderNo
        content.find { it.type.lowercase() == "orderno" }?.let { column ->
            val value = rowData[column.name]
            if (value == null) {
                errors.add("列 '${column.name}' 必须是存在不能为空")
            } else {
                orderNo = value.trim()
            }
        }

        // 如果 orderNo 有效,再解析其他列
        if (orderNo.isNotEmpty()) {
            content.forEach { column ->
                val value = rowData[column.name]
                when (column.type.lowercase()) {
                    "url" -> {
                        if (!value.isNullOrBlank()) {
                            val splitToken =
                                when (urlSplitToken) {
                                    "0" -> "|"
                                    "1" -> ";"
                                    "2" -> "\n"
                                    else -> "|"
                                }
                            value.split(splitToken).filter { it.isNotBlank() }.map { url ->
                                if (!url.isValidUrl()) {
                                    errors.add("列 '${column.name}' 的值 '$url' 不是有效的请求地址")
                                } else {
                                    tasks.add(
                                        OrderImageTask(
                                            orderNo = orderNo,
                                            downloadUrl = url,
                                            tag = column.name,
                                            bizId = UserContextHolder.user!!.bizId,
                                        ).apply {
                                            createdByName = UserContextHolder.user!!.name
                                            updatedByName = UserContextHolder.user!!.name
                                        },
                                    )
                                }
                            }
                        }
                    }
                }
            }
        }

        return ValidationResult(errors.isEmpty(), errors, tasks)
    }

    fun validateOrderTaskRow(
        rowData: Map<String, String>,
        urlSplitToken: String,
    ): ValidationResult {
        val errors = mutableListOf<String>()
        val tasks = mutableListOf<OrderImageTask>()
        var subOrder: SubOrder? = null

        // 验证必填字段的类型列表
        val requiredTypes =
            listOf(
                "orderNo",
                "userName",
                "spu",
                "receiveName",
                "addr1",
                "country",
                "city",
            )

        // 根据类型查找对应的列名并验证
        requiredTypes.forEach { type ->
            val column = content.find { it.type == type }
            if (column == null) {
                errors.add("缺少必要的列类型: $type")
            } else {
                if (rowData[column.name].isNullOrBlank()) {
                    errors.add("列 '${column.name}' 不能为空")
                }
            }
        }

        // 如果没有错误，创建SubOrder对象
        if (errors.isEmpty()) {
            try {
                subOrder =
                    SubOrder().apply {
                        // 获取 orderNo
                        content.find { it.type == "orderNo" }?.let { column ->
                            orderNo = rowData[column.name]?.trim()
                        }

                        content.find { it.type == "detail" }?.let { column ->
                            remark = rowData[column.name]?.trim() ?: ""
                        }
//
//                    content.find { it.type == "customerOrderNo" }?.let { column ->
//                        customerOrderNo = rowData[column.name]?.trim() ?: ""
//                    }

                        recipient.apply {
                            // 获取用户名
                            content.find { it.type == "userName" }?.let { column ->
                                userName = rowData[column.name]!!.trim().removeNameSpecialChars()
                            }
                            content.find { it.type == "url" }?.let { column ->
                                urls = rowData[column.name]?.trim()
                            }

                            content.find { it.type == "effectUrl" }?.let { column ->
                                effectUrl = rowData[column.name]?.trim()
                            }
                            content.find { it.type == "designUrl" }?.let { column ->
                                designUrl = rowData[column.name]?.trim()
                            }
                            // 获取收件人名
                            content.find { it.type == "receiveName" }?.let { column ->
                                receiverName = rowData[column.name]?.trim().removeNameSpecialChars()
                            }
                            // 获取地址信息
                            content.find { it.type == "addr1" }?.let { column ->
                                address1 = rowData[column.name]?.trim()?.removeAddressSpecialChars()
                            }
                            content.find { it.type == "addr2" }?.let { column ->
                                address2 = rowData[column.name]?.trim()?.removeAddressSpecialChars()
                            }
                            content.find { it.type == "country" }?.let { column ->
                                country = rowData[column.name]?.trim()?.removeAddressSpecialChars()
                            }
                            content.find { it.type == "state" }?.let { column ->
                                state = rowData[column.name]?.trim()?.removeAddressSpecialChars()
                            }
                            content.find { it.type == "city" }?.let { column ->
                                city = rowData[column.name]?.trim()?.removeAddressSpecialChars()
                            }
                            content.find { it.type == "postCode" }?.let { column ->
                                postcode =
                                    if (country.onUs()) {
                                        rowData[column.name]?.trim()?.paddingAheadZero(5)
                                    } else {
                                        rowData[column.name]?.trim()
                                    }
                            }
                            content.find { it.type == "phone" }?.let { column ->
                                phone = rowData[column.name]?.trim()
                            }
                            content.find { it.type == "email" }?.let { column ->
                                email = rowData[column.name]?.trim()
                            }
                            content.find { it.type == "search" }?.let { column ->
                                googleSearch = rowData[column.name]?.trim()
                            }
                        }

                        product.apply {
                            content.find { it.type == "spu" }?.let { column ->
                                spu = rowData[column.name]?.trim()
                            }
                            content.find { it.type == "size" }?.let { column ->
                                size = rowData[column.name]?.trim()
                            }
                            content.find { it.type == "color" }?.let { column ->
                                color = rowData[column.name]?.trim()
                            }
                            content.find { it.type == "qty" }?.let { column ->
                                qty = rowData[column.name]?.toIntOrNull() ?: 1
                            }
                            content.find { it.type == "customerName" }?.let { column ->
                                customName = rowData[column.name]?.trim()
                            }
                            content.find { it.type == "material" }?.let { column ->
                                material = rowData[column.name]?.trim()
                            }
                            content.find { it.type == "hsCode" }?.let { column ->
                                hsCode = rowData[column.name]?.trim()
                            }
                        }

                        shipping.apply {
                            content.find { it.type == "channel" }?.let { column ->
                                channel =
                                    try {
                                        WaybillChannel.matchchannel(rowData[column.name]?.trim()?.uppercase() ?: "SF")
                                            ?: WaybillChannel.SF
                                    } catch (e: IllegalArgumentException) {
                                        errors.add("无效的channel值: ${rowData[column.name]}")
                                        WaybillChannel.SF
                                    }
                            }
                            content.find { it.type == "shipMethod" }?.let { column ->
                                shipMethod = rowData[column.name]?.trim()
                            }
                            content.find { it.type == "wayBillRelation" }?.let { column ->
                                wayBillRelation = rowData[column.name]?.trim() ?: orderNo!!
                                customerOrderNo = rowData[column.name]?.trim() ?: ""
                            }
                            content.find { it.type == "deliveryMethod" }?.let { column ->
                                deliveryMethod =
                                    rowData[column.name]?.let {
                                        try {
                                            DeliveryMethod.valueOf(it.trim().uppercase())
                                        } catch (e: IllegalArgumentException) {
                                            errors.add("无效的deliveryMethod值: $it")
                                            null
                                        }
                                    }
                            }
                        }

                        // 处理URL类型的列，创建OrderImageTask
                        content
                            .filter { it.type == "url" }
                            .forEach { column ->
                                val value = rowData[column.name]
                                if (!value.isNullOrBlank()) {
                                    val splitToken =
                                        when (urlSplitToken) {
                                            "0" -> "|"
                                            "1" -> ";"
                                            "2" -> "\n"
                                            else -> "|"
                                        }
                                    value.split(splitToken).filter { it.isNotBlank() }.map { url ->
                                        tasks.add(
                                            OrderImageTask(
                                                id = nextId(),
                                                orderNo = orderNo!!,
                                                downloadUrl = url,
                                                tag = column.name,
                                                bizId = UserContextHolder.user!!.bizId,
                                            ).apply {
                                                createdByName = UserContextHolder.user!!.name
                                                updatedByName = UserContextHolder.user!!.name
                                            },
                                        )
                                    }
                                }
                            }
                    }
            } catch (e: Exception) {
                errors.add("创建订单时发生错误: ${e.message}")
            }
        }

        // 基础验证
        content.find { it.type == "qty" }?.let { column ->
            rowData[column.name]?.let { qtyStr ->
                try {
                    val qty = qtyStr.toInt()
                    if (qty <= 0) {
                        errors.add("qty 必须大于0")
                    } else {
                    }
                } catch (e: NumberFormatException) {
                    errors.add("qty 必须是有效的数字")
                }
            } ?: errors.add("qty 不能为空")
        }

        content.find { it.type == "email" }?.let { column ->
            rowData[column.name]?.let { email ->
                if (!email.matches(Regex("^[A-Za-z0-9+_.-]+@(.+)$"))) {
                    errors.add("email 格式不正确")
                }
            }
        }

        content.find { it.type == "phone" }?.let { column ->
            rowData[column.name]?.let { phone ->
                if (!phone.matches(Regex("^[0-9+\\-() ]+$"))) {
                    errors.add("phone 格式不正确")
                }
            }
        }

        content.find { it.type == "postcode" }?.let { column ->
            rowData[column.name]?.let { postcode ->
                if (!postcode.matches(Regex("^[0-9A-Za-z\\-]+$"))) {
                    errors.add("postcode 格式不正确")
                }
            }
        }

        subOrder?.apply {
            orderDownloadTaskIds = tasks.map { it.id }
        }

        return ValidationResult(
            isValid = errors.isEmpty(),
            errors = errors,
            tasks = tasks,
            subOrders = subOrder,
        )
    }

    companion object {
        private val log = KotlinLogging.logger {}
    }
}

data class DownloadExcelColumn(
    val name: String,
    val type: String,
)

// 添加验证结果数据类
data class ValidationResult(
    val isValid: Boolean,
    val errors: List<String> = emptyList(),
    val tasks: List<OrderImageTask> = emptyList(),
    val subOrders: SubOrder? = null,
)

fun String?.paddingAheadZero(length: Int): String? {
    if (this == null) {
        return null
    }
    return if (this.length < length) {
        "0".repeat(length - this.length) + this
    } else {
        this
    }
}

fun String?.onUs(): Boolean {
    if ("us".equals(this, ignoreCase = true)) {
        return true
    }

    if ("united states".equals(this, ignoreCase = true)) {
        return true
    }

    return false
}
