package io.github.clive.luxomssystem.domain.supplierSpu.model

import io.github.clive.luxomssystem.common.enums.BaseStatus
import io.github.clive.luxomssystem.domain.doanload.base.AbstractBaseEntity
import io.github.clive.luxomssystem.nextId
import jakarta.persistence.*
import jakarta.validation.constraints.NotNull
import org.hibernate.annotations.ColumnDefault

@Entity(name = "supplier_spu")
@Table(name = "supplier_spu")
class SupplierSpu(
    //    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Id
    val id: Long = nextId(),
    @NotNull
    @Column(name = "biz_id", nullable = false)
    var bizId: Long,
    @NotNull
    @Column(name = "supplier_id", nullable = false)
    var supplierId: Long,
    @NotNull
    @Column(name = "system_spu_id", nullable = false)
    var systemSpuId: Long,
    @Column(name = "system_spu_code", nullable = false)
    var systemSpuCode: String,
    @NotNull
    @Column(name = "supplier_spu_code", nullable = false)
    var supplierSpuCode: String,
    @NotNull
    @Enumerated(EnumType.STRING)
    @Column(name = "status", nullable = false)
    var status: BaseStatus = BaseStatus.DISABLED,
    @Column(name = "title", nullable = false, columnDefinition = "text")
    @ColumnDefault("''")
    var title: String,
    @Column(name = "name", nullable = false, columnDefinition = "text")
    @ColumnDefault("''")
    var name: String,
    @Column(name = "cnName", nullable = false, columnDefinition = "text")
    @ColumnDefault("''")
    var cnName: String,
    @Column(name = "category", nullable = false, columnDefinition = "text")
    @ColumnDefault("''")
    var category: String,
) : AbstractBaseEntity()
