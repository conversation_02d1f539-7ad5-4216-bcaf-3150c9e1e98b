// package io.github.clive.luxomssystem.domain.record
//
// import io.github.clive.luxomssystem.common.enums.WaybillChannel
// import io.github.clive.luxomssystem.nextId
// import jakarta.persistence.Entity
// import jakarta.persistence.Enumerated
// import jakarta.persistence.Id
// import jakarta.persistence.Table
// import java.time.ZonedDateTime
//
// @Entity
// @Table(name = "predict_record")
// data class PredictRecord(
//    @Id
//    val id: Long = nextId(),
//
//    val orderNo: String,
//
//    @Enumerated
//    val channel: WaybillChannel,
//
//    val request: String,
//
//    val response: String,
//
//    val success: Boolean,
//
//    val createdAt: ZonedDateTime = ZonedDateTime.now(),
// )
