
package io.github.clive.luxomssystem.domain.customerSku.event

/**
 * Base class for CustomerSku related events.
 */
sealed class CustomerSkuEvent(
    val customerSkuId: Long,
)

/**
 * Event signifying that a new CustomerSku has been created.
 */
class CustomerSkuCreatedEvent(
    customerSkuId: Long,
) : CustomerSkuEvent(customerSkuId)

/**
 * Event signifying that an existing CustomerSku has been updated.
 */
class CustomerSkuUpdatedEvent(
    customerSkuId: Long,
) : CustomerSkuEvent(customerSkuId)

/**
 * Event signifying that a CustomerSku has been deleted.
 */
class CustomerSkuDeletedEvent(
    customerSkuId: Long,
) : CustomerSkuEvent(customerSkuId)
