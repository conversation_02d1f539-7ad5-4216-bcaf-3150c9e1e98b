package io.github.clive.luxomssystem.domain

import io.github.clive.luxomssystem.domain.doanload.base.AbstractBaseEntity
import io.github.clive.luxomssystem.nextId
import io.hypersistence.utils.hibernate.type.array.ListArrayType
import jakarta.persistence.Column
import jakarta.persistence.Entity
import jakarta.persistence.Id
import jakarta.persistence.Table
import org.hibernate.annotations.ColumnDefault
import org.hibernate.annotations.Type

@Entity
@Table(name = "supplier_main_orders")
data class SupplierMainOrder(
    @Id
    val id: Long = nextId(),
    var supplierId: Long,
    var supplierName: String,
    val mainOrderId: Long,
    val fileName: String,
    val bizId: Long,
    @ColumnDefault("false")
    @Column(name = "accepted", nullable = false)
    var accepted: Boolean = false,
    @Type(value = ListArrayType::class)
    @ColumnDefault("'{}'")
    @Column(name = "attachment_urls", nullable = false, columnDefinition = "text[]")
    var attachmentUrls: List<String> = emptyList(),
) : AbstractBaseEntity()
