package io.github.clive.luxomssystem.domain.convertor

import io.github.clive.luxomssystem.domain.SubOrder
import io.github.clive.luxomssystem.domain.supplierOrder.model.SupplierOrder
import io.github.clive.luxomssystem.domain.supplierOrder.model.SupplierOrderStatus
import io.github.clive.luxomssystem.domain.waybill.model.Waybill
import io.github.clive.luxomssystem.infrastructure.config.auth.UserContextHolder
import java.text.SimpleDateFormat
import java.util.*

object SupplierOrderConverter {
    fun fromSubOrder(subOrder: SubOrder): SupplierOrder =
        SupplierOrder(
            subOrderId = subOrder.id,
            bizId = subOrder.bizId,
            googleSearch = subOrder.googleSearch,
            customerId = subOrder.customerId,
            orderNo = subOrder.orderNo ?: "",
            product = subOrder.product,
            recipient = subOrder.recipient,
            shipping = subOrder.shipping,
            mainOrderId = subOrder.parentId,
            fileName = subOrder.fileName,
            urls = subOrder.urls,
            fileOssPath = "/OMS/" + subOrder.createdAt!!.formatTimestampToDate() + "/" + subOrder.parentId + "/img/" + subOrder.orderNo,
            remark = subOrder.remark,
        ).apply {
            createdBy = subOrder.createdBy
            updatedBy = subOrder.updatedBy
            createdByName = UserContextHolder.user!!.name
            updatedByName = UserContextHolder.user!!.name
            if (product.supplierId != null) {
                status = SupplierOrderStatus.MATCHED_SUPPLY
            }
        }

    fun toWaybill(subOrders: List<SubOrder>): Waybill {
        val subOrder = subOrders[0]
        return Waybill().apply {
            bizId = subOrder.bizId
            googleSearch = subOrder.googleSearch
            mainOrderId = subOrder.parentId
            subOrderId = subOrder.id
            customerId = subOrder.customerId
            orderNo = subOrder.orderNo!!
            fileName = subOrder.fileName
            product = subOrder.product
            recipient = subOrder.recipient
            shipping = subOrder.shipping
            createdBy = subOrder.createdBy
            updatedBy = subOrder.updatedBy
            orderNos = subOrders.mapNotNull { it.orderNo }.joinToString(",")
            createdByName = UserContextHolder.user!!.name
            updatedByName = UserContextHolder.user!!.name
        }
    }
}

fun Long.formatTimestampToDate(): String {
    val dateFormat = SimpleDateFormat("yyyy-M-d")
    return dateFormat.format(Date(this))
}
