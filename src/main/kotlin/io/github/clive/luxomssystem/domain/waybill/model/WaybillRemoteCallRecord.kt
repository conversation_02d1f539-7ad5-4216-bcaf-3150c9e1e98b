package io.github.clive.luxomssystem.domain.waybill.model

import com.fasterxml.jackson.annotation.JsonFormat
import io.github.clive.luxomssystem.common.enums.WaybillChannel
import io.github.clive.luxomssystem.nextId
import jakarta.persistence.Entity
import jakarta.persistence.Id
import jakarta.persistence.Table
import java.time.ZonedDateTime

@Table
@Entity
data class WaybillRemoteCallRecord(
    @Id
    val id: Long = nextId(),
    val waybillId: Long = 0,
    val waybillNo: String = "",
    val waybillLabelUrl: String = "",
    val channel: WaybillChannel? = null,
    val shipMethod: String? = null,
    val createdBy: Long = 0,
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    val createdAt: ZonedDateTime = ZonedDateTime.now(),
)
