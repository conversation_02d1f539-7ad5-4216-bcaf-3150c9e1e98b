package io.github.clive.luxomssystem.domain.doanload.auth

import io.github.clive.luxomssystem.common.enums.BaseStatus
import io.github.clive.luxomssystem.common.enums.RoleLimit
import io.github.clive.luxomssystem.domain.doanload.base.AbstractBaseEntity
import io.github.clive.luxomssystem.nextId
import jakarta.persistence.*
import org.hibernate.annotations.ColumnDefault

@Entity(name = "users")
@Table(name = "users")
data class User(
    @Id
    val id: Long = nextId(),
    @Column(name = "name", nullable = false, columnDefinition = "text")
    val name: String,
    @Column(name = "account", nullable = false, columnDefinition = "text")
    val account: String,
    @Column(name = "password", nullable = false, columnDefinition = "text")
    var password: String,
    @Column(name = "biz_id", nullable = false, columnDefinition = "bigint")
    @ColumnDefault("1")
    var bizId: Long = 1,
    @Column(name = "role_id", nullable = true, columnDefinition = "bigint")
    var roleId: Long? = null,
    @Column(name = "role_name", nullable = true, columnDefinition = "text")
    var roleName: String = "",
    @ColumnDefault("'SELF'")
    @Enumerated(EnumType.STRING)
    @Column(name = "role_limit", nullable = false)
    var roleLimit: RoleLimit = RoleLimit.SELF,
    @Enumerated(EnumType.ORDINAL)
    @Column(name = "status", nullable = false)
    @ColumnDefault("1")
    var status: BaseStatus = BaseStatus.ENABLED,
    @Column(name = "supplier_id", nullable = true, columnDefinition = "bigint")
    var supplierId: Long? = null,
) : AbstractBaseEntity()
