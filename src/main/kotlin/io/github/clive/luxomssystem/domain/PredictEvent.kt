package io.github.clive.luxomssystem.domain

import io.github.clive.luxomssystem.common.enums.WaybillChannel
import io.github.clive.luxomssystem.nextId
import jakarta.persistence.Entity
import jakarta.persistence.Id
import jakarta.persistence.Table

@Entity
@Table(name = "predict_events")
data class PredictEvent(
    @Id
    val id: Long = nextId(),
    val waybillChannel: WaybillChannel,
    val type: String,
    val wayBillId: Long,
    val request: String,
    val response: String,
    val createdAt: Long = System.currentTimeMillis(),
)
