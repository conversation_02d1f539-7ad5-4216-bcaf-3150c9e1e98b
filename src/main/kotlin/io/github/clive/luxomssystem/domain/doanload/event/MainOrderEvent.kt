package io.github.clive.luxomssystem.domain.doanload.event

object MainOrderEvent {
    data class MainOrderCreatedEvent(
        val id: Long,
    )

    data class RetryOrderImageTaskEvent(
        val id: Long,
    )

    data class CompressOrderImageTaskEvent(
        val id: Long,
    )

    data class MainOrderDeletedEvent(
        val id: Long,
    )

    data class OrderPushSupplyEvent(
        val orderId: Long,
    )

    data class OrderNtfySupplyEvent(
        val orderId: Long,
    )
}
