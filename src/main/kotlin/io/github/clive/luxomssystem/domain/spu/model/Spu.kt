package io.github.clive.luxomssystem.domain.spu.model

import io.github.clive.luxomssystem.common.enums.BaseStatus
import io.github.clive.luxomssystem.domain.doanload.base.AbstractBaseEntity
import io.github.clive.luxomssystem.nextId
import io.hypersistence.utils.hibernate.type.array.ListArrayType
import jakarta.persistence.*
import org.hibernate.annotations.ColumnDefault
import org.hibernate.annotations.Type

@Entity(name = "spus")
@Table(name = "spus")
class Spu(
    @Id
    val id: Long = nextId(),
    @ColumnDefault("-1")
    val bizId: Long = -1,
    @Column(name = "spu_code", nullable = false, unique = true)
    var spuCode: String,
    @Column(nullable = false)
    var title: String,
    @Column(name = "name", nullable = false)
    var name: String,
    @Column(name = "cn_name", nullable = false)
    var cnName: String,
    @Column(nullable = false)
    var category: String,
    @Column(name = "product_image", nullable = false)
    var productImage: String = "",
    @Column(name = "package_quantity", nullable = false)
    var packageQuantity: Int,
    @Type(ListArrayType::class)
    @Column(
        name = "image_url",
        columnDefinition = "text[]",
    )
    var showImages: MutableList<String> = mutableListOf(),
    @Column(nullable = false)
    var description: String = "",
    @Type(ListArrayType::class)
    @Column(
        name = "size",
        columnDefinition = "text[]",
    )
    var sizes: MutableList<String> = mutableListOf(),
    @Type(ListArrayType::class)
    @Column(
        name = "color",
        columnDefinition = "text[]",
    )
    var colors: MutableList<String> = mutableListOf(),
    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    var status: BaseStatus = BaseStatus.ENABLED,
    @Column(name = "hs_code", nullable = true)
    var hsCode: String? = null,
    @ColumnDefault("false")
    @Column(name = "is_set", nullable = false)
    var isSet: Boolean = false,
    @Column(name = "set_quantity", nullable = true)
    var setQuantity: Int? = null,
) : AbstractBaseEntity()
