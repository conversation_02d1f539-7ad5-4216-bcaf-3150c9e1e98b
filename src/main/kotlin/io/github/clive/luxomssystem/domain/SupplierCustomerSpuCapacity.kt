package io.github.clive.luxomssystem.domain

import io.github.clive.luxomssystem.nextId
import jakarta.persistence.Column
import jakarta.persistence.Entity
import jakarta.persistence.Id
import jakarta.persistence.Table
import java.time.Instant

@Entity
@Table(name = "supplier_customer_spu_capacity")
data class SupplierCustomerSpuCapacity(
    @Id
    val id: Long = nextId(),
    @Column(name = "date", nullable = false)
    val date: String,
    @Column(name = "supplier_id", nullable = false)
    val supplierId: Long,
    @Column(name = "customer_spu_id", nullable = false)
    val customerSpuId: Long,
    @Column(name = "used_capacity", nullable = false)
    var usedCapacity: Long,
    @Column(name = "total_capacity", nullable = false)
    val totalCapacity: Long,
    @Column(name = "created_at", nullable = false)
    val createdAt: Long = Instant.now().toEpochMilli(),
    @Column(name = "updated_at", nullable = false)
    val updatedAt: Long = Instant.now().toEpochMilli(),
) {
    fun canSupport(quantity: Long): Boolean = totalCapacity == -1L || usedCapacity + quantity <= totalCapacity

    fun remainCapacity(): Long =
        if (totalCapacity == -1L) {
            -1L
        } else {
            totalCapacity - usedCapacity
        }
}
