package io.github.clive.luxomssystem.domain.doanload.auth

import io.github.clive.luxomssystem.common.enums.BaseStatus
import io.github.clive.luxomssystem.common.enums.BusinessType
import io.github.clive.luxomssystem.common.enums.PricingType
import io.github.clive.luxomssystem.domain.doanload.base.AbstractBaseEntity
import io.github.clive.luxomssystem.nextId
import jakarta.persistence.*
import org.hibernate.annotations.ColumnDefault

@Entity
@Table(name = "business")
data class Business(
    @Id
    var id: Long = nextId(),
    var name: String = "",
    @Enumerated(EnumType.STRING)
    @Column(name = "type", nullable = false, columnDefinition = "text")
    @ColumnDefault("'TRAIL'")
    var type: BusinessType = BusinessType.TRAIL,
    @ColumnDefault("-1")
    var subscribeFrom: Long = -1,
    @ColumnDefault("-1")
    var subscribeTo: Long = -1,
    @Enumerated(EnumType.STRING)
    @ColumnDefault("'UNDEFINED'")
    var pricingType: PricingType = PricingType.UNDEFINED,
    @Enumerated(EnumType.ORDINAL)
    var status: BaseStatus = BaseStatus.ENABLED,
) : AbstractBaseEntity()
