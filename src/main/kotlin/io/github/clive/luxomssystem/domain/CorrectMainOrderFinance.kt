package io.github.clive.luxomssystem.domain

import io.github.clive.luxomssystem.domain.doanload.base.AbstractBaseEntity
import jakarta.persistence.Column
import jakarta.persistence.Entity
import jakarta.persistence.Id
import jakarta.persistence.Table
import java.math.BigDecimal

@Entity(name = "correct_main_order_finance")
@Table
data class CorrectMainOrderFinance(
    @Id
    @Column(name = "main_order_id")
    val mainOrderId: Long,
    @Column(name = "correct_finance_cost")
    val correctFinanceCost: BigDecimal,
    @Column(name = "customer_id")
    val customerId: Long,
    @Column(name = "biz_id")
    val bizId: Long,
) : AbstractBaseEntity()
