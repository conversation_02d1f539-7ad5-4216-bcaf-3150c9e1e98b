
package io.github.clive.luxomssystem.domain.suppliersku.event

sealed class SupplierSkuEvent(
    open val supplierSkuId: Long,
)

data class SupplierSkuCreatedEvent(
    override val supplierSkuId: Long,
) : SupplierSkuEvent(supplierSkuId)

data class SupplierSkuUpdatedEvent(
    override val supplierSkuId: Long,
) : SupplierSkuEvent(supplierSkuId)

data class SupplierSkuDeletedEvent(
    override val supplierSkuId: Long,
) : SupplierSkuEvent(supplierSkuId)
