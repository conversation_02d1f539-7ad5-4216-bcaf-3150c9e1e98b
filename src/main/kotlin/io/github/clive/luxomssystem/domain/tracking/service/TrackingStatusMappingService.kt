package io.github.clive.luxomssystem.domain.tracking.service

import io.github.clive.luxomssystem.common.enums.SubOrderStatus
import io.github.clive.luxomssystem.domain.tracking.model.TrackingStatus
import org.springframework.stereotype.Service

/**
 * 轨迹状态映射服务
 * 负责将TrackingStatus映射到SubOrderStatus
 */
@Service
class TrackingStatusMappingService {

    /**
     * 将TrackingStatus映射到SubOrderStatus
     * 
     * @param trackingStatus 轨迹状态
     * @return 对应的子订单状态，如果不需要更新则返回null
     */
    fun mapTrackingStatusToSubOrderStatus(trackingStatus: TrackingStatus): SubOrderStatus? {
        return when (trackingStatus) {
            TrackingStatus.NOT_FOUND -> SubOrderStatus.TRACKING_NOT_FOUND
            TrackingStatus.PRE_ADVICE_RECEIVED -> SubOrderStatus.TRACKING_PRE_ADVICE_RECEIVED
            TrackingStatus.PICKED_UP -> SubOrderStatus.TRACKING_PICKED_UP
            TrackingStatus.IN_TRANSIT -> SubOrderStatus.TRACKING_IN_TRANSIT
            TrackingStatus.ARRIVED_DESTINATION_COUNTRY -> SubOrderStatus.TRACKING_ARRIVED_DESTINATION_COUNTRY
            TrackingStatus.IN_CUSTOMS -> SubOrderStatus.TRACKING_IN_CUSTOMS
            TrackingStatus.CUSTOMS_CLEARED -> SubOrderStatus.TRACKING_CUSTOMS_CLEARED
            TrackingStatus.ARRIVED_FOR_PICKUP -> SubOrderStatus.TRACKING_ARRIVED_FOR_PICKUP
            TrackingStatus.OUT_FOR_DELIVERY -> SubOrderStatus.TRACKING_OUT_FOR_DELIVERY
            TrackingStatus.DELIVERY_FAILED -> SubOrderStatus.TRACKING_DELIVERY_FAILED
            TrackingStatus.DELIVERED -> SubOrderStatus.TRACKING_DELIVERED
            TrackingStatus.EXCEPTION -> SubOrderStatus.TRACKING_EXCEPTION
            TrackingStatus.RETURNED -> SubOrderStatus.TRACKING_RETURNED
            TrackingStatus.CANCELLED -> SubOrderStatus.TRACKING_CANCELLED
            TrackingStatus.UNKNOWN -> SubOrderStatus.TRACKING_UNKNOWN
        }
    }

    /**
     * 判断是否应该更新SubOrder状态
     * 某些情况下可能不需要更新，比如SubOrder已经是最终状态
     * 
     * @param currentSubOrderStatus 当前子订单状态
     * @param newTrackingStatus 新的轨迹状态
     * @return 是否应该更新
     */
    fun shouldUpdateSubOrderStatus(
        currentSubOrderStatus: SubOrderStatus,
        newTrackingStatus: TrackingStatus
    ): Boolean {
        // 如果SubOrder已经是非轨迹相关的最终状态，则不更新
        val finalNonTrackingStatuses = setOf(
            SubOrderStatus.COMPLETED,
            SubOrderStatus.CANCELLED,
            SubOrderStatus.FAILED,
            SubOrderStatus.SPLIT
        )
        
        if (currentSubOrderStatus in finalNonTrackingStatuses) {
            return false
        }
        
        // 如果当前已经是已签收状态，且新状态不是更终态的状态，则不更新
        if (currentSubOrderStatus == SubOrderStatus.TRACKING_DELIVERED) {
            return newTrackingStatus in setOf(
                TrackingStatus.RETURNED,
                TrackingStatus.CANCELLED,
                TrackingStatus.EXCEPTION
            )
        }
        
        return true
    }

    /**
     * 获取轨迹状态的优先级
     * 用于判断状态更新的优先级，数值越大优先级越高
     */
    private fun getTrackingStatusPriority(status: TrackingStatus): Int {
        return when (status) {
            TrackingStatus.NOT_FOUND -> 0
            TrackingStatus.PRE_ADVICE_RECEIVED -> 1
            TrackingStatus.PICKED_UP -> 2
            TrackingStatus.IN_TRANSIT -> 3
            TrackingStatus.ARRIVED_DESTINATION_COUNTRY -> 4
            TrackingStatus.IN_CUSTOMS -> 5
            TrackingStatus.CUSTOMS_CLEARED -> 6
            TrackingStatus.ARRIVED_FOR_PICKUP -> 7
            TrackingStatus.OUT_FOR_DELIVERY -> 8
            TrackingStatus.DELIVERY_FAILED -> 9
            TrackingStatus.DELIVERED -> 10
            TrackingStatus.EXCEPTION -> 11
            TrackingStatus.RETURNED -> 12
            TrackingStatus.CANCELLED -> 13
            TrackingStatus.UNKNOWN -> -1 // 未知状态优先级最低
        }
    }
}
