package io.github.clive.luxomssystem.domain.customerSku.model

import io.github.clive.luxomssystem.common.enums.BaseStatus
import io.github.clive.luxomssystem.domain.doanload.base.AbstractBaseEntity
import io.hypersistence.utils.hibernate.type.json.JsonType
import jakarta.persistence.Column
import jakarta.persistence.Entity
import jakarta.persistence.EnumType
import jakarta.persistence.Enumerated
import jakarta.persistence.GeneratedValue
import jakarta.persistence.GenerationType
import jakarta.persistence.Id
import jakarta.persistence.Table
import org.hibernate.annotations.ColumnDefault
import org.hibernate.annotations.Type
import java.math.BigDecimal

/** CustomerSku entity representing a customer-specific Stock Keeping Unit. */
@Entity
@Table(name = "customer_sku")
class CustomerSku(
    @Id @GeneratedValue(strategy = GenerationType.IDENTITY) val id: Long = 0,
    @Column(name = "biz_id", nullable = false) val bizId: Long,
    @Column(name = "customer_id", nullable = false) val customerId: Long,
    @Column(name = "system_spu_id", nullable = false) val systemSpuId: Long,
    @Column(name = "system_sku_id", nullable = false) val systemSkuId: Long,
    @Column(name = "system_spu_code", nullable = false) val systemSpuCode: String,
    @Column(name = "system_sku_code", nullable = false) var systemSkuCode: String,
    @Column(name = "customer_spu_code") var customerSpuCode: String? = null,
    @Column(name = "customer_spu_id") var customerSpuId: Long? = null,
    @Column(name = "customer_sku_code", unique = true) var customerSkuCode: String? = null,
    @Column(nullable = false) var size: String,
    @Column(nullable = false) var color: String,
    @Column(name = "offline_price", nullable = false) var offlinePrice: BigDecimal,
    @Column(name = "offline_price_currency", nullable = false) var offlinePriceCurrency: String,
    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    var status: BaseStatus = BaseStatus.DISABLED,
    @Type(value = JsonType::class)
    @Column(name = "sku_country_prices", nullable = false, columnDefinition = "jsonb")
    @ColumnDefault("'{}'")
    var skuCountryPrices: Map<Long, CountryPrice> = emptyMap(),
    @Type(value = JsonType::class)
    @Column(name = "sku_pcs_prices", nullable = false, columnDefinition = "jsonb")
    @ColumnDefault("'[]'")
    var skuPcsPrices: List<PcsPrice> = emptyList(),
    @Column(name = "trigger_discount_quantity", nullable = true)
    var triggerDiscountQuantity: Int? = null,
    @Column(name = "discount", precision = 10, scale = 2, nullable = true)
    var discount: BigDecimal? = null,
) : AbstractBaseEntity() {
    fun updateDetails(
        offlinePrice: BigDecimal,
        offlinePriceCurrency: String,
        skuCountryPrices: Map<Long, CountryPrice> = emptyMap(),
        skuPcsPrices: List<PcsPrice> = emptyList(),
        discount: BigDecimal? = null,
        triggerDiscountQuantity: Int? = null,
    ) {
        this.offlinePrice = offlinePrice
        this.offlinePriceCurrency = offlinePriceCurrency
        this.skuCountryPrices = skuCountryPrices
        this.skuPcsPrices = skuPcsPrices
        this.discount = discount
        this.triggerDiscountQuantity = triggerDiscountQuantity
    }

    fun getPrice(
        countryId: Long?,
        qty: Int,
    ): PriceDetail {
        fun usePrice(
            useSkuPcsPrices: List<PcsPrice>,
            defaultPrice: BigDecimal,
        ): PriceDetail {
            for (skuPcsPrice in useSkuPcsPrices) {
                if (qty >= skuPcsPrice.atLeastPcs) {
                    return PriceDetail(skuPcsPrice.price, skuPcsPrice.atLeastPcs)
                }
            }
            return PriceDetail(defaultPrice, qty)
        }

        if (countryId == null) {
            return usePrice(skuPcsPrices, offlinePrice)
        }
        val countryPrice = skuCountryPrices[countryId]
        return if (countryPrice != null) {
            usePrice(countryPrice.skuPcsPrices ?: emptyList(), countryPrice.price)
        } else {
            usePrice(skuPcsPrices, offlinePrice)
        }
    }
}

data class PriceDetail(
    val price: BigDecimal,
    val calcCostGty: Int,
)

data class CountryPrice(
    val price: BigDecimal,
    val countryName: String,
    val isoAlphaTwo: String,
    var skuPcsPrices: List<PcsPrice>? = emptyList(),
) {
    init {
        skuPcsPrices = skuPcsPrices?.sortedBy { it.atLeastPcs }
    }
}

data class PcsPrice(
    val price: BigDecimal,
    val atLeastPcs: Int,
)
