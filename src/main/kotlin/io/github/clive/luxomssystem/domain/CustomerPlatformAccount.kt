package io.github.clive.luxomssystem.domain

import com.fasterxml.jackson.databind.annotation.JsonSerialize
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer
import io.github.clive.luxomssystem.common.enums.BaseStatus
import io.github.clive.luxomssystem.domain.doanload.base.AbstractBaseEntity
import io.github.clive.luxomssystem.nextId
import jakarta.persistence.*
import org.hibernate.annotations.ColumnDefault

/**
 * 客户平台账户实体
 */
@Entity(name = "customer_platform_accounts")
@Table(name = "customer_platform_accounts")
class CustomerPlatformAccount : AbstractBaseEntity() {
    @Id
    @Column(name = "id", nullable = false)
    @JsonSerialize(using = ToStringSerializer::class)
    var id: Long = nextId()

    @Column(name = "customer_id", nullable = false, columnDefinition = "bigint")
    var customerId: Long = 0

    @Column(name = "email", nullable = false, columnDefinition = "text")
    var email: String = ""

    @Column(name = "password", nullable = false, columnDefinition = "text")
    var password: String = ""

    @Column(name = "account_name", nullable = false, columnDefinition = "text")
    var accountName: String = ""

    @Column(name = "description", columnDefinition = "text")
    var description: String = ""

    @Enumerated(EnumType.STRING)
    @Column(name = "status", nullable = false)
    @ColumnDefault("'ENABLED'")
    var status: BaseStatus = BaseStatus.ENABLED

    @ColumnDefault("1")
    @Column(name = "biz_id", nullable = false, columnDefinition = "bigint")
    var bizId: Long = 1

    @Column(name = "last_login_at", columnDefinition = "bigint")
    var lastLoginAt: Long? = null
}
