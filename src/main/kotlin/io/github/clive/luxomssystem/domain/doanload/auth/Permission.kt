package io.github.clive.luxomssystem.domain.doanload.auth

import io.github.clive.luxomssystem.common.enums.BaseStatus
import io.github.clive.luxomssystem.domain.doanload.base.AbstractBaseEntity
import jakarta.persistence.*

@Entity
class Permission : AbstractBaseEntity() {
    @Id
    @Column(name = "id", nullable = false)
    var id: Long? = null

    @Column(name = "name", nullable = false, columnDefinition = "text")
    var name: String = ""

    @Column(name = "description", columnDefinition = "text")
    var description: String = ""

    @Column(name = "resource", nullable = false, columnDefinition = "text")
    var resource = ""

    @Column(name = "parent_id", nullable = false, columnDefinition = "bigint")
    var parentId: Long = -1

    @Enumerated(EnumType.STRING)
    var type = PermissionType.MENU

    @Column(name = "icon", columnDefinition = "text")
    var icon: String? = ""

    @Enumerated(EnumType.ORDINAL)
    var status = BaseStatus.ENABLED
}

enum class PermissionType {
    BUTTON,
    MENU,
}
