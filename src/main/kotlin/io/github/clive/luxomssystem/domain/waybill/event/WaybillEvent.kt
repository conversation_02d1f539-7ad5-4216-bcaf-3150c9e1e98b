package io.github.clive.luxomssystem.domain.waybill.event

import java.time.Instant

sealed class WaybillEvent(
    val waybillId: Long,
    val timestamp: Instant = Instant.now(),
)

class WaybillCreatedEvent(
    waybillId: Long,
) : WaybillEvent(waybillId)

class WaybillPendingEvent(
    waybillId: Long,
    val orderNos: String,
) : WaybillEvent(waybillId)

class WaybillFailedEvent(
    waybillId: Long,
    @Deprecated("设计问题,运单应该对应多个子订单,并不建议使用这个字段,请使用orderNos", level = DeprecationLevel.ERROR)
    val subOrderId: Long,
) : WaybillEvent(waybillId)

class WaybillCompletedEvent(
    waybillId: Long,
    @Deprecated("设计问题,运单应该对应多个子订单,并不建议使用这个字段,请使用orderNos", level = DeprecationLevel.ERROR)
    val subOrderId: Long,
    val orderNos: String,
) : WaybillEvent(waybillId)

class WaybillCancelEvent(
    waybillId: Long,
    val orderNos: String,
) : WaybillEvent(waybillId)

class WaybillOutBoundEvent(
    waybillId: Long,
    val subOrderId: Long,
    val orderNos: String,
) : WaybillEvent(waybillId)