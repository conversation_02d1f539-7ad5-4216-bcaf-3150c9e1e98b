package io.github.clive.luxomssystem.domain.doanload.auth

import io.github.clive.luxomssystem.common.enums.BaseStatus
import io.github.clive.luxomssystem.common.enums.RoleLimit
import io.github.clive.luxomssystem.domain.doanload.base.AbstractBaseEntity
import io.hypersistence.utils.hibernate.type.array.ListArrayType
import jakarta.persistence.*
import org.hibernate.annotations.ColumnDefault
import org.hibernate.annotations.Type

@Entity
class Role : AbstractBaseEntity() {
    @Id
    @Column(name = "id", nullable = false)
    var id: Long? = null

    @Column(name = "name", nullable = false, columnDefinition = "text")
    var name: String = ""

    @Column(name = "biz_id", nullable = false, columnDefinition = "bigint")
    @ColumnDefault("1")
    var bizId: Long = 1

    @Enumerated(EnumType.ORDINAL)
    var status = BaseStatus.ENABLED

    @ColumnDefault("'SELF'")
    @Enumerated(EnumType.STRING)
    @Column(name = "role_limit", nullable = false)
    var limit: RoleLimit = RoleLimit.SELF

    @Type(ListArrayType::class)
    @Column(
        name = "permissions",
        columnDefinition = "bigint[]",
    )
    @ColumnDefault("'{}'")
    var permissions: List<Long> = listOf()
}
