package io.github.clive.luxomssystem.domain

import io.github.clive.luxomssystem.domain.doanload.base.AbstractBaseEntity
import io.hypersistence.utils.hibernate.type.json.JsonType
import jakarta.persistence.Column
import jakarta.persistence.Entity
import jakarta.persistence.Id
import jakarta.persistence.Table
import org.hibernate.annotations.ColumnDefault
import org.hibernate.annotations.Type
import java.math.BigDecimal

@Entity
@Table(name = "customer_country_tax_config")
data class CustomerCountryTaxConfig(
    @Id
    val id: Long,
    val customerId: Long,
    /**
     *
     * null 表示所有国家的默认税率
     *
     */
    @Column(name = "country_id", nullable = true)
    val countryId: Long,
    val countryName: String,
    @ColumnDefault("0")
    @Column(name = "additional_tax", precision = 10, scale = 2, nullable = false)
    val additionalTax: BigDecimal,
    @ColumnDefault("0")
    @Column(name = "vat_tax", precision = 10, scale = 2, nullable = false)
    val vatTax: BigDecimal,
    @Type(value = JsonType::class)
    @Column(name = "state_tax_configs", columnDefinition = "jsonb")
    @ColumnDefault("'[]'")
    override val stateTaxConfigs: List<CountryStateTaxConfig>,
) : AbstractBaseEntity(),
    CountryStateTaxConfigMatcher

interface CountryStateTaxConfigMatcher {
    val stateTaxConfigs: List<CountryStateTaxConfig>

    fun findStateTaxConfig(state: String?): BigDecimal {
        if (state == null) {
            return BigDecimal.ZERO
        }
        val stateTax =
            stateTaxConfigs.find { it.isoAlphaTwo.equals(state, ignoreCase = true) } ?: stateTaxConfigs.find {
                it.enName.equals(
                    state,
                    ignoreCase = true,
                )
            }
        return stateTax?.additionalTax ?: BigDecimal.ZERO
    }
}

data class CountryStateTaxConfig(
    val enName: String,
    val cnName: String,
    val isoAlphaTwo: String,
    val additionalTax: BigDecimal,
)
