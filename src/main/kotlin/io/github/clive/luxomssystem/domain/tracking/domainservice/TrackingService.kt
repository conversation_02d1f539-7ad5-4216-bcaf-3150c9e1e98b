package io.github.clive.luxomssystem.domain.tracking.domainservice

import io.github.clive.luxomssystem.domain.tracking.converter.TrackingConverter
import io.github.clive.luxomssystem.domain.tracking.model.TrackingData
import io.github.clive.luxomssystem.domain.tracking.model.TrackingInfo
import io.github.clive.luxomssystem.domain.tracking.model.TrackingStatus
import io.github.oshai.kotlinlogging.KotlinLogging
import org.springframework.stereotype.Service

/**
 * 轨迹服务
 * 提供轨迹信息的统一处理和转换功能
 */
@Service
class TrackingService(
    private val trackingConverters: List<TrackingConverter<*>>,
) {
    private val log = KotlinLogging.logger {}

    /**
     * 根据渠道名称获取对应的转换器
     */
    fun getConverterByChannel(channel: String): TrackingConverter<*>? =
        trackingConverters.find { it.getSupportedChannel().equals(channel, ignoreCase = true) }

    /**
     * 转换渠道轨迹响应为OMS统一格式
     *
     * @param channelResponse 渠道原始响应
     * @param channel 渠道名称
     * @param waybillNo 运单号
     * @return 转换后的轨迹数据，转换失败返回null
     */
    @Suppress("UNCHECKED_CAST")
    fun <T> convertChannelResponse(
        channelResponse: T,
        channel: String,
        waybillNo: String,
    ): TrackingData? {
        val converter = getConverterByChannel(channel) as? TrackingConverter<T>

        if (converter == null) {
            log.warn { "未找到渠道 $channel 对应的轨迹转换器" }
            return null
        }

        return try {
            val trackingData = converter.convert(channelResponse, waybillNo)
            if (trackingData != null) {
                log.info { "成功转换轨迹信息 | 运单号: $waybillNo | 渠道: $channel | 状态: ${trackingData.currentStatus}" }
            } else {
                log.warn { "轨迹信息转换失败 | 运单号: $waybillNo | 渠道: $channel" }
            }
            trackingData
        } catch (e: Exception) {
            log.error(e) { "轨迹信息转换异常 | 运单号: $waybillNo | 渠道: $channel" }
            null
        }
    }

    /**
     * 获取支持的渠道列表
     */
    fun getSupportedChannels(): List<String> = trackingConverters.map { it.getSupportedChannel() }

    /**
     * 判断轨迹信息是否需要更新
     *
     * @param existingInfo 现有轨迹信息
     * @param newInfo 新的轨迹信息
     * @return 是否需要更新
     */
    fun shouldUpdateTracking(
        existingInfo: TrackingInfo?,
        newInfo: TrackingInfo,
    ): Boolean {
        if (existingInfo == null) {
            return true
        }

        // 如果已经是最终状态，不再更新
        if (TrackingStatus.isFinalStatus(existingInfo.currentStatus)) {
            return false
        }

        // 如果状态发生变化，需要更新
        if (existingInfo.currentStatus != newInfo.currentStatus) {
            return true
        }

        // 如果事件数量发生变化，需要更新
        if (existingInfo.trackingEvents.size != newInfo.trackingEvents.size) {
            return true
        }

        // 如果最新事件时间发生变化，需要更新
        val existingLatestTime = existingInfo.getLatestEvent()?.eventTime
        val newLatestTime = newInfo.getLatestEvent()?.eventTime

        return existingLatestTime != newLatestTime
    }

    /**
     * 合并轨迹信息
     * 将新的轨迹信息合并到现有信息中
     *
     * @param existingInfo 现有轨迹信息
     * @param newInfo 新的轨迹信息
     * @return 合并后的轨迹信息
     */
    fun mergeTrackingInfo(
        existingInfo: TrackingInfo,
        newInfo: TrackingInfo,
    ): TrackingInfo {
        // 合并事件列表，去重并按时间排序
        val allEvents =
            (existingInfo.trackingEvents + newInfo.trackingEvents)
                .distinctBy { "${it.eventTime}_${it.status}_${it.description}" }
                .sortedByDescending { it.eventTime }

        // 更新轨迹信息
        existingInfo.updateTracking(
            status = newInfo.currentStatus,
            events = allEvents,
            deliveryDays = newInfo.deliveryDays ?: existingInfo.deliveryDays,
            trackingNumber = newInfo.trackingNumber ?: existingInfo.trackingNumber,
            destinationCountry = newInfo.destinationCountry ?: existingInfo.destinationCountry,
            originCountry = newInfo.originCountry ?: existingInfo.originCountry,
            lastMileProvider = newInfo.lastMileProvider ?: existingInfo.lastMileProvider,
            podLinks = if (newInfo.podLinks.isNotEmpty()) newInfo.podLinks else existingInfo.podLinks
        )

        return existingInfo
    }

}
