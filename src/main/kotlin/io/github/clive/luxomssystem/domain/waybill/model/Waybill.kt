package io.github.clive.luxomssystem.domain.waybill.model

import io.github.clive.luxomssystem.domain.doanload.base.AbstractBaseEntity
import io.github.clive.luxomssystem.domain.valueobject.ProductInfo
import io.github.clive.luxomssystem.domain.valueobject.RecipientInfo
import io.github.clive.luxomssystem.domain.valueobject.ShippingInfo
import io.github.clive.luxomssystem.nextId
import io.hypersistence.utils.hibernate.type.json.JsonType
import jakarta.persistence.*
import org.hibernate.annotations.ColumnDefault
import org.hibernate.annotations.Type
import java.math.BigDecimal

@Entity(name = "waybills")
@Table(name = "waybills")
class Waybill : AbstractBaseEntity() {
    @Id
    val id: Long = nextId()

    var mainOrderId: Long = 0

    @get:Deprecated(
        "设计问题,运单应该对应多个子订单,并不建议使用这个字段,请使用orderNos",
        level = DeprecationLevel.WARNING,
    )
    var subOrderId: Long = 0

    @Column(name = "biz_id", nullable = false)
    var bizId: Long = 0

    var fileName: String? = null

    @Column(name = "customer_id", nullable = false)
    var customerId: Long = 0

    @get:Deprecated(
        "设计问题,运单应该对应多个子订单,并不建议使用这个字段,请使用orderNos",
        level = DeprecationLevel.WARNING,
    )
    var orderNo: String = ""

    var googleSearch: String? = null

    @Column(name = "package_no")
    @Suppress("DEPRECATION_ERROR")
    var packageNo: String = "BG-$orderNo"

    @Column(name = "total_price", nullable = true, precision = 10, scale = 2)
    var totalPrice: BigDecimal? = null

    @Embedded
    var product = ProductInfo()

    @Embedded
    var recipient: RecipientInfo = RecipientInfo()

    @Embedded
    var shipping: ShippingInfo = ShippingInfo()

    @Column(name = "waybill_no")
    var waybillNo: String? = null

    var trackingNumber: String? = null

    @Column(name = "error_msg", columnDefinition = "text")
    var errorMsg: String? = null

    @Column(nullable = false)
    @Enumerated(EnumType.STRING)
    var status: WayBillStatus = WayBillStatus.CREATED

    @Enumerated(EnumType.STRING)
    @Column(name = "fail_at_status")
    var failAtStatus: WayBillStatus? = null

    @Column(name = "order_nos", columnDefinition = "text")
    var orderNos: String = ""

    @Column(name = "quick_channel_tax", nullable = false, precision = 10, scale = 2)
    @ColumnDefault("'0'")
    var quickChannelTax: BigDecimal = BigDecimal.ZERO

    @Column(name = "customer_need_pay_cost", nullable = false, precision = 10, scale = 2)
    @ColumnDefault("'0'")
    var customerNeedPayCost: BigDecimal = BigDecimal.ZERO

    @ColumnDefault("'0'")
    @Column(name = "customer_need_pay_tax", nullable = false, precision = 10, scale = 2)
    var customerNeedPayTax: BigDecimal = BigDecimal.ZERO

    @Column(columnDefinition = "text", nullable = false)
    @ColumnDefault("''")
    var calculateCustomerNeedPayCostFlow: String = ""

    @Type(value = JsonType::class)
    @Column(
        name = "calculate_customer_need_pay_cost_data",
        nullable = true,
        columnDefinition = "jsonb",
    )
    var calculateCustomerNeedPayCostData: CalculateCustomerNeedPayCostData? = null

    var cancelReason: String? = null

    @Column(name = "tax_number")
    var taxNumber: String? = null

    @Column(name = "ioss_number")
    var iossNumber: String? = null

    var originalId: Long? = null

    fun failed(msg: String) {
        failAtStatus = status
        status = WayBillStatus.FAILED
        errorMsg = msg
    }

    fun clearErrorMsg() {
        errorMsg = null
        failAtStatus = null
    }

    fun address(): String = recipient.fullAddress()
}

enum class WayBillStatus {
    CREATED,
    PENDING,
    COMPLETED,
    FAILED,
    CANCELLED,
    ;

    companion object {
        fun fromString(value: String): WayBillStatus =
            when (value) {
                "CREATED" -> CREATED
                "PENDING" -> PENDING
                "COMPLETED" -> COMPLETED
                "FAILED" -> FAILED
                "CANCELLED" -> CANCELLED
                else -> throw IllegalArgumentException()
            }
    }

    fun onCompleted(): Boolean = this == COMPLETED
}

data class CalculateCustomerNeedPayCostData(
    val orders: List<CalculateCustomerNeedPayCostPreOrderData>,
) {
    data class CalculateCustomerNeedPayCostPreOrderData(
        val orderNo: String,
        val uniqueCode: String,
        val productName: String,
        val spu: String?,
        val comboCustomerSpuId: Long?,
        val comboCustomerSpu: String?,
        val comboCustomerSkuId: Long?,
        val comboCustomerSku: String?,
        val quantity: Int,
        val priceSum: BigDecimal,
        val vatTax: BigDecimal?,
        val additionalTax: BigDecimal?,
        val discounted: BigDecimal?,
        val comment: String,
    )
}
