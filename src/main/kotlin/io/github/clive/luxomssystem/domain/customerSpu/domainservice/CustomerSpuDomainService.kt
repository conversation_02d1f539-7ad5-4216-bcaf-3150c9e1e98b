package io.github.clive.luxomssystem.domain.customerSpu.domainservice

import io.github.clive.luxomssystem.domain.customerSpu.model.CustomerSpu
import org.springframework.stereotype.Service

@Service
class CustomerSpuDomainService {
    /**
     * Perform initial setup for a newly created CustomerSpu.
     * This method can be extended to include any domain-specific logic or validations.
     */
    fun performInitialSetup(customerSpu: CustomerSpu) {
        // Implement any domain-specific logic for initial setup
        // For example, you might want to validate the discount value or set a default status
    }

    /**
     * Validate CustomerSpu before updates.
     * This method ensures that any changes to the CustomerSpu adhere to domain rules.
     */
    fun validateUpdate(customerSpu: CustomerSpu) {
        // Implement validation logic
        // For example, you might want to check if the discount is within a valid range
//        require(customerSpu.discount.compareTo(BigDecimal.ZERO) >= 0 && customerSpu.discount.compareTo(BigDecimal.ONE) <= 0) {
//            "Discount must be between 0 and 1"
//        }
    }
}
