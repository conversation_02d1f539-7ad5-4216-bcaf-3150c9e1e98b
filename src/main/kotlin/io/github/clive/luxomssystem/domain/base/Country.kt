package io.github.clive.luxomssystem.domain.base

import io.github.clive.luxomssystem.domain.doanload.base.AbstractBaseEntity
import io.github.clive.luxomssystem.nextId
import io.hypersistence.utils.hibernate.type.array.ListArrayType
import io.hypersistence.utils.hibernate.type.json.JsonType
import jakarta.persistence.Column
import jakarta.persistence.Entity
import jakarta.persistence.Id
import jakarta.persistence.Table
import org.hibernate.annotations.ColumnDefault
import org.hibernate.annotations.Type

@Entity
@Table(name = "country")
data class Country(
    @Id
    var id: Long = nextId(),
    var countryName: String,
    var isoAlphaTwo: String,
    var isoAlphaThree: String,
    @Type(value = JsonType::class)
    @Column(
        name = "states",
        nullable = false,
        columnDefinition = "jsonb",
    )
    @ColumnDefault("'[]'")
    var states: List<CountryState>,
    @Type(ListArrayType::class)
    @Column(
        name = "include_iso_alpha_threes",
        columnDefinition = "text[]",
    )
    @ColumnDefault("array[]::text[]")
    var includeIsoAlphaThrees: List<String>,
) : AbstractBaseEntity()

data class CountryState(
    val enName: String,
    val cnName: String,
    val isoAlphaTwo: String,
)

// fun main() {
//
//    val res =    listOf(
//            CountryState(
//                enName = "Alaska",
//                cnName = "阿拉斯加州",
//                isoAlphaTwo = "AK",
//            ),
//            CountryState(
//                enName = "Hawaii",
//                cnName = "夏威夷",
//                isoAlphaTwo = "HI",
//            ),
//        CountryState(
//            enName = "Puerto Rico",
//            cnName = "波多黎各",
//            isoAlphaTwo = "PR",
//        ),
//        )
//    println(jacksonObjectMapper().writeValueAsString(res))
// }
