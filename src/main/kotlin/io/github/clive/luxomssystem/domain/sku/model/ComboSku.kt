package io.github.clive.luxomssystem.domain.sku.model

import io.github.clive.luxomssystem.domain.doanload.base.AbstractBaseEntity
import jakarta.persistence.Column
import jakarta.persistence.Entity
import jakarta.persistence.Id
import jakarta.persistence.Table
import java.math.BigDecimal

@Entity(name = "combo_sku")
@Table(name = "combo_sku")
data class ComboSku(
    @Id
    val id: Long,
    @Column(name = "combo_spu_id")
    val comboSpuId: Long,
    @Column(name = "spu_id")
    val spuId: Long,
    @Column(name = "spu_code")
    val spuCode: String,
    @Column(name = "sku_code")
    val skuCode: String,
    @Column(name = "spu_ref_id")
    val spuRefId: String,
    @Column(name = "system_sku_id")
    val systemSkuId: Long,
    @Column
    val size: String,
    @Column
    val color: String,
    @Column(name = "purchase_cost")
    val purchaseCost: BigDecimal,
    @Column(name = "purchase_cost_currency")
    val purchaseCostCurrency: String,
    @Column(name = "sale_price")
    val salePrice: BigDecimal,
    @Column
    val weight: BigDecimal,
    @Column
    val volume: BigDecimal,
    @Column
    val status: String,
) : AbstractBaseEntity()
