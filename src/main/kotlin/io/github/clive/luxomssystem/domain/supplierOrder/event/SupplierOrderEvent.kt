package io.github.clive.luxomssystem.domain.supplierOrder.event

import org.springframework.context.ApplicationEvent

sealed class SupplierOrderEvent(
    source: Any,
) : ApplicationEvent(source) {
    data class AllCreated(
        val mainOrderId: Long,
    ) : SupplierOrderEvent(mainOrderId)

    data class Created(
        val id: Long,
    ) : SupplierOrderEvent(id)

    data class MatchedSupplier(
        val id: Long,
        val orderNo: String,
    ) : SupplierOrderEvent(id)

    data class Updated(
        val id: Long,
    ) : SupplierOrderEvent(id)

    data class StatusChanged(
        val id: Long,
        val newStatus: String,
    ) : SupplierOrderEvent(id)
}
