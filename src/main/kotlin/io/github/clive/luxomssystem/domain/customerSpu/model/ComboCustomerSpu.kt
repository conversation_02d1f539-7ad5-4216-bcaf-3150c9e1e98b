package io.github.clive.luxomssystem.domain.customerSpu.model

import io.github.clive.luxomssystem.common.enums.BaseStatus
import io.github.clive.luxomssystem.domain.doanload.base.AbstractBaseEntity
import io.hypersistence.utils.hibernate.type.array.ListArrayType
import jakarta.persistence.Column
import jakarta.persistence.Entity
import jakarta.persistence.EnumType
import jakarta.persistence.Enumerated
import jakarta.persistence.Id
import jakarta.persistence.Table
import org.hibernate.annotations.Type
import java.math.BigDecimal

@Entity(name = "combo_customer_spu")
@Table(name = "combo_customer_spu")
data class ComboCustomerSpu(
    @Id
    val id: Long,
    @Column(name = "biz_id")
    val bizId: Long,
    @Column(name = "customer_id")
    val customerId: Long,
    @Type(ListArrayType::class)
    @Column(name = "system_spu_ids", columnDefinition = "int8[]")
    val systemSpuIds: List<Long>,
    @Type(ListArrayType::class)
    @Column(name = "system_spu_codes", columnDefinition = "text[]")
    val systemSpuCodes: List<String>,
    @Column(name = "system_combo_spu_id")
    val systemComboSpuId: Long,
    @Column(name = "trigger_discount_quantity")
    val triggerDiscountQuantity: Int,
    @Column(name = "discount", precision = 10, scale = 2)
    val discount: BigDecimal,
    @Enumerated(EnumType.STRING)
    @Column(name = "status")
    val status: BaseStatus,
) : AbstractBaseEntity()
