package io.github.clive.luxomssystem.domain.valueobject

import jakarta.persistence.Column
import jakarta.persistence.Embeddable
import java.math.BigDecimal
import java.math.RoundingMode

@Embeddable
class RecipientInfo {
    @Column(name = "user_name", columnDefinition = "text")
    var userName: String? = null

    @Column(name = "receiver_name", columnDefinition = "text")
    var receiverName: String? = null

    @Column(name = "country", columnDefinition = "text")
    var country: String? = null

    @Column(name = "state", columnDefinition = "text")
    var state: String? = null

    @Column(name = "city", columnDefinition = "text")
    var city: String? = null

    @Column(name = "address1", columnDefinition = "text")
    var address1: String? = null

    @Column(name = "address2", columnDefinition = "text")
    var address2: String? = null

    @Column(name = "postcode", columnDefinition = "text")
    var postcode: String? = null

    @Column(name = "phone", columnDefinition = "text")
    var phone: String? = null

    @Column(name = "email", columnDefinition = "text")
    var email: String? = null

    fun fullAddress(): String =
        listOfNotNull(
            address1,
            address2,
            city,
            state,
            country,
            postcode,
        ).filter { it.isNotBlank() }.joinToString(",") { it.trim() }

    fun address(): String {
        val s = address1 ?: ""
        val s1 = address2 ?: ""
        return s + s1
    }

    fun price(): BigDecimal = 3.toBigDecimal()

    fun price(multiSize: Int): BigDecimal =
        if (multiSize == 1) {
            price()
        } else {
            BigDecimal.valueOf(5).divide(BigDecimal(multiSize), 2, RoundingMode.DOWN)
        }

    fun priceMinFive(multiSize: Int): BigDecimal =
        if (multiSize == 1) {
            BigDecimal(5.1)
        } else {
            BigDecimal.valueOf(5.1).divide(BigDecimal(multiSize), 2, RoundingMode.UP)
        }
}
