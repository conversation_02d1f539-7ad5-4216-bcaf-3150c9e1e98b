package io.github.clive.luxomssystem.domain.customerSku.model

import io.github.clive.luxomssystem.common.enums.BaseStatus
import io.github.clive.luxomssystem.domain.doanload.base.AbstractBaseEntity
import io.hypersistence.utils.hibernate.type.array.ListArrayType
import io.hypersistence.utils.hibernate.type.json.JsonType
import jakarta.persistence.Column
import jakarta.persistence.Entity
import jakarta.persistence.EnumType
import jakarta.persistence.Enumerated
import jakarta.persistence.Id
import jakarta.persistence.Table
import org.hibernate.annotations.ColumnDefault
import org.hibernate.annotations.Type
import java.math.BigDecimal

@Entity(name = "combo_customer_sku")
@Table(name = "combo_customer_sku")
data class ComboCustomerSku(
    @Id
    val id: Long,
    @Column(name = "biz_id")
    val bizId: Long,
    @Column(name = "customer_id")
    val customerId: Long,
    @Column(name = "combo_customer_spu_id")
    val comboCustomerSpuId: Long,
    @Type(ListArrayType::class)
    @Column(name = "system_spu_ids_of_combo", columnDefinition = "int8[]")
    val systemSpuIdsOfCombo: List<Long>,
    @Type(ListArrayType::class)
    @Column(name = "system_spu_codes_of_combo", columnDefinition = "text[]")
    val systemSpuCodesOfCombo: List<String>,
    @Column(name = "combo_spu_id")
    val comboSpuId: Long,
    @Column(name = "combo_sku_id")
    val comboSkuId: Long,
    @Column(name = "system_spu_id")
    val systemSpuId: Long,
    @Column(name = "system_spu_code")
    val systemSpuCode: String,
    @Column(name = "system_sku_id")
    val systemSkuId: Long,
    @Column(name = "system_sku_code")
    val systemSkuCode: String,
    @Column
    val size: String,
    @Column
    val color: String,
    @Column(name = "offline_price")
    val offlinePrice: BigDecimal,
    @Column(name = "offline_price_currency")
    val offlinePriceCurrency: String,
    @Enumerated(EnumType.STRING)
    @Column
    val status: BaseStatus = BaseStatus.DISABLED,
    @Type(value = JsonType::class)
    @Column(name = "sku_country_prices", columnDefinition = "jsonb")
    @ColumnDefault("'{}'")
    val skuCountryPrices: Map<Long, CountryPrice>,
    @Type(value = JsonType::class)
    @Column(name = "sku_pcs_prices", columnDefinition = "jsonb")
    @ColumnDefault("'[]'")
    val skuPcsPrices: List<PcsPrice>,
    @Column(name = "trigger_discount_quantity", nullable = true)
    val triggerDiscountQuantity: Int?,
    @Column(name = "discount", precision = 10, scale = 2, nullable = true)
    val discount: BigDecimal?,
) : AbstractBaseEntity() {
    fun getPrice(
        countryId: Long?,
        qty: Int,
    ): PriceDetail {
        fun usePrice(
            useSkuPcsPrices: List<PcsPrice>,
            defaultPrice: BigDecimal,
        ): PriceDetail {
            for (skuPcsPrice in useSkuPcsPrices) {
                if (qty >= skuPcsPrice.atLeastPcs) {
                    return PriceDetail(skuPcsPrice.price, skuPcsPrice.atLeastPcs)
                }
            }
            return PriceDetail(defaultPrice, qty)
        }

        if (countryId == null) {
            return usePrice(skuPcsPrices, offlinePrice)
        }
        val countryPrice = skuCountryPrices[countryId]
        return if (countryPrice != null) {
            usePrice(countryPrice.skuPcsPrices ?: emptyList(), countryPrice.price)
        } else {
            usePrice(skuPcsPrices, offlinePrice)
        }
    }
}
