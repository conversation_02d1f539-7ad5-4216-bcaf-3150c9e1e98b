package io.github.clive.luxomssystem.domain.tracking.converter

import io.github.clive.luxomssystem.common.utils.JSON
import io.github.clive.luxomssystem.domain.tracking.model.*
import io.github.clive.luxomssystem.infrastructure.channel.prediction.channel.yuntu.OrderTrackingDetail
import io.github.clive.luxomssystem.infrastructure.channel.prediction.channel.yuntu.YuntuTrackingResponse
import io.github.clive.luxomssystem.infrastructure.channel.prediction.channel.yuntu.YuntuTrackingStatus
import org.springframework.stereotype.Component
import java.time.ZoneId

/**
 * 云途物流轨迹转换器
 * 将云途API响应转换为OMS统一格式
 */
@Component
class YuntuTrackingConverter : TrackingConverter<YuntuTrackingResponse> {
    override fun getSupportedChannel(): String = "YUNTU"

    override fun isValidResponse(channelResponse: YuntuTrackingResponse): Boolean = channelResponse.code == "0000"

    override fun convert(
        channelResponse: YuntuTrackingResponse,
        waybillNo: String,
    ): TrackingData? {
        if (!isValidResponse(channelResponse)) {
            return null
        }

        val item = channelResponse.item

        // 转换轨迹事件
        val events =
            item.orderTrackingDetails
                .mapNotNull { detail ->
                    convertTrackingDetailToEvent(detail)
                }.sortedByDescending { it.eventTime }

        // 确定当前状态
        val currentStatus = convertYuntuStatus(item.trackingStatus)

        // 构建末端服务商信息
        val lastMileProvider =
            if (item.providerName.isNotBlank()) {
                LastMileProvider(
                    name = item.providerName,
                    telephone = item.providerTelephone.takeIf { it.isNotBlank() },
                    website = item.providerSite.takeIf { it.isNotBlank() },
                )
            } else {
                null
            }

        // 处理POD链接
        val podLinks = mutableListOf<String>()
        if (item.pod.isNotBlank()) {
            podLinks.add(item.pod)
        }
        item.pods?.forEach { pod ->
            if (pod.isNotBlank() && !podLinks.contains(pod)) {
                podLinks.add(pod)
            }
        }

        return TrackingData(
            trackingNumber = item.trackingNumber.takeIf { it.isNotBlank() },
            currentStatus = currentStatus,
            destinationCountry = item.countryCode.takeIf { it.isNotBlank() },
            lastMileProvider = lastMileProvider,
            trackingEvents = events,
            deliveryDays = if (item.intervalDays > 0) item.intervalDays.toInt() else null,
            podLinks = podLinks,
            rawData =
                mapOf(
                    "packageState" to item.packageState,
                    "trackingStatus" to item.trackingStatus,
                ),
        )
    }

    /**
     * 转换云途轨迹详情为轨迹事件
     */
    private fun convertTrackingDetailToEvent(
        detail: OrderTrackingDetail,
    ): TrackingEvent? {
        try {
            val status = convertYuntuTrackNodeCode(detail.trackNodeCode)

            val locationDetail =
                LocationDetail(
                    country = detail.processCountry.takeIf { it.isNotBlank() },
                    province = detail.processProvince.takeIf { it.isNotBlank() },
                    city = detail.processCity.takeIf { it.isNotBlank() },
                    address = detail.processLocation.takeIf { it.isNotBlank() },
                )

            val extraInfo = mutableMapOf<String, String>()
            extraInfo["trackNodeCode"] = detail.trackNodeCode
            extraInfo["trackCodeDescription"] = detail.trackCodeDescription

            // 添加异常原因信息
            detail.abnormalReasons?.forEach { reason ->
                extraInfo["abnormalReasonCode"] = reason.abnormalReasonCode
                extraInfo["abnormalReason"] = reason.abnormalReason
            }

            return TrackingEvent(
                eventTime = detail.processDate.atZone(ZoneId.of("UTC")), // YunTu默认使用UTC时区
                status = status,
                description = detail.processContent,
                location = detail.processLocation.takeIf { it.isNotBlank() },
                locationDetail = locationDetail,
                isLastMileEvent = isLastMileTrackNode(detail.trackNodeCode),
                extraInfo = extraInfo,
                originalStatusCode = detail.trackNodeCode,
                originalStatusDescription = detail.trackCodeDescription,
            )
        } catch (e: Exception) {
            return null
        }
    }

    /**
     * 转换云途状态为OMS统一状态
     */
    private fun convertYuntuStatus(yuntuStatusCode: Int): TrackingStatus =
        when (YuntuTrackingStatus.fromCode(yuntuStatusCode)) {
            YuntuTrackingStatus.NOT_FOUND -> TrackingStatus.NOT_FOUND
            YuntuTrackingStatus.PRE_ADVICE_RECEIVED -> TrackingStatus.PRE_ADVICE_RECEIVED
            YuntuTrackingStatus.IN_TRANSIT -> TrackingStatus.IN_TRANSIT
            YuntuTrackingStatus.ARRIVED_FOR_PICKUP -> TrackingStatus.ARRIVED_FOR_PICKUP
            YuntuTrackingStatus.DELIVERY_FAILED -> TrackingStatus.DELIVERY_FAILED
            YuntuTrackingStatus.DELIVERED -> TrackingStatus.DELIVERED
            YuntuTrackingStatus.EXCEPTION -> TrackingStatus.EXCEPTION
            YuntuTrackingStatus.UNKNOWN -> TrackingStatus.UNKNOWN
            YuntuTrackingStatus.RETURNED -> TrackingStatus.RETURNED
            YuntuTrackingStatus.CANCELLED -> TrackingStatus.CANCELLED
        }

    /**
     * 根据轨迹节点代码转换为OMS状态
     */
    private fun convertYuntuTrackNodeCode(trackNodeCode: String): TrackingStatus =
        when (trackNodeCode.uppercase()) {
            "10", "PREADVICE" -> TrackingStatus.PRE_ADVICE_RECEIVED
            "20", "PICKUP" -> TrackingStatus.PICKED_UP
            "30", "INTRANSIT" -> TrackingStatus.IN_TRANSIT
            "40", "CUSTOMS" -> TrackingStatus.IN_CUSTOMS
            "50", "CUSTOMSCLEARED" -> TrackingStatus.CUSTOMS_CLEARED
            "60", "OUTFORDELIVERY" -> TrackingStatus.OUT_FOR_DELIVERY
            "70", "DELIVERED" -> TrackingStatus.DELIVERED
            "80", "EXCEPTION" -> TrackingStatus.EXCEPTION
            "90", "RETURNED" -> TrackingStatus.RETURNED
            else -> TrackingStatus.IN_TRANSIT // 默认为运输途中
        }

    /**
     * 判断是否为末端配送节点
     */
    private fun isLastMileTrackNode(trackNodeCode: String): Boolean =
        trackNodeCode.uppercase() in listOf("60", "70", "OUTFORDELIVERY", "DELIVERED")
}
