package io.github.clive.luxomssystem.domain.supplierOrder.model

import io.github.clive.luxomssystem.domain.doanload.base.AbstractBaseEntity
import io.github.clive.luxomssystem.domain.valueobject.ProductInfo
import io.github.clive.luxomssystem.domain.valueobject.RecipientInfo
import io.github.clive.luxomssystem.domain.valueobject.ShippingInfo
import io.github.clive.luxomssystem.nextId
import jakarta.persistence.*
import org.hibernate.annotations.ColumnDefault
import java.time.ZonedDateTime

@Entity(name = "supplier_orders")
@Table(name = "supplier_orders")
class SupplierOrder(
    @Id
    val id: Long = nextId(),
    @Column(name = "sub_order_id", nullable = false)
    val subOrderId: Long,
    @Column(name = "biz_id", nullable = false)
    val bizId: Long,
    var fileName: String? = null,
    var googleSearch: String? = null,
    @Column(name = "customer_id", nullable = false)
    val customerId: Long,
    @Column(name = "waybill_id")
    var waybillId: Long? = null,
    @Column(name = "order_no", nullable = false, unique = true)
    val orderNo: String,
    @Embedded
    var product: ProductInfo = ProductInfo(),
    @Embedded
    var recipient: RecipientInfo = RecipientInfo(),
    @Embedded
    var shipping: ShippingInfo = ShippingInfo(),
    @Column(name = "error_msg")
    var errorMsg: String? = null,
    @Column(name = "status", nullable = false)
    @Enumerated(EnumType.STRING)
    var status: SupplierOrderStatus = SupplierOrderStatus.CREATED,
    @Column(name = "fail_at_status")
    @Enumerated(EnumType.STRING)
    var failAtStatus: SupplierOrderStatus? = null,
    @Column(columnDefinition = "text")
    var fileOssPath: String? = null,
    var mainOrderId: Long? = null,
    var supplierMainOrderId: Long? = null,
    @Column(columnDefinition = "text")
    var urls: String? = null,
    @Column(columnDefinition = "text")
    var effectUrl: String? = null,
    @Column(columnDefinition = "text")
    var designUrl: String? = null,
    @ColumnDefault("true")
    @Column(nullable = false)
    var canPrintWayBillPdf: Boolean = true,
    @ColumnDefault("''")
    @Column(columnDefinition = "text", nullable = false)
    var stopPrintWayBillWarning: String = "",
    @ColumnDefault("''")
    @Column(name = "remark", nullable = false, columnDefinition = "text")
    var remark: String = "",
    @ColumnDefault("false")
    @Column(name = "accepted", nullable = false)
    var accepted: Boolean = false,
    @Column(name = "scan_at", nullable = true)
    var scanAt: ZonedDateTime? = null,
) : AbstractBaseEntity() {
    fun failed(msg: String) {
        failAtStatus = status
        status = SupplierOrderStatus.FAILED
        errorMsg = msg
    }

    fun address(): String = recipient.fullAddress()

    fun uniqueCode() = "$orderNo-${product.spu}"
}

enum class SupplierOrderStatus {
    CREATED,
    MATCHED_SUPPLY,
    PRODUCING,
    PRODUCT_COMPLETED,
    SHIPPED,
    FAILED,
    CANCELLED,
}
