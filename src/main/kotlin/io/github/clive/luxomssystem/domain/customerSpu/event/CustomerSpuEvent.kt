package io.github.clive.luxomssystem.domain.customerSpu.event

import io.github.clive.luxomssystem.facade.customerSku.request.CreateCustomerSkuRequest
import io.github.clive.luxomssystem.facade.customerSku.request.UpdateCustomerSkuRequest
import org.springframework.context.ApplicationEvent

sealed class CustomerSpuEvent(
    source: Any,
) : ApplicationEvent(source) {
    data class Created(
        val customerSpuId: Long,
        val skus: List<CreateCustomerSkuRequest>,
    ) : CustomerSpuEvent(customerSpuId)

    data class Updated(
        val customerSpuId: Long,
        val cmds: List<UpdateCustomerSkuRequest>,
    ) : CustomerSpuEvent(customerSpuId)

    data class Deleted(
        val customerSpuId: Long,
    ) : CustomerSpuEvent(customerSpuId)
}
