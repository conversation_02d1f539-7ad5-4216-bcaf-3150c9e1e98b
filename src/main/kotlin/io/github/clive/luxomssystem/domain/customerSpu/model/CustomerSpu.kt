package io.github.clive.luxomssystem.domain.customerSpu.model

import io.github.clive.luxomssystem.common.enums.BaseStatus
import io.github.clive.luxomssystem.domain.doanload.base.AbstractBaseEntity
import io.github.clive.luxomssystem.nextId
import io.hypersistence.utils.hibernate.type.json.JsonType
import jakarta.persistence.*
import org.hibernate.annotations.ColumnDefault
import org.hibernate.annotations.Type
import java.math.BigDecimal

@Entity
@Table(name = "customer_spu")
class CustomerSpu(
    @Id
    var id: Long = nextId(),
    @Column(name = "biz_id", nullable = false)
    var bizId: Long,
    @Column(name = "customer_id", nullable = false)
    var customerId: Long,
    @Column(name = "system_spu_id", nullable = false)
    var systemSpuId: Long,
    @Column(name = "system_spu_code")
    var systemSpuCode: String? = null,
    @Column(name = "customer_spu_code", unique = true)
    var customerSpuCode: String? = null,
    @Column(name = "trigger_discount_quantity")
    var triggerDiscountQuantity: Int = 1,
    @Column(name = "discount", precision = 10, scale = 2)
    var discount: BigDecimal = BigDecimal("0.80"),
    @Enumerated(EnumType.STRING)
    @Column(name = "status", nullable = false)
    var status: BaseStatus = BaseStatus.DISABLED,
    @ColumnDefault("'[]'")
    @Type(value = JsonType::class)
    @Column(columnDefinition = "jsonb", name = "supplier_capacities")
    var supplierCapacities: List<SupplierCapacity> = emptyList(),
) : AbstractBaseEntity() {
    fun matchDiscountQuantity(quantity: Int): Boolean = quantity >= triggerDiscountQuantity
}

/**
 * 供应商容量
 * @property supplierId Long
 * @property supplierName String
 * @property totalCapacity Long -1: 无限制
 */
data class SupplierCapacity(
    val supplierId: Long,
    val supplierName: String,
    val totalCapacity: Int,
) {
    fun canSupport(qty: Int): Boolean = totalCapacity == -1 || totalCapacity >= qty
}
