package io.github.clive.luxomssystem.domain.spu.model

import io.github.clive.luxomssystem.common.enums.BaseStatus
import io.github.clive.luxomssystem.domain.doanload.base.AbstractBaseEntity
import io.hypersistence.utils.hibernate.type.array.ListArrayType
import jakarta.persistence.Column
import jakarta.persistence.Entity
import jakarta.persistence.EnumType
import jakarta.persistence.Enumerated
import jakarta.persistence.Id
import jakarta.persistence.Table
import org.hibernate.annotations.ColumnDefault
import org.hibernate.annotations.Type

@Entity(name = "combo_spu")
@Table(name = "combo_spu")
data class ComboSpu(
    @Id
    val id: Long,
    @ColumnDefault("-1")
    val bizId: Long,
    @Type(ListArrayType::class)
    @Column(name = "spu_ids", columnDefinition = "int8[]")
    val spuIds: List<Long>,
    @Type(ListArrayType::class)
    @Column(name = "spu_codes", columnDefinition = "text[]")
    val spuCodes: List<String>,
    @Column(name = "spu_ref_id", unique = true)
    val spuRefId: String,
    @Column
    val title: String,
    @Column
    val name: String,
    @Column(name = "cn_name")
    val cnName: String,
    @Column
    val category: String,
    @Column(name = "product_image")
    val productImage: String,
    @Column(name = "package_quantity", nullable = false)
    val packageQuantity: Int,
    @Type(ListArrayType::class)
    @Column(name = "image_url", columnDefinition = "text[]")
    val showImages: List<String>,
    @Column(nullable = false)
    val description: String,
    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    val status: BaseStatus,
    @Column(name = "hs_code", nullable = true)
    val hsCode: String?,
) : AbstractBaseEntity()
