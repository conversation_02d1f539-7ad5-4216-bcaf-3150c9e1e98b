package io.github.clive.luxomssystem

import io.github.clive.luxomssystem.common.utils.SequenceGenerator
import org.springframework.boot.autoconfigure.SpringBootApplication
import org.springframework.boot.runApplication
import org.springframework.cache.annotation.EnableCaching
import org.springframework.scheduling.annotation.EnableScheduling

@EnableCaching
@EnableScheduling
@SpringBootApplication
class LuxOmsSystemApplication

fun main(args: Array<String>) {
    runApplication<LuxOmsSystemApplication>(*args)
}

object SequenceGeneratorUtils {
    val sequence = SequenceGenerator()
}

fun nextId(): Long = SequenceGeneratorUtils.sequence.nextId()
