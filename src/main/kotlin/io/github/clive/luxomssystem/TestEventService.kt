// package io.github.clive.luxomssystem
//
// import io.github.clive.luxomssystem.infrastructure.config.auth.UserContext
// import io.github.clive.luxomssystem.infrastructure.config.auth.UserContextHolder
// import io.github.oshai.kotlinlogging.KotlinLogging
// import org.springframework.context.ApplicationEventPublisher
// import org.springframework.context.event.EventListener
// import org.springframework.web.bind.annotation.RequestMapping
// import org.springframework.web.bind.annotation.RestController
//
// @RestController
// class TestEventService(
//    private val eventBus: ApplicationEventPublisher,
// ) {
//
//
//    @RequestMapping("/test/1")
//    fun testEvent() {
//        UserContextHolder.set(UserContext(1, "test"))
//        eventBus.publishEvent(TestAsyncEvent(165114584756670464))
//    }
//
//
//    @org.springframework.scheduling.annotation.Async
//    @EventListener(TestAsyncEvent::class)
//    fun handleTestEvent(event: TestAsyncEvent) {
//        log.info { "TestAsyncEvent | id: ${event.id} | ${UserContextHolder.user}" }
//    }
//
//    companion object {
//        private val log = KotlinLogging.logger { }
//    }
// }
//
//
// data class TestAsyncEvent(val id: Long)
