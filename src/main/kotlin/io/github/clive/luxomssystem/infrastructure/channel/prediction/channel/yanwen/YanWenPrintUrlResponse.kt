package io.github.clive.luxomssystem.infrastructure.channel.prediction.channel.yanwen

import com.fasterxml.jackson.annotation.JsonProperty

data class YanWenPrintUrlResponse(
    @JsonProperty("code")
    var code: String?,
    @JsonProperty("data")
    var `data`: Data?,
    @JsonProperty("message")
    var message: String?,
    @JsonProperty("success")
    var success: Boolean,
) {
    data class Data(
        @JsonProperty("waybillNumber")
        var waybillNumber: String?,
        var errorMsg: String?,
        var isSuccess: Boolean,
        var base64String: String?,
        @JsonProperty("yanwenNumber")
        var yanwenNumber: String?,
    )
}
