@file:Suppress("DEPRECATION_ERROR")

package io.github.clive.luxomssystem.infrastructure.repository.jpa

import io.github.clive.luxomssystem.domain.customerSpu.model.ComboCustomerSpuCountryTaxConfig
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.stereotype.Repository

@Deprecated("税率不应该跟spu挂钩", level = DeprecationLevel.ERROR)
@Repository
interface ComboCustomerSpuCountryTaxConfigRepository : JpaRepository<ComboCustomerSpuCountryTaxConfig, Long> {
    fun findByComboCustomerSpuId(comboCustomerSpuId: Long): List<ComboCustomerSpuCountryTaxConfig>

    fun deleteByComboCustomerSpuIdAndCountryId(
        comboCustomerSpuId: Long,
        countryId: Long,
    )

    fun findByComboCustomerSpuIdAndCountryId(
        comboCustomerSpuId: Long,
        countryId: Long,
    ): ComboCustomerSpuCountryTaxConfig?
}
