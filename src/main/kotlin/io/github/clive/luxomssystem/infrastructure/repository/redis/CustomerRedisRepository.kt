package io.github.clive.luxomssystem.infrastructure.repository.redis

import org.springframework.data.redis.core.RedisTemplate
import org.springframework.stereotype.Repository

@Repository
class CustomerRedisRepository(
    private val redisTemplate: RedisTemplate<String, String>,
) {
    fun saveOpenApiKey(
        id: Long,
        openapiKey: String,
    ) {
        redisTemplate.opsForValue().set("OPENAPIKEY:$openapiKey", id.toString())
    }

    fun findIdByOpenApiKey(openapiKey: String): Long? {
        val id = redisTemplate.opsForValue().get("OPENAPIKEY:$openapiKey") ?: return null
        return id.toLong()
    }
}
