package io.github.clive.cognitionbank.infrastructure.remote.prediction.channel.cainiao.req

import com.fasterxml.jackson.annotation.JsonProperty
import io.github.clive.luxomssystem.infrastructure.channel.prediction.channel.cainiao.req.*

data class SolutionParam(
    @JsonProperty("cainiaoPayTaxParam")
    var cainiaoPayTaxParam: CainiaoPayTaxParam?,
    @JsonProperty("doorPickupParam")
    var doorPickupParam: DoorPickupParam?,
    @JsonProperty("importCustomsParam")
    var importCustomsParam: ImportCustomsParam?,
    @JsonProperty("selfPickupParam")
    var selfPickupParam: SelfPickupParam?,
    @JsonProperty("selfPostParam")
    var selfPostParam: SelfPostParam?,
    @JsonProperty("selfSendParam")
    var selfSendParam: SelfSendParam?,
    @JsonProperty("solutionCode")
    var solutionCode: String?,
    @JsonProperty("unreachableReturnParam")
    var unreachableReturnParam: UnreachableReturnParam?,
)
