package io.github.clive.luxomssystem.infrastructure.repository.jpa

import io.github.clive.luxomssystem.common.enums.MainOrderType
import io.github.clive.luxomssystem.common.enums.order.MainOrderImageDownloadStatus
import io.github.clive.luxomssystem.common.enums.order.MainOrderStatus
import io.github.clive.luxomssystem.domain.doanload.MainOrder
import io.github.clive.luxomssystem.infrastructure.config.AuthRewriter
import org.springframework.data.domain.Page
import org.springframework.data.domain.Pageable
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.jpa.repository.Modifying
import org.springframework.data.jpa.repository.Query
import org.springframework.data.repository.query.Param
import org.springframework.stereotype.Repository
import org.springframework.transaction.annotation.Transactional

@Repository
interface MainOrderRepository : JpaRepository<MainOrder, Long> {
    fun findByImgDownloadStatus(status: MainOrderImageDownloadStatus): List<MainOrder>

    fun findByUniqueKey(uniqueKey: String): MainOrder?

    @Query(
        """
        select m from MainOrder m
        where
        1=1 and
         (:fileName is null or  m.fileName like %:fileName%)
        and (:id is null or m.id = :id)
        and (:status is null or m.status in :status)
        and (:wayBillPushed is null or m.wayBillPushed = :wayBillPushed)
        and (:supplierPushed is null or m.supplierPushed = :supplierPushed)
        and (:createdFrom is null or m.createdAt >= :createdFrom)
        and (:createdTo is null or m.createdAt <= :createdTo)
        and (:bizId is null or m.bizId = :bizId)
        and (:type is null or m.type = :type)
        order by m.createdAt desc 
        """,
        queryRewriter = AuthRewriter::class,
    )
    fun pageQuery(
        fileName: String?,
        id: Long?,
        createdFrom: Long?,
        createdTo: Long?,
        status: List<MainOrderStatus>?,
        pageable: Pageable,
        wayBillPushed: Boolean? = null,
        supplierPushed: Boolean? = null,
        bizId: Long? = null,
        type: MainOrderType? = null,
    ): Page<MainOrder>

    @Query(
        """
            select m from MainOrder m
            where m.customerId = :customerId
            and m.type = :type
            and m.wayBillPushed = :wayBillPushed
            order by m.id desc
        """,
        queryRewriter = AuthRewriter::class,
    )
    fun pageQueryFinancial(
        customerId: Long,
        type: MainOrderType,
        wayBillPushed: Boolean,
        pageable: Pageable,
    ): Page<MainOrder>

    fun findByStatusIn(status: List<MainOrderStatus>): List<MainOrder>

    @Transactional
    @Modifying(clearAutomatically = true)
    @Query(
        """
            update MainOrder set status = :update where id = :id and status = :expect
        """,
    )
    fun updateStatusCAS(
        id: Long,
        expect: MainOrderStatus,
        update: MainOrderStatus,
    ): Int

    @Query(
        """
        SELECT m.status FROM MainOrder m WHERE m.id = :mainOrderId AND m.bizId = :bizId
        """,
        queryRewriter = AuthRewriter::class,
    )
    fun getMainOrderStatus(@Param("mainOrderId") mainOrderId: Long, @Param("bizId") bizId: Long): String?

    @Query(
        """
        SELECT COUNT(s) FROM sub_orders s WHERE s.parentId = :mainOrderId AND s.bizId = :bizId
        """,
        queryRewriter = AuthRewriter::class,
    )
    fun getTotalSubOrders(@Param("mainOrderId") mainOrderId: Long, @Param("bizId") bizId: Long): Long

    @Query(
        """
        SELECT COUNT(s) FROM sub_orders s WHERE s.parentId = :mainOrderId AND s.bizId = :bizId AND s.status = :status
        """,
        queryRewriter = AuthRewriter::class,
    )
    fun getCompletedSubOrders(@Param("mainOrderId") mainOrderId: Long, @Param("bizId") bizId: Long, @Param("status") status: io.github.clive.luxomssystem.common.enums.SubOrderStatus): Long

    @Query(
        """
        SELECT s.status, COUNT(s) FROM sub_orders s WHERE s.parentId = :mainOrderId AND s.bizId = :bizId GROUP BY s.status
        """,
        queryRewriter = AuthRewriter::class,
    )
    fun getSubOrderStatusCounts(@Param("mainOrderId") mainOrderId: Long, @Param("bizId") bizId: Long): List<Array<Any>>
}
