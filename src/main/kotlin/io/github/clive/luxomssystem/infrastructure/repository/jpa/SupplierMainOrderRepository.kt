package io.github.clive.luxomssystem.infrastructure.repository.jpa

import io.github.clive.luxomssystem.domain.SupplierMainOrder
import io.github.clive.luxomssystem.infrastructure.config.AuthRewriter
import org.springframework.data.domain.Page
import org.springframework.data.domain.Pageable
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.jpa.repository.Query
import org.springframework.stereotype.Repository

@Repository
interface SupplierMainOrderRepository : JpaRepository<SupplierMainOrder, Long> {
    @Query(
        """
        select so from SupplierMainOrder so
        where
            so.bizId = :bizId
            and (:createdAtFrom is null or so.createdAt >= :createdAtFrom)
            and (:createdAtTo is null or so.createdAt <= :createdAtTo)
            and (:#{#supplierIds.isEmpty()} = true or so.supplierId IN :supplierIds)
        order by so.createdAt desc
    """,
        queryRewriter = AuthRewriter::class,
    )
    fun listNoSupplierForExport(
        bizId: Long,
        createdAtFrom: Long?,
        createdAtTo: Long?,
        supplierIds: List<Long>,
    ): List<SupplierMainOrder>

    @Query(
        """
        select so from SupplierMainOrder so
        where
            so.bizId = :bizId
            and 1=1 
            and (:supplierId is null or so.supplierId = :supplierId)
            and (:createdAtFrom is null or so.createdAt >= :createdAtFrom)
            and (:createdAtTo is null or so.createdAt <= :createdAtTo)
        order by so.createdAt desc
    """,
    )
    fun listForExport(
        supplierId: Long?,
        bizId: Long,
        createdAtFrom: Long?,
        createdAtTo: Long?,
    ): List<SupplierMainOrder>

    @Query(
        """
        select so from SupplierMainOrder so
        where
            so.bizId = :bizId
            and (:mainOrderId is null or so.mainOrderId = :mainOrderId)
            and (:createdAtFrom is null or so.createdAt >= :createdAtFrom)
            and (:createdAtTo is null or so.createdAt <= :createdAtTo)
            and (:fileName is null or so.fileName ilike %:fileName%)
            and (:#{#supplierIds.isEmpty()} = true or so.supplierId IN :supplierIds)
        order by so.createdAt desc
    """,
        queryRewriter = AuthRewriter::class,
    )
    fun pageNoSupplier(
        bizId: Long,
        pageable: Pageable,
        mainOrderId: Long?,
        createdAtFrom: Long?,
        createdAtTo: Long?,
        fileName: String?,
        supplierIds: List<Long>,
    ): Page<SupplierMainOrder>

    @Query(
        """
        select so from SupplierMainOrder so
        where
            so.bizId = :bizId
            and 1=1 
            and (:supplierId is null or so.supplierId = :supplierId)
            and (:mainOrderId is null or so.mainOrderId = :mainOrderId)
            and (:createdAtFrom is null or so.createdAt >= :createdAtFrom)
            and (:createdAtTo is null or so.createdAt <= :createdAtTo)
            and (:fileName is null or so.fileName ilike %:fileName%)
        order by so.createdAt desc
    """,
    )
    fun page(
        supplierId: Long?,
        bizId: Long,
        pageable: Pageable,
        mainOrderId: Long?,
        createdAtFrom: Long?,
        createdAtTo: Long?,
        fileName: String?,
    ): Page<SupplierMainOrder>

    fun findByMainOrderIdAndSupplierId(
        mainOrderId: Long,
        supplierId: Long,
    ): SupplierMainOrder?

    fun findByCreatedAtLessThanAndAccepted(
        createdAt: Long,
        accepted: Boolean,
    ): List<SupplierMainOrder>

    fun deleteByMainOrderId(mainOrderId: Long)

    @Query(
        """
        select so from SupplierMainOrder so
        where
            so.createdAt >= :createdAtFrom
            and so.createdAt < :createdAtTo
        order by so.supplierName, so.createdAt desc
    """,
    )
    fun findOrdersByCreatedAtRange(
        createdAtFrom: Long,
        createdAtTo: Long,
    ): List<SupplierMainOrder>
}
