# TrackingInfo索引优化方案

## 概述

基于TrackingInfo实体的查询模式分析，设计了一套全面的索引优化方案，以提高查询性能和系统响应速度。

## 索引分类

### 1. 基础查询索引

| 索引名称 | 字段 | 类型 | 用途 |
|---------|------|------|------|
| `idx_tracking_waybill_no` | waybill_no | UNIQUE | 运单号唯一查询 |
| `idx_tracking_waybill_id` | waybill_id | BTREE | 运单ID查询 |
| `idx_tracking_tracking_number` | tracking_number | BTREE | 跟踪号查询 |

**优化的查询**：
- `findByWaybillNo()`
- `findByWaybillId()`
- 按跟踪号查询

### 2. 业务查询索引

| 索引名称 | 字段 | 类型 | 用途 |
|---------|------|------|------|
| `idx_tracking_channel` | channel | BTREE | 渠道查询 |
| `idx_tracking_current_status` | current_status | BTREE | 状态查询 |
| `idx_tracking_destination_country` | destination_country | BTREE | 目的地国家查询 |
| `idx_tracking_origin_country` | origin_country | BTREE | 起始国家查询 |

**优化的查询**：
- `findByChannel()`
- `findByIsCompleted()`
- 按国家筛选查询

### 3. 时间相关索引

| 索引名称 | 字段 | 类型 | 用途 |
|---------|------|------|------|
| `idx_tracking_last_updated_at` | last_updated_at | BTREE | 更新时间查询 |
| `idx_tracking_last_event_time` | last_event_time | BTREE | 事件时间查询 |
| `idx_tracking_created_at` | created_at | BTREE | 创建时间查询 |

**优化的查询**：
- `findTrackingInfoOlderThan()`
- 时间范围统计查询
- 分页查询排序

### 4. 复合索引

| 索引名称 | 字段组合 | 用途 |
|---------|----------|------|
| `idx_tracking_update_query` | last_updated_at, last_event_time | 定时任务查询 |
| `idx_tracking_channel_status` | channel, current_status | 渠道状态复合查询 |
| `idx_tracking_status_updated` | current_status, last_updated_at | 状态时间复合查询 |
| `idx_tracking_created_status` | created_at, current_status | 统计查询优化 |
| `idx_tracking_created_channel` | created_at, channel | 渠道统计优化 |

**优化的查询**：
- `findTrackingInfoForUpdate()`
- `countByStatusWithDateRange()`
- `countByChannelWithDateRange()`

### 5. 特殊索引

| 索引名称 | 类型 | 字段 | 用途 |
|---------|------|------|------|
| `idx_tracking_order_nos_gin` | GIN | order_nos | 数组查询优化 |
| `idx_tracking_page_query` | BTREE | last_updated_at DESC, id | 分页查询优化 |

**优化的查询**：
- `findByOrderNos()` (数组查询)
- `pageQuery()` (分页查询)

## 查询性能优化

### 1. 定时任务查询优化

**原查询**：
```sql
SELECT t FROM TrackingInfo t
WHERE t.lastUpdatedAt < :updateCutoffTime
AND (t.lastEventTime IS NULL OR t.lastEventTime >= :staleCutoffTime)
ORDER BY t.lastUpdatedAt ASC
```

**优化索引**：
- `idx_tracking_update_query(last_updated_at, last_event_time)`
- `idx_tracking_scheduler_query` (带WHERE条件的部分索引)

**性能提升**：查询时间从秒级降低到毫秒级

### 2. 分页查询优化

**原查询**：
```sql
SELECT * FROM tracking_info t
WHERE ... (多个条件)
ORDER BY t.last_updated_at DESC
```

**优化索引**：
- `idx_tracking_page_query(last_updated_at DESC, id)`
- `idx_tracking_page_complex` (覆盖索引)

**性能提升**：避免文件排序，直接使用索引排序

### 3. 统计查询优化

**原查询**：
```sql
SELECT t.currentStatus, COUNT(t) 
FROM TrackingInfo t 
WHERE t.createdAt >= :startDate AND t.createdAt <= :endDate
GROUP BY t.currentStatus
```

**优化索引**：
- `idx_tracking_created_status(created_at, current_status)`
- `idx_tracking_stats_query` (统计专用索引)

**性能提升**：GROUP BY操作直接使用索引，避免全表扫描

### 4. 数组查询优化

**原查询**：
```sql
SELECT * FROM tracking_info 
WHERE :orderNo = ANY(order_nos)
```

**优化索引**：
- `idx_tracking_order_nos_gin` (GIN索引)

**性能提升**：数组包含查询性能大幅提升

## 索引维护策略

### 1. 定期维护

```sql
-- 重建关键索引
REINDEX INDEX CONCURRENTLY idx_tracking_waybill_no;
REINDEX INDEX CONCURRENTLY idx_tracking_page_query;
REINDEX INDEX CONCURRENTLY idx_tracking_update_query;

-- 更新表统计信息
ANALYZE tracking_info;
```

### 2. 性能监控

```sql
-- 查看索引使用情况
SELECT 
    schemaname, 
    tablename, 
    indexname, 
    idx_scan, 
    idx_tup_read, 
    idx_tup_fetch 
FROM pg_stat_user_indexes 
WHERE tablename = 'tracking_info' 
ORDER BY idx_scan DESC;

-- 查看表和索引大小
SELECT 
    schemaname,
    tablename,
    pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) as table_size,
    pg_size_pretty(pg_indexes_size(schemaname||'.'||tablename)) as indexes_size
FROM pg_tables 
WHERE tablename = 'tracking_info';
```

### 3. 慢查询分析

```sql
-- 查看慢查询
SELECT 
    query, 
    mean_time, 
    calls, 
    total_time 
FROM pg_stat_statements 
WHERE query LIKE '%tracking_info%' 
ORDER BY mean_time DESC 
LIMIT 10;
```

## 部署建议

### 1. 索引创建顺序

1. **基础索引**：先创建单字段索引
2. **复合索引**：再创建多字段复合索引
3. **特殊索引**：最后创建GIN等特殊类型索引

### 2. 创建方式

```sql
-- 使用CONCURRENTLY避免锁表
CREATE INDEX CONCURRENTLY idx_tracking_waybill_no 
ON tracking_info(waybill_no);
```

### 3. 监控指标

- **索引命中率**：应该 > 95%
- **查询响应时间**：单表查询 < 100ms
- **索引大小**：控制在表大小的50%以内

## 预期性能提升

| 查询类型 | 优化前 | 优化后 | 提升比例 |
|---------|--------|--------|----------|
| 运单号查询 | 100ms | 5ms | 95% |
| 分页查询 | 500ms | 50ms | 90% |
| 统计查询 | 2s | 200ms | 90% |
| 定时任务查询 | 1s | 100ms | 90% |
| 数组查询 | 800ms | 80ms | 90% |

## 注意事项

### 1. 索引维护成本

- **写入性能**：每个索引都会影响INSERT/UPDATE性能
- **存储空间**：索引会占用额外的存储空间
- **维护开销**：需要定期维护和监控

### 2. 索引选择原则

- **高频查询优先**：为最常用的查询创建索引
- **复合索引优化**：合理设计复合索引字段顺序
- **避免冗余索引**：删除不必要的重复索引

### 3. 监控和调优

- **定期检查**：监控索引使用情况
- **性能测试**：在生产环境验证性能提升
- **持续优化**：根据业务变化调整索引策略
