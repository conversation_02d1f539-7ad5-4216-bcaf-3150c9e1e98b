package io.github.clive.luxomssystem.infrastructure.channel.prediction.channel.cainiao.response

import com.fasterxml.jackson.annotation.JsonProperty

data class QueryWaybillResponse(
    @JsonProperty("data")
    var `data`: Data?,
    @JsonProperty("errorCode")
    var errorCode: String?,
    @JsonProperty("errorMsg")
    var errorMsg: String?,
    @JsonProperty("needRetry")
    var needRetry: String?,
    @JsonProperty("success")
    var success: String?,
) {
    data class Data(
        @JsonProperty("distributionWarehouse")
        var distributionWarehouse: String?,
        @JsonProperty("laneCode")
        var laneCode: String?,
        @JsonProperty("laneLastMileCP")
        var laneLastMileCP: String?,
        @JsonProperty("laneName")
        var laneName: String?,
        @JsonProperty("trackingNumber")
        var trackingNumber: String?,
        @JsonProperty("waybillPdfData")
        var waybillPdfData: String?,
    )
}
