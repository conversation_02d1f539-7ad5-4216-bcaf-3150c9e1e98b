package io.github.clive.luxomssystem.infrastructure.tracking.listener

import io.github.clive.luxomssystem.application.tracking.TrackingApplicationService
import io.github.clive.luxomssystem.common.enums.WaybillChannel
import io.github.clive.luxomssystem.domain.tracking.model.TrackingInfo
import io.github.clive.luxomssystem.domain.waybill.event.WaybillOutBoundEvent
import io.github.clive.luxomssystem.domain.waybill.model.WayBillStatus
import io.github.clive.luxomssystem.infrastructure.repository.jpa.WaybillRepository
import io.github.clive.luxomssystem.infrastructure.tracking.remote.TrackingRemoteService
import io.github.clive.luxomssystem.infrastructure.tracking.repository.TrackingInfoRepository
import io.github.oshai.kotlinlogging.KotlinLogging
import org.springframework.boot.context.event.ApplicationReadyEvent
import org.springframework.context.event.EventListener
import org.springframework.stereotype.Component
import org.springframework.transaction.annotation.Propagation
import org.springframework.transaction.annotation.Transactional
import org.springframework.transaction.event.TransactionalEventListener

/**
 * 运单状态变更监听器
 * 当运单状态变为OUTBOUND时，创建初始轨迹记录
 */
@Component
class WaybillStatusChangeListener(
    private val waybillRepository: WaybillRepository,
    private val trackingInfoRepository: TrackingInfoRepository,
    private val trackingRemoteService: TrackingRemoteService,
    private val trackingApplicationService: TrackingApplicationService,
    private val subOrderTrackingStatusUpdateService: io.github.clive.luxomssystem.domain.tracking.service.SubOrderTrackingStatusUpdateService
) {
    
    private val log = KotlinLogging.logger {}

//    @EventListener(ApplicationReadyEvent::class)
//    fun enable(){
//        handleWaybillOutBoundEvent(WaybillOutBoundEvent(180695600636383234, 1L,"ORD20241229001"))
//    }

    /**
     * 监听运单完成事件（状态变为OUTBOUND）
     */
    @org.springframework.scheduling.annotation.Async
    @TransactionalEventListener(WaybillOutBoundEvent::class)
    @Transactional(rollbackFor = [Exception::class], propagation = Propagation.REQUIRES_NEW)
    fun handleWaybillOutBoundEvent(event: WaybillOutBoundEvent) {
        log.info { "处理运单状态变更事件 | WaybillOutBoundEvent | 运单ID: ${event.waybillId} | orderNos: ${event.orderNos}" }
        try {
            val waybill = waybillRepository.findById(event.waybillId).orElse(null)
            if (waybill == null) {
                log.warn { "运单不存在 | 运单ID: ${event.waybillId}" }
                return
            }

            val waybillNo = waybill.waybillNo
            if (waybillNo.isNullOrBlank()) {
                log.warn { "运单号为空，跳过轨迹记录创建 | 运单ID: ${event.waybillId}" }
                return
            }

            // 检查是否已存在轨迹记录
            val existingTracking = trackingInfoRepository.findByWaybillNo(waybillNo)
            if (existingTracking != null) {
                log.debug { "轨迹记录已存在，跳过创建 | 运单号: $waybillNo" }
                return
            }

            // 检查渠道是否支持轨迹查询
            if (!trackingRemoteService.isTrackingSupportedChannel(waybill.shipping.channel)) {
                log.debug { "渠道不支持轨迹查询，跳过轨迹记录创建 | 运单号: $waybillNo | 渠道: ${waybill.shipping.channel.displayName}" }
                return
            }

            // 创建初始轨迹记录
            val initialTracking = trackingRemoteService.createInitialTrackingRecord(
                orderNo = waybill.orderNos,
                waybillId = waybill.id,
                waybillNo = waybillNo,
                channel = waybill.shipping.channel,
                mainOrderId = waybill.mainOrderId
            )

            if (initialTracking != null) {
                // 立即获取并更新轨迹信息
                val trackingInfo = fetchAndUpdateTrackingInfo(initialTracking)
                trackingInfoRepository.save(trackingInfo)
                log.info { "成功创建并初始化轨迹记录 | 运单号: $waybillNo | 渠道: ${waybill.shipping.channel.displayName} | 状态: ${trackingInfo.currentStatus}" }
            } else {
                log.warn { "创建初始轨迹记录失败 | 运单号: $waybillNo | 渠道: ${waybill.shipping.channel.displayName}" }
            }

        } catch (e: Exception) {
            log.error(e) { "处理运单状态变更事件异常 | 运单ID: ${event.waybillId}" }
        }
    }

    /**
     * 获取并更新轨迹信息
     */
    private fun fetchAndUpdateTrackingInfo(trackingInfo: TrackingInfo): TrackingInfo {
        val waybillNo = trackingInfo.waybillNo
        val channel = getWaybillChannelByConverterName(trackingInfo.channel)

        if (channel == null) {
            log.warn { "无法识别轨迹渠道 | 运单号: $waybillNo | 渠道: ${trackingInfo.channel}" }
            return trackingInfo
        }

        try {
            // 获取远程轨迹信息
            val remoteResponse = trackingRemoteService.getTrackingByChannel(waybillNo, channel)
            if (remoteResponse == null) {
                log.warn { "获取轨迹信息失败 | 运单号: $waybillNo | 渠道: ${trackingInfo.channel}" }
                return trackingInfo
            }

            // 转换为统一格式
            val newTrackingData = when (trackingInfo.channel) {
                "YANWEN" -> trackingApplicationService.processYanWenTracking(
                    remoteResponse as io.github.clive.luxomssystem.infrastructure.channel.prediction.channel.yanwen.YanWenTrackingResponse,
                    waybillNo
                )
                "YUNTU" -> trackingApplicationService.processYuntuTracking(
                    remoteResponse as io.github.clive.luxomssystem.infrastructure.channel.prediction.channel.yuntu.YuntuTrackingResponse,
                    waybillNo
                )
                else -> null
            }

            if (newTrackingData != null) {
                // 更新轨迹信息
                trackingInfo.updateTracking(
                    status = newTrackingData.currentStatus,
                    events = newTrackingData.trackingEvents,
                    deliveryDays = newTrackingData.deliveryDays,
                    trackingNumber = newTrackingData.trackingNumber,
                    destinationCountry = newTrackingData.destinationCountry,
                    originCountry = newTrackingData.originCountry,
                    lastMileProvider = newTrackingData.lastMileProvider,
                    podLinks = newTrackingData.podLinks,
                    rawData = newTrackingData.rawData,
                    subOrderTrackingStatusUpdateService = subOrderTrackingStatusUpdateService
                )
                log.info { "成功获取并更新轨迹信息 | 运单号: $waybillNo | 状态: ${newTrackingData.currentStatus}" }
            } else {
                log.warn { "轨迹信息转换失败 | 运单号: $waybillNo | 渠道: ${trackingInfo.channel}" }
            }

        } catch (e: Exception) {
            log.error(e) { "获取轨迹信息异常 | 运单号: $waybillNo | 渠道: ${trackingInfo.channel}" }
        }

        return trackingInfo
    }

    /**
     * 根据转换器名称获取运单渠道
     */
    private fun getWaybillChannelByConverterName(converterName: String): WaybillChannel? {
        return when (converterName) {
            "YANWEN" -> WaybillChannel.YW_HZ // 默认使用杭州燕文
            "YUNTU" -> WaybillChannel.YUNTU
            else -> null
        }
    }
}
