package io.github.clive.luxomssystem.infrastructure.channel.prediction.channel.shunfeng

import io.github.clive.luxomssystem.common.utils.JSON
import okhttp3.OkHttpClient
import okhttp3.Request
import org.springframework.beans.factory.annotation.Value
import org.springframework.context.annotation.Configuration
import java.util.concurrent.locks.ReentrantLock
import kotlin.concurrent.withLock

@Configuration
class ShunFenApiTokenCache(
    val client: OkHttpClient,
) {
    @Value("\${shun-fen.account}")
    private lateinit var account: String

    @Value("\${shun-fen.apiSecret}")
    private lateinit var apiSecret: String

    @Value("\${shun-fen.api.token}")
    private lateinit var getTokenUrl: String

    private data class ShunFenApiToken(
        val token: String,
        val expireTime: Long,
    )

    private var shunFenApiToken: ShunFenApiToken? = null
    private val lock = ReentrantLock()

    fun getToken(): String {
        shunFenApiToken?.let { token ->
            if (token.expireTime > System.currentTimeMillis()) {
                return token.token
            }
        }

        return lock.withLock {
            shunFenApiToken?.let { token ->
                if (token.expireTime > System.currentTimeMillis()) {
                    return token.token
                }
            }

            // Generate new token
            val request =
                Request
                    .Builder()
                    .url("$getTokenUrl?appKey=$account&appSecret=$apiSecret")
                    .build()

            val response = client.newCall(request).execute()
            val responseBody = response.body?.string() ?: throw IllegalArgumentException("Empty response body")

            val resShunFenTokenDTO = JSON.parseObject<ResShunFenTokenDTO>(responseBody)
            if (resShunFenTokenDTO.apiResultCode != 0) {
                throw RuntimeException(resShunFenTokenDTO.apiErrorMsg ?: "Unknown error")
            }

            val newToken =
                ShunFenApiToken(
                    token = resShunFenTokenDTO.apiResultData?.accessToken ?: throw RuntimeException("Access token is null"),
                    expireTime = System.currentTimeMillis() + 1 * 60 * 60 * 1000,
                )
            shunFenApiToken = newToken
            newToken.token
        }
    }
}
