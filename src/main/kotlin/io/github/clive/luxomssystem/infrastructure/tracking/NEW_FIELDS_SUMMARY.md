# TrackingInfo新增字段说明

## 新增字段

### 1. orderNo (订单号)
- **类型**: `String?`
- **数据库字段**: `order_no VARCHAR(100)`
- **用途**: 关联订单信息，支持按订单查询所有相关轨迹
- **索引**: `idx_tracking_info_order_no`

### 2. waybillId (运单ID)
- **类型**: `Long?`
- **数据库字段**: `waybill_id BIGINT`
- **用途**: 关联运单实体，提供更精确的关联关系
- **索引**: `idx_tracking_info_waybill_id`

## 业务价值

### 1. 订单维度查询
- **场景**: 一个订单可能有多个包裹（多个运单）
- **需求**: 查看订单下所有包裹的轨迹信息
- **实现**: 通过`orderNo`字段查询该订单的所有轨迹记录

### 2. 数据关联优化
- **原方案**: 通过`waybillNo`字符串关联
- **新方案**: 通过`waybillId`数字ID关联，性能更好
- **优势**: 数字索引比字符串索引更高效

### 3. 数据一致性
- **问题**: 运单号可能重复或变更
- **解决**: 使用运单ID作为主键关联，确保数据一致性
- **备份**: 保留运单号作为业务标识

## API接口扩展

### 新增查询接口

```http
# 根据订单号查询轨迹（返回数组）
GET /api/tracking/order/{orderNo}

# 根据运单ID查询轨迹
GET /api/tracking/waybill-id/{waybillId}
```

### 响应数据结构

```json
{
  "id": 1001,
  "orderNo": "ORD20241229001",      // 新增：订单号
  "waybillId": 2001,               // 新增：运单ID
  "waybillNo": "YT2509821272328511",
  "trackingNumber": "YT2509821272328511",
  "channel": "YUNTU",
  "currentStatus": "DELIVERED",
  "currentStatusDisplay": "已签收",
  "destinationCountry": "US",
  "originCountry": "CN",
  "deliveryDays": 12,
  "lastUpdatedAt": "2024-12-29T10:30:00",
  "lastEventTime": "2024-12-27T16:20:00",
  "isCompleted": true
}
```

## 数据库变更

### 表结构变更

```sql
-- 添加新字段
ALTER TABLE tracking_info 
ADD COLUMN order_no VARCHAR(100),
ADD COLUMN waybill_id BIGINT;

-- 添加索引
CREATE INDEX idx_tracking_info_order_no ON tracking_info(order_no);
CREATE INDEX idx_tracking_info_waybill_id ON tracking_info(waybill_id);
CREATE INDEX idx_tracking_info_order_status ON tracking_info(order_no, current_status);
```

### 数据迁移

```sql
-- 为现有数据填充新字段
UPDATE tracking_info 
SET 
    order_no = w.order_no,
    waybill_id = w.id
FROM waybills w 
WHERE tracking_info.waybill_no = w.waybill_no
AND tracking_info.order_no IS NULL;
```

## 代码变更

### 1. 实体类变更
- `TrackingInfo`: 添加`orderNo`和`waybillId`字段
- 构造函数参数增加
- 响应DTO增加对应字段

### 2. Repository变更
- 添加`findByOrderNo()`方法
- 添加`findByWaybillId()`方法

### 3. Service变更
- `createInitialTrackingRecord()`方法增加参数
- 监听器传递订单号和运单ID

### 4. Controller变更
- 添加按订单号查询接口
- 添加按运单ID查询接口
- 响应数据包含新字段

## 使用示例

### 1. 按订单查询所有轨迹

```kotlin
// 查询订单下所有包裹的轨迹
val trackingList = trackingInfoRepository.findByOrderNo("ORD20241229001")
trackingList.forEach { tracking ->
    println("包裹: ${tracking.waybillNo}, 状态: ${tracking.currentStatus.displayName}")
}
```

### 2. 按运单ID精确查询

```kotlin
// 通过运单ID查询轨迹
val tracking = trackingInfoRepository.findByWaybillId(2001L)
if (tracking != null) {
    println("轨迹状态: ${tracking.currentStatus.displayName}")
}
```

### 3. 创建轨迹记录

```kotlin
// 创建时传入订单号和运单ID
val trackingInfo = trackingRemoteService.createInitialTrackingRecord(
    orderNo = waybill.orderNo,
    waybillId = waybill.id,
    waybillNo = waybill.waybillNo,
    channel = waybill.shipping.channel
)
```

## 性能优化

### 1. 查询性能
- **订单查询**: 通过`order_no`索引快速查找
- **运单查询**: 通过`waybill_id`数字索引，比字符串更快
- **复合查询**: `order_no + current_status`复合索引

### 2. 存储优化
- **数字ID**: `waybill_id`占用空间小于字符串
- **索引效率**: 数字索引比字符串索引更紧凑

### 3. 关联查询
- **直接关联**: 通过ID直接关联，避免字符串匹配
- **外键约束**: 可以添加外键约束保证数据完整性

## 兼容性

### 1. 向后兼容
- 原有的`waybillNo`查询接口保持不变
- 新字段允许为空，不影响现有数据

### 2. 数据迁移
- 自动填充现有数据的新字段
- 渐进式迁移，不影响业务

### 3. API兼容
- 新增接口，不修改现有接口
- 响应数据向后兼容

## 总结

通过添加`orderNo`和`waybillId`字段，TrackingInfo实体具备了更强的关联能力和查询灵活性：

- ✅ **订单维度查询**: 支持按订单查看所有包裹轨迹
- ✅ **性能优化**: 数字ID关联比字符串更高效
- ✅ **数据完整性**: 多重关联保证数据一致性
- ✅ **业务扩展**: 为后续功能扩展提供基础
- ✅ **向后兼容**: 不影响现有功能和数据

这些改进为轨迹系统提供了更好的数据组织和查询能力，支持更复杂的业务场景。
