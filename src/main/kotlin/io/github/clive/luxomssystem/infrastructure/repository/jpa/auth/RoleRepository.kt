package io.github.clive.luxomssystem.infrastructure.repository.jpa.auth

import io.github.clive.luxomssystem.domain.doanload.auth.Role
import io.github.clive.luxomssystem.infrastructure.config.AuthRewriter
import org.springframework.data.domain.Page
import org.springframework.data.domain.Pageable
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.jpa.repository.Query
import org.springframework.stereotype.Repository

@Repository
interface RoleRepository : JpaRepository<Role, Long> {
    fun findByBizId(bizId: Long): List<Role>

    fun findByNameContaining(name: String): List<Role>

    @Query(
        """
        select r from Role r
        where 1=1 and r.bizId = :bizId
        order by r.id desc 
    """,
        queryRewriter = AuthRewriter::class,
    )
    fun findByBizId(
        bizId: Long,
        pageable: Pageable,
    ): Page<Role>
}
