package io.github.clive.luxomssystem.infrastructure.repository.jpa

import io.github.clive.luxomssystem.domain.supplier.model.Supplier
import io.github.clive.luxomssystem.facade.supplier.response.SupplierSelectResponse
import io.github.clive.luxomssystem.infrastructure.config.AuthRewriter
import org.springframework.data.domain.Page
import org.springframework.data.domain.Pageable
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.jpa.repository.Query
import org.springframework.stereotype.Repository

@Repository
interface SupplierRepository : JpaRepository<Supplier, Long> {
    @Query(
        """
        select new io.github.clive.luxomssystem.facade.supplier.response.SupplierSelectResponse(s.id, s.name)
        from supplier s
        where s.bizId = :bizId and
        (:id is null or s.id = :id)
    """,
    )
    fun findSelectsByBizId(
        bizId: Long,
        id: Long? = null,
    ): List<SupplierSelectResponse>

    @Query("select u from supplier u where u.bizId = ?1")
    fun findNameByBizId(bizId: Long): List<Supplier>

    @Query(
        """
        select u from supplier u where 
        1=1 and
        u.bizId = :bizId and
        (:createdBy is null or u.createdBy = :createdBy)
        order by u.createdAt DESC
           """,
        queryRewriter = AuthRewriter::class,
    )
    fun pageQuery(
        bizId: Long,
        createdBy: Long?,
        page: Pageable,
    ): Page<Supplier>
}
