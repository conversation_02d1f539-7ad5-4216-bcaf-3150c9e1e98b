package io.github.clive.luxomssystem.infrastructure.repository.jpa.spu

import io.github.clive.luxomssystem.domain.customerSku.model.ComboCustomerSku
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.stereotype.Repository

@Repository
interface ComboCustomerSkuRepository : JpaRepository<ComboCustomerSku, Long> {
    fun findAllByComboCustomerSpuId(comboCustomerSpuId: Long): List<ComboCustomerSku>

    fun deleteAllBySystemSpuId(systemSpuId: Long)

    fun deleteAllBySystemSpuIdAndSystemSkuCodeNotIn(
        systemSpuId: Long,
        systemSkuCodes: Collection<String>,
    )

    fun deleteAllByComboCustomerSpuId(comboCustomerSpuId: Long)

    fun deleteAllByComboSpuId(comboSpuId: Long)

    fun findByComboCustomerSpuIdAndSystemSkuIdAndCustomerId(
        comboCustomerSpuId: Long,
        systemSkuId: Long,
        customerId: Long,
    ): ComboCustomerSku?
}
