package io.github.clive.luxomssystem.infrastructure.repository.jpa

import io.github.clive.luxomssystem.domain.SupplierCustomerSpuCapacity
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.stereotype.Repository

@Repository
interface SupplierCustomerSpuCapacityRepository : JpaRepository<SupplierCustomerSpuCapacity, Long> {
    fun findByCustomerSpuIdAndDate(
        customerSpuId: Long,
        date: String,
    ): List<SupplierCustomerSpuCapacity>
}
