package io.github.clive.luxomssystem.infrastructure.channel.prediction.channel.cainiao.response

import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import com.fasterxml.jackson.annotation.JsonProperty

@JsonIgnoreProperties(ignoreUnknown = true)
data class CreateLogisticsProviderResponse(
    @JsonProperty("data")
    var `data`: Data?,
    @JsonProperty("errorCode")
    var errorCode: String?,
    @JsonProperty("errorMsg")
    var errorMsg: String?,
    @JsonProperty("needRetry")
    var needRetry: String?,
    @JsonProperty("success")
    var success: String?,
) {
    @JsonIgnoreProperties(ignoreUnknown = true)
    data class Data(
        @JsonProperty("laneCode")
        var laneCode: String?,
        @JsonProperty("laneLastMileCP")
        var laneLastMileCP: String?,
        @JsonProperty("laneName")
        var laneName: String?,
        @JsonProperty("orderCode")
        var orderCode: String?,
        @JsonProperty("sortCode")
        var sortCode: String?,
        @JsonProperty("trackingNumber")
        var trackingNumber: String?,
        @JsonProperty("userSortCode")
        var userSortCode: String?,
    )
}
