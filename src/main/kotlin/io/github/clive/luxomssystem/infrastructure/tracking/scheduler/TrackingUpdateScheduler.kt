package io.github.clive.luxomssystem.infrastructure.tracking.scheduler

import io.github.clive.luxomssystem.application.tracking.TrackingApplicationService
import io.github.clive.luxomssystem.common.enums.WaybillChannel
import io.github.clive.luxomssystem.domain.tracking.model.*
import io.github.clive.luxomssystem.infrastructure.tracking.remote.TrackingRemoteService
import io.github.clive.luxomssystem.infrastructure.tracking.repository.TrackingInfoRepository
import io.github.oshai.kotlinlogging.KotlinLogging
import jakarta.annotation.PreDestroy
import org.springframework.data.domain.PageRequest
import org.springframework.data.redis.core.RedisTemplate
import org.springframework.scheduling.annotation.Scheduled
import org.springframework.stereotype.Service
import java.time.ZonedDateTime
import java.util.concurrent.CompletableFuture
import java.util.concurrent.Executors
import java.util.concurrent.TimeUnit
import java.util.concurrent.atomic.AtomicInteger

/**
 * 轨迹更新定时任务（重新设计版本）
 * 每天凌晨2点扫描tracking_info表中需要更新的轨迹信息
 * 使用流式处理避免内存压力，增加15天过滤条件
 */
@Service
class TrackingUpdateScheduler(
    private val trackingInfoRepository: TrackingInfoRepository,
    private val trackingRemoteService: TrackingRemoteService,
    private val trackingApplicationService: TrackingApplicationService,
    private val redisTemplate: RedisTemplate<String, String>,
    private val subOrderTrackingStatusUpdateService: io.github.clive.luxomssystem.domain.tracking.service.SubOrderTrackingStatusUpdateService
) {
    
    private val log = KotlinLogging.logger {}

    // 并发处理线程池
    private val concurrentExecutor = Executors.newFixedThreadPool(CONCURRENT_THREADS)
    
    companion object {
        private const val TRACKING_UPDATE_LOCK_KEY = "tracking:update:lock"
        private const val TRACKING_UPDATE_LOCK_TIMEOUT = 3600L // 1小时超时
        private const val MAX_DAYS_WITHOUT_UPDATE = 15L // 15天不更新阈值
        private const val BATCH_SIZE = 200 // 批量处理大小
        private const val UPDATE_INTERVAL_HOURS = 6L // 6小时更新一次

        // 并发处理配置
        private const val ENABLE_CONCURRENT_PROCESSING = true // 是否启用并发处理
        private const val CONCURRENT_THREADS = 40 // 并发线程数
    }

    /**
     * 每天凌晨2点执行轨迹更新任务
     * cron表达式: 秒 分 时 日 月 周
     */
    @Scheduled(cron = "0 0 2 * * ?")
    fun updateTrackingInfo() {
        val lockKey = TRACKING_UPDATE_LOCK_KEY
        val lockValue = System.currentTimeMillis().toString()
        
        try {
            // 尝试获取分布式锁，防止多实例重复执行
            val lockAcquired = redisTemplate.opsForValue()
                .setIfAbsent(lockKey, lockValue, TRACKING_UPDATE_LOCK_TIMEOUT, TimeUnit.SECONDS) ?: false
            
            if (!lockAcquired) {
                log.info { "轨迹更新任务已在其他实例执行中，跳过本次执行" }
                return
            }
            
            log.info { "开始执行轨迹更新定时任务" }
            val startTime = System.currentTimeMillis()

            // 计算时间范围（固定时间戳，确保查询一致性）
            val taskStartTime = ZonedDateTime.now()
            val updateCutoffTime = taskStartTime.minusHours(UPDATE_INTERVAL_HOURS) // 6小时前
            val staleCutoffTime = taskStartTime.minusDays(MAX_DAYS_WITHOUT_UPDATE) // 15天前

            // 统计需要更新的轨迹数量（仅用于日志记录和监控）
            val estimatedCount = trackingInfoRepository.countTrackingInfoForUpdate(updateCutoffTime, staleCutoffTime)
            log.info { "预估需要更新轨迹的记录数量: $estimatedCount" }

            if (estimatedCount == 0L) {
                log.info { "没有需要更新轨迹的记录，任务结束" }
                return
            }

            // 根据配置选择处理方式
            val results = if (ENABLE_CONCURRENT_PROCESSING) {
                log.info { "使用并发处理模式" }
                processTrackingInfoInBatchesConcurrent(updateCutoffTime, staleCutoffTime, estimatedCount)
            } else {
                log.info { "使用串行处理模式" }
                processTrackingInfoInBatches(updateCutoffTime, staleCutoffTime, estimatedCount)
            }
            
            val endTime = System.currentTimeMillis()
            val duration = endTime - startTime

            log.info {
                "轨迹更新定时任务完成 | " +
                        "预估记录数: $estimatedCount | " +
                        "实际处理: ${results.successCount + results.failureCount + results.skippedCount} | " +
                "成功更新: ${results.successCount} | " +
                "失败: ${results.failureCount} | " +
                "跳过: ${results.skippedCount} | " +
                "耗时: ${duration}ms"
            }
            
        } catch (e: Exception) {
            log.error(e) { "轨迹更新定时任务执行异常" }
        } finally {
            // 释放锁
            try {
                val currentLockValue = redisTemplate.opsForValue().get(lockKey)
                if (lockValue == currentLockValue) {
                    redisTemplate.delete(lockKey)
                }
            } catch (e: Exception) {
                log.warn(e) { "释放轨迹更新锁失败" }
            }
        }
    }

    /**
     * 流式分批处理轨迹信息（动态分页版本）
     * 不依赖总数计算，动态检查是否还有数据，避免数据不一致问题
     */
    private fun processTrackingInfoInBatches(
        updateCutoffTime: ZonedDateTime,
        staleCutoffTime: ZonedDateTime,
        estimatedCount: Long,
    ): BatchProcessResult {
        var successCount = 0
        var failureCount = 0
        var skippedCount = 0
        var page = 0
        var processedCount = 0

        log.info { "开始动态分批处理，预估记录数: $estimatedCount，每页大小: $BATCH_SIZE" }

        while (true) {
            try {
                val pageable = PageRequest.of(page, BATCH_SIZE)
                val trackingInfoPage = trackingInfoRepository.findTrackingInfoForUpdate(
                    updateCutoffTime, staleCutoffTime, pageable
                )

                // 如果当前页没有数据，说明处理完成
                if (trackingInfoPage.content.isEmpty()) {
                    log.info { "第${page + 1}页无数据，处理完成" }
                    break
                }

                log.info { "处理第${page + 1}页，记录数: ${trackingInfoPage.content.size}" }

                trackingInfoPage.content.forEach { trackingInfo ->
                    try {
                        val result = updateSingleTrackingInfo(trackingInfo)
                        when (result) {
                            UpdateResult.SUCCESS -> successCount++
                            UpdateResult.FAILURE -> failureCount++
                            UpdateResult.SKIPPED -> skippedCount++
                        }
                        processedCount++
                    } catch (e: Exception) {
                        log.error(e) { "处理轨迹信息异常 | 运单号: ${trackingInfo.waybillNo}" }
                        failureCount++
                        processedCount++
                    }
                }

                // 安全检查：如果处理的记录数远超预估数，可能有问题，停止处理
                if (processedCount > estimatedCount * 2) {
                    log.warn { "处理记录数($processedCount)远超预估数($estimatedCount)，可能存在数据异常，停止处理" }
                    break
                }

            } catch (e: Exception) {
                log.error(e) { "处理第${page + 1}页异常" }
                // 发生异常时，假设当前页的记录都失败，但继续处理下一页
                failureCount += BATCH_SIZE
                page++

                // 连续异常保护：如果连续多页都异常，停止处理
                if (page > 0 && failureCount > successCount + skippedCount) {
                    log.error { "连续异常过多，停止处理" }
                    break
                }
            }
        }

        log.info { "动态分批处理完成 | 总页数: $page | 实际处理记录数: $processedCount | 预估记录数: $estimatedCount" }
        return BatchProcessResult(successCount, failureCount, skippedCount)
    }

    /**
     * 并发版本的流式分批处理轨迹信息
     * 使用线程池并发处理每页数据，显著提高处理速度
     */
    private fun processTrackingInfoInBatchesConcurrent(
        updateCutoffTime: ZonedDateTime,
        staleCutoffTime: ZonedDateTime,
        estimatedCount: Long,
    ): BatchProcessResult {
        val successCount = AtomicInteger(0)
        val failureCount = AtomicInteger(0)
        val skippedCount = AtomicInteger(0)
        var page = 0
        var processedCount = 0

        log.info { "开始并发动态分批处理，预估记录数: $estimatedCount，每页大小: $BATCH_SIZE，并发线程数: $CONCURRENT_THREADS" }

        while (true) {
            try {
                val pageable = PageRequest.of(page, BATCH_SIZE)
                val trackingInfoPage = trackingInfoRepository.findTrackingInfoForUpdate(
                    updateCutoffTime, staleCutoffTime, pageable
                )

                // 如果当前页没有数据，说明处理完成
                if (trackingInfoPage.content.isEmpty()) {
                    log.info { "第${page + 1}页无数据，处理完成" }
                    break
                }

                log.info { "并发处理第${page + 1}页，记录数: ${trackingInfoPage.content.size}" }

                // 并发处理当前页的所有轨迹信息
                val futures = trackingInfoPage.content.map { trackingInfo ->
                    CompletableFuture.supplyAsync({
                        try {
                            val result = updateSingleTrackingInfo(trackingInfo)
                            when (result) {
                                UpdateResult.SUCCESS -> successCount.incrementAndGet()
                                UpdateResult.FAILURE -> failureCount.incrementAndGet()
                                UpdateResult.SKIPPED -> skippedCount.incrementAndGet()
                            }
                            result
                        } catch (e: Exception) {
                            log.error(e) { "并发处理轨迹信息异常 | 运单号: ${trackingInfo.waybillNo}" }
                            failureCount.incrementAndGet()
                            UpdateResult.FAILURE
                        }
                    }, concurrentExecutor)
                }

                // 等待当前页所有任务完成
                CompletableFuture.allOf(*futures.toTypedArray()).join()
                processedCount += trackingInfoPage.content.size

                page++

                // 安全检查：如果处理的记录数远超预估数，可能有问题，停止处理
                if (processedCount > estimatedCount * 2) {
                    log.warn { "处理记录数($processedCount)远超预估数($estimatedCount)，可能存在数据异常，停止处理" }
                    break
                }

            } catch (e: Exception) {
                log.error(e) { "并发处理第${page + 1}页异常" }
                failureCount.addAndGet(BATCH_SIZE)
                page++

                // 连续异常保护：如果连续多页都异常，停止处理
                if (page > 0 && failureCount.get() > successCount.get() + skippedCount.get()) {
                    log.error { "连续异常过多，停止处理" }
                    break
                }
            }
        }

        log.info { "并发动态分批处理完成 | 总页数: $page | 实际处理记录数: $processedCount | 预估记录数: $estimatedCount" }
        return BatchProcessResult(successCount.get(), failureCount.get(), skippedCount.get())
    }

    /**
     * 根据转换器名称获取运单渠道
     */
    private fun getWaybillChannelByConverterName(converterName: String): WaybillChannel? {
        return when (converterName) {
            "YANWEN" -> WaybillChannel.YW_HZ // 默认使用杭州燕文
            "YUNTU" -> WaybillChannel.YUNTU
            else -> null
        }
    }

    /**
     * 更新单个轨迹信息
     */
    private fun updateSingleTrackingInfo(trackingInfo: TrackingInfo): UpdateResult {
        val waybillNo = trackingInfo.waybillNo

        try {
            log.debug { "开始更新轨迹信息 | 运单号: $waybillNo | 渠道: ${trackingInfo.channel}" }

            // 获取并转换轨迹信息
            val trackingData = fetchAndConvertTrackingInfo(trackingInfo)
            if (trackingData == null) {
                return UpdateResult.FAILURE
            }

            // 使用简化的参数更新轨迹信息
            updateSingleTrackingInfo(
                trackingInfo = trackingInfo,
                status = trackingData.currentStatus,
                events = trackingData.trackingEvents,
                deliveryDays = trackingData.deliveryDays,
                trackingNumber = trackingData.trackingNumber,
                destinationCountry = trackingData.destinationCountry,
                originCountry = trackingData.originCountry,
                lastMileProvider = trackingData.lastMileProvider,
                podLinks = trackingData.podLinks
            )

            trackingInfoRepository.save(trackingInfo)

            log.debug { "轨迹信息更新成功 | 运单号: $waybillNo | 状态: ${trackingData.currentStatus.displayName}" }
            return UpdateResult.SUCCESS

        } catch (e: Exception) {
            log.error(e) { "更新轨迹信息异常 | 运单号: $waybillNo" }
            return UpdateResult.FAILURE
        }
    }

    /**
     * 更新单个轨迹信息（简化参数版本）
     */
    private fun updateSingleTrackingInfo(
        trackingInfo: TrackingInfo,
        status: TrackingStatus,
        events: List<TrackingEvent>,
        deliveryDays: Int?,
        trackingNumber: String? = null,
        destinationCountry: String? = null,
        originCountry: String? = null,
        lastMileProvider: LastMileProvider? = null,
        podLinks: List<String>? = null
    ) {
        trackingInfo.updateTracking(
            status = status,
            events = events,
            deliveryDays = deliveryDays,
            trackingNumber = trackingNumber,
            destinationCountry = destinationCountry,
            originCountry = originCountry,
            lastMileProvider = lastMileProvider,
            podLinks = podLinks,
            subOrderTrackingStatusUpdateService = subOrderTrackingStatusUpdateService
        )
    }

    /**
     * 获取并转换轨迹信息
     */
    private fun fetchAndConvertTrackingInfo(trackingInfo: TrackingInfo): TrackingData? {
        val waybillNo = trackingInfo.waybillNo
        val channel = getWaybillChannelByConverterName(trackingInfo.channel)

        if (channel == null) {
            log.warn { "无法识别轨迹渠道 | 运单号: $waybillNo | 渠道: ${trackingInfo.channel}" }
            return null
        }

        // 调用远程API获取轨迹信息
        val remoteResponse = trackingRemoteService.getTrackingByChannel(waybillNo, channel)
        if (remoteResponse == null) {
            log.warn { "获取轨迹信息失败 | 运单号: $waybillNo | 渠道: ${trackingInfo.channel}" }
            return null
        }

        // 转换为统一格式
        val newTrackingData = when (trackingInfo.channel) {
            "YANWEN" -> trackingApplicationService.processYanWenTracking(
                remoteResponse as io.github.clive.luxomssystem.infrastructure.channel.prediction.channel.yanwen.YanWenTrackingResponse,
                waybillNo
            )
            "YUNTU" -> trackingApplicationService.processYuntuTracking(
                remoteResponse as io.github.clive.luxomssystem.infrastructure.channel.prediction.channel.yuntu.YuntuTrackingResponse,
                waybillNo
            )
            else -> null
        }

        if (newTrackingData == null) {
            log.warn { "轨迹信息转换失败 | 运单号: $waybillNo | 渠道: ${trackingInfo.channel}" }
            return null
        }

        return newTrackingData
    }

    /**
     * 批处理结果
     */
    data class BatchProcessResult(
        val successCount: Int,
        val failureCount: Int,
        val skippedCount: Int
    )

    /**
     * 资源清理
     */
    @PreDestroy
    fun cleanup() {
        log.info { "关闭轨迹更新调度器线程池" }
        concurrentExecutor.shutdown()
        try {
            if (!concurrentExecutor.awaitTermination(60, TimeUnit.SECONDS)) {
                concurrentExecutor.shutdownNow()
            }
        } catch (e: InterruptedException) {
            concurrentExecutor.shutdownNow()
        }
    }

    /**
     * 更新结果枚举
     */
    enum class UpdateResult {
        SUCCESS,
        FAILURE,
        SKIPPED
    }
}
