package io.github.clive.luxomssystem.infrastructure.channel.cos

import com.qcloud.cos.COSClient
import com.qcloud.cos.ClientConfig
import com.qcloud.cos.auth.BasicCOSCredentials
import com.qcloud.cos.http.HttpProtocol
import com.qcloud.cos.model.ListObjectsRequest
import com.qcloud.cos.model.ObjectMetadata
import com.qcloud.cos.model.PutObjectRequest
import com.qcloud.cos.model.ciModel.common.MediaOutputObject
import com.qcloud.cos.model.ciModel.job.FileProcessJobResponse
import com.qcloud.cos.model.ciModel.job.FileProcessJobType
import com.qcloud.cos.model.ciModel.job.FileProcessRequest
import io.github.clive.luxomssystem.domain.doanload.auth.SystemConfig
import io.github.clive.luxomssystem.infrastructure.config.auth.UserContextHolder
import io.github.oshai.kotlinlogging.KotlinLogging
import jakarta.annotation.PostConstruct
import kotlinx.coroutines.async
import kotlinx.coroutines.awaitAll
import kotlinx.coroutines.coroutineScope
import org.springframework.stereotype.Component
import java.io.InputStream

@Component
class CosInnerRemoteService {
    private lateinit var ossConfig: SystemConfig.OssConfig

    @PostConstruct
    fun initOss() {
        ossConfig =
            createCosClient(
                SystemConfig.OssConfig(
                    "https://hcos-1324937288.cos.ap-shanghai.myqcloud.com",
                    "ap-shanghai",
                    "hcos-1324937288",
                    "AKIDKWWdqGvYkswvwsTE2w5ZzHmYKzogO6xl",
                    "9HZwqjyFSarPydGGTl3IYm3MjLuMyb6Y",
                ),
            )
    }

    private fun createCosClient(ossConfig: SystemConfig.OssConfig): SystemConfig.OssConfig {
        with(ossConfig) {
            val cred = BasicCOSCredentials(tmpSecretId, tmpSecretKey)
            val clientConfig = ClientConfig(region)
            clientConfig.httpProtocol = HttpProtocol.https
            ossConfig.cosClient = COSClient(cred, clientConfig)
        }
        return ossConfig
    }

    suspend fun listFilesInDirectoriesConcurrently(
        directoryPaths: List<String>,
        bizId: Long,
    ): List<String> =
        coroutineScope {
            directoryPaths
                .map { directoryPath ->
                    async {
                        listFilesInDirectory(directoryPath, bizId)
                    }
                }.awaitAll()
                .flatten()
        }

    fun listFilesInDirectory(
        directoryPath: String,
        bizId: Long,
    ): List<String> {
        with(ossConfig) {
            val listObjectsRequest = ListObjectsRequest()
            listObjectsRequest.bucketName = bucketName
            listObjectsRequest.prefix = directoryPath

            val objectListing = cosClient!!.listObjects(listObjectsRequest)
            return objectListing.objectSummaries.map { it.key }
        }
    }

    fun packageImagesUseKeysFromCOS(
        bizId: Long,
        order: String,
        keyIds: List<String>,
        zipPath: String,
    ): String {
        with(ossConfig) {
            val request = FileProcessRequest()
            request.bucketName = bucketName
            request.tag = FileProcessJobType.FileCompress

            val fileCompressConfig = request.operation.fileCompressConfig
            fileCompressConfig.format = "zip"
            fileCompressConfig.flatten = "3"
            fileCompressConfig.key = keyIds

            val output = request.operation.output
            output.bucket = bucketName
            output.region = region.regionName
            output.`object` = "/" + zipPath + "/$order.zip"

            // 发起文件压缩任务
            val response: FileProcessJobResponse = cosClient!!.createFileProcessJob(request)

            // 获取任务 ID
            return response.jobDetail.jobId
        }
    }

    fun queryFileProcessJob(
        bizId: Long,
        jobId: String?,
    ): String? {
        with(ossConfig) {
            val request = FileProcessRequest()
            request.bucketName = bucketName
            request.jobId = jobId
            try {
                val response = cosClient!!.describeFileProcessJob(request)
                if (response.jobDetail.code == "Success") {
                    val objectUrl: String = getObjectUrl(response.jobDetail.operation.output)
                    return domain + response.jobDetail.operation.output.`object`
                } else {
                    log.error { "failed to query file process job ：${response.jobDetail.message}" }
                    return null
                }
            } catch (e: Exception) {
                log.error(e) { "failed to query file process job" }
                throw RuntimeException("failed to query file process job")
            }
        }
    }

    fun uploadFile(
        key: String,
        inputStream: InputStream,
    ): String {
        val user = UserContextHolder.user!!
        with(ossConfig) {
            val putObjectRequest = PutObjectRequest(bucketName, key, inputStream, ObjectMetadata())
            cosClient!!.putObject(putObjectRequest)
            val storedUrl = getStoredUrl(user.bizId, key)
            log.info { "upload file success, url: $storedUrl" }
            return storedUrl
        }
    }

    fun uploadFile(
        bizId: Long,
        key: String,
        inputStream: InputStream,
    ): String {
        with(ossConfig) {
            val putObjectRequest = PutObjectRequest(bucketName, key, inputStream, ObjectMetadata())
            cosClient!!.putObject(putObjectRequest)
            return getStoredUrl(bizId, key)
        }
    }

    fun getStoredUrl(
        bizId: Long,
        path: String,
    ): String = "${ossConfig.domain}/$path"

    private fun getObjectUrl(output: MediaOutputObject): String {
        @Suppress("HttpUrlsUsage")
        return "http://" + output.bucket + ".cos." + output.region + ".myqcloud.com/" + output.getObject()
    }

    companion object {
        private val log = KotlinLogging.logger {}
    }
}
