package io.github.clive.luxomssystem.infrastructure.channel.prediction.channel.cainiao.req

import com.fasterxml.jackson.annotation.JsonProperty

data class ItemParam(
    @JsonProperty("brand")
    var brand: String?,
    @JsonProperty("categoryId")
    var categoryId: String?,
    @JsonProperty("categoryName")
    var categoryName: String?,
    @JsonProperty("chineseName")
    var chineseName: String?,
    @JsonProperty("clearanceShipUnitPrice")
    var clearanceShipUnitPrice: String?,
    @JsonProperty("clearanceShipVat")
    var clearanceShipVat: String?,
    @JsonProperty("clearanceUnitPrice")
    var clearanceUnitPrice: String?,
    @JsonProperty("clearanceVat")
    var clearanceVat: String?,
    @JsonProperty("englishName")
    var englishName: String?,
    @JsonProperty("hscode")
    var hscode: String?,
    @JsonProperty("itemId")
    var itemId: String?,
    @JsonProperty("itemType")
    var itemType: String?,
    @JsonProperty("itemUrl")
    var itemUrl: String?,
    @JsonProperty("localName")
    var localName: String?,
    @JsonProperty("material")
    var material: String?,
    @JsonProperty("msds")
    var msds: String?,
    @JsonProperty("quantity")
    var quantity: String?,
    @JsonProperty("sku")
    var sku: String?,
    @JsonProperty("specification")
    var specification: String?,
    @JsonProperty("taxCurrency")
    var taxCurrency: String?,
    @JsonProperty("taxRate")
    var taxRate: String?,
    @JsonProperty("title")
    var title: String?,
    @JsonProperty("unitPrice")
    var unitPrice: String?,
    @JsonProperty("unitPriceCurrency")
    var unitPriceCurrency: String?,
    @JsonProperty("weight")
    var weight: String?,
)
