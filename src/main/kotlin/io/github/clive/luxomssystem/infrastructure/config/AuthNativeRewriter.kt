package io.github.clive.luxomssystem.infrastructure.config

import io.github.clive.luxomssystem.common.enums.RoleLimit
import io.github.clive.luxomssystem.infrastructure.config.auth.UserContextHolder
import org.springframework.data.domain.Sort
import org.springframework.data.jpa.repository.QueryRewriter

class AuthNativeRewriter : QueryRewriter {
    override fun rewrite(
        query: String,
        sort: Sort,
    ): String =
        if (UserContextHolder.user!!.roleLimit == RoleLimit.SELF) {
            query.replace("1=1", "created_by = ${UserContextHolder.user!!.id}")
        } else {
            query
        }
}
