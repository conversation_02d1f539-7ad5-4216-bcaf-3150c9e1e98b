package io.github.clive.luxomssystem.infrastructure.repository.jpa.spu

import io.github.clive.luxomssystem.domain.spu.model.ComboSpu
import org.springframework.data.domain.Page
import org.springframework.data.domain.Pageable
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.jpa.repository.Modifying
import org.springframework.data.jpa.repository.Query
import org.springframework.data.repository.query.Param

interface ComboSpuRepository : JpaRepository<ComboSpu, Long> {
    @Query(
        """
        select spu.* from combo_spu spu
        where
            (cast(:spuCode as text) is null or exists (select 1 from unnest(spu.spu_codes) as sc where sc ilike :spuCode))
            and (cast(:category as text) is null or spu.category = :category)
            and (cast(:bizId as int8) is null or spu.biz_id = :bizId)
            and (cast(:title as text) is null or spu.title ilike :title)
        order by id desc 
    """,
        nativeQuery = true,
    )
    fun pageQuery(
        @Param("spuCode") spuCode: String?,
        @Param("category") category: String?,
        @Param("bizId") bizId: Long?,
        @Param("title") title: String?,
        pageable: Pageable,
    ): Page<ComboSpu>

    fun findAllByBizId(bizId: Long): List<ComboSpu>

    fun findAllByBizIdAndIdNotIn(
        bizId: Long,
        ids: Collection<Long>,
    ): List<ComboSpu>

    @Query(
        """
            select spu.* from combo_spu spu
            where :spuId = ANY(spu.spu_ids)
        """,
        nativeQuery = true,
    )
    fun findAllBySpuIdsContains(spuId: Long): List<ComboSpu>

    @Modifying
    @Query(
        """
            delete from combo_spu spu
            where :spuId = ANY(spu.spu_ids)
        """,
        nativeQuery = true,
    )
    fun deleteAllBySpuIdsContains(spuId: Long)

    fun existsBySpuRefId(spuRefId: String): Boolean
}
