# 轨迹定时更新系统（重新设计版本）

## 概述

轨迹定时更新系统是一个重新设计的自动化物流轨迹信息管理系统，采用了更优的架构设计：

1. **运单状态监听**：当运单状态变为OUTBOUND时自动创建轨迹记录
2. **定时扫描轨迹表**：每天凌晨2点扫描tracking_info表而非waybills表
3. **流式处理**：使用分页查询避免内存压力，支持大数据量处理
4. **智能过滤**：15天未更新的轨迹自动停止查询，节省API调用
5. **防重复执行**：使用Redis分布式锁防止多实例重复执行

## 核心功能

### 1. 运单状态监听
- **触发时机**：运单状态变更为OUTBOUND时
- **自动创建**：为支持的渠道自动创建初始轨迹记录
- **支持渠道**：燕文（YW_HZ, YW_QZ, YW_GZ, YW_YW）、云途（YUNTU）
- **去重机制**：避免重复创建轨迹记录

### 2. 自动定时更新
- **执行时间**：每天凌晨2:00
- **扫描范围**：tracking_info表中的未完成轨迹记录
- **流式处理**：分页查询，每批50条记录，避免内存压力
- **智能过滤**：
  - 只更新未完成的轨迹（isCompleted = false）
  - 跳过轨迹最后事件超过15天的记录（基于lastEventTime而非lastUpdatedAt）
  - 6小时内更新过的跳过（避免频繁调用API）

### 3. 手动触发更新
- **单个运单更新**：根据运单号更新指定轨迹
- **渠道批量更新**：更新指定渠道的所有未完成轨迹
- **强制更新**：忽略15天限制强制更新
- **全量更新触发**：手动触发定时任务

### 3. 轨迹统计和监控
- 轨迹总数统计
- 状态分布统计
- 渠道分布统计
- 过期轨迹统计

### 4. 数据清理
- 自动清理超过30天的已完成轨迹
- 手动触发清理功能

## API接口

### 查询接口

```http
# 分页查询轨迹信息
GET /api/tracking/page?page=0&size=20&waybillNo=xxx&channel=YUNTU

# 根据运单号查询轨迹
GET /api/tracking/waybill/{waybillNo}

# 根据订单号查询轨迹（可能返回多条）
GET /api/tracking/order/{orderNo}

# 根据运单ID查询轨迹
GET /api/tracking/waybill-id/{waybillId}

# 获取轨迹统计信息
GET /api/tracking/statistics

# 获取支持的渠道列表
GET /api/tracking/channels
```

### 更新接口

```http
# 手动更新指定运单轨迹
POST /api/tracking/update/waybill/{waybillNo}

# 强制更新指定运单轨迹（忽略15天限制）
POST /api/tracking/force-update/waybill/{waybillNo}

# 手动更新指定渠道的所有轨迹
POST /api/tracking/update/channel/{channel}

# 手动触发全量轨迹更新
POST /api/tracking/update/all
```

### 管理接口

```http
# 清理过期轨迹信息
POST /api/tracking/cleanup
```

## 配置说明

### 定时任务配置
```yaml
# application.yml
spring:
  task:
    scheduling:
      pool:
        size: 2  # 定时任务线程池大小
```

### Redis配置（用于分布式锁）
```yaml
spring:
  redis:
    host: localhost
    port: 6379
    database: 0
```

### 渠道API配置
```yaml
# 云途配置
yun-tu:
  account: CN0217725
  apiSecret: I7SBNclmqIkZpaT7nmEBGw==

# 燕文配置（使用固定Authorization）
# Authorization: ********
```

## 数据库表结构

### tracking_info表
```sql
CREATE TABLE tracking_info (
    id BIGINT PRIMARY KEY,
    order_no VARCHAR(100),
    waybill_id BIGINT,
    waybill_no VARCHAR(100) NOT NULL UNIQUE,
    tracking_number VARCHAR(100),
    channel VARCHAR(50) NOT NULL,
    current_status VARCHAR(50) NOT NULL,
    destination_country VARCHAR(10),
    origin_country VARCHAR(10),
    last_mile_provider JSONB,
    tracking_events JSONB,
    delivery_days INTEGER,
    pod_links JSONB,
    last_updated_at TIMESTAMP,
    last_event_time TIMESTAMP,
    is_completed BOOLEAN DEFAULT FALSE,
    raw_data JSONB,
    created_at BIGINT,
    updated_at BIGINT,
    created_by_name VARCHAR(100),
    updated_by_name VARCHAR(100)
);

-- 索引
CREATE INDEX idx_tracking_info_waybill_no ON tracking_info(waybill_no);
CREATE INDEX idx_tracking_info_order_no ON tracking_info(order_no);
CREATE INDEX idx_tracking_info_waybill_id ON tracking_info(waybill_id);
CREATE INDEX idx_tracking_info_channel ON tracking_info(channel);
CREATE INDEX idx_tracking_info_status ON tracking_info(current_status);
CREATE INDEX idx_tracking_info_updated_at ON tracking_info(last_updated_at);
CREATE INDEX idx_tracking_info_last_event_time ON tracking_info(last_event_time);
CREATE INDEX idx_tracking_info_completed ON tracking_info(is_completed);
CREATE INDEX idx_tracking_info_order_status ON tracking_info(order_no, current_status);
CREATE INDEX idx_tracking_info_update_query_v2 ON tracking_info(is_completed, last_updated_at, last_event_time);
```

## 使用示例

### 1. 查看轨迹统计
```bash
curl -X GET "http://localhost:8080/api/tracking/statistics"
```

响应示例：
```json
{
  "totalTracking": 1250,
  "completedTracking": 980,
  "pendingTracking": 270,
  "staleTracking": 45,
  "statusCounts": {
    "已签收": 980,
    "运输途中": 180,
    "已揽收": 90
  },
  "channelCounts": {
    "YUNTU": 800,
    "YANWEN": 450
  }
}
```

### 2. 根据订单号查询轨迹
```bash
curl -X GET "http://localhost:8080/api/tracking/order/ORD20241229001"
```

响应示例：
```json
[
  {
    "id": 1001,
    "orderNo": "ORD20241229001",
    "waybillId": 2001,
    "waybillNo": "YT2509821272328511",
    "trackingNumber": "YT2509821272328511",
    "channel": "YUNTU",
    "currentStatus": "DELIVERED",
    "currentStatusDisplay": "已签收",
    "destinationCountry": "US",
    "originCountry": "CN",
    "deliveryDays": 12,
    "lastUpdatedAt": "2024-12-29T10:30:00",
    "lastEventTime": "2024-12-27T16:20:00",
    "isCompleted": true
  }
]
```

### 3. 根据运单ID查询轨迹
```bash
curl -X GET "http://localhost:8080/api/tracking/waybill-id/2001"
```

### 2. 手动更新单个运单
```bash
curl -X POST "http://localhost:8080/api/tracking/update/waybill/YT2509821272328511"
```

### 3. 强制更新运单（忽略15天限制）
```bash
curl -X POST "http://localhost:8080/api/tracking/force-update/waybill/YT2509821272328511"
```

### 4. 批量更新渠道轨迹
```bash
curl -X POST "http://localhost:8080/api/tracking/update/channel/YUNTU"
```

## 监控和日志

### 关键日志
- 定时任务执行日志
- 轨迹更新成功/失败日志
- API调用异常日志
- 分布式锁获取/释放日志

### 监控指标
- 每日处理运单数量
- 成功率统计
- API响应时间
- 错误率统计

## 性能优化

### 1. 流式处理
- **分页查询**：每页50条记录，避免一次性加载大量数据
- **内存友好**：不会因为数据量大而导致内存溢出
- **批次间休息**：每批处理后休息1秒，避免API压力

### 2. 智能过滤
- **数据库层过滤**：在SQL查询中直接过滤，减少无效数据传输
- **时间范围过滤**：只查询6小时前到15天内的记录
- **状态过滤**：只处理未完成的轨迹记录

### 3. 分布式锁
- **防重复执行**：使用Redis锁防止多实例同时执行
- **锁超时保护**：1小时超时，防止死锁
- **优雅释放**：任务完成后主动释放锁

### 4. 数据清理
- **自动清理**：清理超过30天的已完成轨迹
- **存储优化**：减少数据库存储压力
- **索引优化**：合理的数据库索引提升查询性能

## 故障排查

### 常见问题

1. **定时任务不执行**
   - 检查@EnableScheduling注解
   - 检查cron表达式
   - 查看应用日志

2. **轨迹更新失败**
   - 检查API配置
   - 检查网络连接
   - 查看错误日志

3. **重复执行**
   - 检查Redis连接
   - 检查分布式锁配置

4. **性能问题**
   - 调整批处理大小
   - 增加批次间休息时间
   - 检查数据库索引

### 日志级别配置
```yaml
logging:
  level:
    io.github.clive.luxomssystem.infrastructure.tracking: INFO
    io.github.clive.luxomssystem.application.tracking: INFO
```

## 扩展说明

### 添加新渠道支持
1. 在TrackingRemoteService中添加新渠道的API调用方法
2. 创建对应的TrackingConverter实现
3. 更新渠道判断逻辑

### 自定义更新策略
1. 修改shouldUpdateWaybillTracking方法
2. 调整MAX_DAYS_WITHOUT_UPDATE常量
3. 添加新的过滤条件
