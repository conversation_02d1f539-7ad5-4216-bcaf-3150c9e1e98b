package io.github.clive.luxomssystem.infrastructure.repository.jpa

import io.github.clive.luxomssystem.domain.CorrectMainOrderFinance
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.jpa.repository.Query
import org.springframework.data.repository.query.Param
import org.springframework.stereotype.Repository
import java.math.BigDecimal

@Repository
interface CorrectMainOrderFinanceRepository : JpaRepository<CorrectMainOrderFinance, Long> {
    @Query(
        """
        SELECT COALESCE(SUM(c.correctFinanceCost), 0) as correctFinanceCost 
        FROM correct_main_order_finance c 
        WHERE c.customerId = :customerId and c.bizId = :bizId
    """,
    )
    fun getCorrectFinanceByCustomer(
        @Param("customerId") customerId: Long,
        bizId: Long,
    ): BigDecimal

    fun findByMainOrderId(mainOrderId: Long): CorrectMainOrderFinance?
}
