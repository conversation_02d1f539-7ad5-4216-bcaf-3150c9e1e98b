package io.github.clive.luxomssystem.infrastructure.channel.prediction

import io.github.clive.luxomssystem.domain.SubOrder
import io.github.clive.luxomssystem.domain.waybill.model.Waybill
import io.github.clive.luxomssystem.infrastructure.channel.cos.CosInnerRemoteService
import io.github.clive.luxomssystem.infrastructure.repository.jpa.SubOrderRepository
import okhttp3.OkHttpClient
import okhttp3.Request
import java.util.*

interface OrderPredictionRemoteService {
    val ossClient: CosInnerRemoteService

    val okHttpClient: OkHttpClient

    val subOrderRepository: SubOrderRepository

    fun createPredication(waybill: Waybill)

    fun deletePredication(waybill: Waybill): Boolean

    fun getPrintUrl(waybill: Waybill): String

    fun getStoreUrl(waybill: Waybill): String = "/waybill/pdf/${waybill.bizId}/${UUID.randomUUID().toString() + ".pdf"}"

    fun loadSubOrder(waybill: Waybill): List<SubOrder> =
        subOrderRepository.findByShipping_WayBillRelationAndParentId(
            waybill.shipping.wayBillRelation!!,
            waybill.mainOrderId,
        )

    fun uploadWaybill(
        waybill: Waybill,
        pdfUrl: String,
    ): String {
        okHttpClient
            .newCall(
                Request
                    .Builder()
                    .url(pdfUrl)
                    .build(),
            ).execute()
            .use { pdfResponse ->
                pdfResponse.body?.byteStream()?.use { inputStream ->
                    return ossClient.uploadFile(waybill.bizId, getStoreUrl(waybill), inputStream)
                } ?: throw Exception("Empty PDF response body")
            }
    }
}
