package io.github.clive.luxomssystem.infrastructure.repository.jpa

import io.github.clive.luxomssystem.common.enums.BaseStatus
import io.github.clive.luxomssystem.common.enums.DownloadTemplateType
import io.github.clive.luxomssystem.domain.doanload.DownloadTemplate
import org.springframework.data.domain.Page
import org.springframework.data.domain.Pageable
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.jpa.repository.Query
import org.springframework.stereotype.Repository

@Repository
interface DownloadTemplateRepository : JpaRepository<DownloadTemplate, Long> {
    fun findByNameContainingIgnoreCase(name: String): List<DownloadTemplate>

    @Query(
        """
        select t from DownloadTemplate t
        where (:name is null or t.name like %:name%)
        and (:type is null or t.type = :type)
        and t.status = :status
        and t.bizId = :bizId
        order by t.createdAt desc
    """,
    )
    fun findByName(
        name: String?,
        type: DownloadTemplateType?,
        status: BaseStatus,
        bizId: Long,
    ): List<DownloadTemplate>

    fun findByStatusAndBizId(
        status: BaseStatus,
        bizId: Long,
        pageable: Pageable,
    ): Page<DownloadTemplate>

    fun findByTypeAndBizId(
        type: DownloadTemplateType,
        bizId: Long,
    ): List<DownloadTemplate>
}
