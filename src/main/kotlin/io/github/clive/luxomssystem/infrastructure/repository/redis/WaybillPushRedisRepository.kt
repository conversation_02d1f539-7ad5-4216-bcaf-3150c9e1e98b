package io.github.clive.luxomssystem.infrastructure.repository.redis

import io.github.oshai.kotlinlogging.KotlinLogging
import org.springframework.data.redis.core.RedisTemplate
import org.springframework.stereotype.Repository
import java.util.concurrent.TimeUnit

@Repository
class WaybillPushRedisRepository(
    private val redisTemplate: RedisTemplate<String, String>,
) {
    companion object {
        private val log = KotlinLogging.logger {}
        private const val WAYBILL_PUSH_PREFIX = "WAYBILL_PUSH:"
        private const val WAYBILL_PENDING_PREFIX = "WAYBILL_PENDING:"
        private const val WAYBILL_RETRY_LOCK_PREFIX = "WAYBILL_RETRY_LOCK:"
        private const val DEFAULT_EXPIRE_HOURS = 1L
        private const val PENDING_EXPIRE_SECONDS = 30L
        private const val RETRY_LOCK_EXPIRE_SECONDS = 10L
    }

    /**
     * 检查并设置运单推送标志
     * @param mainOrderId 主订单ID
     * @return true表示可以推送，false表示已经在推送中
     */
    fun trySetPushFlag(mainOrderId: Long): Boolean {
        val key = "$WAYBILL_PUSH_PREFIX$mainOrderId"

        return try {
            val result = redisTemplate.opsForValue().setIfAbsent(key, "1", DEFAULT_EXPIRE_HOURS, TimeUnit.HOURS)
            if (result == true) {
                log.info { "设置运单推送标志成功 | 主订单ID: $mainOrderId | 过期时间: ${DEFAULT_EXPIRE_HOURS}小时" }
                true
            } else {
                log.warn { "运单推送标志已存在，防止重复推送 | 主订单ID: $mainOrderId" }
                false
            }
        } catch (e: Exception) {
            log.error(e) { "设置运单推送标志异常 | 主订单ID: $mainOrderId" }
            // 异常时允许推送，避免阻塞正常业务
            true
        }
    }

    /**
     * 清除运单推送标志
     * @param mainOrderId 主订单ID
     */
    fun clearPushFlag(mainOrderId: Long) {
        val key = "$WAYBILL_PUSH_PREFIX$mainOrderId"

        try {
            redisTemplate.delete(key)
            log.info { "清除运单推送标志 | 主订单ID: $mainOrderId" }
        } catch (e: Exception) {
            log.error(e) { "清除运单推送标志异常 | 主订单ID: $mainOrderId" }
        }
    }

    /**
     * 检查并设置运单预报标志
     * @param waybillId 运单ID
     * @return true表示可以预报，false表示已经在预报中
     */
    fun trySetPendingFlag(waybillId: Long): Boolean {
        val key = "$WAYBILL_PENDING_PREFIX$waybillId"

        return try {
            val result = redisTemplate.opsForValue().setIfAbsent(key, "1", PENDING_EXPIRE_SECONDS, TimeUnit.SECONDS)
            if (result == true) {
                log.info { "设置运单预报标志成功 | 运单ID: $waybillId | 过期时间: ${PENDING_EXPIRE_SECONDS}分钟" }
                true
            } else {
                log.warn { "运单预报标志已存在，防止重复预报 | 运单ID: $waybillId" }
                false
            }
        } catch (e: Exception) {
            log.error(e) { "设置运单预报标志异常 | 运单ID: $waybillId" }
            // 异常时允许预报，避免阻塞正常业务
            true
        }
    }

    /**
     * 清除运单预报标志
     * @param waybillId 运单ID
     */
    fun clearPendingFlag(waybillId: Long) {
        val key = "$WAYBILL_PENDING_PREFIX$waybillId"

        try {
            redisTemplate.delete(key)
            log.info { "清除运单预报标志 | 运单ID: $waybillId" }
        } catch (e: Exception) {
            log.error(e) { "清除运单预报标志异常 | 运单ID: $waybillId" }
        }
    }

    /**
     * 检查运单推送标志是否存在
     * @param mainOrderId 主订单ID
     * @return true表示标志存在，false表示不存在
     */
    fun isPushFlagExists(mainOrderId: Long): Boolean {
        val key = "$WAYBILL_PUSH_PREFIX$mainOrderId"
        return try {
            redisTemplate.hasKey(key)
        } catch (e: Exception) {
            log.error(e) { "检查运单推送标志异常 | 主订单ID: $mainOrderId" }
            false
        }
    }

    /**
     * 检查运单预报标志是否存在
     * @param waybillId 运单ID
     * @return true表示标志存在，false表示不存在
     */
    fun isPendingFlagExists(waybillId: Long): Boolean {
        val key = "$WAYBILL_PENDING_PREFIX$waybillId"
        return try {
            redisTemplate.hasKey(key)
        } catch (e: Exception) {
            log.error(e) { "检查运单预报标志异常 | 运单ID: $waybillId" }
            false
        }
    }

    /**
     * 设置运单预报成功标志
     * @param waybillId 运单ID
     * @return true表示设置成功，false表示已存在
     */
    fun setPendingSuccessFlag(waybillId: Long): Boolean {
        val key = "$WAYBILL_PENDING_PREFIX$waybillId"

        return try {
            val result = redisTemplate.opsForValue().setIfAbsent(key, "1", PENDING_EXPIRE_SECONDS, TimeUnit.SECONDS)
            if (result == true) {
                log.info { "设置运单预报成功标志 | 运单ID: $waybillId | 过期时间: ${PENDING_EXPIRE_SECONDS}秒" }
                true
            } else {
                log.warn { "运单预报成功标志已存在 | 运单ID: $waybillId" }
                false
            }
        } catch (e: Exception) {
            log.error(e) { "设置运单预报成功标志异常 | 运单ID: $waybillId" }
            // 异常时返回false，避免影响业务逻辑
            false
        }
    }

    /**
     * 尝试获取运单重试锁
     * @param waybillId 运单ID
     * @param lockValue 锁的值，用于释放锁时验证
     * @return true表示获取锁成功，false表示锁已被占用
     */
    fun tryAcquireRetryLock(waybillId: Long, lockValue: String): Boolean {
        val key = "$WAYBILL_RETRY_LOCK_PREFIX$waybillId"

        return try {
            val result = redisTemplate.opsForValue().setIfAbsent(key, lockValue, RETRY_LOCK_EXPIRE_SECONDS, TimeUnit.SECONDS)
            if (result == true) {
                log.info { "获取运单重试锁成功 | 运单ID: $waybillId | 锁值: $lockValue | 过期时间: ${RETRY_LOCK_EXPIRE_SECONDS}秒" }
                true
            } else {
                log.warn { "运单重试锁已被占用 | 运单ID: $waybillId" }
                false
            }
        } catch (e: Exception) {
            log.error(e) { "获取运单重试锁异常 | 运单ID: $waybillId" }
            // 异常时允许操作，避免阻塞正常业务
            true
        }
    }

    /**
     * 释放运单重试锁
     * @param waybillId 运单ID
     * @param lockValue 锁的值，用于验证是否为当前持有者
     */
    fun releaseRetryLock(waybillId: Long, lockValue: String) {
        val key = "$WAYBILL_RETRY_LOCK_PREFIX$waybillId"

        try {
            val currentLockValue = redisTemplate.opsForValue().get(key)
            if (lockValue == currentLockValue) {
                redisTemplate.delete(key)
                log.info { "释放运单重试锁成功 | 运单ID: $waybillId | 锁值: $lockValue" }
            } else {
                log.warn { "释放运单重试锁失败，锁值不匹配 | 运单ID: $waybillId | 期望锁值: $lockValue | 实际锁值: $currentLockValue" }
            }
        } catch (e: Exception) {
            log.error(e) { "释放运单重试锁异常 | 运单ID: $waybillId | 锁值: $lockValue" }
        }
    }

    /**
     * 原子性检查并设置运单预报标志
     * 使用 Redis 的 setIfAbsent 确保原子性操作
     * @param waybillId 运单ID
     * @return true表示设置成功，false表示标志已存在
     */
    fun trySetPendingFlagAtomic(waybillId: Long): Boolean {
        val key = "$WAYBILL_PENDING_PREFIX$waybillId"

        return try {
            val result = redisTemplate.opsForValue().setIfAbsent(key, "1", PENDING_EXPIRE_SECONDS, TimeUnit.SECONDS)
            if (result == true) {
                log.info { "原子性设置运单预报标志成功 | 运单ID: $waybillId | 过期时间: ${PENDING_EXPIRE_SECONDS}秒" }
                true
            } else {
                log.warn { "运单预报标志已存在，防止重复操作 | 运单ID: $waybillId" }
                false
            }
        } catch (e: Exception) {
            log.error(e) { "原子性设置运单预报标志异常 | 运单ID: $waybillId" }
            // 异常时返回false，避免重复操作
            false
        }
    }

}
