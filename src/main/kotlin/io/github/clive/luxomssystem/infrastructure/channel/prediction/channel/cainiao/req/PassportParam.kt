package io.github.clive.luxomssystem.infrastructure.channel.prediction.channel.cainiao.req

import com.fasterxml.jackson.annotation.JsonProperty

data class PassportParam(
    @JsonProperty("birthDate")
    var birthDate: String?,
    @JsonProperty("firstName")
    var firstName: String?,
    @JsonProperty("lastName")
    var lastName: String?,
    @JsonProperty("middleName")
    var middleName: String?,
    @JsonProperty("passportIssuDate")
    var passportIssuDate: String?,
    @JsonProperty("passportNo")
    var passportNo: String?,
    @JsonProperty("passportOrganName")
    var passportOrganName: String?,
)
