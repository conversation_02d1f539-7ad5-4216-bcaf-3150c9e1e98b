package io.github.clive.luxomssystem.infrastructure.config.properties

import org.springframework.boot.context.properties.ConfigurationProperties
import org.springframework.boot.context.properties.EnableConfigurationProperties
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration

@ConfigurationProperties(prefix = "lark")
data class LarkConfigProperties(
    val webhooks: Map<WebHookType, String>,
)

enum class WebHookType {
    CAN_PUSH_SUPPLIER,
    SUPPLIER_ORDER_ON_HOLD,
    MAIN_ORDER_SOME_SUB_ORDER_FAILED,
    WAY_BILL_FINANCE_CALC_COMBO_CROSS_ERROR,
}

class LarkWebhooks(
    private val webhooks: Map<WebHookType, String>,
) {
    fun getByType(type: WebHookType): String? = webhooks[type]
}

@Configuration
@EnableConfigurationProperties(LarkConfigProperties::class)
class LarkConfig(
    private val larkConfigProperties: LarkConfigProperties,
) {
    @Bean
    fun larkWebhooks(): LarkWebhooks = LarkWebhooks(larkConfigProperties.webhooks)
}
