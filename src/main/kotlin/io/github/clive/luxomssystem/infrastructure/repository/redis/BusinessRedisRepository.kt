package io.github.clive.luxomssystem.infrastructure.repository.redis

import io.github.clive.luxomssystem.common.exception.CognitionWebException
import io.github.clive.luxomssystem.common.exception.OmsBaseErrorCode
import io.github.oshai.kotlinlogging.KotlinLogging
import org.springframework.data.redis.core.RedisTemplate
import org.springframework.stereotype.Repository

@Repository
class BusinessRedisRepository(
    private val redisTemplate: RedisTemplate<String, String>,
) {
    companion object {
        private val log = KotlinLogging.logger {}

        private const val TEST_LIMIT = 100
    }

    fun countTask(
        bizId: Long,
        count: Long,
    ): Long {
        val key = "TASK:COUNT:$bizId"
        val currentCount = redisTemplate.opsForValue().get(key)?.toLongOrNull() ?: 0L

        if (currentCount + count > TEST_LIMIT) {
            throw CognitionWebException(OmsBaseErrorCode.TEST_UNDER_LIMIT)
        }

        redisTemplate.opsForValue().increment(key, count)?.let { newCount ->
            log.info { "增加任务数量成功。bizId: $bizId, 新数量: $newCount" }
            return newCount
        }

        return currentCount + count
    }
}
