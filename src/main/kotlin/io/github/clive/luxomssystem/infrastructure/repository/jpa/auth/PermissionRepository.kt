package io.github.clive.luxomssystem.infrastructure.repository.jpa.auth

import io.github.clive.luxomssystem.domain.doanload.auth.Permission
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.stereotype.Repository

@Repository
interface PermissionRepository : JpaRepository<Permission, Long> {
    fun findByParentId(parentId: Long): List<Permission>

    fun findByNameContaining(name: String): List<Permission>

    fun findByResource(resource: String): List<Permission>
}
