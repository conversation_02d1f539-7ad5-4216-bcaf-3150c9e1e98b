package io.github.clive.luxomssystem.infrastructure.config.auth

import io.github.clive.luxomssystem.common.enums.RoleLimit
import io.github.clive.luxomssystem.common.utils.Jwt
import io.github.clive.luxomssystem.domain.doanload.auth.User
import io.github.clive.luxomssystem.infrastructure.config.auth.UserContext.Companion.fromUser
import io.github.clive.luxomssystem.infrastructure.repository.jpa.UserRepository
import io.github.clive.luxomssystem.infrastructure.repository.redis.UserRedisRepository
import io.github.oshai.kotlinlogging.KotlinLogging
import jakarta.servlet.http.HttpServletRequest
import jakarta.servlet.http.HttpServletResponse
import org.springframework.data.repository.findByIdOrNull
import org.springframework.stereotype.Component
import org.springframework.web.servlet.HandlerInterceptor
import org.springframework.web.servlet.ModelAndView
import java.util.*

data class UserContext(
    val id: Long,
    val token: String,
    val name: String = "",
    val account: String = "",
    val password: String = "",
    val createdBy: Long? = null,
    val updatedBy: Long? = null,
    val roleId: Long = 1,
    val roleName: String = "",
    var bizId: Long = -1,
    var supplierId: Long? = null,
    var roleLimit: RoleLimit = RoleLimit.SELF,
) {
    companion object {
        fun fromUser(
            user: User,
            newToken: String,
        ): UserContext =
            UserContext(
                id = user.id,
                token = newToken,
                name = user.name,
                account = user.account,
                password = user.password,
                roleId = user.roleId!!,
                roleName = user.roleName,
                bizId = user.bizId,
                supplierId = user.supplierId,
                roleLimit = user.roleLimit,
            )
    }
}

@Component
class ApiSignatureInterceptor(
    private val userRepository: UserRepository,
    private val userRedisRepository: UserRedisRepository,
) : HandlerInterceptor {
    @Throws(Exception::class)
    override fun preHandle(
        httpServletRequest: HttpServletRequest,
        httpServletResponse: HttpServletResponse,
        `object`: Any,
    ): Boolean {
        if (httpServletRequest.method == "OPTIONS") {
            return true
        }
        val authHeader = httpServletRequest.getHeader("Authorization")
        if (authHeader == null || !authHeader.startsWith("Bearer ")) {
            httpServletResponse.sendError(401, "Unauthorized")
            return false
        }

        val token = authHeader.substring(7)
        val userId =
            try {
                Jwt.extractUserFromJwt(token)["userId"]!!.asLong()
            } catch (e: Exception) {
                log.error(e) { "JWT error:" }
                httpServletResponse.sendError(401, "Unauthorized")
                return false
            }
        val check = userRedisRepository.findUserIdByToken(userId, token)

        if (check == null) {
            httpServletResponse.sendError(401, "Unauthorized")
            return false
        }
        val user = userRepository.findByIdOrNull(userId)
        if (user == null) {
            httpServletResponse.sendError(401, "Unauthorized")
            return false
        }

        UserContextHolder.set(fromUser(user, token))
        return true
    }

    override fun postHandle(
        request: HttpServletRequest,
        response: HttpServletResponse,
        o: Any,
        modelAndView: ModelAndView?,
    ) {
        UserContextHolder.remove()
    }

    companion object {
        private val log = KotlinLogging.logger { }
    }
}

fun HttpServletRequest.clientIp(): String? {
    val xfHeader = getHeader("X-Forwarded-For") ?: return remoteAddr
    return xfHeader.split(",".toRegex()).dropLastWhile { it.isEmpty() }.toTypedArray()[0] // for proxies
}
