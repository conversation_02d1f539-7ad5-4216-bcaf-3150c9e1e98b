package io.github.clive.luxomssystem.infrastructure.tracking.remote

import io.github.clive.luxomssystem.common.enums.WaybillChannel
import io.github.clive.luxomssystem.domain.tracking.model.TrackingInfo
import io.github.clive.luxomssystem.domain.tracking.model.TrackingStatus
import io.github.clive.luxomssystem.infrastructure.channel.prediction.channel.YanWenPredictionRemoteService
import io.github.clive.luxomssystem.infrastructure.channel.prediction.channel.YunTuPredictionRemoteService
import io.github.clive.luxomssystem.infrastructure.channel.prediction.channel.yanwen.YanWenTrackingResponse
import io.github.clive.luxomssystem.infrastructure.channel.prediction.channel.yuntu.YuntuTrackingResponse
import io.github.oshai.kotlinlogging.KotlinLogging
import org.springframework.stereotype.Service

/**
 * 轨迹远程服务
 * 负责调用不同渠道的轨迹查询API
 */
@Service
class TrackingRemoteService(
    private val yanWenPredictionRemoteService: YanWenPredictionRemoteService,
    private val yunTuPredictionRemoteService: YunTuPredictionRemoteService
) {

    private val log = KotlinLogging.logger {}

    /**
     * 获取燕文轨迹信息
     */
    fun getYanWenTracking(waybillNo: String, channel: WaybillChannel): YanWenTrackingResponse? {
        val yanWenChannel = getYanWenChannelName(channel)
        if (yanWenChannel == null) {
            log.warn { "无效的燕文渠道 | 运单号: $waybillNo | 渠道: ${channel.displayName}" }
            return null
        }
        return yanWenPredictionRemoteService.tracking(waybillNo, yanWenChannel)
    }

    /**
     * 获取云途轨迹信息
     */
    fun getYuntuTracking(waybillNo: String): YuntuTrackingResponse? {
        return yunTuPredictionRemoteService.tracking(waybillNo)
    }

    /**
     * 根据渠道获取轨迹信息
     */
    fun getTrackingByChannel(waybillNo: String, channel: WaybillChannel): Any? {
        return when {
            isYanWenChannel(channel) -> getYanWenTracking(waybillNo, channel)
            isYuntuChannel(channel) -> getYuntuTracking(waybillNo)
            else -> {
                log.warn { "不支持的轨迹查询渠道 | 运单号: $waybillNo | 渠道: ${channel.displayName}" }
                null
            }
        }
    }

    /**
     * 判断是否为燕文渠道
     */
    private fun isYanWenChannel(channel: WaybillChannel): Boolean {
        return channel in listOf(
            WaybillChannel.YW_HZ,
            WaybillChannel.YW_QZ,
            WaybillChannel.YW_GZ,
            WaybillChannel.YW_YW
        )
    }

    /**
     * 获取燕文渠道对应的channel名称
     */
    private fun getYanWenChannelName(channel: WaybillChannel): String? {
        return when (channel) {
            WaybillChannel.YW_HZ -> "YW_HZ"
            WaybillChannel.YW_QZ -> "YW_QZ"
            WaybillChannel.YW_GZ -> "YW_GZ"
            WaybillChannel.YW_YW -> "YW_YW"
            else -> null
        }
    }

    /**
     * 判断是否为云途渠道
     */
    private fun isYuntuChannel(channel: WaybillChannel): Boolean {
        return channel == WaybillChannel.YUNTU
    }

    /**
     * 获取渠道对应的转换器名称
     */
    fun getConverterChannelName(channel: WaybillChannel): String? {
        return when {
            isYanWenChannel(channel) -> "YANWEN"
            isYuntuChannel(channel) -> "YUNTU"
            else -> null
        }
    }

    /**
     * 创建初始轨迹记录（当运单状态变为OUTBOUND时调用）
     */
    fun createInitialTrackingRecord(
        orderNo: String?,
        waybillId: Long?,
        waybillNo: String,
        channel: WaybillChannel,
        mainOrderId: Long
    ): TrackingInfo? {
        val converterChannel = getConverterChannelName(channel)
        if (converterChannel == null) {
            log.debug { "渠道不支持轨迹查询，跳过创建轨迹记录 | 运单号: $waybillNo | 渠道: ${channel.displayName}" }
            return null
        }

        log.info { "创建初始轨迹记录 | 订单号: $orderNo | 运单ID: $waybillId | 运单号: $waybillNo | 渠道: ${channel.displayName}" }

        return TrackingInfo(
            orderNos = orderNo?.split(",") ?: emptyList(),
            waybillId = waybillId ?: 0L,
            mainOrderId = mainOrderId,
            waybillNo = waybillNo,
            channel = converterChannel,
            currentStatus = TrackingStatus.NOT_FOUND,
            trackingEvents = emptyList(),
            lastEventTime = null // 初始状态没有事件时间
        )
    }

    /**
     * 检查渠道是否支持轨迹查询
     */
    fun isTrackingSupportedChannel(channel: WaybillChannel): Boolean {
        return getConverterChannelName(channel) != null
    }
}
