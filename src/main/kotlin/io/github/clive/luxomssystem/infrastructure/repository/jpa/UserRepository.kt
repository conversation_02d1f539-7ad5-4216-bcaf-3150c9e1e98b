package io.github.clive.luxomssystem.infrastructure.repository.jpa

import io.github.clive.luxomssystem.common.enums.BaseStatus
import io.github.clive.luxomssystem.domain.doanload.auth.User
import io.github.clive.luxomssystem.infrastructure.config.AuthRewriter
import org.springframework.data.domain.Page
import org.springframework.data.domain.Pageable
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.jpa.repository.Query
import org.springframework.data.repository.query.Param
import org.springframework.stereotype.Repository

@Repository
interface UserRepository : JpaRepository<User, Long> {
    fun findByAccountAndPassword(
        account: String,
        password: String,
    ): User?

    @Query(
        """
    select u from users u where 
    1=1 and
    (:name is null or u.name ilike %:name%) and
    (:#{#statuses.isEmpty()} = true or u.status IN :statuses)
    order by u.createdAt DESC
""",
        queryRewriter = AuthRewriter::class,
    )
    fun findByNameLikeAndStatusInOrderByCreatedAtDesc(
        @Param("name") name: String?,
        @Param("statuses") statuses: List<BaseStatus>,
        pageable: Pageable,
    ): Page<User>

    @Query("select (count(u) > 0) from users u where u.name = ?1")
    fun existsByName(name: String): Boolean

    @Query("select (count(u) > 0) from users u where u.account = ?1")
    fun existsByAccount(account: String): Boolean

    @Query("select u from users u where u.bizId = ?1 and (?2 is null or u.createdBy = ?2)")
    fun findNameByBizIdAndUserId(
        bizId: Long,
        createdBy: Long?,
    ): List<User>

    @Query("select u from users u where u.bizId = ?1")
    fun findNameByBizId(bizId: Long): List<User>

    @Query(
        """
    select u from users u where
    1=1 and
     u.bizId = :bizId order by u.createdAt desc
    """,
        queryRewriter = AuthRewriter::class,
    )
    fun findByBizId(
        bizId: Long,
        pageable: Pageable,
    ): Page<User>
}
