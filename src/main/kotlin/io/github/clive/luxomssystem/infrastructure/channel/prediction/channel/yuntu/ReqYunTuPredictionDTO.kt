package io.github.clive.luxomssystem.infrastructure.channel.prediction.channel.yuntu

import com.fasterxml.jackson.annotation.JsonProperty
import java.math.BigDecimal

// Data classes and enums remain the same as in the previous version

// Data classes for request and response objects
data class ReqYunTuPredictionDTO(
    val customerOrderNumber: String,
    val shippingMethodCode: String,
    val packageCount: Int,
    val weight: BigDecimal,
//    val orderExtra: List<ReqYunTuOrderExtraDTO>,
    val receiver: ReqYunTuReceiveDTO,
    val parcels: List<ReqYunTuProductDTO>,
    val taxNumber: String? = null,
    val iossCode: String? = null,
)

data class ReqYunTuOrderExtraDTO(
    val ExtraCode: String = "V1",
    val ExtraName: String = "云途预缴1",
)

data class ReqYunTuReceiveDTO(
    val firstName: String,
    val city: String,
    val zip: String,
    val street: String,
    val countryCode: String,
    val phone: String,
    val state: String,
)

data class ReqYunTuProductDTO(
    val quantity: Int,
    val eName: String,
    val cName: String,
    val hsCode: String?,
    val invoicePart: String?,
    val currencyCode: String,
    val unitPrice: BigDecimal,
    val unitWeight: BigDecimal,
    val remark: String,
)

data class RstYunTuWayBillDTO(
    @JsonProperty("Code") val code: String,
    @JsonProperty("Message") val message: String,
    @JsonProperty("Item") val item: List<RstYunTuWayBillItemDTO> = emptyList(),
    @JsonProperty("RequestId") val requestId: String,
    @JsonProperty("TimeStamp") val timeStamp: String,
)

data class RstYunTuWayBillItemDTO(
    @JsonProperty("CustomerOrderNumber") val customerOrderNumber: String?,
    @JsonProperty("Success") val success: Int,
    @JsonProperty("TrackType") val trackType: String?,
    @JsonProperty("Remark") val remark: String?,
    @JsonProperty("RequireSenderAddress") val requireSenderAddress: Int?,
    @JsonProperty("AgentNumber") val agentNumber: String?,
    @JsonProperty("WayBillNumber") val wayBillNumber: String?,
    @JsonProperty("TrackingNumber") val trackingNumber: String?,
    @JsonProperty("ShipperBoxs") val shipperBoxs: String?,
    @JsonProperty("BarCodes") val barCodes: String?,
    @JsonProperty("AddrType") val addrType: Int,
)

data class RstYunTuPrintDTO(
    @JsonProperty("Item") val item: List<RstYunTuPrintItemDTO>,
    @JsonProperty("Code") val code: String?,
    @JsonProperty("Message") val message: String?,
    @JsonProperty("RequestId") val requestId: String?,
    @JsonProperty("TimeStamp") val timeStamp: String?,
)

data class RstYunTuPrintItemDTO(
    @JsonProperty("Url") val url: String,
    @JsonProperty("LabelType") val labelType: String?,
    @JsonProperty("LabelString") val labelString: String?,
    @JsonProperty("OrderInfos") val orderInfos: List<RstYunTuPrintOrderInfoDTO> = emptyList(),
)

data class RstYunTuPrintOrderInfoDTO(
    @JsonProperty("CustomerOrderNumber") val customerOrderNumber: String?,
    @JsonProperty("Error") val error: String?,
    @JsonProperty("Code") val code: Int?,
)

data class ReqYunTuDelPredictionDTO(
    val orderType: String,
    val orderNumber: String,
)

data class RstYunTuDelPredictionDTO(
    @JsonProperty("Code")
    val code: String,
    @JsonProperty("Message")
    val message: String,
)
