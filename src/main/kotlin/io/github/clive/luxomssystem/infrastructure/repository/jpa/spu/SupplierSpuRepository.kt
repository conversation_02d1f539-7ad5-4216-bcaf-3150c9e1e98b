package io.github.clive.luxomssystem.infrastructure.repository.jpa.spu

import io.github.clive.luxomssystem.common.enums.BaseStatus
import io.github.clive.luxomssystem.domain.supplierSpu.model.SupplierSpu
import io.github.clive.luxomssystem.facade.supplierSpu.response.SupplierSpuWithSpuDTO
import org.springframework.data.domain.Page
import org.springframework.data.domain.Pageable
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.jpa.repository.Query
import org.springframework.stereotype.Repository

@Repository
interface SupplierSpuRepository : JpaRepository<SupplierSpu, Long> {
    fun deleteBySupplierId(supplierId: Long)

    @Query(
        """
        select s.systemSpuId
        from supplier_spu s
        where s.supplierId = :supplierId
    """,
    )
    fun findBySupplierId(supplierId: Long): List<Long>

    fun findBySystemSpuId(systemSpuId: Long): List<SupplierSpu>

    @Query(
        """
        select s
        from supplier_spu s
        where (:supplierSpuCode is null or s.supplierSpuCode ilike %:supplierSpuCode%)
        and (:systemSpuCode is null or s.systemSpuCode ilike %:systemSpuCode%)
        and (:supplierId is null or s.supplierId = :supplierId)
        and (:createdBy is null or s.createdBy = :createdBy)
        and  (:status is null or s.status = :status)
        order by s.createdAt desc
    """,
    )
    fun pageQuery(
        supplierId: Long?,
        supplierSpuCode: String?,
        systemSpuCode: String?,
        pageable: Pageable,
        createdBy: Long?,
        status: BaseStatus?,
    ): Page<SupplierSpu>

    @Query(
        """
    select new io.github.clive.luxomssystem.facade.supplierSpu.response.SupplierSpuWithSpuDTO(
        s.id,
        s.bizId,
        s.supplierId,
        s.supplierSpuCode,
        s.systemSpuId,
        s.systemSpuCode,
        p.title ,
        p.name ,
        p.cnName ,
        p.category ,
        p.productImage ,
        p.packageQuantity ,
        p.description ,
        p.showImages,
        p.sizes,
        p.colors,
        s.status ,
        s.createdAt,
        s.updatedAt,
        s.createdByName,
        s.updatedByName
    )
    from supplier_spu s
    left join spus p on s.systemSpuId = p.id
    where (:supplierId is null or s.supplierId = :supplierId)
      and (:supplierSpuCode is null or s.supplierSpuCode ilike %:supplierSpuCode%)
      and (:systemSpuCode is null or s.systemSpuCode ilike %:systemSpuCode%)
      and (:status is null or s.status = :status)
        and (:title is null or p.title ilike %:title%)
    order by s.id desc
""",
    )
    fun findSupplierSpuWithSpuDetails(
        supplierId: Long?,
        supplierSpuCode: String?,
        systemSpuCode: String?,
        status: BaseStatus?,
        title: String?,
        pageable: Pageable,
    ): Page<SupplierSpuWithSpuDTO>


    fun findBySystemSpuIdAndStatus(systemSpuId: Long, status: BaseStatus): List<SupplierSpu>
}
