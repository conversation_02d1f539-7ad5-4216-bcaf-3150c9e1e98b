package io.github.clive.luxomssystem.infrastructure.repository.redis

import org.springframework.data.redis.core.RedisTemplate
import org.springframework.data.redis.core.script.DefaultRedisScript
import org.springframework.stereotype.Component
import java.io.Serial
import java.time.Duration
import java.time.Instant
import java.util.concurrent.TimeUnit

@Component
class RedisRateLimiter(
    private val redisTemplate: RedisTemplate<String, String>,
) {
    /**
     * 固定窗口限流 - 简单实现
     * 固定时间重置计数器
     *
     * @param id 限流标识ID
     * @param maxRequests 每分钟最大请求次数
     * @param firstWindowTime 第一个窗口的时长
     * @return true表示允许请求，false表示被限流
     */
    fun allowRequest(
        id: String,
        maxRequests: Int,
        firstWindowTime: Duration = Duration.ofMinutes(1),
    ): Boolean {
        val key = RATE_LIMIT_KEY_PREFIX + id

        return try {
            val currentCount = redisTemplate.opsForValue().increment(key) ?: 0L

            if (currentCount == 1L) {
                // 第一次访问，设置过期时间为60秒
                redisTemplate.expire(key, firstWindowTime.toMillis(), TimeUnit.MILLISECONDS)
            }

            currentCount <= maxRequests
        } catch (e: Exception) {
            // 发生异常时，默认允许请求
            true
        }
    }

    /**
     * 滑动窗口限流 - 精确实现
     * 使用有序集合记录请求时间戳，实现真正的滑动窗口
     *
     * @param id 限流标识ID
     * @param maxRequests 每分钟最大请求次数
     * @param windowSize 窗口大小，默认1分钟
     * @return true表示允许请求，false表示被限流
     */
    fun allowRequestSlidingWindow(
        id: String,
        maxRequests: Int,
        windowSize: Duration = Duration.ofMinutes(1),
    ): Boolean {
        val key = SLIDING_WINDOW_KEY_PREFIX + id
        val now = Instant.now().toEpochMilli()
        val windowStart = now - windowSize.toMillis()

        // language=lua
        val luaScript =
            """
            local key = KEYS[1]
            local now = tonumber(ARGV[1])
            local window = tonumber(ARGV[2])
            local limit = tonumber(ARGV[3])
            
            -- 删除窗口外的记录
            redis.call('ZREMRANGEBYSCORE', key, 0, window)
            
            -- 获取当前窗口内的请求数量
            local current = redis.call('ZCARD', key)
            
            -- 如果未超过限制，添加当前请求
            if current < limit then
                redis.call('ZADD', key, now, now)
                redis.call('EXPIRE', key, 61)
                return 1
            else
                return 0
            end
            """.trimIndent()

        val script =
            DefaultRedisScript<Long>().apply {
                setScriptText(luaScript)
                resultType = Long::class.java
            }

        return try {
            val result = redisTemplate.execute(script, listOf(key), now, windowStart, maxRequests)
            result == 1L
        } catch (e: Exception) {
            // 发生异常时，默认允许请求
            true
        }
    }

    /**
     * 获取当前限流状态信息
     *
     * @param id 限流标识ID
     * @return 限流状态信息
     */
    fun getRateLimitInfo(id: String): RateLimitInfo {
        val fixedWindowKey = RATE_LIMIT_KEY_PREFIX + id
        val slidingWindowKey = SLIDING_WINDOW_KEY_PREFIX + id

        return try {
            // 固定窗口信息
            val fixedCount =
                redisTemplate
                    .opsForValue()
                    .get(fixedWindowKey)
                    ?.toString()
                    ?.toIntOrNull() ?: 0
            val fixedWindowTtl = redisTemplate.getExpire(fixedWindowKey, TimeUnit.SECONDS)

            // 滑动窗口信息
            val now = Instant.now().toEpochMilli()
            val windowStart = now - 60000
            val slidingCount = redisTemplate.opsForZSet().count(slidingWindowKey, windowStart.toDouble(), now.toDouble())?.toInt() ?: 0

            RateLimitInfo(
                id = id,
                fixedWindowCount = fixedCount,
                fixedWindowTtl = fixedWindowTtl,
                slidingWindowCount = slidingCount,
            )
        } catch (e: Exception) {
            // 异常时返回默认值
            RateLimitInfo(id = id)
        }
    }

    /**
     * 清除指定ID的限流记录
     *
     * @param id 限流标识ID
     */
    fun clearRateLimit(id: String) {
        try {
            redisTemplate.delete(RATE_LIMIT_KEY_PREFIX + id)
            redisTemplate.delete(SLIDING_WINDOW_KEY_PREFIX + id)
        } catch (e: Exception) {
            // 忽略清除异常
        }
    }

    /**
     * 批量检查多个ID的限流状态
     */
    fun batchCheck(requests: List<RateLimitRequest>): List<RateLimitResult> =
        requests.map { request ->
            val allowed =
                if (request.useSlidingWindow) {
                    allowRequestSlidingWindow(request.id, request.maxRequests)
                } else {
                    allowRequest(request.id, request.maxRequests)
                }
            RateLimitResult(request.id, allowed)
        }

    /**
     * 获取剩余请求次数
     */
    fun getRemainingRequests(
        id: String,
        maxRequests: Int,
        useSlidingWindow: Boolean = false,
    ): Int =
        try {
            val info = getRateLimitInfo(id)
            val currentCount = if (useSlidingWindow) info.slidingWindowCount else info.fixedWindowCount
            (maxRequests - currentCount).coerceAtLeast(0)
        } catch (e: Exception) {
            maxRequests
        }

    companion object {
        private const val RATE_LIMIT_KEY_PREFIX = "rate_limit:"
        private const val SLIDING_WINDOW_KEY_PREFIX = "sliding_window:"
    }
}

/**
 * 限流状态信息
 */
data class RateLimitInfo(
    val id: String,
    val fixedWindowCount: Int = 0,
    val fixedWindowTtl: Long = -1,
    val slidingWindowCount: Int = 0,
)

/**
 * 限流请求
 */
data class RateLimitRequest(
    val id: String,
    val maxRequests: Int,
    val useSlidingWindow: Boolean = false,
)

/**
 * 限流结果
 */
data class RateLimitResult(
    val id: String,
    val allowed: Boolean,
) {
    companion object {
        @Serial
        private const val serialVersionUID: Long = 1L
    }
}
