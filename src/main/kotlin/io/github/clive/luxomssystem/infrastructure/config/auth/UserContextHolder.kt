package io.github.clive.luxomssystem.infrastructure.config.auth

import io.netty.util.concurrent.FastThreadLocal

/**
 * 登录用户上下文
 */
object UserContextHolder {
    /**
     * FastThreadLocal快，稳，没内存泄露问题
     */
    private val userHolder = FastThreadLocal<UserContext>()

    var user: UserContext?
        /**
         * 从FastThreadLocal中获取用户
         */
        get() = userHolder.get()
        /**
         * 设置用户到 FastThreadLocal
         */
        set(loginUser) {
            userHolder.set(loginUser)
        }

    /**
     * 清除FastThreadLocal，反之内存泄露
     */
    fun remove() {
        userHolder.remove()
    }

    fun set(user: UserContext) {
        userHolder.set(user)
    }
}

inline fun <T> UserContextHolder.withContext(
    ctx: UserContext? = null,
    block: () -> T,
): T {
    val old = user
    val use = ctx ?: old
    if (use == null) {
        remove()
    } else {
        set(use)
    }
    try {
        return block()
    } finally {
        if (old == null) {
            remove()
        } else {
            set(old)
        }
    }
}
