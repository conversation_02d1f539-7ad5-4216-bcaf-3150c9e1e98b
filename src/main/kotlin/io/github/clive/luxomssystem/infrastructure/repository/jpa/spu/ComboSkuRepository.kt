package io.github.clive.luxomssystem.infrastructure.repository.jpa.spu

import io.github.clive.luxomssystem.domain.sku.model.ComboSku
import org.springframework.data.jpa.repository.JpaRepository

interface ComboSkuRepository : JpaRepository<ComboSku, Long> {
    fun findAllByComboSpuId(comboSpuId: Long): List<ComboSku>

    fun deleteAllBySpuIdAndSkuCodeNotIn(
        spuId: Long,
        skuCode: Collection<String>,
    )

    fun findAllBySpuIdAndSkuCodeNotIn(
        spuId: Long,
        skuCodes: Collection<String>,
    ): List<ComboSku>

    fun deleteAllBySpuId(spuId: Long)

    fun findAllByComboSpuIdAndSkuCodeIn(
        comboSpuId: Long,
        skuCodes: Collection<String>,
    ): List<ComboSku>

    fun deleteAllByComboSpuId(comboSpuId: Long)
}
