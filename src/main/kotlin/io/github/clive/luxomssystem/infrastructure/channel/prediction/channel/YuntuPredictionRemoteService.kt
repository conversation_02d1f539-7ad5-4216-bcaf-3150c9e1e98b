package io.github.clive.luxomssystem.infrastructure.channel.prediction.channel

import io.github.clive.luxomssystem.common.utils.EUCountryEnum
import io.github.clive.luxomssystem.common.utils.JSON
import io.github.clive.luxomssystem.domain.waybill.event.WaybillCompletedEvent
import io.github.clive.luxomssystem.domain.waybill.model.WayBillStatus
import io.github.clive.luxomssystem.domain.waybill.model.Waybill
import io.github.clive.luxomssystem.infrastructure.channel.cos.CosInnerRemoteService
import io.github.clive.luxomssystem.infrastructure.channel.prediction.OrderPredictionRemoteService
import io.github.clive.luxomssystem.infrastructure.channel.prediction.channel.request.WaybillRequest
import io.github.clive.luxomssystem.infrastructure.channel.prediction.channel.yuntu.*
import io.github.clive.luxomssystem.infrastructure.repository.jpa.SubOrderRepository
import io.github.clive.luxomssystem.infrastructure.repository.jpa.WaybillRepository
import io.github.oshai.kotlinlogging.KotlinLogging
import okhttp3.Headers.Companion.toHeaders
import okhttp3.OkHttpClient
import okhttp3.Request
import okhttp3.RequestBody.Companion.toRequestBody
import org.springframework.beans.factory.annotation.Value
import org.springframework.context.ApplicationEventPublisher
import org.springframework.stereotype.Service
import java.math.BigDecimal
import java.util.*

@Service
class YunTuPredictionRemoteService(
    private val eventBus: ApplicationEventPublisher,
    override val ossClient: CosInnerRemoteService,
    override val okHttpClient: OkHttpClient,
    override val subOrderRepository: SubOrderRepository,
    @Value("\${yun-tu.account}") private val account: String,
    @Value("\${yun-tu.apiSecret}") private val apiSecret: String,
    @Value("\${yun-tu.api.waybill}") private val waybillUrl: String,
    @Value("\${yun-tu.api.print}") private val printUrl: String,
    @Value("\${yun-tu.api.delPrediction}") private val delPredictionUrl: String,
    private val waybillRepository: WaybillRepository,
) : OrderPredictionRemoteService {
    override fun createPredication(waybill: Waybill) {
        try {
            val orderList = loadSubOrder(waybill)
            if (orderList.isEmpty()) {
                log.error { "YunTu 创建预报失败 | 订单号: ${waybill.orderNo} | 原因: 未找到子订单" }
                waybill.failed("No suborder found")
                return
            }
            val waybillRequests = orderList.flatMap { WaybillRequest.of(it, waybill) }
            val weight = waybillRequests.sumOf { it.weight }
            val reqYunTuPredictionDTO =
                ReqYunTuPredictionDTO(
                    taxNumber = waybill.taxNumber,
                    iossCode = if (EUCountryEnum.needIoss(waybill.recipient.country))
                        waybill.iossNumber
                    else
                        null,
                    customerOrderNumber = waybill.orderNo,
                    shippingMethodCode = waybill.shipping.shipMethod ?: "",
                    packageCount = 1,
                    weight = if (weight == BigDecimal.ZERO) BigDecimal("0.01") else weight,
//                    orderExtra =
//                        if (EUCountryEnum.allKey.contains(waybill.recipient.country)) {
//                            listOf(ReqYunTuOrderExtraDTO())
//                        } else {
//                            emptyList()
//                        },
                    receiver =
                        ReqYunTuReceiveDTO(
                            firstName = waybill.recipient.receiverName ?: "",
                            city = waybill.recipient.city ?: "",
                            zip = waybill.recipient.postcode ?: "",
                            street = waybill.recipient.address(),
                            countryCode = waybill.recipient.country ?: "",
                            phone = waybill.recipient.phone ?: "",
                            state = waybill.recipient.state ?: "",
                        ),
                    parcels =
                        buildList {
                            for (req in waybillRequests) {
                                add(
                                    ReqYunTuProductDTO(
                                        quantity = req.qty,
                                        eName = req.name,
                                        cName = req.cnName,
                                        currencyCode = "USD",
                                        unitPrice = req.price(waybillRequests.sumOf { it.qty }, req.country),
                                        unitWeight = req.weight,
                                        remark = "${req.orderNo}-${req.skuCode()}",
                                        invoicePart = req.material,
                                        hsCode = req.hsCode,
                                    ),
                                )
                            }
                        },
                )
            // TODO         // 欧盟国家走附加服务

            val headers =
                mapOf(
                    "Authorization" to "Basic ${
                        Base64.getEncoder().encodeToString("$account&$apiSecret".toByteArray())
                    }",
                )

            val body = JSON.toJSONString(listOf(reqYunTuPredictionDTO))
            log.info { "YunTu 创建运单预报 | 订单号: ${waybill.orderNo} | 请求参数: $body" }
            val response =
                okHttpClient
                    .newCall(
                        Request
                            .Builder()
                            .url(waybillUrl)
                            .post(body.toRequestBody(jsonMedia))
                            .headers(headers.toHeaders())
                            .build(),
                    ).execute()

            val responseBody = response.body?.string() ?: throw Exception("Empty response body")

            log.info { "YunTu 创建运单预报 | 订单号: ${waybill.orderNo} | 响应参数: $responseBody" }
            val rstYunTuWayBillDTO = JSON.parseObject<RstYunTuWayBillDTO>(responseBody)

            if (rstYunTuWayBillDTO.code == "0000") {
                waybill.status = WayBillStatus.COMPLETED
                waybill.waybillNo = rstYunTuWayBillDTO.item[0].wayBillNumber
                waybill.trackingNumber = rstYunTuWayBillDTO.item[0].trackingNumber
                // Use the shared getPrintUrl method
                waybill.shipping.waybillLabelUrl = getPrintUrl(waybill)
                waybill.clearErrorMsg()
                eventBus.publishEvent(WaybillCompletedEvent(waybill.id, waybill.subOrderId, waybill.orderNos))
            } else {
                log.error { "YunTu 创建预报失败 | 订单号: ${waybill.orderNo} | 原因: ${rstYunTuWayBillDTO.message}" }
                waybill.run { failed(rstYunTuWayBillDTO.item[0].remark ?: rstYunTuWayBillDTO.message) }
            }
            waybillRepository.saveAndFlush(waybill)
        } catch (e: Exception) {
            log.error(e) { "YunTu 创建预报异常 | 订单号: ${waybill.orderNo} | 原因: ${e.message}" }
            waybill.failed(e.message ?: "Unknown error")
            waybillRepository.saveAndFlush(waybill)
        }
    }

    override fun deletePredication(waybill: Waybill): Boolean {
        val reqYunTuDelPredictionDTO =
            ReqYunTuDelPredictionDTO(
                orderType = "2",
                orderNumber = waybill.orderNo,
            )

        val headers =
            mapOf(
                "Authorization" to "Basic ${Base64.getEncoder().encodeToString("$account&$apiSecret".toByteArray())}",
            )

        try {
            val response =
                okHttpClient
                    .newCall(
                        Request
                            .Builder()
                            .url(delPredictionUrl)
                            .post(JSON.toJSONString(reqYunTuDelPredictionDTO).toRequestBody(jsonMedia))
                            .headers(headers.toHeaders())
                            .build(),
                    ).execute()

            val responseBody = response.body?.string() ?: throw Exception("Empty response body")
            val rstYunTuDelPredictionDTO = JSON.parseObject<RstYunTuDelPredictionDTO>(responseBody)
            log.info { "YunTu 删除运单预报 | 订单号: ${waybill.orderNo} | 响应参数: $responseBody" }
            return rstYunTuDelPredictionDTO.code == "SUCCESS"
        } catch (e: Exception) {
            log.error(e) { "YunTu 删除运单预报异常 | 订单号: ${waybill.orderNo} | 错误信息: ${e.message}" }
            return false
        }
    }

    // Shared method to get print URL
    override fun getPrintUrl(waybill: Waybill): String {
        val headers =
            mapOf(
                "Authorization" to "Basic ${Base64.getEncoder().encodeToString("$account&$apiSecret".toByteArray())}",
            )

        val printResponse =
            okHttpClient
                .newCall(
                    Request
                        .Builder()
                        .url(printUrl)
                        .post(JSON.toJSONString(listOf(waybill.waybillNo)).toRequestBody(jsonMedia))
                        .headers(headers.toHeaders())
                        .build(),
                ).execute()

        val printResponseBody = printResponse.body?.string() ?: throw Exception("Empty print response body")
        log.info { "YunTu 获取打印URL | 订单号: ${waybill.orderNo} | 响应参数: $printResponseBody" }
        val rstYunTuPrintDTO = JSON.parseObject<RstYunTuPrintDTO>(printResponseBody)

        if (rstYunTuPrintDTO.item[0].orderInfos[0].code == 100) {
            val printUrl = rstYunTuPrintDTO.item[0].url
            val uploadWaybill = uploadWaybill(waybill, printUrl)
            log.info { "YunTu 获取打印URL成功 | 订单号: ${waybill.orderNo} | 运单号: ${waybill.waybillNo} | 打印URL: $printUrl" }
            return uploadWaybill
        } else {
            log.error { "YunTu 获取打印URL失败 | 订单号: ${waybill.orderNo} | 运单号: ${waybill.waybillNo} | 错误信息: ${rstYunTuPrintDTO.message}" }
            throw Exception("Failed to get print URL")
        }
    }

    /**
     * 获取云途轨迹信息
     */
    fun tracking(waybillNo: String): YuntuTrackingResponse? {
        return try {
            log.info { "开始获取云途轨迹信息 | 运单号: $waybillNo" }

            val headers =
                mapOf(
                    "Authorization" to "Basic ${
                        Base64.getEncoder().encodeToString("$account&$apiSecret".toByteArray())
                    }",
                )

            val body = mapOf("OrderNumber" to waybillNo)
            val requestBody = JSON.toJSONString(body).toRequestBody(jsonMedia)

            val request = Request.Builder()
                .url("http://oms.api.yunexpress.com/api/Tracking/GetTrackInfo")
                .post(requestBody)
                .headers(headers.toHeaders())
                .build()

            val response = okHttpClient.newCall(request).execute().use { response ->
                response.body?.string() ?: throw Exception("Empty response body")
            }

            log.info { "云途轨迹API响应 | 运单号: $waybillNo | 响应: $response" }

            val trackingResponse = JSON.parseObject<YuntuTrackingResponse>(response)

            if (trackingResponse.code == "0000") {
                log.info { "云途轨迹信息获取成功 | 运单号: $waybillNo" }
                trackingResponse
            } else {
                log.warn { "云途轨迹信息获取失败 | 运单号: $waybillNo | 错误码: ${trackingResponse.code} | 错误信息: ${trackingResponse.message}" }
                null
            }
        } catch (e: Exception) {
            log.error(e) { "云途轨迹信息获取异常 | 运单号: $waybillNo" }
            null
        }
    }

//    @EventListener(ApplicationReadyEvent::class)
//    fun initOss() {
//        tracking("YT2509821272328511")
//    }

    companion object {
        private val log = KotlinLogging.logger {}
    }
}
