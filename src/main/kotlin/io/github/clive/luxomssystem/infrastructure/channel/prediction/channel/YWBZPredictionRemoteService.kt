package io.github.clive.luxomssystem.infrastructure.channel.prediction.channel

import com.fasterxml.jackson.annotation.JsonProperty
import io.github.clive.luxomssystem.common.utils.JSON
import io.github.clive.luxomssystem.domain.waybill.event.WaybillCompletedEvent
import io.github.clive.luxomssystem.domain.waybill.model.WayBillStatus
import io.github.clive.luxomssystem.domain.waybill.model.Waybill
import io.github.clive.luxomssystem.infrastructure.channel.cos.CosInnerRemoteService
import io.github.clive.luxomssystem.infrastructure.channel.prediction.OrderPredictionRemoteService
import io.github.clive.luxomssystem.infrastructure.channel.prediction.channel.request.WaybillRequest
import io.github.clive.luxomssystem.infrastructure.repository.jpa.SubOrderRepository
import io.github.clive.luxomssystem.infrastructure.repository.jpa.WaybillRepository
import io.github.oshai.kotlinlogging.KotlinLogging
import okhttp3.FormBody
import okhttp3.OkHttpClient
import okhttp3.Request
import org.springframework.beans.factory.annotation.Value
import org.springframework.context.ApplicationEventPublisher
import org.springframework.stereotype.Service

@Service
class YWBZPredictionRemoteService(
    private val eventBus: ApplicationEventPublisher,
    override val ossClient: CosInnerRemoteService,
    override val okHttpClient: OkHttpClient,
    override val subOrderRepository: SubOrderRepository,
    @Value("\${ywbz.account}") private val account: String,
    @Value("\${ywbz.apiSecret}") private val apiSecret: String,
    @Value("\${ywbz.api.waybill}") private val waybillUrl: String,
    @Value("\${ywbz.api.print}") private val printUrl: String,
    @Value("\${ywbz.api.delPrediction}") private val delPredictionUrl: String,
    private val waybillRepository: WaybillRepository,
) : OrderPredictionRemoteService {
    private val log = KotlinLogging.logger {}

    override fun createPredication(waybill: Waybill) {
        val orderList = loadSubOrder(waybill)
        if (orderList.isEmpty()) {
            log.error { "YWBZ 创建预报失败 | 订单号: ${waybill.orderNo} | 原因: 未找到子订单" }
            waybill.failed("No suborder found")
            return
        }
        try {
            val waybillRequests = orderList.flatMap { WaybillRequest.of(it, waybill) }

            val reqYWBZOrderPredictionDTO =
                ReqYWBZOrderPredictionDTO(
                    reference_no = waybill.orderNo,
                    shipping_method = waybill.shipping.shipMethod ?: "",
                    order_weight = waybillRequests.sumOf { it.weight }.toString(),
                    shipper =
                        ReqYWBZShipperDTO(
                            shipper_name = "LLLLL",
                            shipper_countrycode = "CN",
                            shipper_street = "88888888888",
                            shipper_telephone = "88888888888",
                        ),
                    consignee =
                        ReqYWBZConsigneeDTO(
                            consignee_name = waybill.recipient.receiverName ?: "",
                            consignee_countrycode = waybill.recipient.country ?: "",
                            consignee_province = waybill.recipient.state ?: "",
                            consignee_city = waybill.recipient.city ?: "",
                            consignee_district = "",
                            consignee_street = waybill.recipient.fullAddress(),
                            consignee_postcode = waybill.recipient.postcode ?: "",
                            consignee_mobile = waybill.recipient.phone ?: "",
                            consignee_tariff = waybill.taxNumber,
                        ),
                    invoice =
                        waybillRequests.map { order ->
                            ReqYWBZInvoiceDTO(
                                invoice_enname = order.name,
                                invoice_cnname = order.cnName,
                                invoice_quantity = order.qty.toString(),
                                invoice_unitcharge = order.price(waybillRequests.size, order.country).toString(),
                                invoice_note = order.skuCode(),
                            )
                        },
                    order_info = orderList.joinToString(";") { it.orderNo + "-" + it.product.skuCode() },
                )

            log.info { "YWBZ 创建运单预报 | 订单号: ${waybill.orderNo} | 请求参数: ${JSON.toJSONString(reqYWBZOrderPredictionDTO)}" }

            val response =
                okHttpClient
                    .newCall(
                        Request
                            .Builder()
                            .url(waybillUrl)
                            .post(
                                FormBody
                                    .Builder()
                                    .add("appToken", account)
                                    .add("appKey", apiSecret)
                                    .add("serviceMethod", "createorder")
                                    .add("paramsJson", JSON.toJSONString(reqYWBZOrderPredictionDTO))
                                    .build(),
                            ).build(),
                    ).execute()

            val responseBody = response.body?.string() ?: throw Exception("Empty response body")
            log.info { "YWBZ 创建运单预报 | 订单号: ${waybill.orderNo} | 响应参数: $responseBody" }

            val restYWBZWayBillDTO = JSON.parseObject<RestYWBZWayBillDTO>(responseBody)

            if (restYWBZWayBillDTO.success == 1) {
                waybill.status = WayBillStatus.COMPLETED
                val data = restYWBZWayBillDTO.data!!
                waybill.waybillNo = data.channel_hawbcode.ifBlank { data.shipping_method_no }
                waybill.shipping.waybillLabelUrl = getPrintUrl(waybill)
                waybill.clearErrorMsg()
                eventBus.publishEvent(WaybillCompletedEvent(waybill.id, waybill.subOrderId, waybill.orderNos))
            } else {
                log.error { "YWBZ 创建预报失败 | 订单号: ${waybill.orderNo} | 错误信息: ${restYWBZWayBillDTO.enmessage}" }
                waybill.failed(restYWBZWayBillDTO.enmessage ?: "")
            }
            waybillRepository.saveAndFlush(waybill)
        } catch (e: Exception) {
            log.error(e) { "YWBZ 创建预报失败 | 订单号: ${waybill.orderNo} | 错误信息: ${e.message}" }
            waybill.failed(e.message ?: "")
            waybillRepository.saveAndFlush(waybill)
        }
    }

    override fun deletePredication(waybill: Waybill): Boolean {
        val reqYWBZDelPredictionDTO = ReqYWBZDelPredictionDTO(waybill.orderNo)

        val response =
            okHttpClient
                .newCall(
                    Request
                        .Builder()
                        .url(delPredictionUrl)
                        .post(
                            FormBody
                                .Builder()
                                .add("appToken", account)
                                .add("appKey", apiSecret)
                                .add("serviceMethod", "removeorder")
                                .add("paramsJson", JSON.toJSONString(reqYWBZDelPredictionDTO))
                                .build(),
                        ).build(),
                ).execute()

        val responseBody = response.body?.string() ?: throw Exception("Empty response body")
        log.info { "YWBZ 删除运单预报 | 订单号: ${waybill.orderNo} | 响应参数: $responseBody" }

        val restYWBZWayBillDTO = JSON.parseObject<RestYWBZWayBillDTO>(responseBody)
        return restYWBZWayBillDTO.success == 1
    }

    override fun getPrintUrl(waybill: Waybill): String {
        val reqYWBZPrintBaseDTO =
            ReqYWBZPrintBaseDTO(
                configInfo =
                    ReqYWBZPrintUrlDTO(
                        lable_file_type = "2",
                        lable_paper_type = "1",
                        lable_content_type = "1",
                        additional_info =
                            ReqYWBZPrintAdditionalInfoDTO(
                                lable_print_invoiceinfo = "N",
                                lable_print_buyerid = "N",
                                lable_print_datetime = "Y",
                                customsdeclaration_print_actualweight = "N",
                            ),
                    ),
                listorder = listOf(ReqYWBZPrintListorderDTO(reference_no = waybill.orderNo)),
            )

        log.info { "YWBZ 获取打印URL | 订单号: ${waybill.orderNo} | 请求参数: ${JSON.toJSONString(reqYWBZPrintBaseDTO)}" }

        val response =
            okHttpClient
                .newCall(
                    Request
                        .Builder()
                        .url(printUrl)
                        .post(
                            FormBody
                                .Builder()
                                .add("appToken", account)
                                .add("appKey", apiSecret)
                                .add("serviceMethod", "getnewlabel")
                                .add("paramsJson", JSON.toJSONString(reqYWBZPrintBaseDTO))
                                .build(),
                        ).build(),
                ).execute()

        val responseBody = response.body?.string() ?: throw Exception("Empty response body")
        log.info { "YWBZ 获取打印URL | 订单号: ${waybill.orderNo} | 响应参数: $responseBody" }

        val restYWBZPrintDTO = JSON.parseObject<RestYWBZPrintDTO>(responseBody)
        val printUrl = restYWBZPrintDTO.data[0].lable_file

        return uploadWaybill(waybill, printUrl)
    }
}

// Data classes
data class ReqYWBZOrderPredictionDTO(
    val reference_no: String,
    val shipping_method: String,
    val order_weight: String,
    val shipper: ReqYWBZShipperDTO,
    val consignee: ReqYWBZConsigneeDTO,
    val invoice: List<ReqYWBZInvoiceDTO>,
    val order_info: String,
)

data class ReqYWBZShipperDTO(
    val shipper_name: String,
    val shipper_countrycode: String,
    val shipper_street: String,
    val shipper_telephone: String,
)

data class ReqYWBZConsigneeDTO(
    val consignee_name: String,
    val consignee_countrycode: String,
    val consignee_province: String,
    val consignee_city: String,
    val consignee_district: String,
    val consignee_street: String,
    val consignee_postcode: String,
    val consignee_mobile: String,
    val consignee_tariff: String?,
)

data class ReqYWBZInvoiceDTO(
    val invoice_enname: String,
    val invoice_cnname: String,
    val invoice_quantity: String,
    val invoice_unitcharge: String,
    val invoice_note: String,
)

data class ReqYWBZDelPredictionDTO(
    val reference_no: String,
)

data class ReqYWBZPrintBaseDTO(
    val configInfo: ReqYWBZPrintUrlDTO,
    val listorder: List<ReqYWBZPrintListorderDTO>,
)

data class ReqYWBZPrintUrlDTO(
    val lable_file_type: String,
    val lable_paper_type: String,
    val lable_content_type: String,
    val additional_info: ReqYWBZPrintAdditionalInfoDTO,
)

data class ReqYWBZPrintAdditionalInfoDTO(
    val lable_print_invoiceinfo: String,
    val lable_print_buyerid: String,
    val lable_print_datetime: String,
    val customsdeclaration_print_actualweight: String,
)

data class ReqYWBZPrintListorderDTO(
    val reference_no: String,
)

// Updated data classes
data class RestYWBZWayBillDTO(
    @JsonProperty("success") val success: Int,
    @JsonProperty("enmessage") val enmessage: String?,
    @JsonProperty("cnmessage") val cnmessage: String?,
    @JsonProperty("cnpopupmsg") val cnpopupmsg: String?,
    @JsonProperty("enpopupmsg") val enpopupmsg: String?,
    @JsonProperty("order_id") val order_id: Int?,
    @JsonProperty("data") val data: RestYWBZWayBillDataDTO?,
)

data class RestYWBZWayBillDataDTO(
    @JsonProperty("shipping_method_no") val shipping_method_no: String,
    @JsonProperty("channel_hawbcode") val channel_hawbcode: String,
)

data class RestYWBZPrintDTO(
    @JsonProperty("data") val data: List<RestYWBZPrintDataDTO>,
)

data class RestYWBZPrintDataDTO(
    @JsonProperty("lable_file") val lable_file: String,
)
