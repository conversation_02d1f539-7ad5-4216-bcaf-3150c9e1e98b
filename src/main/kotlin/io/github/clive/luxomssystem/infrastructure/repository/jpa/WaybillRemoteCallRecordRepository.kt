package io.github.clive.luxomssystem.infrastructure.repository.jpa

import io.github.clive.luxomssystem.domain.waybill.model.WaybillRemoteCallRecord
import org.springframework.data.domain.Sort
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.stereotype.Repository

@Repository
interface WaybillRemoteCallRecordRepository : JpaRepository<WaybillRemoteCallRecord, Long> {
    fun findByWaybillId(
        waybillId: Long,
        sort: Sort,
    ): List<WaybillRemoteCallRecord>
}
