package io.github.clive.luxomssystem.infrastructure.repository.jpa.spu

import io.github.clive.luxomssystem.common.enums.BaseStatus
import io.github.clive.luxomssystem.domain.customerSpu.model.ComboCustomerSpu
import org.springframework.data.domain.Page
import org.springframework.data.domain.Pageable
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.jpa.repository.Modifying
import org.springframework.data.jpa.repository.Query
import org.springframework.stereotype.Repository
import org.springframework.transaction.annotation.Transactional

@Repository
interface ComboCustomerSpuRepository : JpaRepository<ComboCustomerSpu, Long> {
    @Query(
        """
        select c.*
        from combo_customer_spu c
        where c.biz_id = :bizId and
        (:systemComboSpuId is null or c.system_combo_spu_id = :systemComboSpuId) and
        (:customerId is null or c.customer_id = :customerId) and
        (:#{#systemSpuCodes.isEmpty()} = true or exists (select 1 from unnest(c.system_spu_codes) as sc where sc in :systemSpuCodes))
        order by c.created_at desc
    """,
        nativeQuery = true,
    )
    fun pageQueryByCustomerId(
        bizId: Long,
        systemComboSpuId: Long?,
        systemSpuCodes: List<String>,
        customerId: Long?,
        page: Pageable,
    ): Page<ComboCustomerSpu>

    @Query(
        """
        select c.*
        from combo_customer_spu c
        where c.biz_id = :bizId and
        (:systemComboSpuId is null or c.system_combo_spu_id = :systemComboSpuId) and
        (:customerId is null or c.customer_id = :customerId) and
        (:#{#systemSpuCodes.isEmpty()} = true or exists (select 1 from unnest(c.system_spu_codes) as sc where sc in :systemSpuCodes)) and
        (:#{#customerIds.isEmpty()} = true or c.customer_id IN :customerIds)
        order by c.created_at desc
    """,
        nativeQuery = true,
    )
    fun pageQueryByCustomerIdForSelf(
        bizId: Long,
        systemComboSpuId: Long?,
        systemSpuCodes: List<String>,
        customerId: Long?,
        page: Pageable,
        customerIds: List<Long>,
    ): Page<ComboCustomerSpu>

    @Query(
        """
            select c.systemComboSpuId
            from combo_customer_spu c
            where c.customerId = :customerId
        """,
    )
    fun findAllComboSpuIdByCustomerId(customerId: Long): List<Long>

    @Transactional
    @Modifying
    @Query(
        """
            update combo_customer_spu set status = :wantedStatus
            where id = :id
    """,
    )
    fun switchStatus(
        id: Long,
        wantedStatus: BaseStatus,
    )

    @Modifying
    @Query(
        """
            delete from combo_customer_spu spu
            where :systemSpuId = ANY(spu.system_spu_ids)
        """,
        nativeQuery = true,
    )
    fun deleteAllBySystemSpuIdsContains(systemSpuId: Long)

    @Query(
        """
            select spu.* from combo_customer_spu spu
            where :systemSpuId = ANY(spu.system_spu_ids)
        """,
        nativeQuery = true,
    )
    fun findAllBySystemSpuIdsContains(systemSpuId: Long): List<ComboCustomerSpu>

    fun deleteBySystemComboSpuId(systemComboSpuId: Long)

    @Query(
        """
        select combo_customer_spu.* from combo_customer_spu
         where combo_customer_spu.system_spu_codes <@ CAST(:spuCodes AS text[])
         and status = :status
         and customer_id = :customerId
    """,
        nativeQuery = true,
    )
    fun findAllIfComboContainsAnySpu(
        spuCodes: Array<String>,
        status: String,
        customerId: Long,
    ): List<ComboCustomerSpu>
}
