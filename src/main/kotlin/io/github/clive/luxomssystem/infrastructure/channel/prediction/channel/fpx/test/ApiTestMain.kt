// package io.github.clive.cognitionbank.infrastructure.remote.prediction.channel.fpx.test
//
// import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
// import io.github.clive.cognitionbank.infrastructure.remote.prediction.channel.fpx.*
// import io.github.clive.luxomssystem.infrastructure.channel.prediction.channel.fpx.req.FpxWayBillCancelRequest
//
// class ApiTestMain {
//    companion object {
//        @JvmStatic
//        fun main(args: Array<String>) {
//            // Create necessary objects
//            val objectMapper = jacksonObjectMapper()
//                .setSerializationInclusion(com.fasterxml.jackson.annotation.JsonInclude.Include.NON_NULL)
//            val config = FpxApiConfig().apply {
//                appKey = "511aa2be-20f0-4a6e-9ef9-66014eb533e3"
//                appSecret = "41a4d58f-09d1-4649-a9c7-6ca5dba94c0f"
//                apiUrl = "https://open.4px.com/router/api/service"
//            }
//            val apiService = FpxApiService(config, objectMapper)
//
//            // Create a sample request
//            val request = FpxApiRequest(
//                refNo = "TEST${System.currentTimeMillis()}",
//                businessType = "BDS",
//                dutyType = "U",
//                cargoType = "5",
//                isInsure = "N",
//                currencyFreight = "USD",
//                logisticsServiceInfo = LogisticsServiceInfo(
//                    logisticsProductCode = "S5061",
//                    customsService = "N",
//                    signatureService = "N"
//                ),
//                parcelList = listOf(
//                    Parcel(
//                        weight = 1000.00.toBigDecimal(),
//                        length = 10,
//                        width = 10,
//                        height = 10,
//                        parcelValue = 10.0,
//                        currency = "USD",
//                        declareProductInfo = listOf(
//                            DeclareProductInfo(
//                                declareProductNameEn = "T-shirt",
//                                declareProductNameCn = "T恤",
//                                declareProductCodeQty = 1,
//                                declareUnitPriceImport = 10.0,
//                                declareUnitPriceExport = 10.0,
//                                currencyExport = "USD",
//                                currencyImport = "USD",
//                                brandExport = "none",
//                                brandImport = "none",
//                                packageRemarks = "skutest",
//                            )
//                        )
//                    )
//                ),
//                sender = Address(
//                    firstName = "John",
//                    lastName = "Doe",
//                    company = "Test Company",
//                    phone = "1234567890",
//                    email = "<EMAIL>",
//                    country = "CN",
//                    state = "CA",
//                    city = "San Francisco",
//                    street = "123 Test St",
//                    postCode = "94105"
//                ),
//                recipientInfo = Address(
//                    firstName = "Jane",
//                    lastName = "Doe",
//                    phone = "0987654321",
//                    email = "<EMAIL>",
//                    country = "US",
//                    state = "CA",
//                    city = "San Francisco",
//                    street = "123 Test St",
//                    postCode = "94105"
//                ),
//                returnInfo = ReturnInfo(
//                    isReturnOnDomestic = "U",
//                    isReturnOnOversea = "U"
//                ),
//                deliverTypeInfo = DeliverTypeInfo(
//                    deliverType = "1",
//                ),
//            )
//
//            var cancelRequest= FpxWayBillCancelRequest("TEST1726541003580","cancel")
//
//            try {
//                // Call the API service
// //                val response = apiService.createOrder(request)
//                val response = apiService.cancelWaybill(cancelRequest)
// //                val response = apiService.getWaybill(FpxWayBillRequest("TEST1726541003580"))
//                // Print the response
//                println("API Response:")
//                println("Result: ${response?.result}")
//                println("Message: ${response?.msg}")
//                println("Data: ${response?.data}")
//                println("Errors: ${response?.errors}")
//            } catch (e: Exception) {
//                println("An error occurred: ${e.message}")
//                e.printStackTrace()
//            }
//        }
//    }
//
//
//
// }
