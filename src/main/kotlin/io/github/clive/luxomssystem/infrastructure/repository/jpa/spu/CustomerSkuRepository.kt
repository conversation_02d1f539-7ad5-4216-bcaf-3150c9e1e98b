package io.github.clive.luxomssystem.infrastructure.repository.jpa.spu

import io.github.clive.luxomssystem.domain.customerSku.model.CustomerSku
import org.springframework.data.domain.Sort
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.stereotype.Repository

/** Repository interface for CustomerSku entities. */
@Repository
interface CustomerSkuRepository : JpaRepository<CustomerSku, Long> {
    // Add custom query methods here if needed

    fun deleteByCustomerSpuId(customerSpuId: Long)

    fun findByCustomerSpuId(customerSpuId: Long): List<CustomerSku>

    fun findByCustomerSpuId(
        customerSpuId: Long,
        sort: Sort,
    ): List<CustomerSku>

    fun findBySystemSkuIdInAndCustomerId(
        systemSkuIds: List<Long>,
        customerId: Long,
    ): List<CustomerSku>

    fun findBySystemSkuIdAndCustomerId(
        systemSkuId: Long,
        customerId: Long,
    ): CustomerSku?

    fun findAllBySystemSkuId(systemSkuId: Long): List<CustomerSku> // Added new method

    fun findAllBySystemSkuIdInAndCustomerId(
        systemSkuIds: List<Long>,
        customerId: Long,
    ): List<CustomerSku>
}
