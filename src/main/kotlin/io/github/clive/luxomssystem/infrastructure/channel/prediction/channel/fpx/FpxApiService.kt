package io.github.clive.luxomssystem.infrastructure.channel.prediction.channel.fpx

import com.fasterxml.jackson.databind.ObjectMapper
import io.github.clive.luxomssystem.infrastructure.channel.prediction.channel.fpx.req.FpxWayBillCancelRequest
import io.github.clive.luxomssystem.infrastructure.channel.prediction.channel.fpx.req.FpxWayBillRequest
import okhttp3.OkHttpClient
import okhttp3.Request
import okhttp3.RequestBody.Companion.toRequestBody
import org.springframework.stereotype.Service
import java.security.MessageDigest
import java.time.Instant

@Service
class FpxApiService(
    private val config: FpxApiConfig,
    private val objectMapper: ObjectMapper,
) {
    private val client = OkHttpClient()

    fun createOrder(request: FpxApiRequest): FpxApiResponse {
        val timestamp = Instant.now().toEpochMilli()
        val method = "ds.xms.order.create"
        val format = "json"
        val version = "1.1.0"

        val body = objectMapper.writeValueAsString(request)
        val signatureString = buildSignatureString(method, timestamp, body, version)
        val sign = calculateMD5(signatureString)

        val url = buildUrl(method, timestamp, format, sign, version)

        val httpRequest =
            Request
                .Builder()
                .url(url)
                .header("Content-Type", "application/json")
                .post(body.toRequestBody())
                .build()

        val response = client.newCall(httpRequest).execute()
        val responseBody = response.body?.string() ?: throw RuntimeException("Empty response body")

        return objectMapper.readValue(responseBody, FpxApiResponse::class.java)
    }

    fun getWaybill(req: FpxWayBillRequest): FpxApiResponse? {
        val timestamp = Instant.now().toEpochMilli()
        val method = "ds.xms.label.get"
        val format = "json"
        val version = "1.1.0"

        val body = objectMapper.writeValueAsString(req)
        val signatureString = buildSignatureString(method, timestamp, body, version)
        val sign = calculateMD5(signatureString)

        val url = buildUrl(method, timestamp, format, sign, version)

        val httpRequest =
            Request
                .Builder()
                .url(url)
                .header("Content-Type", "application/json")
                .post(body.toRequestBody())
                .build()

        val response = client.newCall(httpRequest).execute()
        val responseBody = response.body?.string() ?: throw RuntimeException("Empty response body")

        return objectMapper.readValue(responseBody, FpxApiResponse::class.java)
    }

    fun cancelWaybill(request: FpxWayBillCancelRequest): FpxApiResponse {
        val timestamp = Instant.now().toEpochMilli()
        val method = "ds.xms.order.cancel"
        val format = "json"
        val version = "1.0.0"

        val body = objectMapper.writeValueAsString(request)
        val signatureString = buildSignatureString(method, timestamp, body, version)
        val sign = calculateMD5(signatureString)

        val url = buildUrl(method, timestamp, format, sign, version)

        val httpRequest =
            Request
                .Builder()
                .url(url)
                .header("Content-Type", "application/json")
                .post(body.toRequestBody())
                .build()

        val response = client.newCall(httpRequest).execute()
        val responseBody = response.body?.string() ?: throw RuntimeException("Empty response body")

        return objectMapper.readValue(responseBody, FpxApiResponse::class.java)
    }

    private fun buildSignatureString(
        method: String,
        timestamp: Long,
        body: String,
        version: String,
    ): String =
        "app_key${config.appKey}" +
            "formatjson" +
            "method$method" +
            "timestamp$timestamp" +
            "v$version" +
            body +
            config.appSecret

    private fun calculateMD5(input: String): String {
        val md = MessageDigest.getInstance("MD5")
        val digest = md.digest(input.toByteArray())
        return digest.joinToString("") { "%02x".format(it) }
    }

    private fun buildUrl(
        method: String,
        timestamp: Long,
        format: String,
        sign: String,
        version: String,
    ): String =
        "${config.apiUrl}?" +
            "method=$method&" +
            "app_key=${config.appKey}&" +
            "v=$version&" +
            "timestamp=$timestamp&" +
            "format=$format&" +
            "sign=$sign"
}
