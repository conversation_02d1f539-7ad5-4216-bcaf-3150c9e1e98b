package io.github.clive.luxomssystem.infrastructure.channel.prediction.channel.sdh

import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import com.fasterxml.jackson.annotation.JsonInclude
import com.fasterxml.jackson.annotation.JsonProperty

/**
 * API 响应实体
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
data class SdhResponse(
    /**
     * 请求处理结果，例如："true"
     */
    @field:JsonProperty("ack")
    val ack: String? = null,
    /**
     * 子单列表
     */
    @field:JsonProperty("childList")
    val childList: List<ChildListItem>? = null,
    /**
     * 延迟类型
     */
    @field:JsonProperty("delay_type")
    val delayType: String? = null,
    /**
     * 是否更换了单号
     */
    @field:JsonProperty("is_changenumbers")
    val isChangeNumbers: String? = null,
    /**
     * 是否延迟获取单号，Y代表需要延迟获取单号
     */
    @field:JsonProperty("is_delay")
    val isDelay: String? = null,
    /**
     * 是否偏远，N为不偏远，Y为偏远，A为fedex偏远A级，B为fedex偏远B级, C为fedex偏远C级
     */
    @field:JsonProperty("is_remote")
    val isRemote: String? = null,
    /**
     * 是否住宅，Y表示住宅地址
     */
    @field:JsonProperty("is_residential")
    val isResidential: String? = null,
    /**
     * 如果未获取到转单号，则该列存放失败原因(该内容请urldecode后查看)
     */
    @field:JsonProperty("message")
    val message: String? = null,
    /**
     * 订单ID，例如：xxxxxxx，id需要保存，用于打印标签
     */
    @field:JsonProperty("order_id")
    val orderId: String? = null,
    /**
     * 内部处理号/客户自定义单号
     */
    @field:JsonProperty("order_privatecode")
    val orderPrivateCode: String? = null,
    /**
     * 转单号，特殊情况下使用，默认不用理会
     */
    @field:JsonProperty("order_transfercode")
    val orderTransferCode: String? = null,
    /**
     * 末端派送商名称
     */
    @field:JsonProperty("post_customername")
    val postCustomerName: String? = null,
    /**
     * 跟踪号API类型，值为3时需要调用获取跟踪号接口来更新单号
     */
    @field:JsonProperty("product_tracknoapitype")
    val productTrackNoApiType: String? = null,
    /**
     * 参考号
     */
    @field:JsonProperty("reference_number")
    val referenceNumber: String? = null,
    /**
     * 退货地址信息
     */
    @field:JsonProperty("return_address")
    val returnAddress: String? = null,
    /**
     * 跟踪号
     */
    @field:JsonProperty("tracking_number")
    val trackingNumber: String? = null,
) {
    /**
     * 子单项信息
     */
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @JsonIgnoreProperties(ignoreUnknown = true)
    data class ChildListItem(
        /**
         * 子单号
         */
        @field:JsonProperty("child_number")
        val childNumber: String? = null,
    )
}
