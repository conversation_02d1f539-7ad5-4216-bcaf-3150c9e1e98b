package io.github.clive.luxomssystem.infrastructure.channel.prediction.channel.changlian

import com.fasterxml.jackson.annotation.JsonProperty

/**
 * 常联创建订单响应
 */
data class ChangLianCreateOrderResponse(
    @JsonProperty("code")
    val code: String,

    @JsonProperty("msg")
    val msg: String,

    @JsonProperty("data")
    val data: ChangLianOrderData?,
)

data class ChangLianOrderData(
    @JsonProperty("customerOrderNo")
    val customerOrderNo: String,

    @JsonProperty("waybillNo")
    val waybillNo: String,

    @JsonProperty("deliveryNo")
    val deliveryNo: String?,

    @JsonProperty("labelUrl")
    val labelUrl: String?,
)
