package io.github.clive.luxomssystem.infrastructure.repository.jpa.spu

import io.github.clive.luxomssystem.common.enums.BaseStatus
import io.github.clive.luxomssystem.domain.suppliersku.model.SupplierSku
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.stereotype.Repository

@Repository
interface SupplierSkuRepository : JpaRepository<SupplierSku, Long> {
    fun deleteBySupplierSpuId(supplierSpuId: Long)

    fun deleteBySupplierId(supplierId: Long)

    fun findBySystemSkuId(systemSkuId: Long): SupplierSku? // Changed to nullable to avoid issues if not found

    fun findAllBySystemSkuId(systemSkuId: Long): List<SupplierSku> // Added new method

    fun findAllBySystemSkuIdIn(systemSkuIds: List<Long>): List<SupplierSku> // Added new method

    fun findBySupplierSpuId(supplierSpuId: Long): List<SupplierSku>

    fun deleteBySystemSkuId(systemSkuId: Long)

    fun findBySystemSkuCodeAndBizIdAndStatus(
        systemSkuCode: String,
        bizId: Long,
        status: BaseStatus,
    ): List<SupplierSku>

    fun existsBySystemSkuIdAndSupplierId(
        id: Long,
        supplierId: Long,
    ): Boolean
}
