package io.github.clive.luxomssystem.infrastructure.channel.prediction.channel.cainiao.req

import com.fasterxml.jackson.annotation.JsonProperty

data class ReturnerParam(
    @JsonProperty("addressId")
    var addressId: String?,
    @JsonProperty("city")
    var city: String?,
    @JsonProperty("countryCode")
    var countryCode: String?,
    @JsonProperty("detailAddress")
    var detailAddress: String?,
    @JsonProperty("district")
    var district: String?,
    @JsonProperty("email")
    var email: String?,
    @JsonProperty("mobilePhone")
    var mobilePhone: String?,
    @JsonProperty("name")
    var name: String?,
    @JsonProperty("state")
    var state: String?,
    @JsonProperty("street")
    var street: String?,
    @JsonProperty("telephone")
    var telephone: String?,
    @JsonProperty("zipCode")
    var zipCode: String?,
)
