package io.github.clive.luxomssystem.infrastructure.config.auth

import io.github.clive.luxomssystem.infrastructure.repository.redis.CustomerRedisRepository
import jakarta.servlet.http.HttpServletRequest
import jakarta.servlet.http.HttpServletResponse
import org.springframework.stereotype.Component
import org.springframework.web.servlet.HandlerInterceptor
import org.springframework.web.servlet.ModelAndView
import java.util.*

data class CustomerContext(
    val id: Long,
)

@Component
class OpenapiSignatureInterceptor(
    private val customerRedisRepository: CustomerRedisRepository,
) : HandlerInterceptor {
    @Throws(Exception::class)
    override fun preHandle(
        httpServletRequest: HttpServletRequest,
        httpServletResponse: HttpServletResponse,
        `object`: Any,
    ): Boolean {
        if (httpServletRequest.method == "OPTIONS") {
            return true
        }

        val authHeader = httpServletRequest.getHeader("Authorization")
        if (authHeader == null || !authHeader.startsWith("Bearer ")) {
            httpServletResponse.sendError(401, "Unauthorized")
            return false
        }

        val token = authHeader.substring(7)
        val customerId = customerRedisRepository.findIdByOpenApiKey(token)
        if (customerId == null) {
            httpServletResponse.sendError(401, "Unauthorized")
            return false
        }

        CustomerContextHolder.set(CustomerContext(customerId))
        return true
    }

    override fun postHandle(
        request: HttpServletRequest,
        response: HttpServletResponse,
        o: Any,
        modelAndView: ModelAndView?,
    ) {
        CustomerContextHolder.remove()
    }
}
