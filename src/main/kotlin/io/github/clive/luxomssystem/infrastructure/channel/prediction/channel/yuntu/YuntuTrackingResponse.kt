package io.github.clive.luxomssystem.infrastructure.channel.prediction.channel.yuntu

import com.fasterxml.jackson.annotation.JsonProperty
import org.springframework.format.annotation.DateTimeFormat
import java.time.LocalDateTime

data class YuntuTrackingResponse(
    @JsonProperty("Item")
    val item: CountryTrackingItem,
    @JsonProperty("Message")
    val message: String? = null,
    @JsonProperty("Code")
    val code: String,
    @JsonProperty("RequestId")
    val requestId: String,
    @JsonProperty("TimeStamp")
    val timeStamp: String,
)

data class CountryTrackingItem(
    @JsonProperty("CountryCode")
    val countryCode: String, // 目的地国家简码
    @JsonProperty("WaybillNumber")
    val waybillNumber: String?, // 运单号
    @JsonProperty("TrackingNumber")
    val trackingNumber: String, // 跟踪号
    @JsonProperty("ProviderName")
    val providerName: String, // 末端服务商名称
    @JsonProperty("ProviderTelephone")
    val providerTelephone: String, // 末端服务商联系方式
    @JsonProperty("ProviderSite")
    val providerSite: String, // 末端服务商官网
    @JsonProperty("POD")
    val pod: String, // 单个POD链接(妥投证明)信息(URL地址)
    @JsonProperty("PODs")
    val pods: List<String>? = null, // 多个POD链接(妥投证明)信息(URL地址)
    @JsonProperty("PackageState")
    val packageState: Int, // 包裹状态
    @JsonProperty("TrackingStatus")
    val trackingStatus: Int, // 跟踪状态
    @JsonProperty("IntervalDays")
    val intervalDays: Double, // 包裹签收天数
    @JsonProperty("OrderTrackingDetails")
    val orderTrackingDetails: List<OrderTrackingDetail>,
)

data class OrderTrackingDetail(
    @JsonProperty("ProcessDate")
    @DateTimeFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss")
    val processDate: LocalDateTime, // 包裹请求日期
    @JsonProperty("ProcessContent")
    val processContent: String, // 包裹请求内容
    @JsonProperty("ProcessLocation")
    val processLocation: String, // 包裹请求地址
    @JsonProperty("TrackNodeCode")
    val trackNodeCode: String, // 轨迹节点代码
    @JsonProperty("TrackCodeDescription")
    val trackCodeDescription: String, // 轨迹节点英文描述
    @JsonProperty("ProcessCountry")
    val processCountry: String, // 轨迹发生地所在国家
    @JsonProperty("ProcessProvince")
    val processProvince: String, // 轨迹发生地所在省州
    @JsonProperty("ProcessCity")
    val processCity: String, // 轨迹发生地所在城市
    @JsonProperty("AbnormalReasons")
    val abnormalReasons: List<AbnormalReason>? = null,
)

data class AbnormalReason(
    @JsonProperty("AbnormalReasonCode")
    val abnormalReasonCode: String, // 轨迹异常原因代码
    @JsonProperty("AbnormalReason")
    val abnormalReason: String,
)

enum class YuntuTrackingStatus(
    val code: Int,
) {
    /**
     * 0-未找到
     */
    NOT_FOUND(0),

    /**
     * 10-电子预报信息已接收
     */
    PRE_ADVICE_RECEIVED(10),

    /**
     * 20-运输途中
     */
    IN_TRANSIT(20),

    /**
     * 30-到达待取
     */
    ARRIVED_FOR_PICKUP(30),

    /**
     * 40-投递失败
     */
    DELIVERY_FAILED(40),

    /**
     * 50-已签收
     */
    DELIVERED(50),

    /**
     * 60-异常
     */
    EXCEPTION(60),

    /**
     * 80-未知
     */
    UNKNOWN(80),

    /**
     * 90-已退回
     */
    RETURNED(90),

    /**
     * 100-已取消
     */
    CANCELLED(100),
    ;

    companion object {
        fun fromCode(code: Int): YuntuTrackingStatus = YuntuTrackingStatus.entries.first { it.code == code }
    }
}
