package io.github.clive.luxomssystem.infrastructure.repository.jpa.spu

import io.github.clive.luxomssystem.domain.spu.model.Spu
import io.github.clive.luxomssystem.facade.spu.response.SpuSelectResponse
import org.springframework.data.domain.Page
import org.springframework.data.domain.Pageable
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.jpa.repository.Query
import org.springframework.stereotype.Repository

@Repository
interface SPURepository : JpaRepository<Spu, Long> {
    fun findBySpuCode(spuCode: String): Spu?

    fun findAllBySpuCodeIn(spuCodes: Collection<String>): List<Spu>

    @Query(
        """
        select spu from spus spu
        where
        spu.spuCode ilike %:search%
        or spu.title ilike %:search%
        or spu.name ilike %:search%
        or spu.cnName ilike %:search%
    """,
    )
    fun searchSpu(search: String): List<Spu>

    @Query(
        """
        select spu from spus spu
        where
            (:spuCode is null or spu.spuCode ilike %:spuCode%)
            and (:category is null or spu.category = :category)
            and (:bizId is null or spu.bizId = :bizId)
            and (:title is null or spu.title ilike %:title%)
        order by id desc 
    """,
    )
    fun pageQuery(
        spuCode: String?,
        category: String?,
        pageable: Pageable,
        bizId: Long?,
        title: String?,
    ): Page<Spu>

    @Query(
        """
        select spu from spus spu
        where
             (:bizId is null or spu.bizId = :bizId) and (spu.id in (:ids))
    """,
    )
    fun exportExcel(
        bizId: Long?,
        ids: List<Long>,
    ): List<Spu>

    @Query(
        """
            select new io.github.clive.luxomssystem.facade.spu.response.SpuSelectResponse(s.id, s.spuCode,s.title,s.name,s.cnName,s.category) 
            from spus s
            where s.bizId = :bizId
    """,
    )
    fun findAllIdAndNam(bizId: Long): List<SpuSelectResponse>
}
