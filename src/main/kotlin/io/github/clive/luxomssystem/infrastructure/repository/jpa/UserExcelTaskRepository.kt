package io.github.clive.luxomssystem.infrastructure.repository.jpa

import io.github.clive.luxomssystem.domain.UserExcelTask
import io.github.clive.luxomssystem.infrastructure.config.AuthRewriter
import org.springframework.data.domain.Page
import org.springframework.data.domain.Pageable
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.jpa.repository.Query
import org.springframework.stereotype.Repository

@Repository
interface UserExcelTaskRepository : JpaRepository<UserExcelTask, Long> {
    @Query(
        """
    select u from user_excel_tasks u where
    1=1 and
     u.userId = :userId order by u.createdAt desc
    """,
        queryRewriter = AuthRewriter::class,
    )
    fun pageQuery(
        toPageable: Pageable,
        userId: Long,
    ): Page<UserExcelTask>
}
