@file:Suppress("DEPRECATION_ERROR")

package io.github.clive.luxomssystem.infrastructure.repository.jpa

import io.github.clive.luxomssystem.domain.customerSpu.model.CustomerSpuCountryTaxConfig
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.stereotype.Repository

@Deprecated("税率不应该跟spu挂钩", level = DeprecationLevel.ERROR)
@Repository
interface CustomerSpuCountryTaxConfigRepository : JpaRepository<CustomerSpuCountryTaxConfig, Long> {
    fun findByCustomerSpuId(customerSpuId: Long): List<CustomerSpuCountryTaxConfig>

    fun deleteByCustomerSpuIdAndCountryId(
        spuId: Long,
        countryId: Long,
    )

    fun findByCustomerSpuIdAndCountryId(
        spuId: Long,
        countryId: Long,
    ): CustomerSpuCountryTaxConfig?
}
