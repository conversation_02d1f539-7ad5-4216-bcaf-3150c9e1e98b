package io.github.clive.luxomssystem.infrastructure.repository.jpa.spu

import io.github.clive.luxomssystem.domain.customerSpu.model.CustomerSpu
import org.springframework.data.domain.Page
import org.springframework.data.domain.Pageable
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.jpa.repository.Query
import org.springframework.stereotype.Repository

@Repository
interface CustomerSpuRepository : JpaRepository<CustomerSpu, Long> {
    @Query(
        """
        select c.systemSpuId
        from CustomerSpu c
        where c.customerId = :customerId
    """,
    )
    fun findByCustomerId(customerId: Long): List<Long>

    fun findBySystemSpuId(systemSpuId: Long): List<CustomerSpu>

    fun findBySystemSpuIdAndCustomerId(
        systemSpuId: Long,
        customerId: Long,
    ): CustomerSpu?

    @Query(
        """
        select c
        from CustomerSpu c
        where c.bizId = :bizId and
        (:customerId is null or c.customerId = :customerId) and
        (:#{#systemSpuCodes.isEmpty()} = true or c.systemSpuCode IN :systemSpuCodes)
        order by c.createdAt desc
    """,
    )
    fun pageQueryByCustomerId(
        bizId: Long,
        systemSpuCodes: List<String>,
        customerId: Long?,
        page: Pageable,
    ): Page<CustomerSpu>

    @Query(
        """
        select c
        from CustomerSpu c
        where c.bizId = :bizId and
        (:customerId is null or c.customerId = :customerId) and
        (:#{#systemSpuCodes.isEmpty()} = true or c.systemSpuCode IN :systemSpuCodes) and
        (:#{#customerIds.isEmpty()} = true or c.customerId IN :customerIds)
        order by c.createdAt desc
    """,
    )
    fun pageQueryByCustomerIdForSelf(
        bizId: Long,
        systemSpuCodes: List<String>,
        customerId: Long?,
        page: Pageable,
        customerIds: List<Long>,
    ): Page<CustomerSpu>

    fun findAllBySystemSpuIdInAndCustomerId(
        systemSpuIds: List<Long>,
        customerId: Long,
    ): List<CustomerSpu>

    fun findAllBySystemSpuId(systemSpuId: Long): List<CustomerSpu>
}
