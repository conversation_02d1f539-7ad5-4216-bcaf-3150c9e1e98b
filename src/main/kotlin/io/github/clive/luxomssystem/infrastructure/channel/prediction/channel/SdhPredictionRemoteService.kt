package io.github.clive.luxomssystem.infrastructure.channel.prediction.channel

import io.github.clive.luxomssystem.common.utils.JSON
import io.github.clive.luxomssystem.common.utils.UrlUtils.decodeURLPart
import io.github.clive.luxomssystem.domain.waybill.event.WaybillCompletedEvent
import io.github.clive.luxomssystem.domain.waybill.model.WayBillStatus
import io.github.clive.luxomssystem.domain.waybill.model.Waybill
import io.github.clive.luxomssystem.infrastructure.channel.cos.CosInnerRemoteService
import io.github.clive.luxomssystem.infrastructure.channel.prediction.OrderPredictionRemoteService
import io.github.clive.luxomssystem.infrastructure.channel.prediction.channel.request.WaybillRequest
import io.github.clive.luxomssystem.infrastructure.channel.prediction.channel.sdh.SdhOrderRequest
import io.github.clive.luxomssystem.infrastructure.channel.prediction.channel.sdh.SdhResponse
import io.github.clive.luxomssystem.infrastructure.repository.jpa.SubOrderRepository
import io.github.clive.luxomssystem.infrastructure.repository.jpa.WaybillRepository
import io.github.oshai.kotlinlogging.KotlinLogging
import okhttp3.OkHttpClient
import okhttp3.Request
import org.springframework.beans.factory.annotation.Value
import org.springframework.context.ApplicationEventPublisher
import org.springframework.stereotype.Service

/**
 * Service for interacting with SDH prediction API.
 */
@Service
class SdhPredictionRemoteService(
    private val eventBus: ApplicationEventPublisher,
    override val ossClient: CosInnerRemoteService,
    override val okHttpClient: OkHttpClient,
    override val subOrderRepository: SubOrderRepository,
    @Value("\${sdh.account.customer-id}") private val customerId: String,
    @Value("\${sdh.account.customer-userid}") private val customerUserId: String,
    @Value("\${sdh.api.waybill}") private val waybillUrl: String,
    @Value("\${sdh.api.print}") private val printUrl: String,
    private val waybillRepository: WaybillRepository,
) : OrderPredictionRemoteService {
    override fun createPredication(waybill: Waybill) {
        try {
            val orderList = loadSubOrder(waybill)
            if (orderList.isEmpty()) {
                log.error { "Sdh 创建预报失败 | 订单号: ${waybill.orderNo} | 原因: 未找到子订单" }
                waybill.failed("No suborder found")
                return
            }
            val waybillRequests = orderList.flatMap { WaybillRequest.of(it, waybill) }
            val weight = waybillRequests.sumOf { it.weight }
            val reqYunTuPredictionDTO =
                SdhOrderRequest(
                    orderPiece = waybillRequests.size.toString(),
                    consigneeMobile = waybill.recipient.phone,
                    tradeType = "ZYXT",
                    consigneeTaxno = waybill.taxNumber,
                    consigneeName = waybill.recipient.receiverName,
                    consigneeAddress = waybill.recipient.address(),
                    consigneeTelephone = waybill.recipient.phone,
                    country = waybill.recipient.country,
                    consigneeState = waybill.recipient.state,
                    consigneeCity = waybill.recipient.city,
                    consigneePostcode = waybill.recipient.postcode,
                    consigneeEmail = waybill.recipient.email,
                    consigneeStreetno = waybill.recipient.address1,
                    orderCustomerinvoicecode = waybill.orderNo + "-",
                    customerId = customerId,
                    customerUserid = customerUserId,
                    productId = waybill.shipping.shipMethod,
                    weight = weight.toString(),
                    cargoType = "P",
                    orderInvoiceParam =
                        waybillRequests.map { req ->
                            SdhOrderRequest.OrderInvoiceParam(
                                invoiceAmount = req.price(waybillRequests.sumOf { it.qty }, req.country).toString(),
                                invoicePcs = req.qty.toString(),
                                invoiceTitle = req.name,
                                invoiceWeight = req.weight.toString(),
                                sku = req.cnName,
                                skuCode = req.skuCode(),
                                hsCode = req.hsCode,
                                invoiceMaterial = req.material,
                            )
                        },
                )

            val startTime = System.currentTimeMillis()
            val body = JSON.toJSONString(reqYunTuPredictionDTO)
            log.info { "闪电猴 创建预报 | 订单号: ${waybill.orderNo} | 请求参数: $body" }

            val formBody =
                okhttp3.FormBody
                    .Builder()
                    .add("param", body)
                    .build()

            val response =
                okHttpClient
                    .newCall(
                        Request
                            .Builder()
                            .url(waybillUrl)
                            .post(formBody)
                            .build(),
                    ).execute()

            val responseBody = response.body?.string() ?: throw Exception("Empty response body")

            log.info { "闪电猴 创建预报 | 订单号: ${waybill.orderNo} | 响应参数: $responseBody" }
            val rstYunTuWayBillDTO = JSON.parseObject<SdhResponse>(responseBody)

            if (rstYunTuWayBillDTO.ack == "true") {
                waybill.status = WayBillStatus.COMPLETED
                waybill.waybillNo = rstYunTuWayBillDTO.trackingNumber
                // Use the shared getPrintUrl method
                waybill.shipping.waybillLabelUrl = getPrintUrl(waybill, rstYunTuWayBillDTO.orderId!!)
                waybill.clearErrorMsg()
                eventBus.publishEvent(WaybillCompletedEvent(waybill.id, waybill.subOrderId, waybill.orderNos))
                val processingTime = System.currentTimeMillis() - startTime
                log.info {
                    """闪电猴 运单创建成功 | 订单ID: ${waybill.orderNos} | 运单号: ${waybill.bizId} | 处理时间: ${processingTime}ms"""
                }
            } else {
                val processingTime = System.currentTimeMillis() - startTime
                log.error {
                    """闪电猴 运单创建失败 | 订单ID: ${waybill.orderNos} | 运单号: ${waybill.bizId} | 错误信息: ${rstYunTuWayBillDTO.message?.decodeURLPart() ?: "未知错误"} | 处理时间: ${processingTime}ms"""
                }
                waybill.run { failed(rstYunTuWayBillDTO.message?.decodeURLPart() ?: "未知错误") }
            }
            waybillRepository.saveAndFlush(waybill)
        } catch (e: Exception) {
            val processingTime = System.currentTimeMillis() - System.currentTimeMillis()
            log.error(e) {
                """闪电猴 运单创建失败 | 订单ID: ${waybill.orderNos} | 运单号: ${waybill.bizId} | 错误信息: ${e.message} | 处理时间: ${processingTime}ms"""
            }
            waybill.failed(e.message ?: "Unknown error")
            waybillRepository.saveAndFlush(waybill)
        }
    }

    override fun deletePredication(waybill: Waybill): Boolean = true

    // Shared method to get print URL
    override fun getPrintUrl(waybill: Waybill): String = "ignore"

    fun getPrintUrl(
        waybill: Waybill,
        orderId: String,
    ): String {
        val startTime = System.currentTimeMillis()
        val printResponse =
            okHttpClient
                .newCall(
                    Request
                        .Builder()
                        .url("$printUrl?PrintType=lab10_10&order_id=$orderId")
                        .get()
                        .build(),
                ).execute()

        if (printResponse.isSuccessful) {
            return printResponse.use { pdfResponse ->
                pdfResponse.body?.byteStream()?.use { inputStream ->
                    val result = ossClient.uploadFile(waybill.bizId, getStoreUrl(waybill), inputStream)
                    val processingTime = System.currentTimeMillis() - startTime
                    log.info {
                        """闪电猴 获取面单成功 | 订单ID: $orderId | 运单号: ${waybill.bizId} | 处理时间: ${processingTime}ms"""
                    }
                    return result
                } ?: throw Exception("Empty PDF response body")
            }
        } else {
            val processingTime = System.currentTimeMillis() - startTime
            val errorMessage = printResponse.body?.string()
            val statusCode = printResponse.code
            log.error {
                """闪电猴 获取面单失败 | 订单ID: $orderId | 运单号: ${waybill.bizId} | 状态码: $statusCode | 错误信息: $errorMessage | 处理时间: ${processingTime}ms"""
            }
            throw Exception("闪电猴请求面单报错: $errorMessage")
        }
    }

    companion object {
        private val log = KotlinLogging.logger {}
    }
}
