# 轨迹更新调度器并发优化

## 优化背景

原有的 `processTrackingInfoInBatches` 方法采用串行处理，每个轨迹记录都需要调用远程API（燕文/云途），存在明显的性能瓶颈：

1. **网络IO密集**：每次API调用耗时约200-500ms
2. **串行处理**：使用 `forEach` 串行处理，无法充分利用多核CPU
3. **固定延迟**：每页处理后固定休息1秒，进一步降低了处理速度

## 并发优化方案

### 核心改进

1. **并发处理**：使用线程池并发处理每页的轨迹记录
2. **智能限流**：通过延迟控制API调用频率，避免对外部服务造成压力
3. **配置化开关**：支持在串行和并发模式之间切换
4. **原子计数器**：使用 `AtomicInteger` 确保并发环境下的计数准确性

### 技术实现

#### 1. 线程池配置

```kotlin
// 并发处理线程池
private val concurrentExecutor = Executors.newFixedThreadPool(CONCURRENT_THREADS)

companion object {
    private const val ENABLE_CONCURRENT_PROCESSING = true // 是否启用并发处理
    private const val CONCURRENT_THREADS = 10 // 并发线程数
    private const val RATE_LIMIT_DELAY_MS = 100L // 限流延迟（毫秒）
}
```

#### 2. 并发处理逻辑

```kotlin
// 并发处理当前页的所有轨迹信息
val futures = trackingInfoPage.content.map { trackingInfo ->
    CompletableFuture.supplyAsync({
        try {
            // 限流：避免对API造成过大压力
            Thread.sleep(RATE_LIMIT_DELAY_MS)

            val result = updateSingleTrackingInfo(trackingInfo)
            // 原子计数器更新
            when (result) {
                UpdateResult.SUCCESS -> successCount.incrementAndGet()
                UpdateResult.FAILURE -> failureCount.incrementAndGet()
                UpdateResult.SKIPPED -> skippedCount.incrementAndGet()
            }
            result
        } catch (e: Exception) {
            log.error(e) { "并发处理轨迹信息异常 | 运单号: ${trackingInfo.waybillNo}" }
            failureCount.incrementAndGet()
            UpdateResult.FAILURE
        }
    }, concurrentExecutor)
}

// 等待当前页所有任务完成
CompletableFuture.allOf(*futures.toTypedArray()).join()
```

#### 3. 配置化切换

```kotlin
val results = if (ENABLE_CONCURRENT_PROCESSING) {
    log.info { "使用并发处理模式" }
    processTrackingInfoInBatchesConcurrent(updateCutoffTime, staleCutoffTime, estimatedCount)
} else {
    log.info { "使用串行处理模式" }
    processTrackingInfoInBatches(updateCutoffTime, staleCutoffTime, estimatedCount)
}
```

## 性能提升分析

### 理论性能提升

假设每个API调用平均耗时300ms：

**串行处理**：

- 每页50个记录：50 × 300ms = 15秒
- 加上1秒休息时间：16秒/页
- 1000个记录（20页）：20 × 16秒 = 320秒（约5.3分钟）

**并发处理（10线程）**：

- 每页50个记录并发处理：300ms + 100ms限流 = 400ms
- 实际处理时间：max(50/10 × 400ms, 300ms) ≈ 2秒/页
- 1000个记录（20页）：20 × 2秒 = 40秒

**性能提升**：约8倍速度提升（320秒 → 40秒）

### 实际考虑因素

1. **API限制**：外部API可能有并发限制
2. **网络延迟**：实际网络环境的影响
3. **数据库连接**：并发数据库操作的开销
4. **内存使用**：并发处理增加的内存消耗

## 配置参数说明

### 关键参数

| 参数                             | 默认值    | 说明       |
|--------------------------------|--------|----------|
| `ENABLE_CONCURRENT_PROCESSING` | `true` | 是否启用并发处理 |
| `CONCURRENT_THREADS`           | `10`   | 并发线程数    |
| `RATE_LIMIT_DELAY_MS`          | `100L` | 限流延迟（毫秒） |

### 调优建议

1. **线程数调优**：
    - 根据服务器CPU核心数调整
    - 建议值：CPU核心数 × 2-4

2. **限流延迟调优**：
    - 根据外部API的承受能力调整
    - 燕文API：建议100-200ms
    - 云途API：建议50-100ms

3. **批次大小调优**：
    - 保持 `BATCH_SIZE = 50` 不变
    - 避免单次查询数据过多

## 监控指标

### 新增监控

1. **并发处理统计**：
    - 平均每页处理时间
    - 并发任务成功率
    - 线程池使用情况

2. **API调用监控**：
    - API响应时间分布
    - 并发请求失败率
    - 限流效果评估

### 日志示例

```
开始并发动态分批处理，预估记录数: 11200，每页大小: 50，并发线程数: 10
并发处理第1页，记录数: 50
并发处理第2页，记录数: 50
...
并发动态分批处理完成 | 总页数: 224 | 实际处理记录数: 11200 | 预估记录数: 11200
```

## 风险控制

### 1. API压力控制

- 限流延迟：防止对外部API造成过大压力
- 错误重试：避免因网络问题导致的失败
- 熔断机制：在API异常时自动降级

### 2. 资源管理

- 线程池管理：应用关闭时正确释放线程池
- 内存监控：防止并发处理导致内存溢出
- 连接池管理：合理配置数据库连接池

### 3. 异常处理

- 单任务异常不影响整体处理
- 连续异常时自动停止处理
- 详细的异常日志记录

## 部署建议

### 1. 分阶段部署

1. **测试环境**：先在测试环境验证并发效果
2. **灰度发布**：小流量验证稳定性
3. **全量部署**：确认无问题后全量部署

### 2. 配置调优

1. **初始配置**：使用保守的并发参数
2. **监控调优**：根据实际运行情况调整参数
3. **性能测试**：定期进行性能基准测试

### 3. 回滚方案

- 通过 `ENABLE_CONCURRENT_PROCESSING = false` 快速回滚到串行模式
- 保留原有串行处理方法作为备用方案

## 总结

并发优化显著提升了轨迹更新的处理速度，理论上可以达到8倍的性能提升。通过合理的配置和监控，可以在保证系统稳定性的前提下，大幅缩短轨迹更新任务的执行时间，提升用户体验。
