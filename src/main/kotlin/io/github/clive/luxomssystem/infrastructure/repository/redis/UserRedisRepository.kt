package io.github.clive.luxomssystem.infrastructure.repository.redis

import io.github.clive.luxomssystem.domain.doanload.auth.User
import org.springframework.data.redis.core.RedisTemplate
import org.springframework.stereotype.Repository
import java.util.concurrent.TimeUnit

@Repository
class UserRedisRepository(
    private val redisTemplate: RedisTemplate<String, String>,
) {
    fun saveUser(
        user: User,
        token: String,
    ) {
        redisTemplate.opsForValue().set("TOKEN:${user.id}:$token", user.id.toString(), 7, TimeUnit.DAYS)
    }

    fun findUserIdByToken(
        uId: Long,
        token: String,
    ): Long? {
        val userId = redisTemplate.opsForValue().get("TOKEN:$uId:$token") ?: return null
        return userId.toLong()
    }

    fun deleteUserByToken(
        user: User,
        token: String,
    ) {
        redisTemplate.delete("TOKEN:${user.id}:$token")
    }

    fun deleteAllUserByToken(user: User) {
        val keys = "TOKEN:${user.id}:*"
        redisTemplate.keys(keys).let {
            redisTemplate.delete(it)
        }
    }
}
