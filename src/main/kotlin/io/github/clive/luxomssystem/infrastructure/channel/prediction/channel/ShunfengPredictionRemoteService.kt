package io.github.clive.luxomssystem.infrastructure.channel.prediction.channel

import io.github.clive.luxomssystem.common.utils.JSON
import io.github.clive.luxomssystem.common.utils.eny.BizMsgCrypt
import io.github.clive.luxomssystem.domain.wayBillPrice
import io.github.clive.luxomssystem.domain.waybill.event.WaybillCompletedEvent
import io.github.clive.luxomssystem.domain.waybill.model.WayBillStatus
import io.github.clive.luxomssystem.domain.waybill.model.Waybill
import io.github.clive.luxomssystem.infrastructure.channel.cos.CosInnerRemoteService
import io.github.clive.luxomssystem.infrastructure.channel.prediction.OrderPredictionRemoteService
import io.github.clive.luxomssystem.infrastructure.channel.prediction.channel.request.WaybillRequest
import io.github.clive.luxomssystem.infrastructure.channel.prediction.channel.shunfeng.ShunFenApiTokenCache
import io.github.clive.luxomssystem.infrastructure.repository.jpa.SubOrderRepository
import io.github.clive.luxomssystem.infrastructure.repository.jpa.WaybillRepository
import io.github.oshai.kotlinlogging.KotlinLogging
import okhttp3.MediaType.Companion.toMediaType
import okhttp3.OkHttpClient
import okhttp3.Request
import okhttp3.RequestBody.Companion.toRequestBody
import org.springframework.beans.factory.annotation.Value
import org.springframework.context.ApplicationEventPublisher
import org.springframework.stereotype.Service
import java.math.BigDecimal
import java.util.*

val jsonMedia = "application/json".toMediaType()

@Service
class ShunfengPredictionRemoteService(
    private val eventBus: ApplicationEventPublisher,
    override val ossClient: CosInnerRemoteService,
    override val okHttpClient: OkHttpClient,
    override val subOrderRepository: SubOrderRepository,
    private val shunFenApiTokenCache: ShunFenApiTokenCache,
    @Value("\${shun-fen.account}") private val account: String,
    @Value("\${shun-fen.api.dispatch}") private val dispatchUrl: String,
    @Value("\${shun-fen.encodingAesKey}") private val encodingAesKey: String,
    @Value("\${shun-fen.apiUsername}") private val apiUsername: String,
    private val waybillRepository: WaybillRepository,
) : OrderPredictionRemoteService {
    override fun createPredication(waybill: Waybill) {
        val orderList = loadSubOrder(waybill)
        if (orderList.isEmpty()) {
            log.error { "Shunfeng 创建运单失败 | 订单号: ${waybill.orderNo} | 原因: 未找到子订单" }
            waybill.failed("No suborder found")
            return
        }
        val waybillRequests = orderList.flatMap { WaybillRequest.of(it, waybill) }

        val reqShunFenPredictionDTO =
            ReqShunFenPredictionDTO(
                customerOrderNo = waybill.orderNo,
                platformOrderId = waybill.orderNo,
                platformCode = "luxurypro",
                erpCode = "0000",
                interProductCode = waybill.shipping.shipMethod ?: "",
                parcelQuantity = 1,
                parcelTotalWeight = waybillRequests.sumOf { it.weight },
                declaredValue = waybill.totalPrice ?: orderList.wayBillPrice(),
                declaredCurrency = "USD",
                isBat = 0,
                apiUsername = apiUsername,
                createOrderType = "1",
                paymentInfo = ReqShunFenPaymentInfoDTO(payMethod = "1"),
                receiverInfo =
                    ReqShunFenReceiverInfoDTO(
                        contact = waybill.recipient.receiverName,
                        country = waybill.recipient.country,
                        postCode = waybill.recipient.postcode,
                        regionFirst = waybill.recipient.state,
                        regionSecond = waybill.recipient.city,
                        address = waybill.recipient.fullAddress(),
                        email = waybill.recipient.email,
                        phoneNo = waybill.recipient.phone,
                    ),
                parcelInfoList =
                    waybillRequests.map { order ->
                        ReqShunFenParcelInfoDTO(
                            name = order.name,
                            cName = order.cnName,
                            unit = "个",
                            amount = order.price(waybillRequests.sumOf { it.qty }, order.country).toString(),
                            currency = "USD",
                            quantity = order.qty.toBigDecimal(),
                            weight = order.weight,
                            goodsDesc = order.skuCode(),
                        )
                    },
            )

        val timestamp = System.currentTimeMillis().toString()
        val nonce = UUID.randomUUID().toString()
        val replyMsg = JSON.toJSONString(reqShunFenPredictionDTO)
        val token = shunFenApiTokenCache.getToken()

        try {
            val pc = BizMsgCrypt(token, encodingAesKey, account)
            val param = pc.encryptMsg(replyMsg, timestamp, nonce)

            log.info { "Shunfeng 创建预报 | 订单号: ${waybill.orderNo} | 请求参数: $replyMsg" }

            val requestBody = param.encrypt.toRequestBody(jsonMedia)
            val request =
                Request
                    .Builder()
                    .url(dispatchUrl)
                    .post(requestBody)
                    .addHeader("msgType", "IECS_CREATE_ORDER")
                    .addHeader("appKey", account)
                    .addHeader("token", token)
                    .addHeader("timestamp", timestamp)
                    .addHeader("nonce", nonce)
                    .addHeader("signature", param.signature)
                    .addHeader("lang", "zh-CN")
                    .addHeader("Content-Type", "application/json")
                    .build()

            okHttpClient.newCall(request).execute().use { response ->
                val responseBody = response.body?.string() ?: throw Exception("Empty response body")
                val restShunFenBaseDTO = JSON.parseObject<RestShunFenBaseDTO>(responseBody)
                log.info { "ShunFeng 创建预报 | 订单号: ${waybill.orderNo} | 响应参数: $restShunFenBaseDTO" }
                if (restShunFenBaseDTO.apiResultCode == 0) {
                    val decryptMsg = restShunFenBaseDTO.apiResultData
                    val restShunFenDTO = JSON.parseObject<RestShunFenDTO>(pc.decryptMsg(decryptMsg))
                    log.info { "ShunFeng 创建预报 | 订单号: ${waybill.orderNo} | 响应体(解密): $restShunFenDTO" }

                    if (restShunFenDTO.success) {
                        waybill.status = WayBillStatus.PENDING
                        waybill.waybillNo = restShunFenDTO.data?.sfWaybillNo ?: ""
                        log.info { "ShunFeng 创建预报成功 | 订单号: ${waybill.orderNo} | 运单号: ${waybill.waybillNo}" }
                        // You might want to implement getPrintUrl() method separately
                        waybill.shipping.waybillLabelUrl = getPrintUrl(waybill)
                        waybill.status = WayBillStatus.COMPLETED
                        eventBus.publishEvent(WaybillCompletedEvent(waybill.id, waybill.subOrderId, waybill.orderNos))
                        waybill.clearErrorMsg()
                    } else {
                        log.error { "ShunFeng 创建预报失败 | 订单号: ${waybill.orderNo} | 原因: ${restShunFenDTO.msg}" }
                        waybill.failed(restShunFenDTO.msg ?: "Unknown error")
                    }
                } else {
                    log.error { "ShunFeng 创建预报失败 | 订单号: ${waybill.orderNo} | 原因: ${restShunFenBaseDTO.apiErrorMsg}" }
                    waybill.failed(restShunFenBaseDTO.apiErrorMsg ?: "Unknown error")
                }
            }
            waybillRepository.saveAndFlush(waybill)
        } catch (e: Exception) {
            log.error(e) { "ShunFeng 创建预报异常 | 订单号: ${waybill.orderNo} | 原因: ${e.message}" }
            waybill.failed(e.message ?: "Unknown error")
            waybillRepository.saveAndFlush(waybill)
        }
    }

    override fun deletePredication(waybill: Waybill): Boolean {
        val resShunFenDelPredictionDTO =
            ReqShunFenDelPredictionDTO(
                sfWaybillNo = waybill.waybillNo,
                customerOrderNo = waybill.orderNo,
                cancelReason = "合并",
            )

        val timestamp = System.currentTimeMillis().toString()
        val nonce = UUID.randomUUID().toString()
        val replyMsg = JSON.toJSONString(resShunFenDelPredictionDTO)
        val token = shunFenApiTokenCache.getToken()

        try {
            val pc = BizMsgCrypt(token, encodingAesKey, account)
            val param = pc.encryptMsg(replyMsg, timestamp, nonce)

            val requestBody = param.encrypt.toRequestBody(jsonMedia)
            val request =
                Request
                    .Builder()
                    .url(dispatchUrl)
                    .post(requestBody)
                    .addHeader("msgType", "IECS_CANCEL_ORDER")
                    .addHeader("appKey", account)
                    .addHeader("token", token)
                    .addHeader("timestamp", timestamp)
                    .addHeader("nonce", nonce)
                    .addHeader("signature", param.signature)
                    .addHeader("lang", "zh-CN")
                    .addHeader("Content-Type", "application/json")
                    .build()
            log.info { "Shunfeng 取消预报 | 订单号: ${waybill.orderNo} | 请求参数: $replyMsg" }
            okHttpClient.newCall(request).execute().use { response ->
                val responseBody = response.body?.string() ?: throw Exception("Empty response body")
                val restShunFenBaseDTO = JSON.parseObject<RestShunFenBaseDTO>(responseBody)

                if (restShunFenBaseDTO.apiResultCode == 0) {
                    val decryptMsg = restShunFenBaseDTO.apiResultData
                    val restShunFenDTO = JSON.parseObject<RestShunFenDTO>(pc.decryptMsg(decryptMsg))
                    log.info { "ShunFeng 取消预报 | 订单号: ${waybill.orderNo} | 响应参数: $responseBody" }
                    return restShunFenDTO.success
                } else {
                    log.error { "ShunFeng 取消预报失败 | 订单号: ${waybill.orderNo} | 原因: ${restShunFenBaseDTO.apiErrorMsg}" }
                }
            }
        } catch (e: Exception) {
            // Handle exception
            log.error(e) { "ShunFeng 取消预报异常 | 订单号: ${waybill.orderNo} | 原因: ${e.message}" }
        }
        return false
    }

    override fun getPrintUrl(waybill: Waybill): String {
        val sfWaybillNo = waybill.waybillNo
        val token = shunFenApiTokenCache.getToken()
        val timestamp = System.currentTimeMillis().toString()
        val nonce = UUID.randomUUID().toString()

        val reqShunFengPrintBaseDTO =
            ReqShunFengPrintBaseDTO(
                apiUsername = apiUsername,
                printWaybillNoDtoList = listOf(ReqShunFemPrintDTO(sfWaybillNo = sfWaybillNo)),
            )

        val pc = BizMsgCrypt(token, encodingAesKey, account)
        val param = pc.encryptMsg(JSON.toJSONString(reqShunFengPrintBaseDTO), timestamp, nonce)

        val request =
            Request
                .Builder()
                .url(dispatchUrl)
                .post(param.encrypt.toRequestBody(jsonMedia))
                .addHeader("msgType", "IECS_PRINT_ORDER")
                .addHeader("appKey", account)
                .addHeader("token", token)
                .addHeader("timestamp", timestamp)
                .addHeader("nonce", nonce)
                .addHeader("signature", param.signature)
                .addHeader("lang", "zh-CN")
                .addHeader("Content-Type", "application/json")
                .build()

        okHttpClient.newCall(request).execute().use { response ->
            val responseBody = response.body?.string() ?: throw Exception("Empty response body")
            val restShunFenBaseDTO = JSON.parseObject<RestShunFenBaseDTO>(responseBody)
            log.info { "ShunFeng 获取运单 | 订单号: ${waybill.orderNo} | 响应: $restShunFenBaseDTO" }

            val decryptMsg = restShunFenBaseDTO.apiResultData
            val resultStr = pc.decryptMsg(decryptMsg)
            log.info { "ShunFeng 获取运单 | 订单号: ${waybill.orderNo} | 解密: $resultStr" }
            val restShunFenPrintBaseDTO = JSON.parseObject<RestShunFenPrintBaseDTO>(resultStr)
            val uploadWaybill = uploadWaybill(waybill, restShunFenPrintBaseDTO.data.url)
            log.info { "ShunFen 获取运单 | 订单号: ${waybill.orderNo} | url: $uploadWaybill" }
            return uploadWaybill
        }
    }

    // Data classes for request and response objects
    data class ReqShunFenPredictionDTO(
        val customerOrderNo: String,
        val platformOrderId: String,
        val platformCode: String,
        val erpCode: String,
        val interProductCode: String,
        val parcelQuantity: Int,
        val parcelTotalWeight: BigDecimal,
        val declaredValue: BigDecimal,
        val declaredCurrency: String,
        val isBat: Int,
        val apiUsername: String,
        val createOrderType: String,
        val paymentInfo: ReqShunFenPaymentInfoDTO,
        val receiverInfo: ReqShunFenReceiverInfoDTO,
        val parcelInfoList: List<ReqShunFenParcelInfoDTO>,
    )

    data class ReqShunFenPaymentInfoDTO(
        val payMethod: String,
    )

    data class ReqShunFenSenderInfoDTO(
        val contact: String,
        val country: String,
        val postCode: String,
        val regionFirst: String,
        val regionSecond: String,
        val address: String,
        val telNo: String,
    )

    data class ReqShunFenReceiverInfoDTO(
        val contact: String?,
        val country: String?,
        val postCode: String?,
        val regionFirst: String?,
        val regionSecond: String?,
        val address: String?,
        val email: String?,
        val phoneNo: String?,
    )

    data class ReqShunFenParcelInfoDTO(
        val name: String,
        val cName: String,
        val unit: String,
        val amount: String,
        val currency: String,
        val quantity: BigDecimal,
        val weight: BigDecimal,
        val goodsDesc: String,
    )

    data class ReqShunFenDelPredictionDTO(
        val sfWaybillNo: String?,
        val customerOrderNo: String,
        val cancelReason: String,
    )

    data class RestShunFenBaseDTO(
        val apiResultCode: Int?,
        val apiErrorMsg: String?,
        val apiResultData: String?,
    )

    data class RestShunFenDTO(
        val success: Boolean,
        val msg: String?,
        val data: RestShunFenDataDTO?,
    )

    data class RestShunFenDataDTO(
        val sfWaybillNo: String,
    )

    data class ReqShunFengPrintBaseDTO(
        val apiUsername: String?,
        val printWaybillNoDtoList: List<ReqShunFemPrintDTO> = emptyList(),
    )

    data class ReqShunFemPrintDTO(
        val sfWaybillNo: String?,
    )

    data class RestShunFenPrintBaseDTO(
        val data: RestShunFenPrintDataDTO,
    )

    data class RestShunFenPrintDataDTO(
        val url: String,
    )

    companion object {
        private val log = KotlinLogging.logger {}
    }
}
