package io.github.clive.luxomssystem.infrastructure.repository.jpa.auth

import io.github.clive.luxomssystem.domain.doanload.auth.Business
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.stereotype.Repository

@Repository
interface BusinessRepository : JpaRepository<Business, Long> {
    fun findByStatus(status: io.github.clive.luxomssystem.common.enums.BaseStatus): List<Business>
}
