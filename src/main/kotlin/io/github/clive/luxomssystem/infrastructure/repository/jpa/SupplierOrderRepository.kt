package io.github.clive.luxomssystem.infrastructure.repository.jpa

import io.github.clive.luxomssystem.domain.supplierOrder.model.SupplierOrder
import io.github.clive.luxomssystem.domain.supplierOrder.model.SupplierOrderStatus
import org.springframework.data.domain.Page
import org.springframework.data.domain.Pageable
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.jpa.repository.Query
import org.springframework.stereotype.Repository

@Repository
interface SupplierOrderRepository : JpaRepository<SupplierOrder, Long> {
    fun findByOrderNo(orderNo: String): SupplierOrder?

    fun findByBizIdAndShipping_WayBillRelation(
        bizId: Long,
        wayBillRelation: String,
    ): List<SupplierOrder>

    fun findByBizIdAndOrderNoIn(
        bizId: Long,
        orderNos: List<String>,
    ): List<SupplierOrder>

    fun findByOrderNoAndBizId(
        orderNo: String,
        bizId: Long,
    ): SupplierOrder?

    fun findByOrderNoIn(orderNos: List<String>): List<SupplierOrder>

    @Query(
        """
        select so from supplier_orders so
        WHERE :orderNo ILIKE so.orderNo || '%' and so.bizId = :bizId
        """,
    )
    fun queryByOrderNoAndBizId(
        orderNo: String,
        bizId: Long,
    ): List<SupplierOrder>

    @Query(
        """
        SELECT so.* FROM supplier_orders so
        WHERE 1=1
            AND so.biz_id = :bizId
            AND so.accepted = true
            AND (:orderNo IS NULL OR so.order_no ILIKE CONCAT('%', :orderNo, '%'))
            AND (:supplierId IS NULL OR so.supplier_id = :supplierId)
            AND (:#{#statues.isEmpty()} = true OR so.status IN (:statues))
            AND (:spu IS NULL OR so.spu ILIKE CONCAT('%', :spu, '%'))
            AND (:mainOrderId IS NULL OR so.main_order_id = :mainOrderId)
            AND (:createdAtFrom IS NULL OR so.created_at >= :createdAtFrom)
            AND (:createdAtTo IS NULL OR so.created_at <= :createdAtTo)
            AND (:fileName IS NULL OR so.file_name ILIKE CONCAT('%', :fileName, '%'))
            AND (:#{#orderNos.isEmpty()} = true OR so.order_no IN (:orderNos))
            AND (:country IS NULL OR UPPER(CAST(so.country AS text)) = UPPER(:country) OR UPPER(CAST(so.country AS text)) = UPPER(:countryCode))
        ORDER BY so.created_at DESC
    """,
        nativeQuery = true,
    )
    fun page(
        supplierId: Long?,
        bizId: Long,
        orderNo: String?,
        statues: List<String>,
        pageable: Pageable,
        spu: String?,
        mainOrderId: Long?,
        createdAtFrom: Long?,
        createdAtTo: Long?,
        fileName: String?,
        orderNos: List<String>? = null,
        country: String? = null,
        countryCode: String? = null,
    ): Page<SupplierOrder>

    @Query(
        """
        SELECT so.* FROM supplier_orders so
        WHERE 1=1
            AND so.biz_id = :bizId
            AND (:orderNo IS NULL OR so.order_no ILIKE CONCAT('%', :orderNo, '%'))
            AND (:#{#statues.isEmpty()} = true OR so.status IN (:statues))
            AND (:spu IS NULL OR so.spu ILIKE CONCAT('%', :spu, '%'))
            AND (:mainOrderId IS NULL OR so.main_order_id = :mainOrderId)
            AND (:createdAtFrom IS NULL OR so.created_at >= :createdAtFrom)
            AND (:createdAtTo IS NULL OR so.created_at <= :createdAtTo)
            AND (:fileName IS NULL OR so.file_name ILIKE CONCAT('%', :fileName, '%'))
            AND (:supplierId IS NULL OR so.supplier_id = :supplierId)
            AND (:#{#orderNos.isEmpty()} = true OR so.order_no IN (:orderNos))
            AND (:country IS NULL OR UPPER(CAST(so.country AS text)) = UPPER(:country) OR UPPER(CAST(so.country AS text)) = UPPER(:countryCode))
        ORDER BY so.created_at DESC
    """,
        nativeQuery = true,
//        queryRewriter = AuthNativeRewriter::class,
    )
    fun pageNoSupplier(
        bizId: Long,
        orderNo: String?,
        statues: List<String>,
        pageable: Pageable,
        spu: String?,
        mainOrderId: Long?,
        createdAtFrom: Long?,
        createdAtTo: Long?,
        fileName: String?,
        supplierId: Long?,
        orderNos: List<String>? = null,
        country: String? = null,
        countryCode: String? = null,
    ): Page<SupplierOrder>

    fun findByMainOrderId(mainOrderId: Long): List<SupplierOrder>

    fun findByMainOrderIdAndProduct_SupplierId(
        mainOrderId: Long,
        supplierId: Long,
    ): List<SupplierOrder>

    @Query(
        """
                select count(so) from supplier_orders so where
                1=1 and
                (:failed is null or so.status = :failed)
                and so.createdAt >= :createdAtFromEpochMilli
                and so.createdAt <= :createdAtToEpochMilli
                and so.bizId = :bizId
        """,
    )
    fun countByStatusAndCreatedAtBetween(
        failed: SupplierOrderStatus?,
        createdAtFromEpochMilli: Long?,
        createdAtToEpochMilli: Long?,
        bizId: Long,
    ): Int

    @Query(
        """
        select distinct so.product.supplierId from supplier_orders so
        where so.mainOrderId = :mainOrderId
        """,
    )
    fun findMainOrderAssignedSuppliers(mainOrderId: Long): List<Long?>

    fun deleteByMainOrderId(mainOrderId: Long)

    fun existsByWaybillIdAndStatusNot(
        waybillId: Long,
        status: SupplierOrderStatus): Boolean
}
