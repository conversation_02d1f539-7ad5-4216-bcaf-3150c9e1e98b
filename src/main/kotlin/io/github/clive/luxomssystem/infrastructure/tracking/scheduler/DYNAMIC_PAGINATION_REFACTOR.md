# 轨迹更新调度器动态分页重构

## 问题背景

原有的 `processTrackingInfoInBatches` 方法会出现"处理第224页，记录数: 0"的问题，主要原因是：

1. **时间窗口问题**：`countTrackingInfoForUpdate` 和 `findTrackingInfoForUpdate` 两个查询之间存在时间差
2. **并发更新问题**：在定时任务执行过程中，其他操作可能同时更新了轨迹记录
3. **数据一致性问题**：两个查询使用相同条件但在不同时间点执行，可能得到不同结果集

## 重构方案

### 方案选择：动态分页处理

采用**方案2（动态分页处理）**，不依赖总数计算页数，而是动态检查是否还有数据。

### 核心改进

#### 1. 固定时间戳

```kotlin
// 在任务开始时固定时间戳，确保查询一致性
val taskStartTime = ZonedDateTime.now()
val updateCutoffTime = taskStartTime.minusHours(UPDATE_INTERVAL_HOURS)
val staleCutoffTime = taskStartTime.minusDays(MAX_DAYS_WITHOUT_UPDATE)
```

#### 2. 动态分页循环

```kotlin
while (true) {
    val trackingInfoPage = trackingInfoRepository.findTrackingInfoForUpdate(...)

    // 如果当前页没有数据，说明处理完成
    if (trackingInfoPage.content.isEmpty()) {
        log.info { "第${page + 1}页无数据，处理完成" }
        break
    }

    // 处理当前页数据...
    page++
}
```

#### 3. 安全保护机制

- **异常处理**：单页异常不影响整体处理
- **连续异常保护**：连续多页异常时停止处理
- **数据异常检测**：处理记录数远超预估时停止处理

## 重构前后对比

### 重构前

```kotlin
val totalCount = trackingInfoRepository.countTrackingInfoForUpdate(...)
val totalPages = ((totalCount + BATCH_SIZE - 1) / BATCH_SIZE).toInt()

for (page in 0 until totalPages) {
    val trackingInfoPage = trackingInfoRepository.findTrackingInfoForUpdate(...)
    // 可能出现 trackingInfoPage.content.size = 0 的情况
}
```

### 重构后

```kotlin
val estimatedCount = trackingInfoRepository.countTrackingInfoForUpdate(...) // 仅用于监控

while (true) {
    val trackingInfoPage = trackingInfoRepository.findTrackingInfoForUpdate(...)
    if (trackingInfoPage.content.isEmpty()) break // 动态检测结束条件
    // 处理数据...
}
```

## 优势

1. **避免空页问题**：不会再出现"记录数: 0"的情况
2. **数据一致性**：使用固定时间戳确保查询条件一致
3. **容错性强**：单页异常不影响整体处理
4. **自适应**：自动适应数据变化，无需依赖预计算的总数
5. **监控友好**：提供详细的处理统计信息

## 日志改进

### 新增日志信息

- 预估记录数 vs 实际处理记录数
- 动态分页处理进度
- 异常保护触发情况

### 日志示例

```
开始动态分批处理，预估记录数: 11200，每页大小: 50
处理第1页，记录数: 50
处理第2页，记录数: 50
...
处理第224页，记录数: 50
第225页无数据，处理完成
动态分批处理完成 | 总页数: 224 | 实际处理记录数: 11200 | 预估记录数: 11200
```

## 性能影响

- **查询次数**：略有增加（多一次空页查询）
- **内存使用**：无变化（仍然是分页处理）
- **处理时间**：基本无变化
- **稳定性**：显著提升

## 配置参数

保持原有配置参数不变：

- `BATCH_SIZE = 50`：每页处理大小
- `UPDATE_INTERVAL_HOURS = 6L`：更新间隔
- `MAX_DAYS_WITHOUT_UPDATE = 15L`：过期天数

## 监控指标

新增监控指标：

- 预估记录数 vs 实际处理记录数的差异
- 动态分页的实际页数
- 异常保护触发次数

## 部署建议

1. **测试环境验证**：先在测试环境验证重构效果
2. **监控观察**：部署后密切观察日志和处理效果
3. **性能对比**：对比重构前后的处理时间和成功率
4. **异常处理**：关注异常保护机制的触发情况

## 总结

这次重构彻底解决了"处理第224页，记录数: 0"的问题，提高了系统的稳定性和容错性，同时保持了原有的性能特征。动态分页处理是处理大数据集时的最佳实践，特别适合在数据可能发生变化的场景中使用。
