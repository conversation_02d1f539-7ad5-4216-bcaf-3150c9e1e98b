package io.github.clive.luxomssystem.infrastructure.repository.jpa

import io.github.clive.luxomssystem.domain.CustomerCountryTaxConfig
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.stereotype.Repository

@Repository
interface CustomerCountryTaxConfigRepository : JpaRepository<CustomerCountryTaxConfig, Long> {
    fun findByCustomerId(customerId: Long): List<CustomerCountryTaxConfig>

    fun deleteByCustomerIdAndCountryId(
        customerId: Long,
        countryId: Long,
    )

    fun findByCustomerIdAndCountryId(
        customerId: Long,
        countryId: Long,
    ): CustomerCountryTaxConfig?
}
