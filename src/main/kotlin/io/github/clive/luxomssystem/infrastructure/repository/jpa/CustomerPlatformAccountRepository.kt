package io.github.clive.luxomssystem.infrastructure.repository.jpa

import io.github.clive.luxomssystem.common.enums.BaseStatus
import io.github.clive.luxomssystem.domain.CustomerPlatformAccount
import io.github.clive.luxomssystem.infrastructure.config.AuthRewriter
import org.springframework.data.domain.Page
import org.springframework.data.domain.Pageable
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.jpa.repository.Query
import org.springframework.data.repository.query.Param
import org.springframework.stereotype.Repository

@Repository
interface CustomerPlatformAccountRepository : JpaRepository<CustomerPlatformAccount, Long> {

    fun findByCustomerIdAndBizId(customerId: Long, bizId: Long): List<CustomerPlatformAccount>

    fun findByEmailAndBizId(email: String, bizId: Long): CustomerPlatformAccount?

    fun findByEmailAndPasswordAndBizId(email: String, password: String, bizId: Long): CustomerPlatformAccount?

    @Query(
        """
        select a from customer_platform_accounts a where 
        a.customerId = :customerId and
        a.bizId = :bizId and
        (:#{#statuses.isEmpty()} = true or a.status IN :statuses)
        order by a.createdAt DESC
        """,
        queryRewriter = AuthRewriter::class,
    )
    fun findByCustomerIdAndStatusIn(
        @Param("customerId") customerId: Long,
        @Param("statuses") statuses: List<BaseStatus>,
        bizId: Long,
        pageable: Pageable,
    ): Page<CustomerPlatformAccount>

    @Query(
        """
        select a from customer_platform_accounts a where 
        a.bizId = :bizId and
        (:email is null or a.email like %:email%) and
        (:accountName is null or a.accountName like %:accountName%) and
        (:customerId is null or a.customerId = :customerId) and
        (:#{#statuses.isEmpty()} = true or a.status IN :statuses)
        order by a.createdAt DESC
        """,
        queryRewriter = AuthRewriter::class,
    )
    fun pageQuery(
        @Param("email") email: String?,
        @Param("accountName") accountName: String?,
        @Param("customerId") customerId: Long?,
        @Param("statuses") statuses: List<BaseStatus>,
        bizId: Long,
        pageable: Pageable,
    ): Page<CustomerPlatformAccount>

    fun countByCustomerIdAndBizId(customerId: Long, bizId: Long): Long

    fun existsByEmailAndBizId(email: String, bizId: Long): Boolean
}
