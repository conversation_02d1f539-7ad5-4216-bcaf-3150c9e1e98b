
package io.github.clive.luxomssystem.infrastructure.repository.jpa.spu

import io.github.clive.luxomssystem.domain.sku.model.Sku
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.stereotype.Repository

@Repository
interface SkuRepository : JpaRepository<Sku, Long> {
    fun findBySkuCode(skuCode: String): Sku?

    fun findBySpuId(spuId: Long): List<Sku>

    fun findAllBySpuIdIn(spuIds: List<Long>): List<Sku>

    fun deleteBySpuId(spuId: Long)

    fun deleteBySpuIdAndSkuCodeNotIn(
        spuId: Long,
        skuCodes: List<String>,
    )

    fun findBySkuCodeIn(skuCodes: List<String>): List<Sku>
}
