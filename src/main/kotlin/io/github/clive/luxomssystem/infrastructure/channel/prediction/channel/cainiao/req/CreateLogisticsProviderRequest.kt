package io.github.clive.luxomssystem.infrastructure.channel.prediction.channel.cainiao.req

import com.fasterxml.jackson.annotation.JsonProperty
import io.github.clive.cognitionbank.infrastructure.remote.prediction.channel.cainiao.req.SolutionParam

data class CreateLogisticsProviderRequest(
    @JsonProperty("features")
    var features: Features?,
    @JsonProperty("locale")
    var locale: String?,
    @JsonProperty("outOrderId")
    var outOrderId: String?,
    @JsonProperty("packageParams")
    var packageParams: List<PackageParam?>?,
    @JsonProperty("receiverParam")
    var receiverParam: ReceiverParam?,
    @JsonProperty("returnerParam")
    var returnerParam: ReturnerParam?,
    @JsonProperty("senderParam")
    var senderParam: SenderParam?,
    @JsonProperty("solutionParam")
    var solutionParam: SolutionParam?,
    @JsonProperty("sourceHandoverParam")
    var sourceHandoverParam: SourceHandoverParam?,
    @JsonProperty("syncGetTrackingNumber")
    var syncGetTrackingNumber: String?,
    @JsonProperty("tradeOrderParam")
    var tradeOrderParam: TradeOrderParam?,
)
