# 轨迹系统重新设计总结

## 设计改进

### 原始设计的问题
1. **内存压力大**：一次性查询所有OUTBOUND运单，数据量大时容易OOM
2. **效率低下**：每次都要扫描waybills表，然后检查是否已有轨迹记录
3. **重复创建**：可能重复为同一运单创建轨迹记录
4. **API浪费**：对不需要更新的运单也会调用API

### 新设计的优势
1. **事件驱动**：运单状态变更时自动创建轨迹记录，避免重复扫描
2. **流式处理**：分页查询tracking_info表，内存友好
3. **智能过滤**：数据库层面过滤，减少无效数据传输
4. **精确过期判断**：基于轨迹最后事件时间（lastEventTime）而非更新时间判断15天过期
5. **性能优化**：15天过期机制，6小时更新间隔，避免频繁API调用

## 架构变更

### 新增组件
1. **WaybillStatusChangeListener**：监听运单状态变更事件
2. **TrackingUpdateSchedulerV2**：重新设计的定时任务调度器
3. **流式查询方法**：TrackingInfoRepository中的分页查询方法

### 修改组件
1. **TrackingRemoteService**：添加初始轨迹记录创建方法
2. **TrackingManagementService**：改为基于轨迹记录而非运单的操作
3. **TrackingInfoRepository**：添加流式查询和过滤方法

## 数据流程

### 轨迹记录创建流程
```
运单状态变更为OUTBOUND 
    ↓
WaybillStatusChangeListener监听事件
    ↓
检查渠道是否支持轨迹查询
    ↓
创建初始轨迹记录（状态：NOT_FOUND）
    ↓
保存到tracking_info表
```

### 定时更新流程
```
每天凌晨2点触发
    ↓
获取分布式锁
    ↓
计算时间范围（6小时前到15天内）
    ↓
分页查询需要更新的轨迹记录
    ↓
批量处理（每批50条）
    ↓
调用API获取最新轨迹信息
    ↓
更新轨迹记录
    ↓
释放锁
```

## 性能对比

### 内存使用
- **原设计**：O(n) - n为所有OUTBOUND运单数量
- **新设计**：O(50) - 固定批处理大小

### 数据库查询
- **原设计**：全表扫描waybills + 逐个查询tracking_info
- **新设计**：单次分页查询tracking_info，带索引优化

### API调用次数
- **原设计**：所有OUTBOUND运单都会调用API
- **新设计**：只对需要更新的轨迹调用API（过滤已完成、过期记录）

## 15天过期判断逻辑

### 重要修正
原始设计中使用`lastUpdatedAt`判断15天过期是错误的，因为每次定时任务执行都会更新这个字段。

### 正确的判断逻辑
- **lastUpdatedAt**：记录定时任务最后执行时间，用于判断6小时更新间隔
- **lastEventTime**：记录轨迹最后事件发生时间，用于判断15天过期
- **过期条件**：`lastEventTime < now - 15天`

### 数据库查询
```sql
SELECT t FROM TrackingInfo t
WHERE t.isCompleted = false
AND t.lastUpdatedAt < :updateCutoffTime  -- 6小时前
AND (t.lastEventTime IS NULL OR t.lastEventTime >= :staleCutoffTime)  -- 15天内
```

### 字段说明
- `lastEventTime IS NULL`：新创建的轨迹记录，还没有事件
- `lastEventTime >= staleCutoffTime`：最后事件在15天内

## 配置参数

### 关键参数
```kotlin
// 15天不更新阈值（基于lastEventTime）
private const val MAX_DAYS_WITHOUT_UPDATE = 15L

// 6小时更新间隔（基于lastUpdatedAt）
private const val UPDATE_INTERVAL_HOURS = 6L

// 批处理大小
private const val BATCH_SIZE = 50

// 分布式锁超时
private const val TRACKING_UPDATE_LOCK_TIMEOUT = 3600L
```

### 可调优参数
- **BATCH_SIZE**：根据内存和API限制调整
- **UPDATE_INTERVAL_HOURS**：根据业务需求调整更新频率
- **MAX_DAYS_WITHOUT_UPDATE**：根据业务要求调整过期时间

## 监控指标

### 关键指标
1. **处理记录数**：每次定时任务处理的轨迹记录数量
2. **成功率**：轨迹更新成功的比例
3. **API调用次数**：实际调用第三方API的次数
4. **执行时间**：定时任务的总执行时间
5. **内存使用**：任务执行期间的内存峰值

### 告警阈值
- 成功率低于90%
- 执行时间超过30分钟
- 内存使用超过1GB

## 部署注意事项

### 数据库索引
确保以下索引存在：
```sql
-- 轨迹信息查询索引
CREATE INDEX idx_tracking_info_update_query ON tracking_info(is_completed, last_updated_at);
CREATE INDEX idx_tracking_info_channel_completed ON tracking_info(channel, is_completed);
```

### Redis配置
确保Redis可用，用于分布式锁：
```yaml
spring:
  redis:
    host: localhost
    port: 6379
    timeout: 2000ms
```

### 应用配置
```yaml
# 定时任务线程池
spring:
  task:
    scheduling:
      pool:
        size: 2

# 日志级别
logging:
  level:
    io.github.clive.luxomssystem.infrastructure.tracking: INFO
```

## 迁移步骤

### 1. 数据迁移
为现有OUTBOUND运单创建轨迹记录：
```sql
INSERT INTO tracking_info (waybill_no, channel, current_status, is_completed, last_updated_at, created_at, updated_at)
SELECT 
    w.waybill_no,
    CASE 
        WHEN w.shipping_channel IN ('YW_HZ', 'YW_QZ', 'YW_GZ', 'YW_YW') THEN 'YANWEN'
        WHEN w.shipping_channel = 'YUNTU' THEN 'YUNTU'
    END as channel,
    'NOT_FOUND' as current_status,
    false as is_completed,
    NOW() as last_updated_at,
    EXTRACT(EPOCH FROM NOW()) * 1000 as created_at,
    EXTRACT(EPOCH FROM NOW()) * 1000 as updated_at
FROM waybills w
WHERE w.status = 'OUTBOUND' 
    AND w.waybill_no IS NOT NULL 
    AND w.shipping_channel IN ('YW_HZ', 'YW_QZ', 'YW_GZ', 'YW_YW', 'YUNTU')
    AND NOT EXISTS (
        SELECT 1 FROM tracking_info t WHERE t.waybill_no = w.waybill_no
    );
```

### 2. 部署顺序
1. 部署新的轨迹系统代码
2. 执行数据迁移脚本
3. 验证定时任务正常运行
4. 监控系统性能和错误率

### 3. 回滚计划
如果出现问题，可以：
1. 停用新的定时任务
2. 启用原有的轨迹查询逻辑
3. 清理tracking_info表中的测试数据

## 总结

新设计通过事件驱动和流式处理，显著提升了系统的性能和可扩展性：

- ✅ **内存友好**：固定内存使用，支持大数据量
- ✅ **性能优化**：减少无效API调用，提升处理效率
- ✅ **架构清晰**：职责分离，易于维护和扩展
- ✅ **监控完善**：详细的日志和统计信息
- ✅ **容错机制**：分布式锁、异常处理、优雅降级

这个重新设计的系统能够更好地支持业务的长期发展和大规模数据处理需求。
