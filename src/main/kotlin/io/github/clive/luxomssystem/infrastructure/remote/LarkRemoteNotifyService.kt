package io.github.clive.luxomssystem.infrastructure.remote

import io.github.clive.luxomssystem.infrastructure.config.properties.LarkWebhooks
import io.github.clive.luxomssystem.infrastructure.config.properties.WebHookType
import io.github.oshai.kotlinlogging.KotlinLogging
import org.springframework.http.MediaType
import org.springframework.scheduling.annotation.Async
import org.springframework.stereotype.Service
import org.springframework.web.client.RestTemplate

@Service
class LarkRemoteNotifyService(
    private val restTemplate: RestTemplate,
    private val larkWebhooks: LarkWebhooks,
) {
    data class LarkMessage(
        val title: String,
        val content: String,
        val link: String? = null,
        val atAll: Boolean = true,
    )

    @Async
    fun sendNotificationAsync(
        webHookType: WebHookType,
        message: LarkMessage,
        overrideWebhookUrl: String? = null,
    ) {
        sendNotification(webHookType, message, overrideWebhookUrl)
    }

    fun sendNotification(
        webHookType: WebHookType,
        message: LarkMessage,
        overrideWebhookUrl: String? = null,
    ) {
        val webhookUrl =
            overrideWebhookUrl ?: larkWebhooks.getByType(webHookType) ?: run {
                log.warn { "未找到飞书群webhook配置 | 类型: $webHookType" }
                return
            }
        sendNotification(webhookUrl, message)
    }

    fun sendNotification(
        webhookUrl: String,
        message: LarkMessage,
    ) {
        val requestBody = buildRequestBody(message)

        val headers = org.springframework.http.HttpHeaders()
        headers.contentType = MediaType.APPLICATION_JSON

        val requestEntity = org.springframework.http.HttpEntity(requestBody, headers)

        restTemplate.postForObject(
            webhookUrl,
            requestEntity,
            String::class.java,
        )
    }

    private fun buildRequestBody(message: LarkMessage): Map<String, Any> {
        val textContent =
            mutableListOf<Map<String, Any>>(
                mapOf(
                    "tag" to "text",
                    "text" to "${message.content}\n",
                ),
            )

        message.link?.let {
            textContent.add(
                mapOf(
                    "tag" to "a",
                    "text" to "点击进入页面查看\n",
                    "href" to it,
                ),
            )
        }

        if (message.atAll) {
            textContent.add(
                mapOf(
                    "tag" to "at",
                    "user_id" to "all",
                ),
            )
        }

        return mapOf(
            "msg_type" to "post",
            "content" to
                mapOf(
                    "post" to
                        mapOf(
                            "zh_cn" to
                                mapOf(
                                    "title" to message.title,
                                    "content" to listOf(textContent),
                                ),
                        ),
                ),
        )
    }

    companion object {
        private val log = KotlinLogging.logger { }
    }
}

// fun main() {
//    val larkRemoteNotifyService = LarkRemoteNotifyService(RestTemplate())
//    larkRemoteNotifyService.sendNotification(
//        LarkRemoteNotifyService.LarkMessage(
//            title = "测试",
//            content = "测试",
//            link = "https://www.baidu.com"
//        )
//    )
// }
