package io.github.clive.luxomssystem.infrastructure.channel.prediction.channel

import io.github.clive.luxomssystem.common.utils.JSON
import io.github.clive.luxomssystem.domain.SubOrder
import io.github.clive.luxomssystem.domain.waybill.event.WaybillCompletedEvent
import io.github.clive.luxomssystem.domain.waybill.model.WayBillStatus
import io.github.clive.luxomssystem.domain.waybill.model.Waybill
import io.github.clive.luxomssystem.infrastructure.channel.cos.CosInnerRemoteService
import io.github.clive.luxomssystem.infrastructure.channel.prediction.OrderPredictionRemoteService
import io.github.clive.luxomssystem.infrastructure.channel.prediction.channel.cainiao.response.BaseCaiNiaoResponse
import io.github.clive.luxomssystem.infrastructure.channel.prediction.channel.cainiao.response.CreateLogisticsProviderResponse
import io.github.clive.luxomssystem.infrastructure.channel.prediction.channel.cainiao.response.QueryWaybillResponse
import io.github.clive.luxomssystem.infrastructure.channel.prediction.channel.request.WaybillRequest
import io.github.clive.luxomssystem.infrastructure.repository.jpa.SubOrderRepository
import io.github.clive.luxomssystem.infrastructure.repository.jpa.WaybillRepository
import io.github.oshai.kotlinlogging.KotlinLogging
import okhttp3.FormBody
import okhttp3.OkHttpClient
import okhttp3.Request
import org.apache.commons.codec.binary.Base64
import org.springframework.beans.factory.annotation.Value
import org.springframework.context.ApplicationEventPublisher
import org.springframework.http.MediaType
import org.springframework.stereotype.Service
import java.io.ByteArrayInputStream
import java.security.MessageDigest
import java.util.*

@Service
class CaiNiaoPredictionRemoteService(
    val eventBus: ApplicationEventPublisher,
    override val ossClient: CosInnerRemoteService,
    override val okHttpClient: OkHttpClient,
    override val subOrderRepository: SubOrderRepository,
    private val waybillRepository: WaybillRepository,
    @Value("\${cai-niao.secret-key}") private val secretKey: String,
    @Value("\${cai-niao.logistic-provider-id}") private val logisticProviderId: String,
    @Value("\${cai-niao.api.url}") private val apiUrl: String,
) : OrderPredictionRemoteService {
    override fun createPredication(waybill: Waybill) {
        val orderList = loadSubOrder(waybill)
        if (orderList.isEmpty()) {
            log.error { "CaiNiao 创建预报失败 | 订单号: ${waybill.orderNo} | 原因: 未找到子订单" }
            waybill.failed("No suborder found")
            return
        }

        val requestBody = buildRequestBody(waybill, orderList)

        try {
            val response = sendRequest(requestBody)
            handleResponse(response, waybill)
        } catch (e: Exception) {
            log.error(e) { "CaiNiao 创建预报失败 | 订单号: ${waybill.orderNo} | 错误信息: ${e.message}" }
            waybill.failed(e.message ?: "Unknown error")
            waybillRepository.saveAndFlush(waybill)
        }
    }

    private fun buildRequestBody(
        waybill: Waybill,
        orderList: List<SubOrder>,
    ): String {
        val waybillRequests = orderList.flatMap { WaybillRequest.of(it, waybill) }

        return JSON.toJSONString(
            mapOf(
                "outOrderId" to waybill.orderNo,
                "syncGetTrackingNumber" to true,
                "solutionParam" to
                    mapOf(
                        "solutionCode" to waybill.shipping.shipMethod,
                        "doorPickupParam" to
                            mapOf(
                                "name" to "DAMU",
                                "mobilePhone" to "15356637391",
                                "zipCode" to "310020",
                                "countryCode" to "CN",
                                "state" to "浙江省",
                                "city" to "杭州市",
                                "district" to "上城区",
                                "detailAddress" to "新塘路1233号 万事利科创中心 2号楼402",
                            ),
                    ),
                "packageParams" to
                    mapOf(
                        "packageParam" to
                            mapOf(
                                "weight" to waybillRequests.sumOf { it.weight }.toInt(),
                                "itemParams" to
                                    waybillRequests.map { order ->
                                        mapOf(
                                            "quantity" to order.qty,
                                            "englishName" to order.name,
                                            "unitPrice" to order.price(waybillRequests.sumOf { it.qty }, order.country),
                                            "unitPriceCurrency" to "USD",
                                        )
                                    },
                            ),
                    ),
                "receiverParam" to
                    mapOf(
                        "name" to waybill.recipient.receiverName,
                        "phone" to waybill.recipient.phone,
                        "country" to waybill.recipient.country,
                        "city" to waybill.recipient.city,
                        "address" to waybill.recipient.fullAddress(),
                        "zipCode" to waybill.recipient.postcode,
                        "state" to waybill.recipient.state,
                    ),
            ),
        )
    }

    private fun sendRequest(
        requestBody: String,
        msgType: String = "cnge.order.create",
        toCode: String = "CNGCP-OPEN",
    ): String {
        val dataDigest = md5(requestBody, secretKey)

        val formBody =
            FormBody
                .Builder()
                .add("msg_type", msgType)
                .add("logistic_provider_id", logisticProviderId)
                .add("data_digest", dataDigest)
                .add("logistics_interface", requestBody)
                .add("to_code", toCode)
                .build()

        val request =
            Request
                .Builder()
                .url(apiUrl)
                .header("Content-Type", MediaType.APPLICATION_FORM_URLENCODED_VALUE)
                .post(formBody)
                .build()

        return okHttpClient.newCall(request).execute().use { response ->
            response.body?.string() ?: throw Exception("空响应体")
        }
    }

    private fun handleResponse(
        responseBody: String,
        waybill: Waybill,
    ) {
        val response: CreateLogisticsProviderResponse = JSON.parseObject(responseBody)
        log.info { "CaiNiao 创建预报 | 订单号: ${waybill.orderNo} | 收到响应: $response" }

        if (response.success == "true") {
            val data = response.data
            waybill.status = WayBillStatus.PENDING
            waybill.waybillNo = data?.orderCode?.takeIf { it.isNotBlank() } ?: ""
            log.info { "CaiNiao 创建预报成功 | 订单号: ${waybill.orderNo} | 运单号 : ${waybill.waybillNo}" }
            waybill.shipping.waybillLabelUrl = getPrintUrl(waybill)
            waybill.status = WayBillStatus.COMPLETED
            eventBus.publishEvent(WaybillCompletedEvent(waybill.id, waybill.subOrderId, waybill.orderNos))
            waybill.clearErrorMsg()
        } else {
            log.error { "CaiNiao 创建预报失败 | 订单号: ${waybill.orderNo} | 错误信息: ${response.errorMsg}" }
            waybill.failed(response.errorMsg ?: "Unknown error")
        }
        waybillRepository.saveAndFlush(waybill)
    }

    override fun deletePredication(waybill: Waybill): Boolean {
        val requestBody =
            """
            {
                "orderCode":"${waybill.waybillNo}",
                "locale":"zh_CN"
            }
            """.trimIndent()

        try {
            val response = sendRequest(requestBody, "cnge.order.cancel")
            val cancelResponse: BaseCaiNiaoResponse = JSON.parseObject(response)

            if (cancelResponse.success == "true") {
                log.info { "CaiNiao 取消预报成功 | 订单号: ${waybill.orderNo} | 运单号: ${waybill.waybillNo}" }
                return true
            } else {
                log.error { "CaiNiao 取消预报失败 | 订单号: ${waybill.orderNo} | 运单号: ${waybill.waybillNo} | 原因: ${cancelResponse.errorMsg}" }
                return false
            }
        } catch (e: Exception) {
            log.error(e) { "CaiNiao 取消预报异常 | 订单号: ${waybill.orderNo} | 运单号: ${waybill.waybillNo}" }
            return false
        }
    }

    override fun getPrintUrl(waybill: Waybill): String {
        val requestBody =
            """
            {
                "orderCode":"${waybill.waybillNo}",
                "locale":"zh_CN"
            }
            """.trimIndent()

        try {
            val response = sendRequest(requestBody, "cnge.waybill.get", "CGOP")
            val waybillResponse: QueryWaybillResponse = JSON.parseObject(response)

            if (waybillResponse.success == "true") {
                val pdfData = waybillResponse.data?.waybillPdfData
                if (pdfData != null) {
                    val decodedPdf = Base64.decodeBase64(pdfData)
                    val inputStream = ByteArrayInputStream(decodedPdf)
                    val ossUrl =
                        ossClient.uploadFile(
                            waybill.bizId,
                            "/waybill/pdf/${waybill.bizId}/${waybill.id}/${UUID.randomUUID()}.pdf",
                            inputStream,
                        )
                    log.info { "CaiNiao waybill PDF uploaded to OSS: $ossUrl" }
                    return ossUrl
                }
            }
            log.error { "CaiNiao 获取运单失败 | 订单号: ${waybill.orderNo} | 运单号: ${waybill.waybillNo} | 原因: ${waybillResponse.errorMsg}" }
        } catch (e: Exception) {
            log.error(e) { "CaiNiao 获取运单异常 | 订单号: ${waybill.orderNo} | 运单号: ${waybill.waybillNo}" }
        }
        return ""
    }

    private fun md5(
        content: String,
        keys: String,
    ): String {
        val contentWithKey = content + keys
        val md = MessageDigest.getInstance("MD5")
        md.update(contentWithKey.toByteArray(charset("utf-8")))
        return String(Base64.encodeBase64(md.digest()), charset("utf-8"))
    }

    companion object {
        private val log = KotlinLogging.logger {}
    }
}
