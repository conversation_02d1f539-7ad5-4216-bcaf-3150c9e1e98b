package io.github.clive.luxomssystem.infrastructure.channel.prediction.channel.cainiao.req

import com.fasterxml.jackson.annotation.JsonProperty

data class PackageParam(
    @JsonProperty("height")
    var height: String?,
    @JsonProperty("itemParams")
    var itemParams: List<ItemParam?>?,
    @JsonProperty("length")
    var length: String?,
    @JsonProperty("packageTradeAmount")
    var packageTradeAmount: String?,
    @JsonProperty("packageTradeAmountCurrency")
    var packageTradeAmountCurrency: String?,
    @JsonProperty("weight")
    var weight: String?,
    @JsonProperty("width")
    var width: String?,
)
