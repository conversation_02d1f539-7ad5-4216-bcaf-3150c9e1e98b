package io.github.clive.luxomssystem.infrastructure.logging

import io.github.clive.luxomssystem.common.utils.JSON
import io.github.clive.luxomssystem.infrastructure.config.auth.CustomerContextHolder
import io.github.clive.luxomssystem.infrastructure.config.auth.UserContextHolder
import io.github.oshai.kotlinlogging.KotlinLogging
import jakarta.servlet.http.HttpServletRequest
import org.aspectj.lang.ProceedingJoinPoint
import org.aspectj.lang.annotation.Around
import org.aspectj.lang.annotation.Aspect
import org.aspectj.lang.reflect.MethodSignature
import org.springframework.stereotype.Component
import org.springframework.web.context.request.RequestContextHolder
import org.springframework.web.context.request.ServletRequestAttributes

@Aspect
@Component
class CrudOperationLoggingAspect {
    private val log = KotlinLogging.logger {}

    @Around(
        "(@annotation(org.springframework.web.bind.annotation.PostMapping) || " +
                "@annotation(org.springframework.web.bind.annotation.PutMapping) || " +
                "@annotation(org.springframework.web.bind.annotation.PatchMapping) || " +
                "@annotation(org.springframework.web.bind.annotation.DeleteMapping)) && within(io.github.clive.luxomssystem.facade..*)",
    )
    fun logCrudOperation(pjp: ProceedingJoinPoint): Any? {
        val start = System.currentTimeMillis()
        var proceeded = false

        // Compute metadata safely and never throw
        val request = safeOrNull { currentHttpRequest() }
        val method = request?.method ?: "UNKNOWN"
        val path = request?.requestURI ?: "UNKNOWN"
        val (actorType, actorId, actorName, roleName) = safeOrNull { resolveActor() } ?: Actor("anonymous", "", "", "")
        val controller = safeOrNull { pjp.signature.declaringType.simpleName } ?: "UNKNOWN"
        val action = safeOrNull { pjp.signature.name } ?: "UNKNOWN"
        val query = safeOrNull { request?.parameterMap?.mapValues { it.value.toList() } }
        val maskedQuery = safeOrNull { maskSensitive(JSON.toJSONString(query ?: emptyMap<String, Any>())) } ?: ""
        val body = safeOrNull { extractRequestBody(pjp) }
        val maskedBody = body?.let { safeOrNull { maskSensitive(it) } } ?: ""

        try {
            val result = pjp.proceed().also { proceeded = true }
            val duration = System.currentTimeMillis() - start
            safeRun {
                log.info {
                    "op=crud_api_call status=SUCCESS method=$method path=$path " +
                            "controller=$controller action=$action actorType=$actorType actorId=$actorId actorName=$actorName role=$roleName durationMs=$duration " +
                            "query=${truncate(maskedQuery)} body=${truncate(maskedBody)}"
                }
            }
            return result
        } catch (ex: Throwable) {
            val duration = System.currentTimeMillis() - start
            if (proceeded) {
                // Business exception; log and rethrow
                safeRun {
                    log.warn(ex) {
                        "op=crud_api_call status=FAIL method=$method path=$path " +
                                "controller=$controller action=$action actorType=$actorType actorId=$actorId actorName=$actorName role=$roleName durationMs=$duration " +
                                "query=${truncate(maskedQuery)} body=${truncate(maskedBody)}"
                    }
                }
                throw ex
            } else {
                // Aspect error before proceeding; do not block business
                safeRun {
                    log.error(ex) { "op=crud_api_call_log_error stage=before_proceed method=$method path=$path controller=$controller action=$action" }
                }
                return pjp.proceed().also {
                    safeRun {
                        log.info { "op=crud_api_call status=SUCCESS method=$method path=$path controller=$controller action=$action durationMs=${System.currentTimeMillis() - start}" }
                    }
                }
            }
        }
    }

    private fun currentHttpRequest(): HttpServletRequest? =
        (RequestContextHolder.getRequestAttributes() as? ServletRequestAttributes)?.request

    private data class Actor(
        val type: String,
        val id: String,
        val name: String,
        val role: String,
    )

    private fun resolveActor(): Actor {
        val user = UserContextHolder.user
        if (user != null) {
            return Actor(
                type = "user",
                id = user.id.toString(),
                name = user.name,
                role = user.roleName,
            )
        }
        val customer = CustomerContextHolder.user
        if (customer != null) {
            return Actor(
                type = "customer",
                id = customer.id.toString(),
                name = "",
                role = "",
            )
        }
        return Actor(type = "anonymous", id = "", name = "", role = "")
    }

    private fun extractRequestBody(pjp: ProceedingJoinPoint): String? {
        val sig = pjp.signature as? MethodSignature ?: return null
        val method = sig.method
        val params = method.parameters
        val args = pjp.args

        // Prefer parameter annotated with @RequestBody
        for (i in params.indices) {
            val hasRequestBody =
                params[i].annotations.any { it.annotationClass.java.name == "org.springframework.web.bind.annotation.RequestBody" }
            if (hasRequestBody) {
                return safeStringify(args.getOrNull(i))
            }
        }
        // Fallback: serialize first non-framework arg
        val idx = args.indexOfFirst { shouldLogArg(it) }
        return if (idx >= 0) safeStringify(args[idx]) else null
    }

    private fun shouldLogArg(arg: Any?): Boolean {
        if (arg == null) return false
        val pkg = arg::class.java.`package`?.name ?: return true
        if (pkg.startsWith("jakarta.servlet") || pkg.startsWith("javax.servlet")) return false
        if (pkg.startsWith("org.springframework")) return false
        return true
    }

    private fun safeStringify(value: Any?): String = try {
        JSON.toJSONString(value)
    } catch (_: Exception) {
        value.toString()
    }

    private fun maskSensitive(json: String): String {
        return json
    }

    private fun truncate(text: String, max: Int = 1000): String =
        if (text.length <= max) text else text.take(max) + "..."

    private fun <T> safeOrNull(block: () -> T): T? = try {
        block()
    } catch (_: Throwable) {
        null
    }

    private fun safeRun(block: () -> Unit) {
        try {
            block()
        } catch (_: Throwable) {
            // swallow
        }
    }
}
