package io.github.clive.luxomssystem.infrastructure.channel.prediction.channel.sdh

import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import com.fasterxml.jackson.annotation.JsonInclude
import com.fasterxml.jackson.annotation.JsonProperty

/**
 * 订单请求信息
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
data class SdhOrderRequest(
    /**
     * 买家ID
     */
    @field:JsonProperty("buyerid")
    val buyerid: String? = null,
    /**
     * 件数，小包默认1，快递需真实填写
     */
    @field:JsonProperty("order_piece")
    val orderPiece: String? = null,
    /**
     * 手机号,选填。为方便派送最好填写
     */
    @field:JsonProperty("consignee_mobile")
    val consigneeMobile: String? = null,
    /**
     * 退回标志，默认N表示不退回，Y标表示退回。中邮可以忽略该属性
     */
    @field:JsonProperty("order_returnsign")
    val orderReturnsign: String? = null,
    /**
     * 交易类型，例如：ZYXT
     */
    @field:JsonProperty("trade_type")
    val tradeType: String? = null,
    /**
     * 关税类型，例如：DDU或DDP
     */
    @field:JsonProperty("duty_type")
    val dutyType: String? = null,
    /**
     * 电池类型代码，联系货代提供
     */
    @field:JsonProperty("battery_type")
    val batteryType: String? = null,
    /**
     * 收件人,必填
     */
    @field:JsonProperty("consignee_name")
    val consigneeName: String? = null,
    /**
     * 收件公司名,如有最好填写
     */
    @field:JsonProperty("consignee_companyname")
    val consigneeCompanyname: String? = null,
    /**
     * 收件地址街道，必填
     */
    @field:JsonProperty("consignee_address")
    val consigneeAddress: String? = null,
    /**
     * 收件电话，必填
     */
    @field:JsonProperty("consignee_telephone")
    val consigneeTelephone: String? = null,
    /**
     * 收件国家二字代码，必填
     */
    @field:JsonProperty("country")
    val country: String? = null,
    /**
     * 州/省
     */
    @field:JsonProperty("consignee_state")
    val consigneeState: String? = null,
    /**
     * 城市
     */
    @field:JsonProperty("consignee_city")
    val consigneeCity: String? = null,
    /**
     * 收件区，选填
     */
    @field:JsonProperty("consignee_suburb")
    val consigneeSuburb: String? = null,
    /**
     * 邮编，有邮编的国家必填
     */
    @field:JsonProperty("consignee_postcode")
    val consigneePostcode: String? = null,
    /**
     * 收件护照号，选填
     */
    @field:JsonProperty("consignee_passportno")
    val consigneePassportno: String? = null,
    /**
     * 邮箱，选填
     */
    @field:JsonProperty("consignee_email")
    val consigneeEmail: String? = null,
    /**
     * 收件人税号
     */
    @field:JsonProperty("consignee_taxno")
    val consigneeTaxno: String? = null,
    /**
     * 收件人税号类型
     */
    @field:JsonProperty("consignee_taxnotype")
    val consigneeTaxnotype: String? = null,
    /**
     * 收件人税号国家
     */
    @field:JsonProperty("consignee_taxnocountry")
    val consigneeTaxnocountry: String? = null,
    /**
     * 街道号
     */
    @field:JsonProperty("consignee_streetno")
    val consigneeStreetno: String? = null,
    /**
     * 门牌号
     */
    @field:JsonProperty("consignee_doorno")
    val consigneeDoorno: String? = null,
    /**
     * 税号类型，邮政产品可选值：IOSS,NO-IOSS,OTHER；DHL可选值：SDT、VAT、FTZ、DAN、EOR、CNP、EIN等(类型说明参照文档底部“DHL发件人税号类型”)
     */
    @field:JsonProperty("shipper_taxnotype")
    val shipperTaxnotype: String? = null,
    /**
     * 发件人税号
     */
    @field:JsonProperty("shipper_taxno")
    val shipperTaxno: String? = null,
    /**
     * 发件人税号国家,用国家二字码
     */
    @field:JsonProperty("shipper_taxnocountry")
    val shipperTaxnocountry: String? = null,
    /**
     * 客户ID，必填
     */
    @field:JsonProperty("customer_id")
    val customerId: String? = null,
    /**
     * 登录人ID，必填
     */
    @field:JsonProperty("customer_userid")
    val customerUserid: String? = null,
    /**
     * 原单号，必填
     */
    @field:JsonProperty("order_customerinvoicecode")
    val orderCustomerinvoicecode: String? = null,
    /**
     * 运输方式ID，必填
     */
    @field:JsonProperty("product_id")
    val productId: String? = null,
    /**
     * 总重，选填，如果sku上有单重可不填该项
     */
    @field:JsonProperty("weight")
    val weight: String? = null,
    /**
     * 图片地址，多图片地址用分号隔开
     */
    @field:JsonProperty("product_imagepath")
    val productImagepath: String? = null,
    /**
     * 产品销售地址
     */
    @field:JsonProperty("order_transactionurl")
    val orderTransactionurl: String? = null,
    /**
     * 选填；用于DHL/FEDEX运费；或用于白关申报（订单实际金额，特殊渠道使用）；或其他用途
     */
    @field:JsonProperty("order_cargoamount")
    val orderCargoamount: String? = null,
    /**
     * 保险金额
     */
    @field:JsonProperty("order_insurance")
    val orderInsurance: String? = null,
    /**
     * 包裹类型，P代表包裹，D代表文件，B代表PAK袋
     */
    @field:JsonProperty("cargo_type")
    val cargoType: String? = null,
    /**
     * 自定义信息
     */
    @field:JsonProperty("order_customnote")
    val orderCustomnote: String? = null,
    /**
     * 订单发票参数列表
     */
    @field:JsonProperty("orderInvoiceParam")
    val orderInvoiceParam: List<OrderInvoiceParam>? = null,
    /**
     * 订单体积参数列表，选填
     */
    @field:JsonProperty("orderVolumeParam")
    val orderVolumeParam: List<OrderVolumeParam>? = null,
) {
    /**
     * 订单发票参数
     */
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @JsonIgnoreProperties(ignoreUnknown = true)
    data class OrderInvoiceParam(
        /**
         * 申报总价值，必填
         */
        @field:JsonProperty("invoice_amount")
        val invoiceAmount: String? = null,
        /**
         * 件数，必填
         */
        @field:JsonProperty("invoice_pcs")
        val invoicePcs: String? = null,
        /**
         * 英文品名，必填
         */
        @field:JsonProperty("invoice_title")
        val invoiceTitle: String? = null,
        /**
         * 单件重
         */
        @field:JsonProperty("invoice_weight")
        val invoiceWeight: String? = null,
        /**
         * 中文品名
         */
        @field:JsonProperty("sku")
        val sku: String? = null,
        /**
         * 配货信息
         */
        @field:JsonProperty("sku_code")
        val skuCode: String? = null,
        /**
         * 海关编码
         */
        @field:JsonProperty("hs_code")
        val hsCode: String? = null,
        /**
         * 进口海关编码
         */
        @field:JsonProperty("import_hs_code")
        val importHsCode: String? = null,
        /**
         * 销售地址
         */
        @field:JsonProperty("transaction_url")
        val transactionUrl: String? = null,
        /**
         * 申报单位
         */
        @field:JsonProperty("invoiceunit_code")
        val invoiceunitCode: String? = null,
        /**
         * 图片地址
         */
        @field:JsonProperty("invoice_imgurl")
        val invoiceImgurl: String? = null,
        /**
         * 品牌
         */
        @field:JsonProperty("invoice_brand")
        val invoiceBrand: String? = null,
        /**
         * 规格
         */
        @field:JsonProperty("invoice_rule")
        val invoiceRule: String? = null,
        /**
         * 申报币种
         */
        @field:JsonProperty("invoice_currency")
        val invoiceCurrency: String? = null,
        /**
         * 税则号
         */
        @field:JsonProperty("invoice_taxno")
        val invoiceTaxno: String? = null,
        /**
         * 原产国
         */
        @field:JsonProperty("origin_country")
        val originCountry: String? = null,
        /**
         * 材质
         */
        @field:JsonProperty("invoice_material")
        val invoiceMaterial: String? = null,
        /**
         * 用途
         */
        @field:JsonProperty("invoice_purpose")
        val invoicePurpose: String? = null,
    )

    /**
     * 订单体积参数
     */
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @JsonIgnoreProperties(ignoreUnknown = true)
    data class OrderVolumeParam(
        /**
         * 高，单位CM
         */
        @field:JsonProperty("volume_height")
        val volumeHeight: String? = null,
        /**
         * 长，单位CM
         */
        @field:JsonProperty("volume_length")
        val volumeLength: String? = null,
        /**
         * 宽，单位CM
         */
        @field:JsonProperty("volume_width")
        val volumeWidth: String? = null,
        /**
         * 实重
         */
        @field:JsonProperty("volume_weight")
        val volumeWeight: String? = null,
    )
}
