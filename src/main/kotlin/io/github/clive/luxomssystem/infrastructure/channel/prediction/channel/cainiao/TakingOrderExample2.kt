// package io.github.clive.cognitionbank.infrastructure.remote.prediction.channel.cainiao
//
// import org.apache.commons.codec.binary.Base64
// import org.springframework.http.HttpEntity
// import org.springframework.http.HttpHeaders
// import org.springframework.http.MediaType
// import org.springframework.http.ResponseEntity
// import org.springframework.util.LinkedMultiValueMap
// import org.springframework.util.MultiValueMap
// import org.springframework.web.client.RestTemplate
// import java.security.MessageDigest
//
// /**
// * <AUTHOR>
// * @description
// * @date 2021/8/17 7:55 下午
// */
// object TakingOrderExample {
//    // 签名SecretKey为菜鸟为CP生成，菜鸟应用的基本信息中可以获取,https://open.gpn.cainiao.com/
//    // 请注意！！此处需要换成您的SecretKey
//    private const val SECRET_KEY = "6Sb32L5a8A1v331j3215R998zbC85Z2I"
//
//    @JvmStatic
//    fun main(args: Array<String>) {
// //        takingOrder()
//        waybill()
// //        cancelOrder()
//    }
//
//
//    fun takingOrder() {
//        // 请求报文支持XML和JSON两种格式，订阅接口时可以设置请求和响应的报文格式
//        val requestBody ="""
// {
//    "outOrderId": "dev20240417223448681",
//    "syncGetTrackingNumber": true,
//    "solutionParam": {
//        "solutionCode": "CN_GLO_STD",
//        "doorPickupParam": {
//            "name": "when",
//            "mobilePhone": "13355667788",
//            "zipCode": "311100",
//            "countryCode": "CN",
//            "state": "浙江省",
//            "city": "杭州市",
//            "district": "余杭区",
//            "detailAddress": "余杭街道凤新路501号"
//        }
//    },
//    "packageParams": {
//        "packageParam": {
//            "weight": "100",
//            "itemParams": {
//                "itemParam": {
//                    "quantity": 1,
//                    "englishName": "clothes",
//                    "unitPrice": 100,
//                    "unitPriceCurrency": "USD"
//                }
//            }
//        }
//    },
//    "senderParam": {
//        "name": "when",
//        "mobilePhone": "13355667788",
//        "zipCode": "311100",
//        "countryCode": "CN",
//        "state": "浙江省",
//        "city": "杭州市",
//        "district": "余杭区",
//        "detailAddress": "余杭街道凤新路501号"
//    },
//    "receiverParam": {
//        "name": "Trinity",
//        "telephone": "6477810669",
//        "zipCode": "M2N1P2",
//        "countryCode": "CA",
//        "state": "Ontario",
//        "city": "Toronto",
//        "district": "",
//        "detailAddress": "127+Harlandale+Ave"
//    },
//    "returnerParam": {
//        "name": "when",
//        "mobilePhone": "13355667788",
//        "zipCode": "311100",
//        "countryCode": "CN",
//        "state": "浙江省",
//        "city": "杭州市",
//        "district": "余杭区",
//        "detailAddress": "余杭街道凤新路501号"
//    },
//    "features":{
//        "productSkuCode":"SKU001",
//    }
// }
//        """.trimIndent()
//
//        val restTemplate: RestTemplate = RestTemplate()
//        // 该地址为联调地址，正式下单时请切换为正式环境地址 https://link.cainiao.com/gateway/link.do
//        val url = "https://link.cainiao.com/gateway/link.do"
//        val headers = HttpHeaders()
//        headers.contentType = MediaType.APPLICATION_FORM_URLENCODED
//
//        val map: MultiValueMap<String, String> = LinkedMultiValueMap()
//        // 消息类型(即API名称)
//        map.add("msg_type", "cnge.order.create")
//
//        // 来源CP编号(即控制台上应用的资源编码code，https://open.gpn.cainiao.com/)
//        // 请注意！！此处需要换成您的资源编码code
//        map.add("logistic_provider_id", "5ee524506ec63cf351db9c121343cbfb")
//
//        // 请求签名（通过控制台产生的签名秘钥、报文内容计算得到)
//        map.add("data_digest", md5(requestBody, SECRET_KEY, "utf-8"))
//
//        //请求报文内容（API对应的XML或JSON格式的报文）
//        map.add("logistics_interface", requestBody)
//
//        //目的方编码(参考对接文档的接口说明)
//        map.add("to_code", "CNGCP-OPEN")
//
//        val request = HttpEntity(map, headers)
//        println("请求头  " + request.headers)
//        println("请求体  " + request.body)
//        val response: ResponseEntity<String> = restTemplate.postForEntity(url, request, String::class.java)
//        println(response.body)
//    }
//
//    fun waybill() {
//        val requestBody = """
//            {
//            	"orderCode":"LP00678070207985",
//            	"locale":"zh_CN"
//            }
//        """.trimIndent()
//
//
//        val restTemplate = RestTemplate()
//        // 该地址为联调地址，正式下单时请切换为正式环境地址 https://link.cainiao.com/gateway/link.do
//        val url = "https://link.cainiao.com/gateway/link.do"
//        val headers = HttpHeaders()
//        headers.contentType = MediaType.APPLICATION_FORM_URLENCODED
//
//        val map: MultiValueMap<String, String> = LinkedMultiValueMap()
//        // 消息类型(即API名称)
//        map.add("msg_type", "cnge.waybill.get")
//
//        // 来源CP编号(即控制台上应用的资源编码code，https://open.gpn.cainiao.com/)
//        // 请注意！！此处需要换成您的资源编码code
//        map.add("logistic_provider_id", "5ee524506ec63cf351db9c121343cbfb")
//
//        // 请求签名（通过控制台产生的签名秘钥、报文内容计算得到)
//        map.add("data_digest", md5(requestBody, SECRET_KEY, "utf-8"))
//
//        //请求报文内容（API对应的XML或JSON格式的报文）
//        map.add("logistics_interface", requestBody)
//
//        //目的方编码(参考对接文档的接口说明)
//        map.add("to_code", "CGOP")
//
//        val request = HttpEntity(map, headers)
//        println("请求头  " + request.headers)
//        println("请求体  " + request.body)
//        val response: ResponseEntity<String> = restTemplate.postForEntity(url, request, String::class.java)
//        println(response.body)
//    }
//
//    fun cancelOrder() {
//        val requestBody = """
//            {
//            	"orderCode":"LP00676642630383",
//            	"locale":"zh_CN"
//            }
//        """.trimIndent()
//
//        val restTemplate = RestTemplate()
//        // 该地址为联调地址，正式下单时请切换为正式环境地址 https://link.cainiao.com/gateway/link.do
//        val url = "https://link.cainiao.com/gateway/link.do"
//        val headers = HttpHeaders()
//        headers.contentType = MediaType.APPLICATION_FORM_URLENCODED
//
//        val map: MultiValueMap<String, String> = LinkedMultiValueMap()
//        // 消息类型(即API名称)
//        map.add("msg_type", "cnge.order.cancel")
//
//        // 来源CP编号(即控制台上应用的资源编码code，https://open.gpn.cainiao.com/)
//        // 请注意！！此处需要换成您的资源编码code
//        map.add("logistic_provider_id", "5ee524506ec63cf351db9c121343cbfb")
//
//        // 请求签名（通过控制台产生的签名秘钥、报文内容计算得到)
//        map.add("data_digest", md5(requestBody, SECRET_KEY, "utf-8"))
//
//        //请求报文内容（API对应的XML或JSON格式的报文）
//        map.add("logistics_interface", requestBody)
//
//        //目的方编码(参考对接文档的接口说明)
//        map.add("to_code", "CNGCP-OPEN")
//
//        val request = HttpEntity(map, headers)
//        println("请求头  " + request.headers)
//        println("请求体  " + request.body)
//        val response: ResponseEntity<String> = restTemplate.postForEntity(url, request, String::class.java)
//        println(response.body)
//    }
//
//    fun md5(content: String, keys: String, charset: String): String {
//        var content = content
//        var sign: String? = null
//        content = content + keys
//        try {
//            val md = MessageDigest.getInstance("MD5")
//            md.update(content.toByteArray(charset(charset)))
//            sign = String(Base64.encodeBase64(md.digest()), charset(charset))
//        } catch (e: Exception) {
//            throw RuntimeException(e)
//        }
//        return sign
//    }
// }
