package io.github.clive.luxomssystem.infrastructure.repository.jpa

import io.github.clive.luxomssystem.common.enums.WaybillChannel
import io.github.clive.luxomssystem.domain.channel.Channel
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.stereotype.Repository

@Repository
interface ChannelRepository : JpaRepository<Channel, Long> {
    fun findByEnabled(status: Boolean): List<Channel>

    fun findByMethodCode(methodCode: String): Channel?

    fun findByNameAndMethodCode(name: WaybillChannel, methodCode: String): Channel?
}
