package io.github.clive.luxomssystem.infrastructure.config.web

import io.github.clive.luxomssystem.infrastructure.config.auth.ApiSignatureInterceptor
import io.github.clive.luxomssystem.infrastructure.config.auth.OpenapiSignatureInterceptor
import org.springframework.stereotype.Component
import org.springframework.web.servlet.config.annotation.CorsRegistry
import org.springframework.web.servlet.config.annotation.InterceptorRegistry
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer

@Component
class WebConfig(
    private val apiSignatureInterceptor: ApiSignatureInterceptor,
    private val openapiSignatureInterceptor: OpenapiSignatureInterceptor,
) : WebMvcConfigurer {
    override fun addInterceptors(registry: InterceptorRegistry) {
        registry
            .addInterceptor(apiSignatureInterceptor)
            .addPathPatterns("/**")
            .excludePathPatterns("/api/v1/users/login", "/google/callback", "/test/**", "/api/openapi/**")

        registry.addInterceptor(openapiSignatureInterceptor).addPathPatterns("/api/openapi/**")
    }

    override fun addCorsMappings(registry: CorsRegistry) {
        registry
            .addMapping("/**")
            .allowedOrigins("*")
            .allowedMethods("*")
            .allowedHeaders("*")
    }
}
