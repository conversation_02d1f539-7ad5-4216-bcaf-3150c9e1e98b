package io.github.clive.luxomssystem.infrastructure.channel.prediction.channel.fpx

import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import com.fasterxml.jackson.annotation.JsonProperty
import java.math.BigDecimal

data class FpxApiRequest(
    @JsonProperty("ref_no") val refNo: String? = null,
    @JsonProperty("business_type") val businessType: String? = null,
    @JsonProperty("duty_type") val dutyType: String? = null,
    @JsonProperty("cargo_type") val cargoType: String? = null,
    @JsonProperty("vat_no") val vatNo: String? = null,
    @JsonProperty("eori_no") val eoriNo: String? = null,
    @JsonProperty("buyer_id") val buyerId: String? = null,
    @JsonProperty("sales_platform") val salesPlatform: String? = null,
    @JsonProperty("seller_id") val sellerId: String? = null,
    @JsonProperty("logistics_service_info")
    val logisticsServiceInfo: LogisticsServiceInfo? = null,
    @JsonProperty("label_barcode") val labelBarcode: String? = null,
    @JsonProperty("return_info") val returnInfo: ReturnInfo? = null,
    @JsonProperty("parcel_list") val parcelList: List<Parcel>? = null,
    @JsonProperty("is_insure") val isInsure: String? = null,
    @JsonProperty("insurance_info") val insuranceInfo: InsuranceInfo? = null,
    @JsonProperty("sender") val sender: Address? = null,
    @JsonProperty("recipient_info") val recipientInfo: Address? = null,
    @JsonProperty("deliver_type_info") val deliverTypeInfo: DeliverTypeInfo? = null,
    @JsonProperty("label_config_info") val labelConfigInfo: LabelConfigInfo? = null,
    @JsonProperty("currency_freight") val currencyFreight: String? = null,
)

data class LogisticsServiceInfo(
    @JsonProperty("logistics_product_code") val logisticsProductCode: String? = null,
    @JsonProperty("customs_service") val customsService: String? = null,
    @JsonProperty("signature_service") val signatureService: String? = null,
    @JsonProperty("value_added_services") val valueAddedServices: String? = null,
)

data class ReturnInfo(
    @JsonProperty("is_return_on_domestic") val isReturnOnDomestic: String? = null,
    @JsonProperty("domestic_return_addr") val domesticReturnAddr: Address? = null,
    @JsonProperty("is_return_on_oversea") val isReturnOnOversea: String? = null,
    @JsonProperty("oversea_return_addr") val overseaReturnAddr: Address? = null,
)

data class Parcel(
    @JsonProperty("weight") val weight: BigDecimal? = null,
    @JsonProperty("length") val length: Int? = null,
    @JsonProperty("width") val width: Int? = null,
    @JsonProperty("height") val height: Int? = null,
    @JsonProperty("parcel_value") val parcelValue: Double? = null,
    @JsonProperty("currency") val currency: String? = null,
    @JsonProperty("include_battery") val includeBattery: String? = null,
    @JsonProperty("battery_type") val batteryType: String? = null,
    @JsonProperty("product_list") val productList: List<Product>? = null,
    @JsonProperty("declare_product_info")
    val declareProductInfo: List<DeclareProductInfo>? = null,
)

data class Product(
    @JsonProperty("sku_code") val skuCode: String? = null,
    @JsonProperty("standard_product_barcode") val standardProductBarcode: String? = null,
    @JsonProperty("product_name") val productName: String? = null,
    @JsonProperty("product_description") val productDescription: String? = null,
    @JsonProperty("product_unit_price") val productUnitPrice: Double? = null,
    @JsonProperty("currency") val currency: String? = null,
    @JsonProperty("qty") val qty: Int? = null,
)

data class DeclareProductInfo(
    @JsonProperty("declare_product_code") val declareProductCode: String? = null,
    @JsonProperty("declare_product_name_cn") val declareProductNameCn: String? = null,
    @JsonProperty("declare_product_name_en") val declareProductNameEn: String? = null,
    @JsonProperty("uses") val uses: String? = null,
    @JsonProperty("specification") val specification: String? = null,
    @JsonProperty("component") val component: String? = null,
    @JsonProperty("unit_net_weight") val unitNetWeight: Int? = null,
    @JsonProperty("unit_gross_weight") val unitGrossWeight: Int? = null,
    @JsonProperty("material") val material: String? = null,
    @JsonProperty("declare_product_code_qty") val declareProductCodeQty: Int? = null,
    @JsonProperty("unit_declare_product") val unitDeclareProduct: String? = null,
    @JsonProperty("origin_country") val originCountry: String? = null,
    @JsonProperty("country_export") val countryExport: String? = null,
    @JsonProperty("country_import") val countryImport: String? = null,
    @JsonProperty("hscode_export") val hscodeExport: String? = null,
    @JsonProperty("hscode_import") val hscodeImport: String? = null,
    @JsonProperty("declare_unit_price_export") val declareUnitPriceExport: Double? = null,
    @JsonProperty("currency_export") val currencyExport: String? = null,
    @JsonProperty("declare_unit_price_import") val declareUnitPriceImport: Double? = null,
    @JsonProperty("currency_import") val currencyImport: String? = null,
    @JsonProperty("brand_export") val brandExport: String? = null,
    @JsonProperty("brand_import") val brandImport: String? = null,
    @JsonProperty("sales_url") val salesUrl: String? = null,
    @JsonProperty("package_remarks") val packageRemarks: String? = null,
)

data class InsuranceInfo(
    @JsonProperty("insure_type") val insureType: String? = null,
    @JsonProperty("insure_value") val insureValue: Double? = null,
    @JsonProperty("currency") val currency: String? = null,
    @JsonProperty("insure_person") val insurePerson: String? = null,
    @JsonProperty("certificate_type") val certificateType: String? = null,
    @JsonProperty("certificate_no") val certificateNo: String? = null,
    @JsonProperty("category_code") val categoryCode: String? = null,
    @JsonProperty("insure_product_name") val insureProductName: String? = null,
    @JsonProperty("package_qty") val packageQty: String? = null,
)

data class Address(
    @JsonProperty("first_name") val firstName: String? = null,
    @JsonProperty("last_name") val lastName: String? = null,
    @JsonProperty("company") val company: String? = null,
    @JsonProperty("phone") val phone: String? = null,
    @JsonProperty("phone2") val phone2: String? = null,
    @JsonProperty("email") val email: String? = null,
    @JsonProperty("post_code") val postCode: String? = null,
    @JsonProperty("country") val country: String? = null,
    @JsonProperty("state") val state: String? = null,
    @JsonProperty("city") val city: String? = null,
    @JsonProperty("district") val district: String? = null,
    @JsonProperty("street") val street: String? = null,
    @JsonProperty("house_number") val houseNumber: String? = null,
    @JsonProperty("certificate_info") val certificateInfo: CertificateInfo? = null,
)

data class CertificateInfo(
    @JsonProperty("certificate_type") val certificateType: String? = null,
    @JsonProperty("certificate_no") val certificateNo: String? = null,
    @JsonProperty("id_front_url") val idFrontUrl: String? = null,
    @JsonProperty("id_back_url") val idBackUrl: String? = null,
)

data class DeliverTypeInfo(
    @JsonProperty("deliver_type") val deliverType: String? = null,
    @JsonProperty("warehouse_code") val warehouseCode: String? = null,
    @JsonProperty("pick_up_info") val pickUpInfo: PickUpInfo? = null,
    @JsonProperty("express_to_4px_info") val expressTo4pxInfo: ExpressTo4pxInfo? = null,
    @JsonProperty("self_send_to_4px_info") val selfSendTo4pxInfo: SelfSendTo4pxInfo? = null,
)

data class PickUpInfo(
    @JsonProperty("expect_pick_up_earliest_time") val expectPickUpEarliestTime: String? = null,
    @JsonProperty("expect_pick_up_latest_time") val expectPickUpLatestTime: String? = null,
    @JsonProperty("pick_up_address_info") val pickUpAddressInfo: Address? = null,
)

data class ExpressTo4pxInfo(
    @JsonProperty("express_company") val expressCompany: String? = null,
    @JsonProperty("tracking_no") val trackingNo: String? = null,
)

data class SelfSendTo4pxInfo(
    @JsonProperty("booking_earliest_time") val bookingEarliestTime: String? = null,
    @JsonProperty("booking_latest_time") val bookingLatestTime: String? = null,
)

data class LabelConfigInfo(
    @JsonProperty("label_size") val labelSize: String? = null,
    @JsonProperty("response_label_format") val responseLabelFormat: String? = null,
    @JsonProperty("create_logistics_label") val createLogisticsLabel: String? = null,
    @JsonProperty("logistics_label_config")
    val logisticsLabelConfig: LogisticsLabelConfig? = null,
    @JsonProperty("create_package_label") val createPackageLabel: String? = null,
)

data class LogisticsLabelConfig(
    @JsonProperty("is_print_time") val isPrintTime: String? = null,
    @JsonProperty("is_print_buyer_id") val isPrintBuyerId: String? = null,
    @JsonProperty("is_print_pick_info") val isPrintPickInfo: String? = null,
)

@JsonIgnoreProperties(ignoreUnknown = true)
data class FpxApiResponse(
    @JsonProperty("result") val result: String?,
    @JsonProperty("msg") val msg: String?,
    @JsonProperty("errors") val errors: List<FpxErrors>?,
    @JsonProperty("data") val data: FpxApiResponseData?,
) {
    fun onSuccess(): Boolean = result == "1"
}

data class FpxErrors(
    @JsonProperty("error_code") val errorCode: String?,
    @JsonProperty("error_msg") val errorMsg: String?,
    @JsonProperty("reference_code") val referenceCode: String?,
)

@JsonIgnoreProperties(ignoreUnknown = true)
data class FpxApiResponseData(
    @JsonProperty("ds_consignment_no") val dsConsignmentNo: String?,
    @JsonProperty("4px_tracking_no") val fpxTrackingNo: String?,
    @JsonProperty("label_barcode") val labelBarcode: String?,
    @JsonProperty("logistics_channel_name") val logisticsChannelName: String?,
    @JsonProperty("ref_no") val refNo: String?,
    @JsonProperty("logistics_channel_no") val logisticsChannelNo: String?,
    @JsonProperty("label_url_info") var labelUrlInfo: FpxLabelUrlInfo?,
)

data class FpxLabelUrlInfo(
    // url link
    @JsonProperty("logistics_label") var logisticsLabel: String?,
//        @JsonProperty("custom_label") var customLabel: String?,
//        @JsonProperty("package_label") var packageLabel: String?
)
