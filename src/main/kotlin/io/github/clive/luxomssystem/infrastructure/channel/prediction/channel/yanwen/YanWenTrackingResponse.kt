package io.github.clive.luxomssystem.infrastructure.channel.prediction.channel.yanwen

import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import com.fasterxml.jackson.annotation.JsonProperty

/**
 * 燕文物流轨迹查询响应
 * API文档: http://api.track.yw56.com.cn/api/tracking?nums=物流单号
 */
@JsonIgnoreProperties(ignoreUnknown = true)
data class YanWenTrackingResponse(
    @JsonProperty("code")
    var code: Int?,
    @JsonProperty("message")
    var message: String?,
    @JsonProperty("result")
    var result: List<TrackingResult>?,
    @JsonProperty("requestTime")
    var requestTime: String?,
    @JsonProperty("elapsedMilliseconds")
    var elapsedMilliseconds: ElapsedTime?,
) {
    /**
     * 轨迹查询结果
     */
    @JsonIgnoreProperties(ignoreUnknown = true)
    data class TrackingResult(
        @JsonProperty("tracking_number")
        var trackingNumber: String?,
        @JsonProperty("waybill_number")
        var waybillNumber: String?,
        /**
         * 尾程单号
         */
        @JsonProperty("exchange_number")
        var exchangeNumber: String?,
        /**
         * 末端服务商名称
         */
        @JsonProperty("last_mile_carrier")
        var lastMileCarrier: String?,
        @JsonProperty("last_mile_carrier_website")
        var lastMileCarrierWebsite: String?,
        @JsonProperty("last_mile_carrier_contact_number")
        var lastMileCarrierContactNumber: String?,
        @JsonProperty("checkpoints")
        var checkpoints: List<Checkpoint>?,
        @JsonProperty("tracking_status")
        var trackingStatus: String?,
        @JsonProperty("tracking_status_waybill")
        var trackingStatusWaybill: TrackingStatusWaybill?,
        @JsonProperty("last_mile_tracking_expected")
        var lastMileTrackingExpected: Boolean?,
        @JsonProperty("origin_country")
        var originCountry: String?,
        @JsonProperty("destination_country")
        var destinationCountry: String?,
    )

    /**
     * 轨迹检查点/节点
     */
    @JsonIgnoreProperties(ignoreUnknown = true)
    data class Checkpoint(
        @JsonProperty("time_stamp")
        var timeStamp: String?,
        @JsonProperty("time_zone")
        var timeZone: String?,
        @JsonProperty("tracking_status")
        var trackingStatus: String?,
        @JsonProperty("message")
        var message: String?,
        @JsonProperty("location")
        var location: String?,
        @JsonProperty("is_last_mile_checkpoint")
        var isLastMileCheckpoint: Int?,
        @JsonProperty("extraProperties")
        var extraProperties: ExtraProperties?,
    )

    /**
     * 扩展属性
     */
    @JsonIgnoreProperties(ignoreUnknown = true)
    data class ExtraProperties(
        @JsonProperty("FlightNumber")
        var flightNumber: String?,
        @JsonProperty("City")
        var city: String?,
        @JsonProperty("State")
        var state: String?,
        @JsonProperty("PostCode")
        var postCode: String?,
        @JsonProperty("Country")
        var country: String?,
        @JsonProperty("Attached")
        var attached: String?,
        @JsonProperty("TrackingStatusDetail")
        var trackingStatusDetail: String?,
    )

    /**
     * 包裹状态
     */
    @JsonIgnoreProperties(ignoreUnknown = true)
    data class TrackingStatusWaybill(
        @JsonProperty("level1")
        var level1: String?,
        @JsonProperty("level2")
        var level2: String?,
        @JsonProperty("level3")
        var level3: String?,
    )

    /**
     * 响应时长
     */
    @JsonIgnoreProperties(ignoreUnknown = true)
    data class ElapsedTime(
        @JsonProperty("total")
        var total: Int?,
    )
}
