package io.github.clive.luxomssystem.infrastructure.repository.jpa

import io.github.clive.luxomssystem.domain.doanload.OrderImageTask
import io.github.clive.luxomssystem.domain.doanload.OrderImageTaskStatus
import org.springframework.data.domain.Page
import org.springframework.data.domain.Pageable
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.jpa.repository.Query

@org.springframework.stereotype.Repository
interface OrderImageTaskRepository : JpaRepository<OrderImageTask, Long> {
    @Query(
        """
        SELECT t.status as status, COUNT(t.id) as count  
        FROM OrderImageTask t  
        WHERE t.orderId = :mainOrderId
        GROUP BY t.status
                    """,
    )
    fun countByMainOrderId(mainOrderId: Long): List<Map<String, Any>>

    @Query(
        """
        SELECT NEW map(t.errorMessage as errorMessage, STRING_AGG(t.orderNo, ',') as orderNos)
        FROM OrderImageTask t
        WHERE t.orderId = :orderId AND t.status = 'FAILED'
        GROUP BY t.errorMessage
    """,
    )
    fun findFailedTasksByOrderId(orderId: Long): List<Map<String, String>>

    @Query(
        """
        SELECT t
        FROM OrderImageTask t
        WHERE
        1=1 and
        t.bizId = :bizId and
         (:status IS NULL OR t.status = :status) AND (:orderNo IS NULL OR t.orderNo in :orderNo)
        AND (:orderId IS NULL OR t.orderId = :orderId)
        ORDER BY t.createdAt DESC
        """,
        queryRewriter = io.github.clive.luxomssystem.infrastructure.config.AuthRewriter::class,
    )
    fun findByStatus(
        status: OrderImageTaskStatus?,
        orderNo: List<String>?,
        orderId: Long?,
        bizId: Long,
        pageable: Pageable,
    ): Page<OrderImageTask>

    fun findByOrderIdAndStatus(
        orderId: Long,
        status: OrderImageTaskStatus,
    ): List<OrderImageTask>

    @Query(
        """
            select t.orderNo from OrderImageTask t where t.orderNo in :orderNo and t.bizId = :bizId
        """,
    )
    fun findByOrderNoIn(
        orderNo: List<String>,
        bizId: Long,
    ): List<String>

    fun findByOrderNo(orderNo: String): List<OrderImageTask>

    fun deleteByOrderId(orderId: Long)
}
