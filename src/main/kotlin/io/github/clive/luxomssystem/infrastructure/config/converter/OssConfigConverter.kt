package io.github.clive.luxomssystem.infrastructure.config.converter

import com.fasterxml.jackson.annotation.JsonInclude
import com.fasterxml.jackson.databind.DeserializationFeature
import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import io.github.clive.luxomssystem.domain.doanload.auth.SystemConfig
import jakarta.persistence.AttributeConverter
import jakarta.persistence.Converter

@Converter
class OssConfigConverter : AttributeConverter<SystemConfig.OssConfig, String> {
    private val mapper =
        jacksonObjectMapper().apply {
            configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false)
            setSerializationInclusion(JsonInclude.Include.NON_NULL)
        }

    override fun convertToDatabaseColumn(attribute: SystemConfig.OssConfig): String = mapper.writeValueAsString(attribute)

    override fun convertToEntityAttribute(dbData: String): SystemConfig.OssConfig =
        mapper.readValue(dbData, SystemConfig.OssConfig::class.java)
}
