package io.github.clive.luxomssystem.infrastructure.channel.cos

import com.qcloud.cos.COSClient
import com.qcloud.cos.ClientConfig
import com.qcloud.cos.auth.BasicCOSCredentials
import com.qcloud.cos.http.HttpProtocol
import com.qcloud.cos.model.ListObjectsRequest
import com.qcloud.cos.model.ObjectMetadata
import com.qcloud.cos.model.PutObjectRequest
import com.qcloud.cos.model.ciModel.common.MediaOutputObject
import com.qcloud.cos.model.ciModel.job.FileProcessJobResponse
import com.qcloud.cos.model.ciModel.job.FileProcessJobType
import com.qcloud.cos.model.ciModel.job.FileProcessRequest
import io.github.clive.luxomssystem.domain.doanload.auth.SystemConfig
import io.github.clive.luxomssystem.infrastructure.config.auth.UserContextHolder
import io.github.clive.luxomssystem.infrastructure.repository.jpa.SystemConfigRepository
import io.github.oshai.kotlinlogging.KotlinLogging
import jakarta.annotation.PostConstruct
import kotlinx.coroutines.async
import kotlinx.coroutines.awaitAll
import kotlinx.coroutines.coroutineScope
import org.springframework.stereotype.Component
import java.io.InputStream

@Component
class CosRemoteService(
    private val systemConfigRepository: SystemConfigRepository,
) {
    private lateinit var ossClients: Map<Long, SystemConfig.OssConfig>

    @PostConstruct
    fun initOss() {
        ossClients =
            systemConfigRepository
                .findByType("COS")
                .associateBy { it.bizId }
                .mapValues { createCosClient(it.value.content) }
        println("[init ossClient:]" + ossClients.map { it.key }.joinToString(":"))
    }

    private fun createCosClient(ossConfig: SystemConfig.OssConfig): SystemConfig.OssConfig {
        with(ossConfig) {
            val cred = BasicCOSCredentials(tmpSecretId, tmpSecretKey)
            val clientConfig = ClientConfig(region)
            clientConfig.httpProtocol = HttpProtocol.https
            ossConfig.cosClient = COSClient(cred, clientConfig)
        }
        return ossConfig
    }

    fun flushOssClient() {
        val newOssClients =
            systemConfigRepository
                .findByType("COS")
                .associateBy { it.bizId }
                .mapValues { createCosClient(it.value.content) }
        ossClients = newOssClients
        println("[flush ossClient:]" + ossClients.map { it.key }.joinToString(":"))
    }

    /**
     * https://www.tencentcloud.com/zh/document/product/436/54269
     */
    fun packageImagesFromCOS(
        bizId: Long,
        folder: String,
        order: String,
        zipPath: String,
    ): String {
        val ossConfig = ossClients[bizId] ?: throw RuntimeException("bizId not found")
        with(ossConfig) {
            val request = FileProcessRequest()
            request.bucketName = bucketName
            request.tag = FileProcessJobType.FileCompress

            val fileCompressConfig = request.operation.fileCompressConfig
            fileCompressConfig.format = "zip"
            // 0：不需要去除目录结构，打包后压缩包中的文件会保留原有的目录结构
            // 1：需要，打包后压缩包内的文件会去除原有的目录结构，所有文件都在同一层级。
            fileCompressConfig.flatten = "0"
            fileCompressConfig.prefix = folder

            val output = request.operation.output
            output.bucket = bucketName
            output.region = region.regionName
            output.`object` = zipPath + "/$order.zip"

            // 发起文件压缩任务
            val response: FileProcessJobResponse = cosClient!!.createFileProcessJob(request)

            // 获取任务 ID
            return response.jobDetail.jobId
        }
    }

    fun queryFileProcessJob(
        bizId: Long,
        jobId: String?,
    ): String? {
        val ossConfig = ossClients[bizId] ?: throw RuntimeException("bizId not found")
        with(ossConfig) {
            val request = FileProcessRequest()
            request.bucketName = bucketName
            request.jobId = jobId
            try {
                val response = cosClient!!.describeFileProcessJob(request)
                if (response.jobDetail.code == "Success") {
                    val objectUrl: String = getObjectUrl(response.jobDetail.operation.output)
                    log.info { "objectUrl: $objectUrl" }
                    return domain + response.jobDetail.operation.output.`object`
                } else {
                    log.error { "failed to query file process job ：${response.jobDetail.message}" }
                    return null
                }
            } catch (e: Exception) {
                log.error(e) { "failed to query file process job" }
                throw RuntimeException("failed to query file process job")
            }
        }
    }

    suspend fun listFilesInDirectoriesConcurrently(
        directoryPaths: List<String>,
        bizId: Long,
    ): List<String> =
        coroutineScope {
            directoryPaths
                .map { directoryPath ->
                    async {
                        listFilesInDirectory(directoryPath, bizId)
                    }
                }.awaitAll()
                .flatten()
        }

    fun listFilesInDirectory(
        directoryPath: String,
        bizId: Long,
    ): List<String> {
        val ossConfig = ossClients[bizId] ?: throw RuntimeException("bizId not found")
        with(ossConfig) {
            val listObjectsRequest = ListObjectsRequest()
            listObjectsRequest.bucketName = bucketName
            listObjectsRequest.prefix = directoryPath

            val objectListing = cosClient!!.listObjects(listObjectsRequest)
            return objectListing.objectSummaries.map { it.key }
        }
    }

    fun packageImagesUseKeysFromCOS(
        bizId: Long,
        order: String,
        keyIds: List<String>,
        zipPath: String,
    ): String {
        val ossConfig = ossClients[bizId] ?: throw RuntimeException("bizId not found")
        with(ossConfig) {
            val request = FileProcessRequest()
            request.bucketName = bucketName
            request.tag = FileProcessJobType.FileCompress

            val fileCompressConfig = request.operation.fileCompressConfig
            fileCompressConfig.format = "zip"
            fileCompressConfig.flatten = "3"
            fileCompressConfig.key = keyIds

            val output = request.operation.output
            output.bucket = bucketName
            output.region = region.regionName
            output.`object` = "/" + zipPath + "/$order.zip"

            // 发起文件压缩任务
            val response: FileProcessJobResponse = cosClient!!.createFileProcessJob(request)

            // 获取任务 ID
            return response.jobDetail.jobId
        }
    }

    fun uploadFile(
        key: String,
        inputStream: InputStream,
    ): String {
        val user = UserContextHolder.user!!
        val ossConfig = ossClients[user.bizId] ?: throw RuntimeException("bizId not found")
        with(ossConfig) {
            val putObjectRequest = PutObjectRequest(bucketName, key, inputStream, ObjectMetadata())
            cosClient!!.putObject(putObjectRequest)
            return getStoredUrl(user.bizId, key)
        }
    }

    fun uploadFile(
        bizId: Long,
        key: String,
        inputStream: InputStream,
    ): String {
        val ossConfig = ossClients[bizId] ?: throw RuntimeException("bizId not found")
        with(ossConfig) {
            val putObjectRequest = PutObjectRequest(bucketName, key, inputStream, ObjectMetadata())
            cosClient!!.putObject(putObjectRequest)
            return getStoredUrl(bizId, key)
        }
    }

    fun getStoredUrl(
        bizId: Long,
        path: String,
    ): String {
        val ossConfig = ossClients[bizId] ?: throw RuntimeException("bizId not found")
        return "${ossConfig.domain}$path"
    }

    private fun getObjectUrl(output: MediaOutputObject): String {
        @Suppress("HttpUrlsUsage")
        return "http://" + output.bucket + ".cos." + output.region + ".myqcloud.com/" + output.getObject()
    }

    companion object {
        private val log = KotlinLogging.logger {}
    }
}
