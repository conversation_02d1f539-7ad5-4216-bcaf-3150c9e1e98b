package io.github.clive.luxomssystem.infrastructure.repository.jpa

import io.github.clive.luxomssystem.domain.base.Country
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.jpa.repository.Query
import org.springframework.stereotype.Repository

@Repository
interface CountryRepository : JpaRepository<Country, Long> {
    fun findByIsoAlphaTwo(isoAlphaTwo: String): Country?

    @Query(
        """
        select c from Country c
        where c.id < 0
    """,
    )
    fun findSpecial(): List<Country>

    @Query(
        """
        select c from Country c 
        where (lower(c.countryName) = :name 
             or lower(c.isoAlphaTwo) = :name 
             or lower(c.isoAlphaThree) = :name) and c.id > 0
             
    """,
    )
    fun findByName(name: String): Country?
}
