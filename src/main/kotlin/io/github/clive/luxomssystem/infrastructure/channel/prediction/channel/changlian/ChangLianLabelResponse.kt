package io.github.clive.luxomssystem.infrastructure.channel.prediction.channel.changlian

import com.fasterxml.jackson.annotation.JsonProperty

/**
 * 常联获取面单响应
 */
data class ChangLianLabelResponse(
    @JsonProperty("code")
    val code: Int,

    @JsonProperty("msg")
    val msg: String,

    @JsonProperty("data")
    val data: Any?,

    @JsonProperty("labelUrl")
    val labelUrl: String?,

    @JsonProperty("labelList")
    val labelList: Any?,
)
