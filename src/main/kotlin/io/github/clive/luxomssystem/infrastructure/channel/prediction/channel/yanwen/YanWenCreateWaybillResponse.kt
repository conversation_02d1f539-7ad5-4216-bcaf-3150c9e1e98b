package io.github.clive.luxomssystem.infrastructure.channel.prediction.channel.yanwen

import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import com.fasterxml.jackson.annotation.JsonProperty

@JsonIgnoreProperties(ignoreUnknown = true)
data class YanWenCreateWaybillResponse(
    @JsonProperty("code")
    var code: String?,
    @JsonProperty("data")
    var `data`: Data?,
    @JsonProperty("message")
    var message: String?,
    @JsonProperty("success")
    var success: Boolean,
) {
    data class Data(
        @JsonProperty("orderNumber")
        var orderNumber: String?,
        @JsonProperty("referenceNumber")
        var referenceNumber: String?,
        @JsonProperty("waybillNumber")
        var waybillNumber: String?,
        @JsonProperty("yanwenNumber")
        var yanwenNumber: String?,
    )
}
