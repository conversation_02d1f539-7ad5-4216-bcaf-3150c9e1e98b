package io.github.clive.luxomssystem.infrastructure.repository.jpa

import io.github.clive.luxomssystem.common.enums.BaseStatus
import io.github.clive.luxomssystem.domain.Customer
import io.github.clive.luxomssystem.infrastructure.config.AuthRewriter
import org.springframework.data.domain.Page
import org.springframework.data.domain.Pageable
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.jpa.repository.Query
import org.springframework.data.repository.query.Param
import org.springframework.stereotype.Repository

@Repository
interface CustomerRepository : JpaRepository<Customer, Long> {
    fun findByCreatedBy(createdBy: Long): List<Customer>

    fun findByEmailAndBizId(
        email: String,
        bizId: Long,
    ): Customer?

    @Query(
        """
    select u from customers u where 
    1=1 and
    (:name is null or u.name like %:name%) and
    (:#{#statuses.isEmpty()} = true or u.status IN :statuses)
    and (:bizId is null or u.bizId = :bizId)
    order by u.createdAt DESC
""",
        queryRewriter = AuthRewriter::class,
    )
    fun findByNameLikeAndStatusInOrderByCreatedAtDesc(
        @Param("name") name: String?,
        @Param("statuses") statuses: List<BaseStatus>,
        pageable: Pageable,
        bizId: Long?,
    ): Page<Customer>

    @Query(
        """
        select u from customers u where  1=1 and u.status = :status and u.bizId = :bizId
    """,
        queryRewriter = AuthRewriter::class,
    )
    fun findByStatusAndBizId(
        status: BaseStatus,
        bizId: Long,
    ): List<Customer>

    fun findByEmail(email: String): List<Customer>

    @Query("select u from customers u where u.bizId = ?1")
    fun findNameByBizId(bizId: Long): List<Customer>

    @Query("select u.name from customers u where u.id = ?1")
    fun findNameById(id: Long): String?

    fun findByIdAndBizId(
        id: Long,
        bizId: Long,
    ): Customer?
}
