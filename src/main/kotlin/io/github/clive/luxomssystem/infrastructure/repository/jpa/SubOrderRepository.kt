package io.github.clive.luxomssystem.infrastructure.repository.jpa

import io.github.clive.luxomssystem.common.enums.SubOrderStatus
import io.github.clive.luxomssystem.domain.SubOrder
import io.github.clive.luxomssystem.infrastructure.config.AuthRewriter
import org.springframework.data.domain.Page
import org.springframework.data.domain.Pageable
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.jpa.repository.Query
import org.springframework.data.repository.query.Param
import org.springframework.stereotype.Repository

@Repository
interface SubOrderRepository : JpaRepository<SubOrder, Long> {
    fun findByParentIdOrderByIdAsc(parentId: Long): List<SubOrder>

    fun findByOrderNo(orderNo: String): SubOrder?

    fun findByOriginId(originId: Long): List<SubOrder>

    fun findByOrderNoIn(orderNos: List<String>): List<SubOrder>

    fun findByShipping_WayBillRelationAndParentId(
        wayBillRelation: String,
        parentId: Long,
    ): List<SubOrder>

    @Query(
        """
        select so from sub_orders so
        where
            1=1 and
            (:orderNo is null or so.orderNo ilike %:orderNo% )
            and (:orderId is null or so.parentId = :orderId)
            and (:customerId is null or so.customerId = :customerId)
            and (:fileName is null or so.fileName ilike %:fileName%)
            and (:createdBy is null or so.createdBy = :createdBy)
            and (:createdAtFrom is null or so.createdAt >= :createdAtFrom)
            and (:createdAtTo is null or so.createdAt <= :createdAtTo)
            and (:customerOrderNo is null or so.customerOrderNo ilike %:customerOrderNo%)
            and (:#{#statues.isEmpty()} = true or so.status IN :statues)
            and (:#{#orderNos.isEmpty()} = true or so.orderNo IN :orderNos)
            and (:#{#country.isEmpty()} = true or so.recipient.country IN :country)
            and (:#{#customerNos.isEmpty()} = true or so.customerOrderNo IN :customerNos)
        order by so.createdAt desc
    """,
        queryRewriter = AuthRewriter::class,
    )
    fun pageQuery(
        orderNo: String?,
        customerId: Long?,
        statues: MutableList<SubOrderStatus>,
        orderId: Long?,
        fileName: String?,
        createdBy: Long?,
        createdAtFrom: Long?,
        createdAtTo: Long?,
        pageable: Pageable,
        orderNos: List<String>? = null,
        country: List<String>? = null,
        customerOrderNo: String?,
        customerNos: List<String>?,
    ): Page<SubOrder>

    @Query(
        """
        select so from sub_orders so
        where
            1=1 and
            (:orderNo is null or so.orderNo ilike %:orderNo% )
            and so.shipping.shipMethod is null
            and (:orderId is null or so.parentId = :orderId)
            and (:customerId is null or so.customerId = :customerId)
            and (:fileName is null or so.fileName ilike %:fileName%)
            and (:createdBy is null or so.createdBy = :createdBy)
            and (:createdAtFrom is null or so.createdAt >= :createdAtFrom)
            and (:createdAtTo is null or so.createdAt <= :createdAtTo)
            and (:customerOrderNo is null or so.customerOrderNo ilike %:customerOrderNo%)
            and (:#{#statues.isEmpty()} = true or so.status IN :statues)
            and (:#{#orderNos.isEmpty()} = true or so.orderNo IN :orderNos)
            and (:#{#country.isEmpty()} = true or so.recipient.country IN :country)
           and (:#{#customerNos.isEmpty()} = true or so.customerOrderNo IN :customerNos)
        order by so.createdAt desc
    """,
        queryRewriter = AuthRewriter::class,
    )
    fun pageQueryWithNoSelectChannel(
        orderNo: String?,
        customerId: Long?,
        statues: MutableList<SubOrderStatus>,
        orderId: Long?,
        fileName: String?,
        createdBy: Long?,
        createdAtFrom: Long?,
        createdAtTo: Long?,
        pageable: Pageable,
        orderNos: List<String>? = null,
        country: List<String>? = null,
        customerOrderNo: String?,
        customerNos: List<String>?,
    ): Page<SubOrder>

    @Query(
        """
        select so from sub_orders so
        where
             so.shipping.shipMethod is not null and
            (:orderNo is null or so.orderNo ilike %:orderNo% )
            and (:orderId is null or so.parentId = :orderId)
            and (:customerId is null or so.customerId = :customerId)
            and (:fileName is null or so.fileName ilike %:fileName%)
            and (:createdBy is null or so.createdBy = :createdBy)
            and (:createdAtFrom is null or so.createdAt >= :createdAtFrom)
            and (:createdAtTo is null or so.createdAt <= :createdAtTo)
            and (:customerOrderNo is null or so.customerOrderNo ilike %:customerOrderNo%)
            and (:#{#statues.isEmpty()} = true or so.status IN :statues)
            and (:#{#orderNos.isEmpty()} = true or so.orderNo IN :orderNos)
            and (:#{#country.isEmpty()} = true or so.recipient.country IN :country)
            and (:#{#customerNos.isEmpty()} = true or so.customerOrderNo IN :customerNos)
        order by so.createdAt desc
    """,
        queryRewriter = AuthRewriter::class,
    )
    fun pageQueryWithSelectChannel(
        orderNo: String?,
        customerId: Long?,
        statues: MutableList<SubOrderStatus>,
        orderId: Long?,
        fileName: String?,
        createdBy: Long?,
        createdAtFrom: Long?,
        createdAtTo: Long?,
        pageable: Pageable,
        orderNos: List<String>? = null,
        country: List<String>? = null,
        customerOrderNo: String?,
        customerNos: List<String>?,
    ): Page<SubOrder>

    fun countByParentIdAndStatusIn(
        parentId: Long,
        statuses: MutableCollection<SubOrderStatus>,
    ): Int

    fun deleteByParentId(parentId: Long)

    @Query(
        """
        SELECT so.status as status, COUNT(so) as count
        FROM sub_orders so
        WHERE so.created_at >= :startDate and so.biz_id = :bizId
        GROUP BY so.status
    """,
        nativeQuery = true,
    )
    fun countSubOrdersByStatusAndTimeRange(
        @Param("startDate") startDate: Long,
        @Param("bizId") bizId: Long,
    ): List<Array<Any>>

    data class SubOrderStatusCount(
        val status: SubOrderStatus,
        val count: Long,
    )

    @Query(
        value = """
            SELECT 
                COUNT(id) AS total,
                SUM(CASE WHEN status = 'FAILED' THEN 1 ELSE 0 END) AS failed,
                SUM(CASE WHEN status = 'CREATED' THEN 1 ELSE 0 END) AS pending,
                SUM(CASE WHEN status NOT IN ('FAILED', 'CREATED') THEN 1 ELSE 0 END) AS success
            FROM sub_orders
            WHERE parent_id = :parentId
        """,
        nativeQuery = true,
    )
    fun getStatsByParentId(
        @Param("parentId") parentId: Long,
    ): Map<String, Any?>

    fun existsByOrderNoIs(orderNo: String): Boolean
}

fun SubOrderRepository.getSubOrderStatusCounts(
    startDate: Long,
    bizId: Long,
): List<SubOrderRepository.SubOrderStatusCount> {
    val results = countSubOrdersByStatusAndTimeRange(startDate, bizId)
    return results.map { row ->
        val status = SubOrderStatus.valueOf(row[0] as String)
        val count = (row[1] as Number).toLong()
        SubOrderRepository.SubOrderStatusCount(status, count)
    }
}
