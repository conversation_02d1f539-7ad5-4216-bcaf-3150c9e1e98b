package io.github.clive.luxomssystem.facade.order.suppliermainorder

import io.github.clive.luxomssystem.application.SupplierMainOrderApplicationService
import io.github.clive.luxomssystem.common.PageResponse
import io.github.clive.luxomssystem.domain.SupplierMainOrder
import io.github.clive.luxomssystem.facade.order.req.SupplierMainOrderPageRequest
import org.springframework.format.annotation.DateTimeFormat
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.RestController
import org.springframework.web.multipart.MultipartFile
import java.time.LocalDate
import java.time.ZoneOffset

@RestController
@RequestMapping("/api/supplier-main-orders")
class SupplierMainOrderController(
    private val supplierMainOrderApplicationService: SupplierMainOrderApplicationService,
) {
    @GetMapping("/page")
    fun pageQuery(page: SupplierMainOrderPageRequest): PageResponse<SupplierMainOrder> = supplierMainOrderApplicationService.page(page)

    @PostMapping("/change-supplier")
    fun changeSupplier(
        @RequestBody req: ChangeSupplierRequest,
    ) {
        supplierMainOrderApplicationService.changeSupplier(req)
    }

    @PostMapping("/export-bill")
    fun exportBill(
        @RequestBody req: SupplierMainOrderExportBillRequest,
    ) {
        supplierMainOrderApplicationService.exportBill(req)
    }

    @PostMapping("/export-bill2")
    fun exportBill(
        @RequestBody req: SupplierMainOrderExportBillRequest2,
    ) {
        supplierMainOrderApplicationService.exportBill(req)
    }

    @PostMapping("/accept/{id}")
    fun accept(
        @PathVariable("id") id: Long,
    ) = supplierMainOrderApplicationService.accept(id)

    @PostMapping("/{id}/upload-attachment")
    fun uploadAttachment(
        @PathVariable("id") id: Long,
        @RequestParam("file") file: MultipartFile,
    ) = supplierMainOrderApplicationService.uploadAttachment(id, file)
}

data class SupplierMainOrderExportBillRequest2(
    val supplierMainOrderIds: List<Long>,
)

data class SupplierMainOrderExportBillRequest(
    val supplierIds: List<Long>,
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    val createdAtFrom: LocalDate,
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    val createdAtTo: LocalDate,
) {
    fun createdAtFromEpochMilli() = createdAtFrom.atStartOfDay().toInstant(ZoneOffset.UTC).toEpochMilli()

    fun createdAtToEpochMilli() =
        createdAtTo
            .plusDays(1)
            .atStartOfDay()
            .toInstant(ZoneOffset.UTC)
            .toEpochMilli()
}

data class ChangeSupplierRequest(
    val orderId: Long,
    val newSupplierId: Long,
)
