package io.github.clive.luxomssystem.facade.common

import io.github.clive.luxomssystem.common.ext.B
import io.github.clive.luxomssystem.common.ext.MiB
import io.github.clive.luxomssystem.common.ext.getExtension
import io.github.clive.luxomssystem.infrastructure.channel.cos.CosInnerRemoteService
import io.github.clive.luxomssystem.infrastructure.config.auth.UserContextHolder
import io.github.oshai.kotlinlogging.KotlinLogging
import okhttp3.OkHttpClient
import okhttp3.Request
import org.springframework.http.HttpHeaders
import org.springframework.http.HttpStatus
import org.springframework.http.MediaType
import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.*
import org.springframework.web.multipart.MultipartFile
import java.io.IOException
import java.util.*

@RestController
@RequestMapping("/api/file")
class FileController(
    private val ossClient: CosInnerRemoteService,
    private val okHttpClient: OkHttpClient,
) {
    @PostMapping("/img:upload")
    fun imgUpload(
        @RequestParam("file") file: MultipartFile,
    ): String {
        check(file.size > 0) { "File is empty" }
        check(!file.originalFilename.isNullOrBlank()) { "File name is empty" }
        check(isValidImageFile(file)) { "Invalid image file" }
        check(file.size.B < 30.MiB) { "File size exceeds 30 MiB" }
        val user = UserContextHolder.user!!
        return ossClient.uploadFile(
            "/spu/img/${user.bizId}/${UUID.randomUUID().toString() + "." + file.originalFilename?.getExtension()}",
            file.inputStream,
        )
    }

    @PostMapping("/attachment:upload")
    fun attachmentUpload(
        @RequestParam("file") file: MultipartFile,
    ): String {
        check(file.size > 0) { "File is empty" }
        check(!file.originalFilename.isNullOrBlank()) { "File name is empty" }
        check(isValidAttachmentFile(file)) { "Invalid attachment file" }
        check(file.size.B < 50.MiB) { "File size exceeds 50 MiB" }
        val user = UserContextHolder.user!!
        return ossClient.uploadFile(
            "/attachments/${user.bizId}/${UUID.randomUUID().toString() + "." + file.originalFilename?.getExtension()}",
            file.inputStream,
        )
    }

    @GetMapping("/proxy-pdf")
    fun proxyPdf(
        @RequestParam url: String,
    ): ResponseEntity<ByteArray> {
        try {
            val request: Request =
                Request
                    .Builder()
                    .url(url)
                    .build()

            okHttpClient.newCall(request).execute().use { response ->
                if (!response.isSuccessful) throw IOException("Unexpected code $response")
                val pdfBytes: ByteArray = response.body?.bytes() ?: throw IOException("Empty response body")

                val headers = HttpHeaders()
                headers.contentType = MediaType.APPLICATION_PDF
                headers.setContentDispositionFormData("filename", "document.pdf")
                return ResponseEntity(pdfBytes, headers, HttpStatus.OK)
            }
        } catch (e: Exception) {
            e.printStackTrace()
            return ResponseEntity(HttpStatus.INTERNAL_SERVER_ERROR)
        }
    }

    @GetMapping("/list/files")
    fun listFiles(): List<String> {
        val listFilesInDirectory =
            ossClient.listFilesInDirectory("/OMS/2024-7-30/76768949647843328/img", UserContextHolder.user!!.bizId)
        val jobId =
            ossClient.packageImagesUseKeysFromCOS(
                UserContextHolder.user!!.bizId,
                "order",
                listFilesInDirectory,
                "/OMS/2024-7-30/76768949647843328/img",
            )
        log.info { "jobId: $jobId" }
        return listFilesInDirectory
    }

    @GetMapping("/fetch/jobs")
    fun fetchJobs(jobId: String): String? = ossClient.queryFileProcessJob(UserContextHolder.user!!.bizId, jobId)

    companion object {
        private val log = KotlinLogging.logger { }

        private val AllowedImgType =
            listOf(
                "image/jpeg",
                "image/png",
                "image/gif",
                "image/bmp",
                "image/tiff",
            )

        fun isValidImageFile(file: MultipartFile): Boolean {
            val contentType = file.contentType
            return contentType != null && AllowedImgType.contains(contentType.lowercase(Locale.getDefault()))
        }

        private val AllowedAttachmentTypes = listOf(
            "application/zip",
            "application/x-rar-compressed",
            "application/x-7z-compressed",
            "application/x-tar",
            "application/gzip",
            "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
            "application/vnd.ms-excel",
            "text/csv",
            "application/pdf",
            "application/msword",
            "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
            "image/jpeg",
            "image/png",
            "image/gif",
            "image/bmp",
            "image/webp",
            "text/plain",
            "application/json",
            "application/xml",
            "text/xml"
        )

        fun isValidAttachmentFile(file: MultipartFile): Boolean {
            val contentType = file.contentType
            val filename = file.originalFilename?.lowercase()

            // 检查MIME类型
            if (contentType != null && AllowedAttachmentTypes.contains(contentType.lowercase(Locale.getDefault()))) {
                return true
            }

            // 检查文件扩展名作为备选方案
            if (filename != null) {
                val supportedExtensions = listOf(
                    ".zip", ".rar", ".7z", ".tar", ".gz",
                    ".xlsx", ".xls", ".csv",
                    ".pdf", ".doc", ".docx",
                    ".jpg", ".jpeg", ".png", ".gif", ".bmp", ".webp",
                    ".txt", ".json", ".xml"
                )
                return supportedExtensions.any { filename.endsWith(it) }
            }

            return false
        }
    }
}
