package io.github.clive.luxomssystem.facade.auth

import io.github.clive.luxomssystem.application.auth.PermissionApplicationService
import io.github.clive.luxomssystem.common.PageResponse
import io.github.clive.luxomssystem.common.toResponse
import io.github.clive.luxomssystem.facade.auth.dto.PermissionRequest
import io.github.clive.luxomssystem.facade.auth.dto.PermissionResponse
import io.github.clive.luxomssystem.facade.auth.dto.toDomain
import org.springframework.data.domain.PageRequest
import org.springframework.http.HttpStatus
import org.springframework.web.bind.annotation.*

@RestController
@RequestMapping("/api/v1/permissions")
class PermissionController(
    private val permissionApplicationService: PermissionApplicationService,
) {
    @GetMapping("/page")
    fun getAllPermissions(
        pageIndex: Int,
        pageSize: Int,
    ): PageResponse<PermissionResponse> =
        permissionApplicationService
            .findAll(PageRequest.of(pageIndex, pageSize))
            .map { PermissionResponse.fromDomain(it) }
            .toResponse()

    @GetMapping("/{id}")
    fun getPermissionById(
        @PathVariable id: Long,
    ): PermissionResponse =
        permissionApplicationService.findById(id)?.let { PermissionResponse.fromDomain(it) }
            ?: throw IllegalArgumentException("Permission not found")

    @GetMapping("/parent/{parentId}")
    fun getPermissionsByParentId(
        @PathVariable parentId: Long,
    ): List<PermissionResponse> = permissionApplicationService.findByParentId(parentId).map { PermissionResponse.fromDomain(it) }

    @GetMapping("/search")
    fun searchPermissions(): List<PermissionResponse> =
        permissionApplicationService.findByNameContaining().map { PermissionResponse.fromDomain(it) }

    @PostMapping
    @ResponseStatus(HttpStatus.CREATED)
    fun createPermission(
        @RequestBody request: PermissionRequest,
    ): PermissionResponse = PermissionResponse.fromDomain(permissionApplicationService.create(request.toDomain()))

    @PutMapping("/{id}")
    fun updatePermission(
        @PathVariable id: Long,
        @RequestBody request: PermissionRequest,
    ): PermissionResponse = PermissionResponse.fromDomain(permissionApplicationService.update(id, request.toDomain()))

    @DeleteMapping("/{id}")
    @ResponseStatus(HttpStatus.NO_CONTENT)
    fun deletePermission(
        @PathVariable id: Long,
    ) = permissionApplicationService.delete(id)
}
