package io.github.clive.luxomssystem.facade.supplierSpu.request

import java.math.BigDecimal

data class UpdateSupplierSpuRequest(
    val supplierSpuCode: String,
    val skus: List<CreateSkuRequest>,
) {
    data class CreateSkuRequest(
        val id: Long,
        val systemSkuId: Long,
        val systemSkuCode: String,
        val size: String,
        val color: String,
        val purchaseCost: BigDecimal,
        val purchaseCostCurrency: String,
    )
}
