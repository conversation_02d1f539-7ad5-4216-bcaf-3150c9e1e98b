package io.github.clive.luxomssystem.facade.download.job

import io.github.clive.luxomssystem.common.enums.MainOrderType
import io.github.clive.luxomssystem.common.enums.order.MainOrderImageDownloadStatus
import io.github.clive.luxomssystem.common.enums.order.MainOrderStatus
import io.github.clive.luxomssystem.domain.doanload.MainOrder
import io.github.clive.luxomssystem.domain.doanload.event.MainOrderEvent
import io.github.clive.luxomssystem.infrastructure.channel.cos.CosRemoteService
import io.github.clive.luxomssystem.infrastructure.repository.jpa.MainOrderRepository
import io.github.oshai.kotlinlogging.KotlinLogging
import org.springframework.context.event.EventListener
import org.springframework.scheduling.annotation.Scheduled
import org.springframework.stereotype.Component
import org.springframework.transaction.annotation.Transactional
import java.time.Instant
import java.time.ZoneId

@Component
class MainOrderCompressTaskJob(
    private val cosRemoteService: CosRemoteService,
    private val mainOrderRepository: MainOrderRepository,
) {
    @Transactional
    @Scheduled(cron = "0 0/1 * * * ?")
    fun compressMainTask() {
        log.info { "Start compress task" }
        mainOrderRepository
            .findByImgDownloadStatus(MainOrderImageDownloadStatus.IMAGE_DOWNED)
            .map {
                handleMainOrderImagCompressTask(it)
                it
            }.also {
                log.info { "Compress task finished" }
                mainOrderRepository.saveAll(it)
            }
    }

    @EventListener(MainOrderEvent.CompressOrderImageTaskEvent::class)
    fun compressMainTask(event: MainOrderEvent.CompressOrderImageTaskEvent) {
        log.info { "Start compress task" }
        mainOrderRepository.findById(event.id).ifPresent {
            handleMainOrderImagCompressTask(it)
            mainOrderRepository.save(it)
        }
        log.info { "Compress task finished" }
    }

    @Transactional
    @Scheduled(cron = "0 0/1 * * * ?")
    fun processCompressMainTask() {
        log.info { "find compress task is completed" }
        mainOrderRepository
            .findByImgDownloadStatus(MainOrderImageDownloadStatus.ZIP_DOING)
            .map {
                handleCheckCompressTask(it)
                it
            }.also {
                log.info { "Compress task finished" }
                mainOrderRepository.saveAll(it)
            }
    }

    private fun handleCheckCompressTask(it: MainOrder) {
        cosRemoteService.queryFileProcessJob(it.bizId, it.compressJobId).let { objectUrl ->
            if (objectUrl != null) {
                it.imgZipDownloadUrl = objectUrl
                it.imgDownloadStatus = MainOrderImageDownloadStatus.ZIP_DONE
                if (it.type == MainOrderType.IMAGE) {
                    it.status = MainOrderStatus.COMPLETED
                }
            }
        }
    }

    private fun handleMainOrderImagCompressTask(it: MainOrder) {
        val path = "/OMS/${currentDate(it.createdAt)}/${it.id}/img"
        val zipPath = "/OMS/${currentDate(it.createdAt)}/${it.id}/zip"
        val packageImagesFromCOS = cosRemoteService.packageImagesFromCOS(it.bizId, path, it.fileName, zipPath)
        it.compressJobId = packageImagesFromCOS
        it.imgDownloadStatus = MainOrderImageDownloadStatus.ZIP_DOING
    }

    fun currentDate(timeMills: Long): String {
        val date = Instant.ofEpochMilli(timeMills).atZone(ZoneId.systemDefault()).toLocalDate()
        val year = date.year
        val month = date.monthValue
        val day = date.dayOfMonth
        return "$year-$month-$day"
    }

    companion object {
        val log = KotlinLogging.logger {}
    }
}
