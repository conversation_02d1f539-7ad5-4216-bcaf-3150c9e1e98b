
package io.github.clive.luxomssystem.facade.customerSku

import io.github.clive.luxomssystem.application.inventory.customerSku.CustomerSkuApplicationService
import io.github.clive.luxomssystem.facade.customerSku.request.CreateCustomerSkuRequest
import io.github.clive.luxomssystem.facade.customerSku.request.UpdateCustomerSkuRequest
import io.github.clive.luxomssystem.facade.customerSku.response.CustomerSkuResponse
import jakarta.validation.Valid
import org.springframework.data.domain.Page
import org.springframework.data.domain.Pageable
import org.springframework.http.HttpStatus
import org.springframework.web.bind.annotation.*

/**
 * REST controller for managing CustomerSku resources.
 */
@RestController
@RequestMapping("/api/customer-skus")
class CustomerSkuController(
    private val customerSkuApplicationService: CustomerSkuApplicationService,
) {
    /**
     * Creates a new CustomerSku.
     */
    @PostMapping
    @ResponseStatus(HttpStatus.CREATED)
    fun createCustomerSku(
        @Valid @RequestBody request: CreateCustomerSkuRequest,
    ): CustomerSkuResponse = customerSkuApplicationService.createCustomerSku(request)

    /**
     * Retrieves a CustomerSku by its ID.
     */
    @GetMapping("/{id}")
    fun getCustomerSku(
        @PathVariable id: Long,
    ): CustomerSkuResponse = customerSkuApplicationService.getCustomerSkuById(id)

    /**
     * Updates an existing CustomerSku.
     */
    @PutMapping("/{id}")
    fun updateCustomerSku(
        @PathVariable id: Long,
        @Valid @RequestBody request: UpdateCustomerSkuRequest,
    ): CustomerSkuResponse = customerSkuApplicationService.updateCustomerSku(id, request)

    /**
     * Deletes a CustomerSku by its ID.
     */
    @DeleteMapping("/{id}")
    @ResponseStatus(HttpStatus.NO_CONTENT)
    fun deleteCustomerSku(
        @PathVariable id: Long,
    ) {
        customerSkuApplicationService.deleteCustomerSku(id)
    }

    /**
     * Retrieves a page of CustomerSku entities.
     */
    @GetMapping
    fun getAllCustomerSkus(pageable: Pageable): Page<CustomerSkuResponse> = customerSkuApplicationService.getAllCustomerSkus(pageable)
}
