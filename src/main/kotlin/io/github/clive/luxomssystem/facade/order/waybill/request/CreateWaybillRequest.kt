
package io.github.clive.luxomssystem.facade.order.waybill.request

import jakarta.validation.constraints.NotBlank
import jakarta.validation.constraints.NotNull
import jakarta.validation.constraints.Positive
import java.math.BigDecimal

data class CreateWaybillRequest(
    @field:NotNull
    val bizId: Long,
    @field:NotNull
    val customerId: Long,
    val supplierId: Long?,
    @field:NotBlank
    val orderNo: String,
    @field:NotNull
    @field:Positive
    val price: BigDecimal,
    @field:NotBlank
    val size: String,
    @field:NotBlank
    val color: String,
    @field:NotBlank
    val skuCode: String,
    @field:NotNull
    @field:Positive
    val qty: Int,
    val fileOssPath: String?,
    @field:NotBlank
    val receiverName: String,
    @field:NotBlank
    val country: String,
    @field:NotBlank
    val state: String,
    @field:NotBlank
    val city: String,
    @field:NotBlank
    val street: String,
    @field:NotBlank
    val postcode: String,
    @field:NotBlank
    val channel: String,
    @field:NotBlank
    val shipMethod: String,
    @field:NotNull
    @field:Positive
    val weight: BigDecimal,
    @field:NotBlank
    val currency: String,
    @field:NotBlank
    val phone: String,
)
