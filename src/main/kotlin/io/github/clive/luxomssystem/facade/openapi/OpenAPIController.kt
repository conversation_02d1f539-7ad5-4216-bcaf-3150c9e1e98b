package io.github.clive.luxomssystem.facade.openapi

import io.github.clive.luxomssystem.application.openapi.OpenAPIApplicationService
import io.github.clive.luxomssystem.facade.openapi.request.CreateOpenAPIOrderRequest
import io.github.clive.luxomssystem.infrastructure.config.auth.CustomerContextHolder
import io.github.oshai.kotlinlogging.KotlinLogging
import jakarta.validation.Valid
import org.springframework.validation.annotation.Validated
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController

@Validated
@RestController
@RequestMapping("/api/openapi")
class OpenAPIController(
    private val openAPIApplicationService: OpenAPIApplicationService,
) {
    @PostMapping("/order")
    fun createOrder(
        @Valid @RequestBody req: CreateOpenAPIOrderRequest,
    ) {
        val user = CustomerContextHolder.user
        log.info { "用户导入订单| User: ${user?.id} | Order: $req" }
        req.validate()
        openAPIApplicationService.createOrder(req)
    }

    companion object {
        private val log = KotlinLogging.logger { }
    }
}
