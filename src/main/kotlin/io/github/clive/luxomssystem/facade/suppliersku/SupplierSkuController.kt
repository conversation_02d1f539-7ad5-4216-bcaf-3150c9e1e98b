
package io.github.clive.luxomssystem.facade.suppliersku

import io.github.clive.luxomssystem.application.inventory.suppliersku.SupplierSkuApplicationService
import io.github.clive.luxomssystem.common.PageReq
import io.github.clive.luxomssystem.common.PageResponse
import io.github.clive.luxomssystem.facade.suppliersku.request.CreateSupplierSkuRequest
import io.github.clive.luxomssystem.facade.suppliersku.request.UpdateSupplierSkuRequest
import io.github.clive.luxomssystem.facade.suppliersku.response.SupplierSkuResponse
import org.springframework.http.HttpStatus
import org.springframework.web.bind.annotation.*

@RestController
@RequestMapping("/api/supplier-skus")
class SupplierSkuController(
    private val supplierSkuApplicationService: SupplierSkuApplicationService,
) {
    @PostMapping
    @ResponseStatus(HttpStatus.CREATED)
    fun createSupplierSku(
        @RequestBody request: CreateSupplierSkuRequest,
    ): SupplierSkuResponse = supplierSkuApplicationService.createSupplierSku(request)

    @PutMapping("/{id}")
    fun updateSupplierSku(
        @PathVariable id: Long,
        @RequestBody request: UpdateSupplierSkuRequest,
    ): SupplierSkuResponse = supplierSkuApplicationService.updateSupplierSku(id, request)

    @GetMapping("/{id}")
    fun getSupplierSku(
        @PathVariable id: Long,
    ): SupplierSkuResponse = supplierSkuApplicationService.getSupplierSku(id)

    @DeleteMapping("/{id}")
    @ResponseStatus(HttpStatus.NO_CONTENT)
    fun deleteSupplierSku(
        @PathVariable id: Long,
    ) {
        supplierSkuApplicationService.deleteSupplierSku(id)
    }

    @GetMapping("/page")
    fun pageSupplierSkus(req: PageReq): PageResponse<SupplierSkuResponse> = supplierSkuApplicationService.pageQuery(req)
}
