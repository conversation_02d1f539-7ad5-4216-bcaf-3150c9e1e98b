package io.github.clive.luxomssystem.facade.order.waybill.request

import jakarta.validation.constraints.NotBlank
import java.math.BigDecimal

data class UpdateWaybillRequest(
    @field:NotBlank
    val receiverName: String,
    @field:NotBlank
    val country: String,
    @field:NotBlank
    val state: String,
    @field:NotBlank
    val city: String,
    @field:NotBlank
    val street: String,
    @field:NotBlank
    val postcode: String,
    @field:NotBlank
    val phone: String,
    val weight: BigDecimal,
    var street2: String? = null,
    val taxNumber: String? = null,
    val hsCode: String? = null,
    val material: String? = null,
    val totalPrice: BigDecimal? = null,
    val name: String? = null,
    val iossNumber: String? = null,
)
