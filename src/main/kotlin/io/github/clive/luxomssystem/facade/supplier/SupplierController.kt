package io.github.clive.luxomssystem.facade.supplier

import io.github.clive.luxomssystem.application.supplier.SupplierApplicationService
import io.github.clive.luxomssystem.common.PageReq
import io.github.clive.luxomssystem.facade.supplier.request.CreateSupplierRequest
import io.github.clive.luxomssystem.facade.supplier.request.UpdateSupplierRequest
import io.github.clive.luxomssystem.facade.supplier.response.SupplierResponse
import org.springframework.http.HttpStatus
import org.springframework.web.bind.annotation.*

@RestController
@RequestMapping("/api/suppliers")
class SupplierController(
    private val supplierApplicationService: SupplierApplicationService,
) {
    @PostMapping
    @ResponseStatus(HttpStatus.CREATED)
    fun createSupplier(
        @RequestBody request: CreateSupplierRequest,
    ): SupplierResponse = supplierApplicationService.createSupplier(request)

    @PutMapping("/{id}")
    fun updateSupplier(
        @PathVariable id: Long,
        @RequestBody request: UpdateSupplierRequest,
    ): SupplierResponse = supplierApplicationService.updateSupplier(id, request)

    @GetMapping("/{id}")
    fun getSupplier(
        @PathVariable id: Long,
    ): SupplierResponse = supplierApplicationService.getSupplier(id)

    @DeleteMapping("/{id}")
    @ResponseStatus(HttpStatus.NO_CONTENT)
    fun deleteSupplier(
        @PathVariable id: Long,
    ) {
        supplierApplicationService.deleteSupplier(id)
    }

    @PostMapping("/{id}/enable")
    fun enableSupplier(
        @PathVariable id: Long,
    ): SupplierResponse = supplierApplicationService.enableSupplier(id)

    @PostMapping("/{id}/disable")
    fun disableSupplier(
        @PathVariable id: Long,
    ): SupplierResponse = supplierApplicationService.disableSupplier(id)

    @GetMapping("/page")
    fun page(page: PageReq) = supplierApplicationService.page(page)

    @GetMapping("/select/list")
    fun list() = supplierApplicationService.list()

    @PostMapping("/priority:change")
    fun changePriority(
        @RequestBody request: ChangePriorityRequest,
    ) {
        supplierApplicationService.changePriority(request)
    }
}

data class ChangePriorityRequest(
    val id: Long,
    val priority: Int,
)
