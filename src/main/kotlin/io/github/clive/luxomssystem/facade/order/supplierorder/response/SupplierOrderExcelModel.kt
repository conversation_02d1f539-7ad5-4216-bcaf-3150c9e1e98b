package io.github.clive.luxomssystem.facade.order.supplierorder.response

import com.alibaba.excel.annotation.ExcelProperty
import com.alibaba.excel.annotation.write.style.ColumnWidth
import com.alibaba.excel.annotation.write.style.ContentRowHeight

@ColumnWidth(35)
@ContentRowHeight(100) // 设置行高为200
data class SupplierOrderExcelModel(
    @ExcelProperty("no")
    var no: String? = null,
    @ExcelProperty("唯一码") var uniqueCode: String? = null,
    @ExcelProperty("sku") var sku: String? = null,
    @ExcelProperty("品名") var productName: String? = null,
    @ExcelProperty("数量") var quantity: Int? = null,
    @ExcelProperty("定制名字") var customName: String? = null,
    @ExcelProperty("备注")
    var detail: String? = null,
    @ExcelProperty("谷歌搜索字段")
    @ColumnWidth(100)
    var googleSearchField: String? = null,
    @ExcelProperty("运单号")
    @ColumnWidth(120)
    var waybillNo: String? = null,
    @ColumnWidth(100)
    @ExcelProperty("通用下载地址")
    var urls: String? = null,
    @ExcelProperty("效果图下载地址")
    @ColumnWidth(100)
    var effectUrl: String? = null,
    @ExcelProperty("设计图下载地址")
    @ColumnWidth(100)
    var designUrl: String? = null,
    @ExcelProperty("效果图")
    @ColumnWidth(100)
    var effectImage: String? = null,
    @ExcelProperty("设计图")
    @ColumnWidth(100)
    var designImage: String? = null,
    @ExcelProperty("姓名") var name: String? = null,
    @ColumnWidth(60)
    @ExcelProperty("地址") var address: String? = null,
    @ExcelProperty("城市") var city: String? = null,
    @ExcelProperty("省州") var state: String? = null,
    @ExcelProperty("国家") var country: String? = null,
    @ExcelProperty("邮编") var postcode: String? = null,
    @ExcelProperty("电话") var phone: String? = null,
    @ExcelProperty("二维码")
    var qrCode: ByteArray? = null,
)
