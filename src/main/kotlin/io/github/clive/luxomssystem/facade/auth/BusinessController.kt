package io.github.clive.luxomssystem.facade.auth

import io.github.clive.luxomssystem.application.auth.BusinessApplicationService
import io.github.oshai.kotlinlogging.KotlinLogging
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController

@RequestMapping("/api/business")
@RestController
class BusinessController(
    private val businessService: BusinessApplicationService,
) {
    data class CreateBusinessRequest(
        val businessName: String,
        val accountName: String,
        val userName: String,
        val accountPassword: String,
        val bucketName: String,
    )

    @PostMapping("")
    fun createBusiness(
        @RequestBody req: CreateBusinessRequest,
    ) {
        log.info { "createBusiness: $req" }
        businessService.create(req)
    }

    companion object {
        private val log = KotlinLogging.logger { }
    }
}
