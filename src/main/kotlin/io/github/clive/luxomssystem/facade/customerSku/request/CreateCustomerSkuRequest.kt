package io.github.clive.luxomssystem.facade.customerSku.request

import io.github.clive.luxomssystem.domain.customerSku.model.CountryPrice
import io.github.clive.luxomssystem.domain.customerSku.model.PcsPrice
import jakarta.validation.constraints.*
import java.math.BigDecimal

/**
 * Request object for creating a new CustomerSku.
 */
data class CreateCustomerSkuRequest(
    val customerSpuId: Long,
    @field:NotNull(message = "Customer ID is required")
    @field:Positive(message = "Customer ID must be positive")
    val customerId: Long,
    @field:NotNull(message = "System SPU ID is required")
    @field:Positive(message = "System SPU ID must be positive")
    val systemSpuId: Long,
    @field:NotNull(message = "System SKU ID is required")
    @field:Positive(message = "System SKU ID must be positive")
    val systemSkuId: Long,
    @field:NotBlank(message = "System SPU code is required")
    @field:Size(max = 255, message = "System SPU code must not exceed 255 characters")
    val systemSpuCode: String,
    @field:NotBlank(message = "System SKU code is required")
    @field:Size(max = 255, message = "System SKU code must not exceed 255 characters")
    val systemSkuCode: String,
    @field:Size(max = 255, message = "Customer SPU code must not exceed 255 characters")
    val customerSpuCode: String? = null,
    @field:Size(max = 255, message = "Customer SKU code must not exceed 255 characters")
    val customerSkuCode: String? = null,
    @field:NotBlank(message = "Size is required")
    @field:Size(max = 50, message = "Size must not exceed 50 characters")
    val size: String,
    @field:NotBlank(message = "Color is required")
    @field:Size(max = 50, message = "Color must not exceed 50 characters")
    val color: String,
    @field:NotNull(message = "Offline price is required")
    @field:Positive(message = "Offline price must be positive")
    val offlinePrice: BigDecimal,
    @field:NotBlank(message = "Offline price currency is required")
    @field:Size(max = 3, message = "Offline price currency must not exceed 3 characters")
    val offlinePriceCurrency: String,
    var skuCountryPrices: Map<Long, CountryPrice> = emptyMap(),
    var skuPcsPrices: List<PcsPrice> = emptyList(),
    @field:Min(1)
    val triggerDiscountQuantity: Int?,
    @field:DecimalMin("0.0")
    @field:DecimalMax("1.0")
    val discount: BigDecimal?,
)
