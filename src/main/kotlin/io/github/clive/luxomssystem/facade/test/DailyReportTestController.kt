package io.github.clive.luxomssystem.facade.test

import io.github.clive.luxomssystem.timer.DailySupplierOrderReportScheduler
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController

/**
 * 测试用控制器，用于手动触发每日订单报告
 */
@RestController
@RequestMapping("/test")
class DailyReportTestController(
    private val dailySupplierOrderReportScheduler: DailySupplierOrderReportScheduler,
) {
    /**
     * 手动触发每日订单报告
     */
    @PostMapping("/daily-report")
    fun triggerDailyReport(): String {
        dailySupplierOrderReportScheduler.sendDailyOrderReport()
        return "每日订单报告已手动触发"
    }
}
