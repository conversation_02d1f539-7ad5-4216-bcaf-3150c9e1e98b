package io.github.clive.luxomssystem.facade.download.dto

import io.github.clive.luxomssystem.common.enums.order.MainOrderImageDownloadStatus
import io.github.clive.luxomssystem.common.enums.order.MainOrderStatus
import io.github.clive.luxomssystem.domain.doanload.MainOrder

data class CreateMainOrderRequest(
    val fileName: String,
    val status: MainOrderStatus = MainOrderStatus.CREATED,
    val imgDownloadStatus: MainOrderImageDownloadStatus =
        MainOrderImageDownloadStatus.IMAGE_DOWNING,
)

data class UpdateMainOrderRequest(
    val fileName: String?,
    val status: MainOrderStatus?,
    val imgDownloadStatus: MainOrderImageDownloadStatus?,
)

fun CreateMainOrderRequest.toEntity() = MainOrder(fileName = fileName, status = status, imgDownloadStatus = imgDownloadStatus)
