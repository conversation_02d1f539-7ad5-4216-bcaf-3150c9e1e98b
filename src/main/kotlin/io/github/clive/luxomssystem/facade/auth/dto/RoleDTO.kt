package io.github.clive.luxomssystem.facade.auth.dto

import io.github.clive.luxomssystem.common.enums.RoleLimit
import io.github.clive.luxomssystem.domain.doanload.auth.Permission
import io.github.clive.luxomssystem.domain.doanload.auth.Role
import io.github.clive.luxomssystem.infrastructure.config.auth.UserContextHolder

data class RoleRequest(
    val name: String,
    val limit: RoleLimit,
    val permissions: List<Long> = emptyList(),
)

data class UpdateRoleRequest(
    val name: String,
    val permissions: List<Long> = emptyList(),
)

data class RoleResponse(
    val id: Long?,
    val name: String,
    val bizId: Long,
    val status: Int,
    val permissions: List<Long>,
    val permissionNames: List<String> = emptyList(),
    val limit: RoleLimit,
    val createdAt: Long? = null,
) {
    companion object {
        fun fromDomain(
            role: Role,
            permissionMap: Map<Long, Permission>,
        ) = RoleResponse(
            id = role.id,
            name = role.name,
            bizId = role.bizId,
            status = role.status.ordinal,
            permissions = role.permissions,
            permissionNames = role.permissions.map { permissionMap[it]!!.name },
            limit = role.limit,
            createdAt = role.createdAt,
        )
    }
}

data class RolePermissionRequest(
    val permissionIds: List<Long>,
)

fun RoleRequest.toDomain() =
    Role().apply {
        name = <EMAIL>
        bizId = UserContextHolder.user!!.bizId
        permissions = <EMAIL>
        limit = <EMAIL>
    }
