package io.github.clive.luxomssystem.facade.download.dto

import io.github.clive.luxomssystem.common.enums.BaseStatus
import io.github.clive.luxomssystem.common.enums.DownloadTemplateType
import io.github.clive.luxomssystem.domain.doanload.DownloadExcelColumn
import io.github.clive.luxomssystem.domain.doanload.DownloadTemplate
import io.github.clive.luxomssystem.infrastructure.config.auth.UserContextHolder
import io.github.clive.luxomssystem.nextId

data class CreateDownloadTemplateRequest(
    val name: String,
    val type: DownloadTemplateType = DownloadTemplateType.IMAGE,
    val content: List<DownloadExcelColumn>,
)

data class UpdateDownloadTemplateRequest(
    val name: String,
    val content: List<DownloadExcelColumn>,
    val status: BaseStatus? = null,
)

fun CreateDownloadTemplateRequest.toEntity() =
    DownloadTemplate(id = nextId(), name = name, content = content, type = type, bizId = UserContextHolder.user!!.bizId)
