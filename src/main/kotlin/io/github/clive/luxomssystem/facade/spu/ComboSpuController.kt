package io.github.clive.luxomssystem.facade.spu

import io.github.clive.luxomssystem.application.inventory.spu.ComboSpuApplicationService
import io.github.clive.luxomssystem.common.PageReq
import io.github.clive.luxomssystem.common.PageResponse
import io.github.clive.luxomssystem.common.enums.BaseStatus
import io.github.clive.luxomssystem.common.toResponse
import io.github.clive.luxomssystem.domain.sku.model.ComboSku
import io.github.clive.luxomssystem.domain.sku.model.Sku
import io.github.clive.luxomssystem.domain.spu.model.ComboSpu
import io.github.clive.luxomssystem.domain.spu.model.Spu
import org.springframework.web.bind.annotation.DeleteMapping
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.PutMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.RestController
import java.math.BigDecimal

@RestController
@RequestMapping("/api/combo-spu")
class ComboSpuController(
    private val comboSpuApplicationService: ComboSpuApplicationService,
) {
    // region flat spu and sku
    data class FlatSpu(
        val id: Long,
        val spuCode: String,
        val title: String,
        val name: String,
        val cnName: String,
        val category: String,
        val productImage: String,
        val packageQuantity: Int,
        val skus: List<FlatSpuSku>,
    ) {
        data class FlatSpuSku(
            val id: Long,
            val spuId: Long,
            val spuCode: String,
            val skuCode: String,
            val size: String,
            val color: String,
            val purchaseCost: BigDecimal,
            val purchaseCostCurrency: String,
            val salePrice: BigDecimal,
            val weight: BigDecimal,
            val volume: BigDecimal,
            val status: String,
        )
    }

    private fun flatSpu(
        spus: List<Spu>,
        skus: List<Sku>,
    ): List<FlatSpu> {
        val skusMap = skus.groupBy { it.spuId }
        return spus.map { spu ->
            val flatSkus =
                skusMap[spu.id]?.map { sku ->
                    FlatSpu.FlatSpuSku(
                        id = sku.id,
                        spuId = sku.spuId,
                        spuCode = spu.spuCode,
                        skuCode = sku.skuCode,
                        size = sku.size,
                        color = sku.color,
                        purchaseCost = sku.purchaseCost,
                        purchaseCostCurrency = sku.purchaseCostCurrency,
                        salePrice = sku.salePrice,
                        weight = sku.weight,
                        volume = sku.volume,
                        status = sku.status,
                    )
                }
            FlatSpu(
                id = spu.id,
                spuCode = spu.spuCode,
                title = spu.title,
                name = spu.name,
                cnName = spu.cnName,
                category = spu.category,
                productImage = spu.productImage,
                packageQuantity = spu.packageQuantity,
                skus = flatSkus ?: emptyList(),
            )
        }
    }

    @PostMapping("/flat-spu:fetch")
    fun fetchFlatSpu(
        @RequestBody spuCodes: List<String>,
    ): List<FlatSpu> {
        val (spus, skus) = comboSpuApplicationService.fetchFlatSpu(spuCodes)
        return flatSpu(spus, skus)
    }

    @GetMapping("/flat-spu:search")
    fun searchFlatSpu(
        @RequestParam search: String,
    ): List<FlatSpu> {
        val (spus, skus) = comboSpuApplicationService.searchFlatSpu(search)
        return flatSpu(spus, skus)
    }

    // endregion

    data class ComboSpuCreateRequest(
        val spuCodes: List<String>,
        val title: String,
        val name: String,
        val cnName: String,
        val category: String,
        val productImage: String,
        val packageQuantity: Int,
        val showImages: List<String>,
        val description: String,
        val status: BaseStatus,
        val hsCode: String?,
        val skus: List<SkuOfComboSkuCreateRequest>,
    ) {
        data class SkuOfComboSkuCreateRequest(
            val spuId: Long,
            val spuCode: String,
            val skuCode: String,
            val systemSkuId: Long,
            val size: String,
            val color: String,
            val purchaseCost: BigDecimal,
            val purchaseCostCurrency: String,
            val salePrice: BigDecimal,
            val weight: BigDecimal,
            val volume: BigDecimal,
        )
    }

    @PutMapping("/")
    fun createComboSpu(
        @RequestBody request: ComboSpuCreateRequest,
    ): Long = comboSpuApplicationService.createComboSpu(request)

    @PostMapping("/{id}")
    fun createComboSpu(
        @PathVariable id: Long,
        @RequestBody request: ComboSpuCreateRequest,
    ) = comboSpuApplicationService.updateComboSpu(id, request)

    @DeleteMapping("/{id}")
    fun deleteComboSpu(
        @PathVariable id: Long,
    ) = comboSpuApplicationService.deleteComboSpu(id)

    data class ComboSpuView(
        val id: Long,
        val bizId: Long,
        val spuIds: List<Long>,
        val spuCodes: List<String>,
        val spuRefId: String,
        val title: String,
        val name: String,
        val cnName: String,
        val category: String,
        val productImage: String,
        val packageQuantity: Int,
        val showImages: List<String>,
        val description: String,
        val status: BaseStatus,
        val hsCode: String?,
        val skus: List<ComboSkuView>,
        val createdAt: Long?,
        val createdBy: Long,
        val createdByName: String,
        val updatedAt: Long?,
        val updatedBy: Long,
        val updatedByName: String,
    ) {
        data class ComboSkuView(
            val id: Long,
            val comboSpuId: Long,
            val spuId: Long,
            val spuCode: String,
            val skuCode: String,
            val spuRefId: String,
            val systemSkuId: Long,
            val size: String,
            val color: String,
            val purchaseCost: BigDecimal,
            val purchaseCostCurrency: String,
            val salePrice: BigDecimal,
            val weight: BigDecimal,
            val volume: BigDecimal,
            val createdAt: Long?,
            val createdBy: Long,
            val createdByName: String,
            val updatedAt: Long?,
            val updatedBy: Long,
            val updatedByName: String,
        )
    }

    @GetMapping("/{id}")
    fun getComboSpuById(
        @PathVariable id: Long,
    ): ComboSpuView {
        val (comboSpu, comboSkus) = comboSpuApplicationService.getComboSpuById(id)
        return comboSpu.toView(comboSkus)
    }

    @GetMapping("/-/select")
    fun selectComboSpu(
        @RequestParam customerId: Long?,
    ): List<ComboSpuView> = comboSpuApplicationService.selectComboSpu(customerId).map { it.toView() }

    data class PageComboSpuRequest(
        val spuCode: String? = null,
        val category: String? = null,
        val title: String? = null,
    ) : PageReq()

    @GetMapping("/-")
    fun pageReq(req: PageComboSpuRequest): PageResponse<ComboSpuView> =
        comboSpuApplicationService.pageReq(req).map { it.toView() }.toResponse()
}

private fun ComboSpu.toView(comboSkus: List<ComboSku> = emptyList()): ComboSpuController.ComboSpuView =
    ComboSpuController.ComboSpuView(
        id = id,
        bizId = bizId,
        spuIds = spuIds,
        spuCodes = spuCodes,
        spuRefId = spuRefId,
        title = title,
        name = name,
        cnName = cnName,
        category = category,
        productImage = productImage,
        packageQuantity = packageQuantity,
        showImages = showImages,
        description = description,
        status = status,
        hsCode = hsCode,
        skus = comboSkus.map { it.toView() },
        createdAt = createdAt,
        createdBy = createdBy,
        createdByName = createdByName,
        updatedAt = updatedAt,
        updatedBy = updatedBy,
        updatedByName = updatedByName,
    )

private fun ComboSku.toView(): ComboSpuController.ComboSpuView.ComboSkuView =
    ComboSpuController.ComboSpuView.ComboSkuView(
        id = id,
        comboSpuId = comboSpuId,
        spuId = spuId,
        spuCode = spuCode,
        skuCode = skuCode,
        spuRefId = spuRefId,
        systemSkuId = systemSkuId,
        size = size,
        color = color,
        purchaseCost = purchaseCost,
        purchaseCostCurrency = purchaseCostCurrency,
        salePrice = salePrice,
        weight = weight,
        volume = volume,
        createdAt = createdAt,
        createdBy = createdBy,
        createdByName = createdByName,
        updatedAt = updatedAt,
        updatedBy = updatedBy,
        updatedByName = updatedByName,
    )
