package io.github.clive.luxomssystem.facade.order.waybill

import io.github.clive.luxomssystem.application.waybill.WaybillRemoteCallApplicationService
import io.github.clive.luxomssystem.domain.waybill.model.WaybillRemoteCallRecord
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController

@RestController
@RequestMapping("/api/waybill/remote-call")
class WaybillRemoteCallController(
    private val waybillRemoteCallService: WaybillRemoteCallApplicationService,
) {
    @GetMapping("/list/{waybillId}")
    fun list(
        @PathVariable("waybillId") waybillId: Long,
    ): List<WaybillRemoteCallRecord> = waybillRemoteCallService.listRemoteCallRecords(waybillId)
}
