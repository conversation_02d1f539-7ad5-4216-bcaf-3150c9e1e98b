package io.github.clive.luxomssystem.facade.customerSpu

import io.github.clive.luxomssystem.application.inventory.customerSpu.ComboCustomerSpuApplicationService
import io.github.clive.luxomssystem.common.PageReq
import io.github.clive.luxomssystem.common.PageResponse
import io.github.clive.luxomssystem.common.enums.BaseStatus
import io.github.clive.luxomssystem.common.toResponse
import io.github.clive.luxomssystem.domain.customerSku.model.ComboCustomerSku
import io.github.clive.luxomssystem.domain.customerSku.model.CountryPrice
import io.github.clive.luxomssystem.domain.customerSku.model.PcsPrice
import io.github.clive.luxomssystem.domain.customerSpu.model.ComboCustomerSpu
import org.springframework.web.bind.annotation.DeleteMapping
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.PutMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.RestController
import java.math.BigDecimal

@RestController
@RequestMapping("/api/combo-customer-spu")
class ComboCustomerSpuController(
    private val comboCustomerSpuApplicationService: ComboCustomerSpuApplicationService,
) {
    data class ComboCustomerSpuSearchReq(
        val systemComboSpuId: Long?,
        val systemSpuCode: String?,
        val customerId: Long?,
    ) : PageReq()

    data class ComboCustomerSpuView(
        val id: Long,
        val bizId: Long,
        val customerId: Long,
        val systemSpuIds: List<Long>,
        val systemSpuCodes: List<String>,
        val systemComboSpuId: Long,
        val triggerDiscountQuantity: Int,
        val discount: BigDecimal,
        val status: BaseStatus,
        val skus: List<ComboCustomerSkuView>,
        val createdAt: Long?,
        val createdBy: Long,
        val createdByName: String,
        val updatedAt: Long?,
        val updatedBy: Long,
        val updatedByName: String,
    ) {
        data class ComboCustomerSkuView(
            val id: Long,
            val bizId: Long,
            val customerId: Long,
            val comboCustomerSpuId: Long,
            val systemSpuIdsOfCombo: List<Long>,
            val systemSpuCodesOfCombo: List<String>,
            val comboSpuId: Long,
            val comboSkuId: Long,
            val systemSpuId: Long,
            val systemSpuCode: String,
            val systemSkuId: Long,
            val systemSkuCode: String,
            val size: String,
            val color: String,
            val offlinePrice: BigDecimal,
            val offlinePriceCurrency: String,
            val status: BaseStatus = BaseStatus.DISABLED,
            val skuCountryPrices: Map<Long, CountryPrice>,
            val skuPcsPrices: List<PcsPrice>,
            val triggerDiscountQuantity: Int?,
            val discount: BigDecimal?,
            val createdAt: Long?,
            val createdBy: Long,
            val createdByName: String,
            val updatedAt: Long?,
            val updatedBy: Long,
            val updatedByName: String,
        )
    }

    @GetMapping("/-")
    fun pageComboCustomerSpu(req: ComboCustomerSpuSearchReq): PageResponse<ComboCustomerSpuView> =
        comboCustomerSpuApplicationService.pageComboCustomerCpu(req).map { it.toView() }.toResponse()

    @GetMapping("/{id}")
    fun getComboCustomerSpu(
        @PathVariable id: Long,
    ): ComboCustomerSpuView {
        val (spu, skus) = comboCustomerSpuApplicationService.getComboCustomerSpu(id)
        return spu.toView(skus)
    }

    @DeleteMapping("/{id}")
    fun deleteComboCustomerSpu(
        @PathVariable id: Long,
    ) {
        comboCustomerSpuApplicationService.deleteComboCustomerSpu(id)
    }

    data class ComboCustomerSpuCreateRequest(
        val customerId: Long,
        val systemSpuIds: List<Long>,
        val systemSpuCodes: List<String>,
        val systemComboSpuId: Long,
        val triggerDiscountQuantity: Int,
        val discount: BigDecimal,
        val skus: List<ComboCustomerSkuCreateRequest>,
    ) {
        data class ComboCustomerSkuCreateRequest(
            val customerId: Long,
            val systemSpuIdsOfCombo: List<Long>,
            val systemSpuCodesOfCombo: List<String>,
            val comboSpuId: Long,
            val comboSkuId: Long,
            val systemSpuId: Long,
            val systemSpuCode: String,
            val systemSkuId: Long,
            val systemSkuCode: String,
            val size: String,
            val color: String,
            val offlinePrice: BigDecimal,
            val offlinePriceCurrency: String,
            val skuCountryPrices: Map<Long, CountryPrice>,
            val skuPcsPrices: List<PcsPrice>,
            val triggerDiscountQuantity: Int?,
            val discount: BigDecimal?,
        )
    }

    @PutMapping("/")
    fun createComboCustomerSpu(
        @RequestBody req: ComboCustomerSpuCreateRequest,
    ) = comboCustomerSpuApplicationService.createComboCustomerSpu(req)

    @PostMapping("/{id}")
    fun updateComboCustomerSpu(
        @PathVariable id: Long,
        @RequestBody req: ComboCustomerSpuCreateRequest,
    ) = comboCustomerSpuApplicationService.updateComboCustomerSpu(id, req)

    @PostMapping("/{id}/status/:change")
    fun changeComboCustomerSpuStatus(
        @PathVariable id: Long,
        @RequestParam wantedStatus: BaseStatus,
    ) {
        comboCustomerSpuApplicationService.changeComboCustomerSpuStatus(id, wantedStatus)
    }
}

private fun ComboCustomerSpu.toView(skus: List<ComboCustomerSku> = emptyList()): ComboCustomerSpuController.ComboCustomerSpuView =
    ComboCustomerSpuController.ComboCustomerSpuView(
        id = id,
        bizId = bizId,
        customerId = customerId,
        systemSpuIds = systemSpuIds,
        systemSpuCodes = systemSpuCodes,
        systemComboSpuId = systemComboSpuId,
        triggerDiscountQuantity = triggerDiscountQuantity,
        discount = discount,
        status = status,
        skus = skus.map { it.toView() },
        createdAt = createdAt,
        createdBy = createdBy,
        createdByName = createdByName,
        updatedAt = updatedAt,
        updatedBy = updatedBy,
        updatedByName = updatedByName,
    )

private fun ComboCustomerSku.toView(): ComboCustomerSpuController.ComboCustomerSpuView.ComboCustomerSkuView =
    ComboCustomerSpuController.ComboCustomerSpuView.ComboCustomerSkuView(
        id = id,
        bizId = bizId,
        customerId = customerId,
        comboCustomerSpuId = comboCustomerSpuId,
        systemSpuIdsOfCombo = systemSpuIdsOfCombo,
        systemSpuCodesOfCombo = systemSpuCodesOfCombo,
        comboSpuId = comboSpuId,
        comboSkuId = comboSkuId,
        systemSpuId = systemSpuId,
        systemSpuCode = systemSpuCode,
        systemSkuId = systemSkuId,
        systemSkuCode = systemSkuCode,
        size = size,
        color = color,
        offlinePrice = offlinePrice,
        offlinePriceCurrency = offlinePriceCurrency,
        status = status,
        skuCountryPrices = skuCountryPrices,
        skuPcsPrices = skuPcsPrices,
        triggerDiscountQuantity = triggerDiscountQuantity,
        discount = discount,
        createdAt = createdAt,
        createdBy = createdBy,
        createdByName = createdByName,
        updatedAt = updatedAt,
        updatedBy = updatedBy,
        updatedByName = updatedByName,
    )
