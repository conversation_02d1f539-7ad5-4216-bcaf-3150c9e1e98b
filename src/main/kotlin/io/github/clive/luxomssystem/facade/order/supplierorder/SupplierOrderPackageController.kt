package io.github.clive.luxomssystem.facade.order.supplierorder

import io.github.clive.luxomssystem.application.SupplierOrderPackageApplicationService
import io.github.clive.luxomssystem.domain.supplierOrder.model.SamePackageOrder
import io.github.clive.luxomssystem.facade.order.supplierorder.request.PageQuerySubOrderPackageRequest
import io.github.oshai.kotlinlogging.KotlinLogging
import org.springframework.web.bind.annotation.*

@RestController
@RequestMapping("/api/supplier-order/package")
class SupplierOrderPackageController(
    private val supplierOrderPackageApplicationService: SupplierOrderPackageApplicationService,
) {
    @PostMapping("")
    fun createPackage(
        @RequestBody req: CreatePackageResponse,
    ) {
        log.info { "createPackage: $req" }
        supplierOrderPackageApplicationService.createPackage(req)
    }

    @PostMapping("/{id}/scan")
    fun scanPackage(
        @PathVariable id: Long,
        @RequestParam orderNo: String,
    ): ScanPackageResponse {
        log.info { "scanPackage: $orderNo : $id" }
        return supplierOrderPackageApplicationService.scanPackage(orderNo, id)
    }

    @PostMapping("/{id}/complete")
    fun completePackage(
        @PathVariable id: Long,
    ) {
        log.info { "completePackage: $id" }
        supplierOrderPackageApplicationService.completePackage(id)
    }

    @PostMapping("/{id}/force-complete-box")
    fun forceCompleteBox(
        @PathVariable id: Long,
        @RequestParam wayBillRelation: String,
    ) {
        log.info { "forceCompleteBox: packageId=$id, wayBillRelation=$wayBillRelation" }
        supplierOrderPackageApplicationService.forceCompleteBox(id, wayBillRelation)
    }

    @GetMapping("/page")
    fun page(req: PageQuerySubOrderPackageRequest) = supplierOrderPackageApplicationService.pageQuery(req)

    @GetMapping("/{id}")
    fun findById(
        @PathVariable id: Long,
    ) = supplierOrderPackageApplicationService.findById(id)

    @PutMapping("/{id}")
    fun update(
        @PathVariable id: Long,
        @RequestBody request: CreatePackageResponse,
    ) = supplierOrderPackageApplicationService.update(id, request)

    companion object {
        private val log = KotlinLogging.logger { }
    }
}

data class CreatePackageResponse(
    val name: String,
)

data class ScanPackageResponse(
    val waybillUrl: String?,
    val index: Int,
    val canPrintWayBillPdf: Boolean,
    val stopPrintWayBillWarning: String,
    val orderNos: List<String>? = null,
    val packageContent: SamePackageOrder
)
