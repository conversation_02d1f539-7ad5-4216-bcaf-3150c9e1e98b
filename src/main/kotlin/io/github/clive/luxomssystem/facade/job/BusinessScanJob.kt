package io.github.clive.luxomssystem.facade.job

import io.github.clive.luxomssystem.common.enums.BaseStatus
import io.github.clive.luxomssystem.common.enums.BusinessType
import io.github.clive.luxomssystem.infrastructure.repository.jpa.auth.BusinessRepository
import org.springframework.scheduling.annotation.Scheduled
import org.springframework.stereotype.Component

@Component
class BusinessScanJob(
    private val businessService: BusinessRepository,
) {
    @Scheduled(cron = "0 0 0 * * ?")
    fun scan() {
        businessService.findByStatus(BaseStatus.ENABLED).forEach {
            if (it.type == BusinessType.SUBSCRIPTION || it.type == BusinessType.TRAIL) {
                if (it.subscribeTo < System.currentTimeMillis()) {
                    it.status = BaseStatus.DISABLED
                    businessService.saveAndFlush(it)
                }
            }
        }
    }
}
