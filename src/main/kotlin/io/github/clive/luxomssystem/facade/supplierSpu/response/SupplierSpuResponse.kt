
package io.github.clive.luxomssystem.facade.supplierSpu.response

import io.github.clive.luxomssystem.domain.supplierSpu.model.SupplierSpu

data class SupplierSpuResponse(
    val id: Long,
    val bizId: Long,
    val supplierId: Long,
    val systemSpuId: Long,
    val systemSpuCode: String,
    val supplierSpuCode: String,
    val status: String,
    val createdBy: Long?,
    val createdAt: Long?,
    val updatedBy: Long?,
    val updatedAt: Long?,
    var title: String? = null,
) {
    companion object {
        fun fromDomain(supplierSpu: SupplierSpu): SupplierSpuResponse =
            SupplierSpuResponse(
                id = supplierSpu.id!!,
                bizId = supplierSpu.bizId,
                supplierId = supplierSpu.supplierId,
                systemSpuId = supplierSpu.systemSpuId,
                systemSpuCode = supplierSpu.systemSpuCode,
                supplierSpuCode = supplierSpu.supplierSpuCode,
                status = supplierSpu.status.name,
                createdBy = supplierSpu.createdBy,
                createdAt = supplierSpu.createdAt,
                updatedBy = supplierSpu.updatedBy,
                updatedAt = supplierSpu.updatedAt,
                title = supplierSpu.title,
            )
    }
}
