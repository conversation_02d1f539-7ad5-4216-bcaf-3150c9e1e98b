package io.github.clive.luxomssystem.facade.auth

import io.github.clive.luxomssystem.application.auth.UserApplicationService
import org.springframework.data.domain.Pageable
import org.springframework.web.bind.annotation.*

@RestController
@RequestMapping("/api/v1/users")
class UserController(
    private val userApplicationService: UserApplicationService,
) {
    data class LoginRequest(
        val account: String,
        val password: String,
    )

    data class ChangePasswdRequest(
        val oldPasswd: String,
        val newPasswd: String,
    )

    data class CreateUserCmd(
        val name: String,
        val roleId: Long? = null,
        val account: String,
        val password: String,
        val supplierName: String? = null,
        val supplierPhone: String? = null,
        val supplierDescription: String? = null,
        val bizId: Long? = null,
    )

    data class UserSelectResponse(
        val id: Long,
        val name: String,
    )

    @PostMapping("/login")
    fun login(
        @RequestBody request: LoginRequest,
    ): String = userApplicationService.login(request.account, request.password)

    @PostMapping("/logout")
    fun logout() {
        userApplicationService.logout()
    }

    @PostMapping("/passwd:change")
    fun changePassword(
        @RequestBody request: ChangePasswdRequest,
    ) {
        userApplicationService.changePassword(request.oldPasswd, request.newPasswd)
    }

    @GetMapping("/info")
    fun userInfo() = userApplicationService.userInfo()

    @PostMapping("/register")
    fun register() {
        println("register")
    }

    @GetMapping("/page")
    fun page(
        pageIndex: Int,
        pageSize: Int,
    ) = userApplicationService.page(Pageable.ofSize(pageSize).withPage(pageIndex))

    @PostMapping("")
    fun createUser(
        @RequestBody request: CreateUserCmd,
    ) {
        userApplicationService.createUser(request)
    }

    @DeleteMapping("/batch")
    fun batchDelete(
        @RequestBody ids: List<Long>,
    ) {
        userApplicationService.batchDelete(ids)
    }

    @PostMapping("/role:change")
    fun changeRole(
        @RequestParam userId: Long,
        @RequestParam roleId: Long,
    ) {
        userApplicationService.changeRole(userId, roleId)
    }
}
