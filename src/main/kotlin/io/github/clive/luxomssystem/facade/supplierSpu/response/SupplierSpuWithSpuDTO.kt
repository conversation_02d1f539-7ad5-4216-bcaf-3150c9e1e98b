package io.github.clive.luxomssystem.facade.supplierSpu.response

import io.github.clive.luxomssystem.common.enums.BaseStatus

data class SupplierSpuWithSpuDTO(
    val id: Long,
    val bizId: Long,
    val supplierId: Long,
    val supplierSpuCode: String,
    val systemSpuId: Long,
    val systemSpuCode: String,
    val title: String,
    val name: String,
    val cnName: String,
    val category: String,
    val productImage: String,
    val packageQuantity: Int,
    val description: String?,
    var showImages: MutableCollection<String>,
    val sizes: MutableCollection<String>,
    val colors: MutableCollection<String>,
    val status: BaseStatus,
    val createdAt: Long,
    val updatedAt: Long,
    val createdByName: String,
    val updatedByName: String,
)
