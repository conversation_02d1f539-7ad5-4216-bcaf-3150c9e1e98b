package io.github.clive.luxomssystem.facade.download.response

import java.math.BigDecimal

data class MainOrderFinancialResponse(
    val id: Long,
    val createdAt: Long?,
    val fileName: String,
    val totalWaybillCost: BigDecimal = BigDecimal.ZERO,
    val totalQuickChannelTax: BigDecimal = BigDecimal.ZERO,
    val totalCustomerNeedPayTax: BigDecimal = BigDecimal.ZERO,
    val totalCustomerNeedPayCost: BigDecimal = BigDecimal.ZERO,
)
