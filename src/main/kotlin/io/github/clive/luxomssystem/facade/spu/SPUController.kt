package io.github.clive.luxomssystem.facade.spu

import io.github.clive.luxomssystem.application.inventory.spu.SPUApplicationService
import io.github.clive.luxomssystem.common.PageResponse
import io.github.clive.luxomssystem.facade.sku.response.SkuResponse
import io.github.clive.luxomssystem.facade.spu.request.CreateSPURequest
import io.github.clive.luxomssystem.facade.spu.request.PageSPURequest
import io.github.clive.luxomssystem.facade.spu.request.UpdateSPURequest
import io.github.clive.luxomssystem.facade.spu.response.SPUPageResponse
import io.github.clive.luxomssystem.facade.spu.response.SPUResponse
import jakarta.validation.Valid
import org.springframework.http.HttpHeaders
import org.springframework.http.HttpStatus
import org.springframework.http.MediaType
import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.*
import org.springframework.web.multipart.MultipartFile
import java.time.LocalDateTime
import java.time.format.DateTimeFormatter

@RestController
@RequestMapping("/api/spus")
class SPUController(
    private val spuApplicationService: SPUApplicationService,
) {
    @PostMapping
    @ResponseStatus(HttpStatus.CREATED)
    fun createSPU(
        @Valid @RequestBody request: CreateSPURequest,
    ): SPUPageResponse = spuApplicationService.createSPU(request)

    @GetMapping("/{id}")
    fun getSPU(
        @PathVariable id: Long,
    ): SPUResponse = spuApplicationService.getSPU(id)

    @GetMapping("/page")
    fun pageReq(req: PageSPURequest): PageResponse<SPUPageResponse> = spuApplicationService.pageQuery(req)

    @PutMapping("/{id}")
    fun updateSPU(
        @PathVariable id: Long,
        @Valid @RequestBody request: UpdateSPURequest,
    ): SPUPageResponse = spuApplicationService.updateSPU(id, request)

    @DeleteMapping("/{id}")
    @ResponseStatus(HttpStatus.NO_CONTENT)
    fun deleteSPU(
        @PathVariable id: Long,
    ) {
        spuApplicationService.deleteSPU(id)
    }

    @GetMapping("/{id}/skus/-")
    fun getSPUSkus(
        @PathVariable id: Long,
    ): List<SkuResponse> = spuApplicationService.getSPUSkus(id)

    @GetMapping("/select/list")
    fun list(supplierId: Long?) = spuApplicationService.list(supplierId)

    @GetMapping("/select/list/customer")
    fun listCustomer(customerId: Long?) = spuApplicationService.listCustomer(customerId)

    @PostMapping("/export-spu")
    fun exportSPU(
        @RequestBody req: ExportSPURequest,
    ): ResponseEntity<ByteArray> {
        val excelBytes = spuApplicationService.generateSPUExcel(req.ids)

        val formatter = DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss")
        val fileName = "spu_list_${LocalDateTime.now().format(formatter)}.xlsx"

        return ResponseEntity
            .ok()
            .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=$fileName")
            .contentType(MediaType.APPLICATION_OCTET_STREAM)
            .body(excelBytes)
    }

    @PostMapping("/import-spu")
    fun importSPU(
        @RequestParam("file") file: MultipartFile,
    ): ResponseEntity<String> {
        val importResult = spuApplicationService.importSPUFromExcel(file.inputStream)
        return ResponseEntity.ok("成功导入 ${importResult.first} 个SPU和 ${importResult.second} 个SKU")
    }

    data class ExportSPURequest(
        val ids: List<Long>,
    )
}
