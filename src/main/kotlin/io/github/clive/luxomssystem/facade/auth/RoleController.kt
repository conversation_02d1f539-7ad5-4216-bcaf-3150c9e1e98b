package io.github.clive.luxomssystem.facade.auth

import io.github.clive.luxomssystem.application.auth.PermissionApplicationService
import io.github.clive.luxomssystem.application.auth.RoleApplicationService
import io.github.clive.luxomssystem.common.PageResponse
import io.github.clive.luxomssystem.common.toResponse
import io.github.clive.luxomssystem.facade.auth.dto.*
import org.springframework.data.domain.Pageable
import org.springframework.http.HttpStatus
import org.springframework.web.bind.annotation.*

@RestController
@RequestMapping("/api/v1/roles")
class RoleController(
    private val roleApplicationService: RoleApplicationService,
    private val permissionApplicationService: PermissionApplicationService,
) {
    @GetMapping("/page")
    fun getAllRoles(
        pageIndex: Int,
        pageSize: Int,
    ): PageResponse<RoleResponse> =
        roleApplicationService
            .findAll(
                Pageable.ofSize(pageSize).withPage(pageIndex),
            ).map { RoleResponse.fromDomain(it, permissionApplicationService.findAll().associateBy { it.id!! }) }
            .toResponse()

    @GetMapping()
    fun getAllRoles(): List<RoleResponse> =
        roleApplicationService
            .findAll()
            .map { RoleResponse.fromDomain(it, permissionApplicationService.findAll().associateBy { it.id!! }) }

    @GetMapping("/{id}")
    fun getRoleById(
        @PathVariable id: Long,
    ): RoleResponse =
        roleApplicationService
            .findById(id)
            ?.let { RoleResponse.fromDomain(it, permissionApplicationService.findAll().associateBy { it.id!! }) }
            ?: throw IllegalArgumentException("Role not found")

    @GetMapping("/business/{bizId}")
    fun getRolesByBizId(
        @PathVariable bizId: Long,
    ): List<RoleResponse> =
        roleApplicationService
            .findByBizId(bizId)
            .map { RoleResponse.fromDomain(it, permissionApplicationService.findAll().associateBy { it.id!! }) }

    @GetMapping("/search")
    fun searchRoles(
        @RequestParam name: String,
    ): List<RoleResponse> =
        roleApplicationService
            .findByNameContaining(name)
            .map { RoleResponse.fromDomain(it, permissionApplicationService.findAll().associateBy { it.id!! }) }

    @PostMapping
    @ResponseStatus(HttpStatus.CREATED)
    fun createRole(
        @RequestBody request: RoleRequest,
    ): RoleResponse =
        RoleResponse.fromDomain(
            roleApplicationService.create(request.toDomain()),
            permissionApplicationService.findAll().associateBy { it.id!! },
        )

    @PutMapping("/{id}")
    fun updateRole(
        @PathVariable id: Long,
        @RequestBody request: UpdateRoleRequest,
    ): RoleResponse =
        RoleResponse.fromDomain(
            roleApplicationService.update(
                id,
                request,
            ),
            permissionApplicationService.findAll().associateBy { it.id!! },
        )

    @DeleteMapping("/{id}")
    @ResponseStatus(HttpStatus.NO_CONTENT)
    fun deleteRole(
        @PathVariable id: Long,
    ) = roleApplicationService.delete(id)

    @PutMapping("/{id}/permissions")
    fun assignPermissions(
        @PathVariable id: Long,
        @RequestBody request: RolePermissionRequest,
    ): RoleResponse =
        RoleResponse.fromDomain(
            roleApplicationService.assignPermissions(id, request.permissionIds),
            permissionApplicationService.findAll().associateBy { it.id!! },
        )
}
