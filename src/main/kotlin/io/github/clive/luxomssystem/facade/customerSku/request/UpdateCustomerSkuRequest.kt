
package io.github.clive.luxomssystem.facade.customerSku.request

import io.github.clive.luxomssystem.domain.customerSku.model.CountryPrice
import io.github.clive.luxomssystem.domain.customerSku.model.PcsPrice
import jakarta.validation.constraints.NotBlank
import jakarta.validation.constraints.NotNull
import jakarta.validation.constraints.Positive
import jakarta.validation.constraints.Size
import java.math.BigDecimal

/**
 * Request object for updating an existing CustomerSku.
 */
data class UpdateCustomerSkuRequest(
    val id: Long?,
    @field:NotNull(message = "Offline price is required")
    @field:Positive(message = "Offline price must be positive")
    val offlinePrice: BigDecimal,
    @field:NotBlank(message = "Offline price currency is required")
    @field:Size(max = 3, message = "Offline price currency must not exceed 3 characters")
    val offlinePriceCurrency: String,
    var skuCountryPrices: Map<Long, CountryPrice> = emptyMap(),
    var skuPcsPrices: List<PcsPrice> = emptyList(),
    val triggerDiscountQuantity: Int?,
    val discount: BigDecimal?,
)
