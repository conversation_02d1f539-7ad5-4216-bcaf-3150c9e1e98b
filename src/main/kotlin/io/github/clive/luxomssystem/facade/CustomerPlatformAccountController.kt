package io.github.clive.luxomssystem.facade

import io.github.clive.luxomssystem.application.CustomerPlatformAccountApplicationService
import io.github.clive.luxomssystem.common.PageReq
import io.github.clive.luxomssystem.common.enums.BaseStatus
import org.springframework.web.bind.annotation.*

@RestController
@RequestMapping("/api/customer-platform-accounts")
class CustomerPlatformAccountController(
    private val customerPlatformAccountApplicationService: CustomerPlatformAccountApplicationService,
) {

    @PostMapping("")
    fun createCustomerPlatformAccount(
        @RequestBody request: CustomerPlatformAccountApplicationService.CreateCustomerPlatformAccountRequest,
    ) = customerPlatformAccountApplicationService.createCustomerPlatformAccount(request)

    @PutMapping("/{id}")
    fun updateCustomerPlatformAccount(
        @PathVariable id: Long,
        @RequestBody request: CustomerPlatformAccountApplicationService.UpdateCustomerPlatformAccountRequest,
    ) = customerPlatformAccountApplicationService.updateCustomerPlatformAccount(id, request)

    @GetMapping("/{id}")
    fun getCustomerPlatformAccount(
        @PathVariable id: Long,
    ) = customerPlatformAccountApplicationService.getCustomerPlatformAccount(id)

    @GetMapping("/page")
    fun pageQuery(
        page: PageReq,
        @RequestParam(required = false) email: String?,
        @RequestParam(required = false) accountName: String?,
        @RequestParam(required = false) customerId: Long?,
        @RequestParam(required = false, defaultValue = "ENABLED") statuses: List<BaseStatus>,
    ) = customerPlatformAccountApplicationService.pageQuery(
        page,
        CustomerPlatformAccountApplicationService.CustomerPlatformAccountPageRequest(
            email = email,
            accountName = accountName,
            customerId = customerId,
            statuses = statuses,
        )
    )

    @GetMapping("/customer/{customerId}")
    fun getCustomerPlatformAccounts(
        @PathVariable customerId: Long,
    ) = customerPlatformAccountApplicationService.getCustomerPlatformAccounts(customerId)

    @DeleteMapping("/{id}")
    fun deleteCustomerPlatformAccount(
        @PathVariable id: Long,
    ) {
        customerPlatformAccountApplicationService.deleteCustomerPlatformAccount(id)
    }

    @PostMapping("/{id}/status:change")
    fun changeStatus(
        @PathVariable id: Long,
        @RequestParam status: BaseStatus,
    ) {
        customerPlatformAccountApplicationService.changeStatus(id, status)
    }
}
