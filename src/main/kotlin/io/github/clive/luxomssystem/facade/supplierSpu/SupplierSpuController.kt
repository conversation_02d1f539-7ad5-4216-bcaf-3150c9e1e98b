package io.github.clive.luxomssystem.facade.supplierSpu

import io.github.clive.luxomssystem.application.inventory.supplierSpu.SupplierSpuApplicationService
import io.github.clive.luxomssystem.facade.supplierSpu.request.CreateSupplierSpuRequest
import io.github.clive.luxomssystem.facade.supplierSpu.request.PageSupplierSpuRequest
import io.github.clive.luxomssystem.facade.supplierSpu.request.UpdateSupplierSpuRequest
import io.github.clive.luxomssystem.facade.supplierSpu.response.SupplierSpuAndSkuResponse
import io.github.clive.luxomssystem.facade.supplierSpu.response.SupplierSpuResponse
import jakarta.validation.Valid
import org.springframework.http.HttpStatus
import org.springframework.web.bind.annotation.*

@RestController
@RequestMapping("/api/supplier/spu")
class SupplierSpuController(
    private val supplierSpuApplicationService: SupplierSpuApplicationService,
) {
    @PostMapping
    @ResponseStatus(HttpStatus.CREATED)
    fun createSupplierSpu(
        @Valid @RequestBody request: CreateSupplierSpuRequest,
    ): SupplierSpuResponse = supplierSpuApplicationService.createSupplierSpu(request)

    @GetMapping("/{id}")
    fun getSupplierSpu(
        @PathVariable id: Long,
    ): SupplierSpuAndSkuResponse = supplierSpuApplicationService.getSupplierSpuById(id)

    @PutMapping("/{id}")
    fun updateSupplierSpu(
        @PathVariable id: Long,
        @Valid @RequestBody request: UpdateSupplierSpuRequest,
    ) {
        supplierSpuApplicationService.updateSupplierSpu(id, request)
    }

    @DeleteMapping("/{id}")
    @ResponseStatus(HttpStatus.NO_CONTENT)
    fun deleteSupplierSpu(
        @PathVariable id: Long,
    ) {
        supplierSpuApplicationService.deleteSupplierSpu(id)
    }

    @GetMapping("/page")
    fun page(page: PageSupplierSpuRequest) = supplierSpuApplicationService.page(page)

    @PostMapping("/{id}/status:change")
    fun changeStatus(
        wantedStatus: String,
        @PathVariable id: Long,
    ) {
        supplierSpuApplicationService.changeStatus(id, wantedStatus)
    }

    @PostMapping("/{id}/sync")
    fun syncPlatformSpu(
        @PathVariable id: Long,
    ) {
        supplierSpuApplicationService.syncPlatformSpu(id)
    }
}
