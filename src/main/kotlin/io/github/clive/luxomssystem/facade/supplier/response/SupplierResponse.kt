
package io.github.clive.luxomssystem.facade.supplier.response

import io.github.clive.luxomssystem.common.enums.BaseStatus
import io.github.clive.luxomssystem.domain.supplier.model.Supplier
import io.github.clive.luxomssystem.domain.supplier.model.SupplierConfig

data class SupplierResponse(
    val id: Long,
    val bizId: Long,
    val name: String,
    val phone: String,
    val description: String?,
    val status: BaseStatus,
    val createdBy: Long,
    val createdAt: Long?,
    val updatedBy: Long,
    val updatedAt: Long?,
    val priority: Int = 0,
    val config: SupplierConfig,
) {
    companion object {
        fun fromDomain(supplier: Supplier): SupplierResponse =
            SupplierResponse(
                id = supplier.id,
                bizId = supplier.bizId,
                name = supplier.name,
                phone = supplier.phone,
                description = supplier.description,
                status = supplier.status,
                createdBy = supplier.createdBy,
                createdAt = supplier.createdAt,
                updatedBy = supplier.updatedBy,
                updatedAt = supplier.updatedAt,
                priority = supplier.priority,
                config = supplier.config,
            )
    }
}
