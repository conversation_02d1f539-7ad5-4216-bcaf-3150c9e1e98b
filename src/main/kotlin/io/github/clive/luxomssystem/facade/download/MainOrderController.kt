package io.github.clive.luxomssystem.facade.download

import io.github.clive.luxomssystem.application.MainOrderService
import io.github.clive.luxomssystem.common.PageResponse
import io.github.clive.luxomssystem.common.enums.MainOrderType
import io.github.clive.luxomssystem.common.enums.order.MainOrderStatus
import io.github.clive.luxomssystem.domain.doanload.MainOrder
import io.github.clive.luxomssystem.facade.download.dto.CreateMainOrderRequest
import io.github.clive.luxomssystem.facade.download.dto.MainOrderDownTaskOverView
import io.github.clive.luxomssystem.facade.download.dto.UpdateMainOrderRequest
import io.github.clive.luxomssystem.facade.download.response.MainOrderFinancialResponse
import io.github.clive.luxomssystem.facade.download.response.MainOrderStatisticsResponse
import org.springframework.format.annotation.DateTimeFormat
import org.springframework.web.bind.annotation.*
import java.time.LocalDate

@RestController
@RequestMapping("/api/main-orders")
class MainOrderController(
    private val mainOrderService: MainOrderService,
) {
    @GetMapping
    fun findAll(
        pageIndex: Int,
        pageSize: Int,
        fileName: String?,
        id: Long?,
        @RequestParam(required = false) status: List<MainOrderStatus>?,
        @DateTimeFormat(pattern = "yyyy-MM-dd")
        createdAtFrom: LocalDate? = null,
        @DateTimeFormat(pattern = "yyyy-MM-dd")
        createdAtTo: LocalDate? = null,
        wayBillPushed: Boolean? = null,
        supplierPushed: Boolean? = null,
        type: MainOrderType? = null,
    ): PageResponse<MainOrder> =
        mainOrderService.findAll(
            pageIndex,
            pageSize,
            fileName,
            id,
            createdAtFrom,
            createdAtTo,
            status,
            wayBillPushed,
            supplierPushed,
            type,
        )

    @GetMapping("/page/financial")
    fun pageForFinancial(
        customerId: Long,
        pageIndex: Int,
        pageSize: Int,
    ): PageResponse<MainOrderFinancialResponse> {
        val res = mainOrderService.pageForFinancial(customerId, pageIndex, pageSize)
        return res
    }

    @GetMapping("/{id}")
    fun findById(
        @PathVariable id: Long,
    ): MainOrder = mainOrderService.findById(id)

    @PostMapping
    fun create(
        @RequestBody request: CreateMainOrderRequest,
    ): MainOrder = mainOrderService.create(request)

    @PutMapping("/{id}")
    fun update(
        @PathVariable id: Long,
        @RequestBody request: UpdateMainOrderRequest,
    ): MainOrder = mainOrderService.update(id, request)

    @DeleteMapping("/{id}")
    fun deleteById(
        @PathVariable id: Long,
    ) {
        mainOrderService.deleteById(id)
    }

    @GetMapping("/{id}/statistics")
    fun getStatistics(
        @PathVariable id: Long
    ): MainOrderStatisticsResponse = mainOrderService.getStatistics(id)

    @GetMapping("/download-task/overview/{id}")
    fun findTaskOverview(
        @PathVariable id: Long,
    ): MainOrderDownTaskOverView = mainOrderService.findTaskOverview(id)

    @PostMapping("/download-task/{id}/retry")
    fun retryTask(
        @PathVariable id: Long,
    ) {
        mainOrderService.retryTask(id)
    }

    @PostMapping("/download-task/{id}/compress")
    fun compressTask(
        @PathVariable id: Long,
    ) {
        mainOrderService.compressTask(id)
    }

    @PostMapping("/{id}/supply:push")
    fun pushSupply(
        @PathVariable id: Long,
    ) {
        mainOrderService.pushSupply(id)
    }

    @PostMapping("/{id}/supply:ntfy")
    fun ntfySupply(
        @PathVariable id: Long,
    ) {
        mainOrderService.ntfySupply(id)
    }
}
