package io.github.clive.luxomssystem.facade.customerSpu

import io.github.clive.luxomssystem.application.inventory.customerSpu.CustomerSpuApplicationService
import io.github.clive.luxomssystem.common.PageReq
import io.github.clive.luxomssystem.facade.customerSpu.request.CreateCustomerSpuRequest
import io.github.clive.luxomssystem.facade.customerSpu.request.UpdateCustomerSpuRequest
import io.github.clive.luxomssystem.facade.customerSpu.response.CustomerSpuPageResponse
import io.github.clive.luxomssystem.facade.customerSpu.response.CustomerSpuResponse
import org.springframework.http.HttpStatus
import org.springframework.web.bind.annotation.*

@RestController
@RequestMapping("/api/customer-spus")
class CustomerSpuController(
    private val customerSpuApplicationService: CustomerSpuApplicationService,
) {
    @PostMapping
    @ResponseStatus(HttpStatus.CREATED)
    fun createCustomerSpu(
        @RequestBody request: CreateCustomerSpuRequest,
    ): CustomerSpuPageResponse = customerSpuApplicationService.createCustomerSpu(request)

    @PutMapping("/{id}")
    fun updateCustomerSpu(
        @PathVariable id: Long,
        @RequestBody request: UpdateCustomerSpuRequest,
    ): CustomerSpuPageResponse = customerSpuApplicationService.updateCustomerSpu(id, request)

    @GetMapping("/{id}")
    fun getCustomerSpu(
        @PathVariable id: Long,
    ): CustomerSpuResponse = customerSpuApplicationService.getCustomerSpu(id)

    @DeleteMapping("/{id}")
    @ResponseStatus(HttpStatus.NO_CONTENT)
    fun deleteCustomerSpu(
        @PathVariable id: Long,
    ) {
        customerSpuApplicationService.deleteCustomerSpu(id)
    }

    @GetMapping("/page")
    fun page(req: CustomerSpuSearchReq) = customerSpuApplicationService.page(req)

    @PostMapping("/{id}/status:change")
    fun changeStatus(
        wantedStatus: String,
        @PathVariable id: Long,
    ) {
        customerSpuApplicationService.changeStatus(id, wantedStatus)
    }
}

data class CustomerSpuSearchReq(
    val systemSpuCode: String?,
    val customerId: Long?,
) : PageReq()
