
package io.github.clive.luxomssystem.facade.sku

import io.github.clive.luxomssystem.application.inventory.sku.SkuApplicationService
import io.github.clive.luxomssystem.common.PageReq
import io.github.clive.luxomssystem.common.PageResponse
import io.github.clive.luxomssystem.facade.sku.request.UpdateSkuRequest
import io.github.clive.luxomssystem.facade.sku.response.SkuResponse
import org.springframework.http.HttpStatus
import org.springframework.web.bind.annotation.*

@RestController
@RequestMapping("/api/skus")
class SkuController(
    private val skuApplicationService: SkuApplicationService,
) {
    @PostMapping
    @ResponseStatus(HttpStatus.CREATED)
    fun createSku(
        @RequestBody request: UpdateSkuRequest,
    ): SkuResponse = skuApplicationService.createSku(request)

    @GetMapping("/{id}")
    fun getSku(
        @PathVariable id: Long,
    ): SkuResponse = skuApplicationService.getSku(id)

    @PutMapping("/{id}")
    fun updateSku(
        @PathVariable id: Long,
        @RequestBody request: UpdateSkuRequest,
    ): SkuResponse = skuApplicationService.updateSku(id, request)

    @DeleteMapping("/{id}")
    @ResponseStatus(HttpStatus.NO_CONTENT)
    fun deleteSku(
        @PathVariable id: Long,
    ) {
        skuApplicationService.deleteSku(id)
    }

    @GetMapping("/page")
    fun pageReq(req: PageReq): PageResponse<SkuResponse> = skuApplicationService.pageQuery(req)
}
