package io.github.clive.luxomssystem.facade.download

import io.github.clive.luxomssystem.application.DownloadTemplateService
import io.github.clive.luxomssystem.common.PageResponse
import io.github.clive.luxomssystem.common.enums.DownloadTemplateType
import io.github.clive.luxomssystem.common.toResponse
import io.github.clive.luxomssystem.domain.doanload.DownloadTemplate
import io.github.clive.luxomssystem.facade.download.dto.CreateDownloadTemplateRequest
import io.github.clive.luxomssystem.facade.download.dto.UpdateDownloadTemplateRequest
import org.springframework.data.domain.Pageable
import org.springframework.http.HttpHeaders
import org.springframework.http.MediaType
import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.*

@RestController
@RequestMapping("/download-templates")
class DownloadTemplateController(
    private val downloadTemplateService: DownloadTemplateService,
) {
    @GetMapping
    fun findAll(pageable: Pageable): PageResponse<DownloadTemplate> = downloadTemplateService.findAll(pageable).toResponse()

    @GetMapping("/{id}")
    fun findById(
        @PathVariable id: Long,
    ): DownloadTemplate = downloadTemplateService.findById(id)

    @PostMapping
    fun create(
        @RequestBody request: CreateDownloadTemplateRequest,
    ): DownloadTemplate = downloadTemplateService.create(request)

    @PutMapping("/{id}")
    fun update(
        @PathVariable id: Long,
        @RequestBody request: UpdateDownloadTemplateRequest,
    ): DownloadTemplate = downloadTemplateService.update(id, request)

    @PutMapping("/{id}/star")
    fun star(
        @PathVariable id: Long,
    ): DownloadTemplate = downloadTemplateService.star(id)

    @DeleteMapping("/{id}")
    fun deleteById(
        @PathVariable id: Long,
    ) {
        downloadTemplateService.deleteById(id)
    }

    // 添加新的不带分页的findAll接口
    @GetMapping("/all")
    fun findAll(
        @RequestParam(required = false) name: String?,
        @RequestParam(required = false) type: DownloadTemplateType?,
    ): List<DownloadTemplate> = downloadTemplateService.findAll(name, type)

    @GetMapping("/download/template/{id}")
    fun downloadTemplate(
        @PathVariable id: Long,
    ): ResponseEntity<ByteArray> {
        val excelData = downloadTemplateService.generateExcelTemplate(id)

        return ResponseEntity
            .ok()
            .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=template.xlsx")
            .contentType(MediaType.APPLICATION_OCTET_STREAM)
            .body(excelData)
    }
}
