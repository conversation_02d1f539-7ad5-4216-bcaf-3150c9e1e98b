package io.github.clive.luxomssystem.facade.common.dto

import io.github.clive.luxomssystem.domain.channel.Channel

data class ChannelResponse(
    val id: Long,
    val name: String,
    val displayName: String,
    val methodCode: String,
    val methodName: String,
    val enabled: Boolean,
    val iossNumber: String? = null,
) {
    companion object {
        fun fromDomain(channel: Channel): ChannelResponse {
            return ChannelResponse(
                id = channel.id,
                name = channel.name.name,
                displayName = channel.displayName,
                methodCode = channel.methodCode,
                methodName = channel.methodName,
                enabled = channel.enabled,
                iossNumber = channel.iossNumber,
            )
        }
    }
}
