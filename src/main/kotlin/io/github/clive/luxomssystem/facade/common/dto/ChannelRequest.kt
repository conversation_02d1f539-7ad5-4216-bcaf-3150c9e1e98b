package io.github.clive.luxomssystem.facade.common.dto

import io.github.clive.luxomssystem.common.enums.WaybillChannel
import io.github.clive.luxomssystem.domain.channel.Channel
import jakarta.validation.constraints.NotBlank

data class UpdateChannelRequest(
    @field:NotBlank
    val displayName: String,
    @field:NotBlank
    val methodCode: String,
    val methodName: String,
    val enabled: Boolean,
    val iossNumber: String? = null,
) {
    fun toDomain(id: Long, name: WaybillChannel): Channel {
        return Channel().apply {
            this.id = id
            this.name = name
            this.displayName = displayName
            this.methodCode = methodCode
            this.methodName = methodName
            this.enabled = enabled
            this.iossNumber = iossNumber
        }
    }
}
