package io.github.clive.luxomssystem.facade.order.req

import io.github.clive.luxomssystem.common.PageReq
import io.github.clive.luxomssystem.common.enums.SubOrderStatus

data class PageQuerySubOrderRequest(
    var orderNo: String? = null,
    var country: List<String> = mutableListOf(),
    var customerId: Long? = null,
    var fileName: String? = null,
    var orderId: Long? = null,
    var createdBy: Long? = null,
    var orderNos: String? = null,
    var customerOrderNo: String? = null,
    var customerOrderNos: String? = null,
    var status: MutableList<SubOrderStatus> = mutableListOf(),
    var hasAssignedChannel: Boolean? = null,
) : PageReq()
