package io.github.clive.luxomssystem.facade.order.waybill.response

import io.github.clive.luxomssystem.common.enums.match
import io.github.clive.luxomssystem.domain.valueobject.ProductInfo
import io.github.clive.luxomssystem.domain.waybill.model.Waybill
import java.math.BigDecimal

data class WaybillResponse(
    val id: Long,
    val bizId: Long,
    val customerId: Long,
    val supplierId: Long?,
    val orderNo: String,
    val receiverName: String,
    val country: String,
    val state: String,
    val city: String,
    val street: String,
    val street2: String,
    val postcode: String,
    val channel: String,
    val shipMethod: String,
    val shipMethodAlias: String,
    val waybillNo: String?,
    val waybillLabelUrl: String?,
    val phone: String,
    val errorMsg: String?,
    val status: String,
    val failAtStatus: String?,
    val createdByName: String?,
    val createdAt: Long?,
    val updatedByName: String?,
    val updatedAt: Long?,
    val wayBillRelation: String,
    val orderNos: String,
    var quickChannelTax: BigDecimal = BigDecimal.ZERO,
    var customerNeedPayCost: BigDecimal = BigDecimal.ZERO,
    var customerNeedPayTax: BigDecimal = BigDecimal.ZERO,
    var calculateCustomerNeedPayCostFlow: String = "",
    var product: ProductInfo,
    var trackingNumber: String?,
    var taxNumber: String? = null,
    var hsCode: String? = null,
    var material: String? = null,
    var name: String? = null,
    var totalPrice: BigDecimal? = null,
    var iossNumber: String? = null,
) {
    companion object {
        fun fromDomain(waybill: Waybill): WaybillResponse =
            WaybillResponse(
                id = waybill.id,
                bizId = waybill.bizId,
                customerId = waybill.customerId,
                supplierId = waybill.product.supplierId,
                orderNo = waybill.orderNo,
                receiverName = waybill.recipient.receiverName ?: "",
                country = waybill.recipient.country ?: "",
                state = waybill.recipient.state ?: "",
                city = waybill.recipient.city ?: "",
                street = waybill.recipient.address1 ?: "",
                street2 = waybill.recipient.address2 ?: "",
                postcode = waybill.recipient.postcode ?: "",
                channel = waybill.shipping.channel.name,
                shipMethod = waybill.shipping.shipMethod ?: "",
                shipMethodAlias =
                    waybill.shipping.shipMethod?.let {
                        waybill.shipping.channel.shipMethods
                            .match(it)
                            ?.name
                    }
                        ?: waybill.shipping.shipMethod ?: "",
                waybillNo = waybill.waybillNo,
                waybillLabelUrl = waybill.shipping.waybillLabelUrl,
                phone = waybill.recipient.phone ?: "",
                errorMsg = waybill.errorMsg,
                status = waybill.status.name,
                failAtStatus = waybill.failAtStatus?.name,
                createdByName = waybill.createdByName,
                createdAt = waybill.createdAt,
                updatedByName = waybill.updatedByName,
                updatedAt = waybill.updatedAt,
                wayBillRelation = waybill.shipping.wayBillRelation ?: "",
                orderNos = waybill.orderNos,
                quickChannelTax = waybill.quickChannelTax,
                customerNeedPayCost = waybill.customerNeedPayCost,
                customerNeedPayTax = waybill.customerNeedPayTax,
                calculateCustomerNeedPayCostFlow = waybill.calculateCustomerNeedPayCostFlow,
                product = waybill.product,
                trackingNumber = waybill.trackingNumber,
                taxNumber = waybill.taxNumber,
                hsCode = waybill.product.hsCode,
                material = waybill.product.material,
                name = waybill.product.name,
                totalPrice = waybill.totalPrice,
                iossNumber = waybill.iossNumber,
            )
    }
}
