import com.alibaba.excel.annotation.ExcelProperty
import io.github.clive.luxomssystem.domain.sku.model.Sku
import io.github.clive.luxomssystem.domain.spu.model.Spu
import java.net.URL

data class SPUExcelRow(
    @ExcelProperty("中文名")
    val cnName: String,
    @ExcelProperty("标题")
    val title: String,
    @ExcelProperty("英文名")
    val name: String,
    @ExcelProperty("SPU")
    val spuCode: String,
    @ExcelProperty("价格￥")
    val price: Double = 0.0,
    @ExcelProperty("规格/尺码")
    val size: String,
    @ExcelProperty("颜色")
    val color: String = "白色",
    @ExcelProperty("HS编码")
    val hsCode: String = "",
    @ExcelProperty("分类")
    val category: String = "",
    @ExcelProperty("克重Kg")
    val weight: Double = 0.0,
    @ExcelProperty("备注")
    val remarks: String = "",
    @ExcelProperty("报价$")
    val priceUSD: Double = 0.0,
    @ExcelProperty("图片1")
    val url1: URL? = null,
    @ExcelProperty("图片2")
    val url2: URL? = null,
    @ExcelProperty("图片3")
    val url3: URL? = null,
    @ExcelProperty("是否套装")
    val isSet: String = "否",
    @ExcelProperty("套装数量")
    val setQuantity: Int? = null,
)

fun Spu.toSpuExcelRow(skus: List<Sku>): SPUExcelRow {
    try {
        return SPUExcelRow(
            cnName = this.cnName,
            title = this.title,
            name = this.name,
            spuCode = this.spuCode,
            size = this.sizes.joinToString(","),
            color = this.colors.joinToString(","),
            hsCode = this.hsCode ?: "",
            weight = skus.firstOrNull()?.weight?.toDouble() ?: 0.0,
            category = this.category,
            remarks = this.description,
            price = skus.firstOrNull()?.purchaseCost?.toDouble() ?: 0.0,
            priceUSD = skus.firstOrNull()?.salePrice?.toDouble() ?: 0.0,
            // todo: showImages
//            url1 = this.showImages.getOrNull(0)?.let {
//                if (StringUtils.hasText(it)) {
//                    return@let URL(it)
//                } else {
//                    null
//                }
//            },
//            url2 = this.showImages.getOrNull(1)?.let {
//                if (StringUtils.hasText(it)) {
//                    return@let URL(it)
//                } else {
//                    null
//                }
//            },
//            url3 = this.showImages.getOrNull(2)?.let {
//                if (StringUtils.hasText(it)) {
//                    return@let URL(it)
//                } else {
//                    null
//                }
//            }
        )
    } catch (e: Exception) {
        throw e
    }
}
