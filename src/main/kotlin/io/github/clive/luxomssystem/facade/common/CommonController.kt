package io.github.clive.luxomssystem.facade.common

import io.github.clive.luxomssystem.application.common.CommonApplicationService
import io.github.clive.luxomssystem.domain.base.Country
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PutMapping
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.RestController

@RestController
@RequestMapping("/api/common")
class CommonController(
    private val commonApplicationService: CommonApplicationService,
) {
    @GetMapping("/dict/value")
    fun getDictValues(type: String): String = commonApplicationService.getDictValues(type)

    @GetMapping("/dict/names")
    fun getDictNames(): SystemNameContainer = commonApplicationService.getDictNames()

    @PutMapping("/dict/value")
    fun addDictValue(
        @RequestParam type: String,
        @RequestParam value: String,
    ) {
        commonApplicationService.addDictValue(type, value)
    }

    @GetMapping("/countries")
    fun getCountries(): List<Country> = commonApplicationService.getCountries()
}
