package io.github.clive.luxomssystem.facade.order.supplierorder.response

import com.fasterxml.jackson.annotation.JsonFormat
import io.github.clive.luxomssystem.common.enums.match
import io.github.clive.luxomssystem.domain.supplierOrder.model.SupplierOrder
import io.github.clive.luxomssystem.domain.supplierOrder.model.SupplierOrderStatus
import java.math.BigDecimal
import java.time.ZonedDateTime

data class SupplierOrderResponse(
    val id: Long,
    val subOrderId: Long,
    val supplierId: Long?,
    val waybillId: Long?,
    val orderNo: String,
    val size: String?,
    val color: String?,
    val qty: Int,
    val address1: String?,
    val address2: String?,
    val country: String,
    val state: String,
    val city: String,
    val postcode: String,
    val channel: String,
    val shipMethod: String,
    val shipMethodAlias: String,
    val phone: String,
    val errorMsg: String?,
    val status: SupplierOrderStatus,
    val failAtStatus: SupplierOrderStatus?,
    val updatedByName: String?,
    val updatedAt: Long?,
    val createdByName: String?,
    val createdAt: Long?,
    val waybillLabelUrl: String?,
    val skuCode: String? = null,
    var weight: BigDecimal? = null,
    var price: BigDecimal? = null,
    var spu: String? = null,
    var mainOrderId: Long? = null,
    val title: String? = null,
    val supplierName: String? = null,
    var canPrintWayBillPdf: Boolean = true,
    var stopPrintWayBillWarning: String = "",
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    var scanAt: ZonedDateTime? = null,
) {
    companion object {
        fun fromDomain(supplierOrder: SupplierOrder): SupplierOrderResponse =
            SupplierOrderResponse(
                id = supplierOrder.id,
                subOrderId = supplierOrder.subOrderId,
                supplierId = supplierOrder.product.supplierId,
                waybillId = supplierOrder.waybillId,
                orderNo = supplierOrder.orderNo,
                size = supplierOrder.product.size,
                color = supplierOrder.product.color,
                qty = supplierOrder.product.qty,
                address1 = supplierOrder.recipient.address1,
                address2 = supplierOrder.recipient.address2,
                country = supplierOrder.recipient.country ?: "",
                state = supplierOrder.recipient.state ?: "",
                city = supplierOrder.recipient.city ?: "",
                postcode = supplierOrder.recipient.postcode ?: "",
                channel = supplierOrder.shipping.channel.displayName,
                shipMethod = supplierOrder.shipping.shipMethod ?: "",
                shipMethodAlias =
                    supplierOrder.shipping.shipMethod?.let {
                        supplierOrder.shipping.channel.shipMethods
                            .match(
                                it,
                            )?.name
                    }
                        ?: supplierOrder.shipping.shipMethod ?: "",
                phone = supplierOrder.recipient.phone ?: "",
                errorMsg = supplierOrder.errorMsg,
                status = supplierOrder.status,
                failAtStatus = supplierOrder.failAtStatus,
                updatedByName = supplierOrder.updatedByName,
                updatedAt = supplierOrder.updatedAt,
                createdByName = supplierOrder.createdByName,
                createdAt = supplierOrder.createdAt,
                waybillLabelUrl = supplierOrder.shipping.waybillLabelUrl,
                skuCode = supplierOrder.product.skuCode(),
                spu = supplierOrder.product.spu,
                mainOrderId = supplierOrder.mainOrderId,
                title = supplierOrder.product.title,
                supplierName = supplierOrder.product.supplierName,
                canPrintWayBillPdf = supplierOrder.canPrintWayBillPdf,
                stopPrintWayBillWarning = supplierOrder.stopPrintWayBillWarning,
                scanAt = supplierOrder.scanAt,
            )
    }
}
