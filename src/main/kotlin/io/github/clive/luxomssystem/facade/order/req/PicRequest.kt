package io.github.clive.luxomssystem.facade.order.req

import com.alibaba.excel.annotation.ExcelProperty

class PicRequest {
    @ExcelProperty("orderNo")
    var orderNo: String? = null

    @ExcelProperty("userName")
    var userName: String? = null

    @ExcelProperty("spu")
    var spu: String? = null

    @ExcelProperty("size")
    var size: String? = null

    @ExcelProperty("color")
    var color: String? = null

    @ExcelProperty("qty")
    var qty: Int = 1

    @ExcelProperty("receiveName")
    var receiveName: String? = null

    @ExcelProperty("addr1")
    var addr1: String? = null

    @ExcelProperty("addr2")
    var addr2: String? = null

    @ExcelProperty("country")
    var country: String? = null

    @ExcelProperty("state")
    var state: String? = null

    @ExcelProperty("city")
    var city: String? = null

    @ExcelProperty("postCode")
    var postcode: String? = null

    @ExcelProperty("phone")
    var phone: String? = null

    @ExcelProperty("email")
    var email: String? = null

    @ExcelProperty("channel")
    var channel: String? = null

    @ExcelProperty("shipMethod")
    var shipMethod: String? = null

    @ExcelProperty("detail")
    var detail: String? = null

    @ExcelProperty("customName")
    var customName: String? = null

    /**
     * real order number
     * 需要合并的订单号
     */
    @ExcelProperty("wayBillRelation")
    var wayBillRelation: String? = null

    @ExcelProperty("deliveryMethod")
    var deliveryMethod: String? = null

    fun skuCode(): String = "$spu-$size-$color"
}
