package io.github.clive.luxomssystem.facade.openapi.request

import jakarta.validation.Valid
import jakarta.validation.constraints.NotBlank
import jakarta.validation.constraints.NotNull
import org.hibernate.validator.constraints.Length

data class CreateOpenAPIOrderRequest(
    @NotBlank
    val uniqueRequestId: String?,
    @Valid
    val orderItems: List<CreateOpenAPIOrderItemRequest> = emptyList(),
) {
    fun validate() {
        checkNotNull(uniqueRequestId) { "uniqueRequestId must not be null" }
        check(orderItems.isNotEmpty()) { "orderItems must not empty" }
        orderItems.forEach { it.validate() }
    }
}

data class CreateOpenAPIOrderItemRequest(
    @NotBlank
    @Length(min = 6, max = 50)
    var orderNo: String? = null,
    var imageUrls: List<String> = emptyList(),
    @Valid
    var recipient: CreateOpenAPIOrderItemRecipientInfoRequest? = null,
    @Valid
    var product: CreateOpenAPIOrderItemProductRequest? = null,
) {
    fun validate() {
        checkNotNull(orderNo) { "orderNo must not be null" }
        check(orderNo!!.length in 6..50) { "orderNo length must between 6 and 50" }
        checkNotNull(recipient) { "recipient must not be null" }
        checkNotNull(product) { "product must not be null" }
        recipient!!.validate()
        product!!.validate()
    }
}

data class CreateOpenAPIOrderItemRecipientInfoRequest(
    @NotBlank
    @Length(min = 1, max = 150)
    var receiverName: String?,
    /**
     * ISO 3166-1 alpha-2
     */
    @NotBlank
    @Length(min = 1, max = 150)
    var country: String?,
    @NotBlank
    @Length(min = 1, max = 150)
    var state: String?,
    @NotBlank
    @Length(min = 1, max = 150)
    var city: String?,
    @NotBlank
    @Length(min = 1, max = 150)
    var postcode: String?,
    @NotBlank
    @Length(min = 1, max = 150)
    var address1: String?,
    @NotBlank
    @Length(min = 1, max = 150)
    var phone: String?,
    @Length(min = 1, max = 300)
    var remark: String?,
    var email: String?,
) {
    fun validate() {
        checkNotNull(receiverName) { "receiverName must not be null" }
        check(receiverName!!.length in 1..150) { "receiverName length must be between 1 and 150" }

        checkNotNull(country) { "country must not be null" }
        check(country!!.length in 1..150) { "country length must be between 1 and 150" }

        checkNotNull(state) { "state must not be null" }
        check(state!!.length in 1..150) { "state length must be between 1 and 150" }

        checkNotNull(city) { "city must not be null" }
        check(city!!.length in 1..150) { "city length must be between 1 and 150" }

        checkNotNull(postcode) { "postcode must not be null" }
        check(postcode!!.length in 1..150) { "postcode length must be between 1 and 150" }

        checkNotNull(address1) { "address1 must not be null" }
        check(address1!!.length in 1..150) { "address1 length must be between 1 and 150" }

        checkNotNull(phone) { "phone must not be null" }
        check(phone!!.length in 1..150) { "phone length must be between 1 and 150" }

        remark?.let {
            check(it.length in 1..300) { "remark length must be between 1 and 300" }
        }

        // email is nullable and has no length constraint in the snippet
    }
}

data class CreateOpenAPIOrderItemProductRequest(
    @NotBlank
    @Length(min = 1, max = 100)
    val spu: String?,
    @NotBlank
    @Length(min = 1, max = 100)
    val size: String?,
    @NotBlank
    @Length(min = 1, max = 100)
    val color: String?,
    @NotNull
    val quantity: Int?,
) {
    fun validate() {
        checkNotNull(spu) { "spu must not be null" }
        check(spu.length in 1..100) { "spu length must be between 1 and 100" }

        checkNotNull(size) { "size must not be null" }
        check(size.length in 1..100) { "size length must be between 1 and 100" }

        checkNotNull(color) { "color must not be null" }
        check(color.length in 1..100) { "color length must be between 1 and 100" }

        checkNotNull(quantity) { "quantity must not be null" }
    }
}
