package io.github.clive.luxomssystem.facade.supplierSpu.response

import io.github.clive.luxomssystem.domain.supplierSpu.model.SupplierSpu
import io.github.clive.luxomssystem.facade.suppliersku.response.SupplierSkuResponse

data class SupplierSpuAndSkuResponse(
    val id: Long,
    val bizId: Long,
    val title: String,
    val name: String,
    val cnName: String,
    val category: String,
    val supplierId: Long,
    val systemSpuId: Long,
    val systemSpuCode: String,
    val supplierSpuCode: String,
    val status: String,
    val createdBy: Long?,
    val createdAt: Long?,
    val updatedBy: Long?,
    val updatedAt: Long?,
    val skus: List<SupplierSkuResponse>,
) {
    companion object {
        fun fromDomain(
            supplierSpu: SupplierSpu,
            skus: List<SupplierSkuResponse>,
        ): SupplierSpuAndSkuResponse =
            SupplierSpuAndSkuResponse(
                id = supplierSpu.id!!,
                bizId = supplierSpu.bizId,
                title = supplierSpu.title,
                name = supplierSpu.name,
                cnName = supplierSpu.cnName,
                category = supplierSpu.category,
                supplierId = supplierSpu.supplierId,
                systemSpuId = supplierSpu.systemSpuId,
                systemSpuCode = supplierSpu.systemSpuCode,
                supplierSpuCode = supplierSpu.supplierSpuCode,
                status = supplierSpu.status.name,
                createdBy = supplierSpu.createdBy,
                createdAt = supplierSpu.createdAt,
                updatedBy = supplierSpu.updatedBy,
                updatedAt = supplierSpu.updatedAt,
                skus = skus,
            )
    }
}
