package io.github.clive.luxomssystem.facade.customerSpu

import io.github.clive.luxomssystem.application.inventory.CustomerTaxApplicationService
import io.github.clive.luxomssystem.domain.CountryStateTaxConfig
import io.github.clive.luxomssystem.domain.CustomerCountryTaxConfig
import org.springframework.web.bind.annotation.DeleteMapping
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.PutMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController
import java.math.BigDecimal

@RestController
@RequestMapping("/api/customer-tax-config")
class CustomerTaxConfigController(
    private val customerTaxApplicationService: CustomerTaxApplicationService,
) {
    @PostMapping("/batch")
    fun batchChangeTax(
        @RequestBody taxReq: BatchCustomerTaxRequest,
    ) {
        customerTaxApplicationService.batchSave(taxReq)
    }

    @PutMapping("/batch")
    fun batchUpdate(
        @RequestBody req: BatchCustomerTaxRequest,
    ) {
        customerTaxApplicationService.batchUpdate(req)
    }

    @DeleteMapping("/{customerId}/{countryId}")
    fun deleteCountryTaxConfig(
        @PathVariable customerId: Long,
        @PathVariable countryId: Long,
    ) {
        customerTaxApplicationService.deleteCountryTaxConfig(customerId, countryId)
    }

    @GetMapping("/by-customer/{id}")
    fun getCountryTaxConfig(
        @PathVariable id: Long,
    ): List<CustomerCountryTaxConfig> = customerTaxApplicationService.getCountryTaxConfig(id)
}

data class BatchCustomerTaxRequest(
    val customerIds: List<Long>,
    val customerTaxReq: List<CustomerTaxReq>,
)

data class CustomerTaxReq(
    val additionalTax: BigDecimal?,
    val vatTax: BigDecimal?,
    val countryId: Long,
    val countryName: String,
    val stateTaxConfigs: List<CountryStateTaxConfig>,
)
