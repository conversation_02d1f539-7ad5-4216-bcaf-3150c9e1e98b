package io.github.clive.luxomssystem.facade.download

import io.github.clive.luxomssystem.application.DownloadTemplateService
import io.github.clive.luxomssystem.application.RowValidationError
import io.github.clive.luxomssystem.common.exception.ErrorResult
import io.github.clive.luxomssystem.infrastructure.channel.cos.CosInnerRemoteService
import io.github.clive.luxomssystem.infrastructure.config.auth.UserContextHolder
import org.springframework.http.HttpStatus
import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.RestController
import org.springframework.web.multipart.MultipartFile
import java.util.*

@RestController
@RequestMapping("/excel")
class ExcelUploadController(
    private val downloadTemplateService: DownloadTemplateService,
    private val ossClient: CosInnerRemoteService,
) {
    @PostMapping("/upload")
    fun uploadExcel(
        @RequestParam("templateId") templateId: Long,
        @RequestParam("selectedUser", required = false) selectedUser: Long?,
        @RequestParam("needAuthorization", defaultValue = "false") needAuthorization: Boolean = false,
        @RequestParam("action") action: String = "compress",
        @RequestParam("file") file: MultipartFile,
        @RequestParam("attachments", required = false) attachments: List<MultipartFile>?,
        @RequestParam("namingType", defaultValue = "default") namingType: String = "default",
        @RequestParam("urlSplitToken", defaultValue = "|") urlSplitToken: String = "|",
        @RequestParam("needDownloadImage", defaultValue = "true") needDownloadImage: Boolean = true,
    ): ResponseEntity<Any> {
        // 验证文件类型
        if (!isExcelFile(file.originalFilename)) {
            return ResponseEntity
                .badRequest()
                .body(ErrorResult.of(HttpStatus.BAD_REQUEST, "请上传Excel文件(.xlsx或.xls)"))
        }

        // 处理附件上传
        val attachmentUrls = mutableListOf<String>()
        attachments?.let { files ->
            files.forEach { attachment ->
                if (isValidAttachmentFile(attachment.originalFilename)) {
                    val user = UserContextHolder.user!!
                    val attachmentKey =
                        "/attachments/${user.bizId}/${UUID.randomUUID()}.${getFileExtension(attachment.originalFilename)}"
                    val attachmentUrl = ossClient.uploadFile(attachmentKey, attachment.inputStream)
                    attachmentUrls.add(attachmentUrl)
                } else {
                    return ResponseEntity
                        .badRequest()
                        .body(
                            ErrorResult.of(
                                HttpStatus.BAD_REQUEST,
                                "附件文件格式不支持: ${attachment.originalFilename}",
                            ),
                        )
                }
            }
        }

        return try {
            val result =
                downloadTemplateService.parseExcelFile(
                    templateId,
                    file,
                    action,
                    selectedUser,
                    namingType,
                    urlSplitToken,
                    needDownloadImage,
                    attachmentUrls,
                )

            if (result.success) {
                ResponseEntity.ok(
                    "成功解析${result.totalRows}行数据",
                )
            } else {
                ResponseEntity
                    .badRequest()
                    .body(
                        ErrorResult.of(HttpStatus.BAD_REQUEST, formatErrors(result.errors).joinToString("; ")),
                    )
            }
        } catch (e: IllegalArgumentException) {
            ResponseEntity
                .badRequest()
                .body(
                    ErrorResult.of(HttpStatus.BAD_REQUEST, e.message ?: ""),
                )
        }
    }

    private fun isExcelFile(filename: String?): Boolean =
        filename?.lowercase()?.let { it.endsWith(".xlsx") || it.endsWith(".xls") } ?: false

    private fun isValidAttachmentFile(filename: String?): Boolean {
        if (filename.isNullOrBlank()) return false
        val lowercaseFilename = filename.lowercase()
        val supportedExtensions =
            listOf(
                ".zip",
                ".rar",
                ".7z",
                ".tar",
                ".gz", // 压缩包
                ".xlsx",
                ".xls",
                ".csv", // Excel文件
                ".pdf",
                ".doc",
                ".docx", // 文档
                ".jpg",
                ".jpeg",
                ".png",
                ".gif",
                ".bmp",
                ".webp", // 图片
                ".txt",
                ".json",
                ".xml", // 其他文本文件
            )
        return supportedExtensions.any { lowercaseFilename.endsWith(it) }
    }

    private fun getFileExtension(filename: String?): String {
        if (filename.isNullOrBlank()) return "unknown"
        val lastDotIndex = filename.lastIndexOf('.')
        return if (lastDotIndex > 0 && lastDotIndex < filename.length - 1) {
            filename.substring(lastDotIndex + 1)
        } else {
            "unknown"
        }
    }

    private fun formatErrors(errors: List<RowValidationError>): List<String> =
        errors.map { error -> "第${error.rowIndex}行: ${error.errors.joinToString("; ")}" }
}

data class ExcelUploadResponse(
    val success: Boolean,
    val message: String,
    val data: List<Map<String, String>> = emptyList(),
    val errors: List<String> = emptyList(),
)
