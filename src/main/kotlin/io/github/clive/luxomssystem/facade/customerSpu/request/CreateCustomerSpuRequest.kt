package io.github.clive.luxomssystem.facade.customerSpu.request

import io.github.clive.luxomssystem.domain.customerSku.model.CountryPrice
import io.github.clive.luxomssystem.domain.customerSku.model.PcsPrice
import io.github.clive.luxomssystem.domain.customerSpu.model.SupplierCapacity
import jakarta.validation.constraints.DecimalMax
import jakarta.validation.constraints.DecimalMin
import jakarta.validation.constraints.Min
import jakarta.validation.constraints.NotNull
import jakarta.validation.constraints.Positive
import java.math.BigDecimal

data class CreateCustomerSpuRequest(
    @field:Positive
    val customerId: Long,
    @field:NotNull
    @field:Positive
    val systemSpuId: Long,
    val systemSpuCode: String?,
    val customerSpuCode: String?,
    @field:Min(1)
    val triggerDiscountQuantity: Int = 1,
    @field:DecimalMin("0.0")
    @field:DecimalMax("1.0")
    val discount: BigDecimal = BigDecimal("0.80"),
    val skus: List<CreateSkuRequest>,
    var supplierCapacities: List<SupplierCapacity> = emptyList(),
) {
    data class CreateSkuRequest(
        val systemSkuId: Long,
        val systemSkuCode: String,
        val size: String,
        val color: String,
        val offlinePrice: BigDecimal,
        val offlinePriceCurrency: String,
        var skuCountryPrices: Map<Long, CountryPrice> = emptyMap(),
        var skuPcsPrices: List<PcsPrice> = emptyList(),
        @field:Min(1)
        val triggerDiscountQuantity: Int?,
        @field:DecimalMin("0.0")
        @field:DecimalMax("1.0")
        val discount: BigDecimal?,
    )
}
