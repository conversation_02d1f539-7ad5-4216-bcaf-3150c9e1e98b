package io.github.clive.luxomssystem.facade

import io.github.clive.luxomssystem.common.PageReq
import io.github.clive.luxomssystem.common.enums.BaseStatus
import org.springframework.web.bind.annotation.*

@RestController
@RequestMapping("/api/customer")
class CustomerController(
    private val customerApplicationService: io.github.clive.luxomssystem.application.CustomerApplicationService,
) {
    @GetMapping("/page")
    fun pageQuery(page: PageReq) = customerApplicationService.pageQuery(page)

    @PostMapping("")
    fun addCustomer(
        @RequestBody req: io.github.clive.luxomssystem.application.AddCustomerRequest,
    ) {
        customerApplicationService.addCustomer(req)
    }

    @GetMapping("/list")
    fun listCustomers() = customerApplicationService.listCustomers()

    @GetMapping("/{id}")
    fun getCustomer(
        @PathVariable id: Long,
    ) = customerApplicationService.getCustomer(id)

    @GetMapping("/{id}/financial")
    fun getCustomerFinancial(
        @PathVariable id: Long,
    ) = customerApplicationService.getCustomerFinancial(id)

    @DeleteMapping("/{id}")
    fun deleteCustomer(
        @PathVariable id: Long,
    ) {
        customerApplicationService.deleteCustomer(id)
    }

    @PostMapping("/{id}/status:change")
    fun changeStatus(
        @RequestParam status: BaseStatus,
        @PathVariable id: Long,
    ) {
        customerApplicationService.changeStatus(id, status)
    }

    @PostMapping("/{id}/generate/open-api/key")
    fun generateOpenApiKey(
        @PathVariable id: Long,
    ): String = customerApplicationService.generateOpenApiKey(id)
}
