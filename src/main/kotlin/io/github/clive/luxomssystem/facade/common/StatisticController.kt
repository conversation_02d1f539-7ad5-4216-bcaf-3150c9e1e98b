package io.github.clive.luxomssystem.facade.common

import io.github.clive.luxomssystem.application.StatisticApplicationService
import io.github.clive.luxomssystem.common.PageReq
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController

@RestController
@RequestMapping("/api/statistics")
class StatisticController(
    private val statisticService: StatisticApplicationService,
) {
    @GetMapping("/failed")
    fun failed(pageRequest: PageReq): Map<String, Int> = statisticService.queryFailedWaybillCount(pageRequest)
}
