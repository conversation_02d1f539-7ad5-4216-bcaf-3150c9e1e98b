
package io.github.clive.luxomssystem.facade.suppliersku.response

import io.github.clive.luxomssystem.common.enums.BaseStatus
import io.github.clive.luxomssystem.domain.suppliersku.model.SupplierSku
import java.math.BigDecimal

data class SupplierSkuResponse(
    val id: Long,
    val bizId: Long,
    val supplierId: Long,
    val supplierSpuId: Long,
    val systemSpuId: Long,
    val systemSkuId: Long?,
    val systemSkuCode: String?,
    val purchaseCost: BigDecimal?,
    val purchaseCostCurrency: String?,
    val status: BaseStatus,
    val createdBy: Long?,
    val createdAt: Long?,
    val updatedBy: Long?,
    val updatedAt: Long?,
    val size: String?,
    val color: String?,
) {
    companion object {
        fun fromDomain(
            supplierSku: SupplierSku,
            size: String? = null,
            color: String? = null,
        ): SupplierSkuResponse =
            SupplierSkuResponse(
                id = supplierSku.id!!,
                bizId = supplierSku.bizId,
                supplierId = supplierSku.supplierId,
                supplierSpuId = supplierSku.supplierSpuId,
                systemSpuId = supplierSku.systemSpuId,
                systemSkuId = supplierSku.systemSkuId,
                systemSkuCode = supplierSku.systemSkuCode,
                purchaseCost = supplierSku.purchaseCost,
                purchaseCostCurrency = supplierSku.purchaseCostCurrency,
                status = supplierSku.status,
                createdBy = supplierSku.createdBy,
                createdAt = supplierSku.createdAt,
                updatedBy = supplierSku.updatedBy,
                updatedAt = supplierSku.updatedAt,
                size = size,
                color = color,
            )
    }
}
