package io.github.clive.luxomssystem.facade.customerSpu.response

import io.github.clive.luxomssystem.domain.customerSpu.model.CustomerSpu
import java.math.BigDecimal

data class CustomerSpuPageResponse(
    val id: Long,
    val bizId: Long,
    val customerId: Long,
    val systemSpuId: Long,
    val systemSpuCode: String?,
    val customerSpuCode: String?,
    val triggerDiscountQuantity: Int,
    val discount: BigDecimal,
    val status: String,
    val updatedByName: String,
    val updatedAt: Long?,
    val createdByName: String,
    val createdAt: Long?,
) {
    companion object {
        fun fromDomain(customerSpu: CustomerSpu): CustomerSpuPageResponse =
            CustomerSpuPageResponse(
                id = customerSpu.id,
                bizId = customerSpu.bizId,
                customerId = customerSpu.customerId,
                systemSpuId = customerSpu.systemSpuId,
                systemSpuCode = customerSpu.systemSpuCode,
                customerSpuCode = customerSpu.customerSpuCode,
                triggerDiscountQuantity = customerSpu.triggerDiscountQuantity,
                discount = customerSpu.discount,
                status = customerSpu.status.name,
                updatedByName = customerSpu.updatedByName,
                updatedAt = customerSpu.updatedAt,
                createdByName = customerSpu.createdByName,
                createdAt = customerSpu.createdAt,
            )
    }
}
