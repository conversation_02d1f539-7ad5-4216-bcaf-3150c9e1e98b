package io.github.clive.luxomssystem.facade.download.job

import io.github.clive.luxomssystem.common.enums.order.MainOrderImageDownloadStatus
import io.github.clive.luxomssystem.common.enums.order.MainOrderImageDownloadTask
import io.github.clive.luxomssystem.domain.doanload.MainOrder
import io.github.clive.luxomssystem.domain.doanload.OrderImageTaskStatus
import io.github.clive.luxomssystem.infrastructure.repository.jpa.MainOrderRepository
import io.github.clive.luxomssystem.infrastructure.repository.jpa.OrderImageTaskRepository
import io.github.oshai.kotlinlogging.KotlinLogging
import org.springframework.scheduling.annotation.Scheduled
import org.springframework.stereotype.Component
import org.springframework.transaction.annotation.Transactional

@Component
class MainOrderDownloadTaskJob(
    private val mainOrderRepository: MainOrderRepository,
    private val imageTaskRepository: OrderImageTaskRepository,
) {
    @Transactional
    @Scheduled(cron = "0 0/1 * * * ?")
    fun downloadMainTask() {
        log.info { "Start download task" }
        mainOrderRepository
            .findByImgDownloadStatus(MainOrderImageDownloadStatus.IMAGE_DOWNING)
            .map {
                handleMainOrderImagDownloadTask(it)
            }.also {
                log.info { "Download task finished" }
                mainOrderRepository.saveAll(it)
            }
    }

    private fun handleMainOrderImagDownloadTask(it: MainOrder): MainOrder {
        val res = imageTaskRepository.countByMainOrderId(it.id)
        var created = 0
        var processing = 0
        var completed = 0
        var failed = 0
        res.forEach { task ->
            val status = task["status"] as OrderImageTaskStatus
            val count = task["count"] as Long
            when (status) {
                OrderImageTaskStatus.CREATED -> created += count.toInt()
                OrderImageTaskStatus.PROCESSING -> processing += count.toInt()
                OrderImageTaskStatus.COMPLETED -> completed += count.toInt()
                OrderImageTaskStatus.FAILED -> failed += count.toInt()
            }
        }
        if (created == 0 && processing == 0 && failed == 0) {
            it.imgDownloadStatus = MainOrderImageDownloadStatus.IMAGE_DOWNED
            it.downloadTask = MainOrderImageDownloadTask(0, 0, completed, 0)
        } else {
            it.downloadTask = MainOrderImageDownloadTask(created, processing, completed, failed)
        }
        return it
    }

    companion object {
        val log = KotlinLogging.logger {}
    }
}
