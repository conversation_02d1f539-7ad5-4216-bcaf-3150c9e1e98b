package io.github.clive.luxomssystem.facade.auth.dto

import io.github.clive.luxomssystem.domain.doanload.auth.Permission
import io.github.clive.luxomssystem.domain.doanload.auth.PermissionType

data class PermissionRequest(
    val name: String,
    val description: String = "",
    val resource: String,
    val parentId: Long = -1,
    val type: PermissionType = PermissionType.MENU,
    val icon: String? = "",
)

data class PermissionResponse(
    val id: Long?,
    val name: String,
    val description: String,
    val resource: String,
    val parentId: Long,
    val type: PermissionType,
    val icon: String?,
    val createdAt: Long,
) {
    companion object {
        fun fromDomain(permission: Permission) =
            PermissionResponse(
                id = permission.id,
                name = permission.name,
                description = permission.description,
                resource = permission.resource,
                parentId = permission.parentId,
                type = permission.type,
                icon = permission.icon,
                createdAt = permission.createdAt!!,
            )
    }
}

fun PermissionRequest.toDomain() =
    Permission().apply {
        name = <EMAIL>
        description = <EMAIL>
        resource = <EMAIL>
        parentId = <EMAIL>
        type = <EMAIL>
        icon = <EMAIL>
    }
