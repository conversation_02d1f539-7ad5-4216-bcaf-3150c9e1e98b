package io.github.clive.luxomssystem.facade.supplierSpu.request

import jakarta.validation.constraints.NotBlank
import jakarta.validation.constraints.NotNull
import java.math.BigDecimal

data class CreateSupplierSpuRequest(
    @field:NotNull(message = "supplierId is required")
    val supplierId: Long,
    @field:NotNull(message = "systemSpuId is required")
    val systemSpuId: Long,
    @field:NotBlank(message = "supplierSpuCode is required")
    val supplierSpuCode: String,
    val title: String,
    val name: String,
    val cnName: String,
    val category: String,
    val skus: List<CreateSkuRequest>,
) {
    data class CreateSkuRequest(
        val systemSkuId: Long,
        val systemSkuCode: String,
        val size: String,
        val color: String,
        val purchaseCost: BigDecimal,
        val purchaseCostCurrency: String,
    )
}
