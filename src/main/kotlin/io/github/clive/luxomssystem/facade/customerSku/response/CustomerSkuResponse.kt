
package io.github.clive.luxomssystem.facade.customerSku.response

import io.github.clive.luxomssystem.domain.customerSku.model.CountryPrice
import io.github.clive.luxomssystem.domain.customerSku.model.CustomerSku
import io.github.clive.luxomssystem.domain.customerSku.model.PcsPrice
import java.math.BigDecimal

/**
 * Response object for CustomerSku data.
 */
data class CustomerSkuResponse(
    val id: Long,
    val bizId: Long,
    val customerId: Long,
    val systemSpuId: Long,
    val systemSkuId: Long,
    val systemSpuCode: String,
    val systemSkuCode: String,
    val customerSpuCode: String?,
    val customerSkuCode: String?,
    val size: String,
    val color: String,
    val offlinePrice: BigDecimal,
    val offlinePriceCurrency: String,
    val status: String,
    val createdAt: Long,
    val updatedAt: Long,
    val createdBy: Long,
    val updatedBy: Long,
    val skuCountryPrices: Map<Long, CountryPrice> = emptyMap(),
    val skuPcsPrices: List<PcsPrice> = emptyList(),
    val discount: BigDecimal? = null,
    val triggerDiscountQuantity: Int? = null,
) {
    companion object {
        fun fromDomain(customerSku: CustomerSku): CustomerSkuResponse =
            CustomerSkuResponse(
                id = customerSku.id,
                bizId = customerSku.bizId,
                customerId = customerSku.customerId,
                systemSpuId = customerSku.systemSpuId,
                systemSkuId = customerSku.systemSkuId,
                systemSpuCode = customerSku.systemSpuCode,
                systemSkuCode = customerSku.systemSkuCode,
                customerSpuCode = customerSku.customerSpuCode,
                customerSkuCode = customerSku.customerSkuCode,
                size = customerSku.size,
                color = customerSku.color,
                offlinePrice = customerSku.offlinePrice,
                offlinePriceCurrency = customerSku.offlinePriceCurrency,
                status = customerSku.status.name,
                createdAt = customerSku.createdAt!!,
                updatedAt = customerSku.updatedAt!!,
                createdBy = customerSku.createdBy,
                updatedBy = customerSku.updatedBy,
                skuCountryPrices = customerSku.skuCountryPrices,
                skuPcsPrices = customerSku.skuPcsPrices,
                discount = customerSku.discount,
                triggerDiscountQuantity = customerSku.triggerDiscountQuantity,
            )
    }
}
