
package io.github.clive.luxomssystem.facade.sku.response

import io.github.clive.luxomssystem.domain.sku.model.Sku
import java.math.BigDecimal

data class SkuResponse(
    val id: Long,
    val spuId: Long,
    val skuCode: String,
    val size: String,
    val color: String,
    val purchaseCost: BigDecimal,
    val purchaseCostCurrency: String,
    val salePrice: BigDecimal,
    val weight: BigDecimal,
    val volume: BigDecimal,
    val status: String,
    val updatedAt: Long?,
    val createdAt: Long?,
) {
    companion object {
        fun fromDomain(sku: Sku): SkuResponse =
            SkuResponse(
                id = sku.id,
                spuId = sku.spuId,
                skuCode = sku.skuCode,
                size = sku.size,
                color = sku.color,
                purchaseCost = sku.purchaseCost,
                purchaseCostCurrency = sku.purchaseCostCurrency,
                weight = sku.weight,
                volume = sku.volume,
                status = sku.status,
                updatedAt = sku.updatedAt,
                createdAt = sku.createdAt,
                salePrice = sku.salePrice,
            )
    }
}
