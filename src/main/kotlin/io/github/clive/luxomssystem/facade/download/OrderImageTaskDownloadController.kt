package io.github.clive.luxomssystem.facade.download

import io.github.clive.luxomssystem.application.DownloadQueryApplicationService
import io.github.clive.luxomssystem.common.PageResponse
import io.github.clive.luxomssystem.common.toResponse
import io.github.clive.luxomssystem.domain.doanload.OrderImageTask
import io.github.clive.luxomssystem.domain.doanload.OrderImageTaskStatus
import org.springframework.web.bind.annotation.*

@RequestMapping("/order-image-tasks")
@RestController
class OrderImageTaskDownloadController(
    private val downloadQueryApplicationService: DownloadQueryApplicationService,
) {
    @GetMapping("/download/page")
    fun page(
        pageIndex: Int,
        pageSize: Int,
        orderNo: String?,
        orderId: Long?,
        status: OrderImageTaskStatus?,
    ): PageResponse<OrderImageTask> =
        downloadQueryApplicationService.findDownloadOrderImageTask(pageIndex, pageSize, status, orderNo, orderId).toResponse()

    @PostMapping("/download/{id}/retry")
    fun retry(
        @PathVariable id: Long,
    ) {
        downloadQueryApplicationService.retryOrderImageTask(id)
    }
}
