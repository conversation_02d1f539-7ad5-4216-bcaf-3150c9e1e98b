package io.github.clive.luxomssystem.facade

import io.github.clive.luxomssystem.application.SubOrderApplicationService
import io.github.clive.luxomssystem.application.suborder.SubOrderQueryApplicationService
import io.github.clive.luxomssystem.common.enums.WaybillChannel
import io.github.clive.luxomssystem.facade.order.req.PageQuerySubOrderRequest
import org.springframework.web.bind.annotation.*

@RestController
@RequestMapping("/api/sub-order")
class SubOrderController(
    private val subOrderApplicationService: SubOrderApplicationService,
    private val subOrderQueryApplicationService: SubOrderQueryApplicationService,
) {
    data class UpdateSubOrderRequest(
        val id: Long,
        var designUrl: String? = null,
        var effectUrl: String? = null,
        var designImgSearchFiled: String? = null,
        var effectImgSearchFiled: String? = null,
        var color: String? = null,
        var size: String? = null,
    )

    @PostMapping("/page")
    fun page(
        @RequestBody req: PageQuerySubOrderRequest,
        paramReq: PageQuerySubOrderRequest,
    ) = subOrderApplicationService.pageQuery(
        paramReq.apply {
            orderNos = req.orderNos
            customerOrderNos = req.customerOrderNos
        },
    )

    @PostMapping("/page/simple")
    fun pageSimple(
        @RequestBody req: PageQuerySubOrderRequest,
        paramReq: PageQuerySubOrderRequest,
    ) = subOrderApplicationService.pageQuery(
        paramReq.apply {
            orderNos = req.orderNos
            customerOrderNos = req.customerOrderNos
        },
    )

    @PostMapping("/update")
    fun update(
        @RequestBody req: UpdateSubOrderRequest,
    ) = subOrderApplicationService.update(req)

    @PostMapping("/{id}/:cancel")
    fun cancel(
        @PathVariable id: Long,
    ) = subOrderApplicationService.cancelSubOrder(id)

    @PostMapping("/remove/img/{id}")
    fun removeImg(
        @PathVariable id: Long,
        imgUrl: String,
    ) = subOrderApplicationService.removeImg(id, imgUrl)

    @PostMapping("/{id}/retry/download")
    fun retryDownload(
        @PathVariable id: Long,
    ) {
        subOrderApplicationService.retryDownload(id)
    }

    @DeleteMapping("/{id}")
    fun deleteSubOrder(
        @PathVariable id: Long,
    ) {
        subOrderApplicationService.deleteSubOrder(id)
    }

    @DeleteMapping("/batch")
    fun deleteSubOrderBatch(
        @RequestBody ids: List<Long>,
    ) {
        subOrderApplicationService.deleteSubOrderBatch(ids)
    }

    @GetMapping("/day/status/count")
    fun countByStatus(select: Int) = subOrderApplicationService.getSubOrderStatusCounts(select)

    @PostMapping("/select/supplier")
    fun selectSupplierForSubOrders(
        @RequestBody req: SubOrderSelectSupplierRequest,
    ) = subOrderApplicationService.selectSupplierForSubOrder(req)

    @GetMapping("/{order-no}/img-urls")
    fun getImgUrls(
        @PathVariable("order-no") orderNo: String,
    ) = subOrderApplicationService.getImgUrls(orderNo)

    @PostMapping("/channel:change")
    fun batchUpdateOrdersChannel(
        @RequestBody req: BatchUpdateOrdersChannelRequest,
    ) = subOrderApplicationService.batchUpdateOrdersChannel(req)

    @PostMapping("/merge")
    fun merge(
        @RequestBody req: MergeSubOrderRequest,
    ) = subOrderApplicationService.merge(req)

    @PostMapping("/split")
    fun split(
        @RequestBody req: List<Map<Long, Int>>,
    ) = subOrderApplicationService.split(
        req.map { map -> map.filterValues { value -> value != 0 } },
    )

    @GetMapping("/{id}/merged-orders")
    fun findMergedOrders(
        @PathVariable id: Long,
    ) = subOrderQueryApplicationService.findMergedSubOrders(id)

    @GetMapping("/{id}/split-orders")
    fun findSplitOrders(
        @PathVariable id: Long,
    ) = subOrderQueryApplicationService.findSplitSubOrders(id)

    @PostMapping("/{id}/match-supplier")
    fun matchSupplier(
        @PathVariable("id") id: Long,
    ) {
        subOrderApplicationService.matchSupplier(id)
    }
}

data class SubOrderSelectSupplierRequest(
    val supplierId: Long,
    val subOrderIds: List<Long>,
)

data class BatchUpdateOrdersChannelRequest(
    val ids: List<Long>,
    val channel: WaybillChannel,
    val method: String,
)

data class MergeSubOrderRequest(
    val ids: List<Long>,
)
