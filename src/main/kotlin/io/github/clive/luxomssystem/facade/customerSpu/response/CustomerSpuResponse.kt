package io.github.clive.luxomssystem.facade.customerSpu.response

import io.github.clive.luxomssystem.domain.customerSpu.model.CustomerSpu
import io.github.clive.luxomssystem.domain.customerSpu.model.SupplierCapacity
import io.github.clive.luxomssystem.facade.customerSku.response.CustomerSkuResponse
import java.math.BigDecimal

data class CustomerSpuResponse(
    val id: Long,
    val bizId: Long,
    val customerId: Long,
    val systemSpuId: Long,
    val systemSpuCode: String?,
    val customerSpuCode: String?,
    val triggerDiscountQuantity: Int,
    val discount: BigDecimal,
    val status: String,
    val updatedBy: Long?,
    val updatedAt: Long?,
    val createdBy: Long?,
    val createdAt: Long?,
    val skus: List<CustomerSkuResponse>?,
    var supplierCapacities: List<SupplierCapacity> = emptyList(),
) {
    companion object {
        fun fromDomain(
            customerSpu: CustomerSpu,
            skus: List<CustomerSkuResponse>,
        ): CustomerSpuResponse =
            CustomerSpuResponse(
                id = customerSpu.id,
                bizId = customerSpu.bizId,
                customerId = customerSpu.customerId,
                systemSpuId = customerSpu.systemSpuId,
                systemSpuCode = customerSpu.systemSpuCode,
                customerSpuCode = customerSpu.customerSpuCode,
                triggerDiscountQuantity = customerSpu.triggerDiscountQuantity,
                discount = customerSpu.discount,
                status = customerSpu.status.name,
                updatedBy = customerSpu.updatedBy,
                updatedAt = customerSpu.updatedAt,
                createdBy = customerSpu.createdBy,
                createdAt = customerSpu.createdAt,
                skus = skus,
                supplierCapacities = customerSpu.supplierCapacities,
            )
    }
}
