
package io.github.clive.luxomssystem.facade.suppliersku.request

import jakarta.validation.constraints.NotNull
import java.math.BigDecimal

data class CreateSupplierSkuRequest(
    @field:NotNull
    val supplierId: Long,
    @field:NotNull
    val supplierSpuId: Long,
    @field:NotNull
    val systemSpuId: Long,
    val systemSkuId: Long? = null,
    val systemSkuCode: String? = null,
    val purchaseCost: BigDecimal? = null,
    val purchaseCostCurrency: String? = null,
)
