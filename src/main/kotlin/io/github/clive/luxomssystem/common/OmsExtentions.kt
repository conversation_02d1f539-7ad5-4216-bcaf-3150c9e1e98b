package io.github.clive.luxomssystem.common

import org.springframework.data.domain.Page
import org.springframework.data.domain.PageRequest
import org.springframework.format.annotation.DateTimeFormat
import java.time.LocalDate
import java.time.ZoneOffset

inline fun <reified T> Page<T>.toResponse() =
    PageResponse(
        content = content,
        total = totalElements,
        totalPages = totalPages,
    )

open class PageReq {
    var pageIndex: Int = 0

    var pageSize: Int = 10

    @DateTimeFormat(pattern = "yyyy-MM-dd")
    var createdAtFrom: LocalDate? = null

    @DateTimeFormat(pattern = "yyyy-MM-dd")
    var createdAtTo: LocalDate? = null

    fun createdAtFromEpochMilli() = createdAtFrom?.atStartOfDay()?.toInstant(ZoneOffset.UTC)?.toEpochMilli()

    fun createdAtToEpochMilli() =
        createdAtTo
            ?.plusDays(1)
            ?.atStartOfDay()
            ?.toInstant(ZoneOffset.UTC)
            ?.toEpochMilli()
}

fun PageReq.toPageable() = PageRequest.of(pageIndex, pageSize)

data class PageResponse<T>(
    val content: List<T>,
    val total: Long,
    val totalPages: Int,
) {
    companion object {
        fun <T> empty() = PageResponse<T>(emptyList(), 0, 0)
    }
}
