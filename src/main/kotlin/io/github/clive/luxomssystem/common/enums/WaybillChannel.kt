@file:Suppress("SpellCheckingInspection")

package io.github.clive.luxomssystem.common.enums

enum class WaybillChannel(
    val channel: String,
    val shipMethods: List<ShipMethod>,
    val displayName: String,
) {
    YUNTU(
        "YT",
        listOf(
            BKPHR,
            THZXR,
            THPHR,
            FZZXR,
            EUB_SZ,
            THPHZXUS,
            DHZXRPH,
        ),
        "云途",
    ),
    YWBZ(
        "YWBZ",
        listOf(
            BZ_USPS,
            BZ_CANADA,
            BZ_MEXICO,
            BZ_EU_THXB,
            USPS_ECONOMY,
            US_FCPM,
            BZ_CANADA_REGULAR,
        ),
        "义乌百洲",
    ),
    SF(
        "SF",
        listOf(
            INT0054,
            INT0255,
            INT0257,
            INT0554,
        ),
        "顺丰",
    ),

    SDH("SDH", listOf(), "闪电猴"),
    ZM("ZM", listOf(), "哲马"),

    CAINIAO("CN", listOf(), "菜鸟"),

    FOURPX("4PX", listOf(), "4PX"),

    YW_HZ("YW-HZ", listOf(), "燕文-杭州"),
    YW_QZ("YW-QZ", listOf(), "燕文-泉州"),
    YW_GZ("YW-GZ", listOf(), "燕文-广州"),

    YW_YW("YW-YW", listOf(), "燕文-义乌"),

    CHANGLIAN("CL", listOf(), "常联"),
    ;

    fun displayName(): String = displayName

    companion object {
        fun matchchannel(channel: String): WaybillChannel? = entries.find { it.channel == channel }
    }
}

data class ShipMethod(
    val code: String,
    val name: String,
)

val BZ_EU_THXB = ShipMethod("BZ021", "BZ-E速宝欧洲特惠小包F")
val BZ_CANADA = ShipMethod("PK0001", "BZ-加拿大邮政快捷--普货")
val BZ_USPS = ShipMethod("PK0074", "BZ-USPS经济线(普货)")
val BZ_MEXICO = ShipMethod("BZ077", "BZ-墨西哥专线")
val USPS_ECONOMY = ShipMethod("PK0075", "BZ-USPS经济线普货(出转单）")
val US_FCPM = ShipMethod("BZ0005", "BZ-美国FCPM线(出转单)")
val BZ_CANADA_REGULAR = ShipMethod("BZ007", "BZ-加拿大邮政普货(出转单)")

val BKPHR = ShipMethod("BKPHR", "云途全球专线挂号（标快普货）")
val THZXR = ShipMethod("THZXR", "云途全球专线挂号（特惠带电）")
val THPHR = ShipMethod("THPHR", "云途全球专线挂号（特惠普货）")
val FZZXR = ShipMethod("FZZXR", "云途全球服装专线挂号")
val EUB_SZ = ShipMethod("EUB-SZ", "省内EUB")
val THPHZXUS = ShipMethod("THPHZXUS", "美国岛屿专线（特惠普货）")
val DHZXRPH = ShipMethod("DHZXRPH", "云途大货专线挂号(特惠普货)")

val INT0054 = ShipMethod("INT0054", "顺丰-国际小包")
val INT0255 = ShipMethod("INT0255", "顺丰-国际电商专递-标准")
val INT0257 = ShipMethod("INT0257", "顺丰-国际电商专递-快速")
val INT0554 = ShipMethod("INT0554", "顺丰-国际电商专递-优先CD")

fun List<ShipMethod>.match(code: String): ShipMethod? = this.find { it.code == code }
