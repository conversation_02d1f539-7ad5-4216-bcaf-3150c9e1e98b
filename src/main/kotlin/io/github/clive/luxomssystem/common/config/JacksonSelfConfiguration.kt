package io.github.clive.luxomssystem.common.config

import com.fasterxml.jackson.annotation.JsonInclude
import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.databind.module.SimpleModule
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.context.annotation.Configuration

@Configuration
internal class JacksonSelfConfiguration {
    @Autowired
    fun config(objectMapper: ObjectMapper) {
        val simpleModule = SimpleModule()
        simpleModule.addSerializer(Long::class.java, ToStringSerializer.instance)
        simpleModule.addSerializer(java.lang.Long.TYPE, ToStringSerializer.instance)
        simpleModule.addSerializer(Long::class.javaObjectType, ToStringSerializer.instance)
        objectMapper.registerModule(simpleModule)

        // Configure ObjectMapper to ignore null values during serialization
        objectMapper.setSerializationInclusion(JsonInclude.Include.NON_NULL)
    }
}
