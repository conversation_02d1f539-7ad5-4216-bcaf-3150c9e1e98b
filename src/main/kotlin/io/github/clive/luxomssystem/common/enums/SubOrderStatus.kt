package io.github.clive.luxomssystem.common.enums

enum class SubOrderStatus {
    CREATED,
    CANCELLED,
    COMPLETED,
    SPLIT,
    SUPPLIER_MATCHED,
    FAILED,

    // 轨迹相关状态 - 与TrackingStatus对应
    TRACKING_NOT_FOUND,
    TRACKING_PRE_ADVICE_RECEIVED,
    TRACKING_PICKED_UP,
    TRACKING_IN_TRANSIT,
    TRACKING_ARRIVED_DESTINATION_COUNTRY,
    TRACKING_IN_CUSTOMS,
    TRACKING_CUSTOMS_CLEARED,
    TRACKING_ARRIVED_FOR_PICKUP,
    TRACKING_OUT_FOR_DELIVERY,
    TRAC<PERSON>ING_DELIVERY_FAILED,
    TRACKING_DELIVERED,
    TRACKING_EXCEPTION,
    TRACKING_RETURNED,
    TRACKING_CANCELLED,
    TRACKING_UNKNOWN,
    ;

    fun onSplitStatus() = this == SPLIT
}
