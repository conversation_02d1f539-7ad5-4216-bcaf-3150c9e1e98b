package io.github.clive.luxomssystem.common.utils

import com.fasterxml.jackson.databind.ObjectMapper
import org.springframework.beans.BeansException
import org.springframework.context.ApplicationContext
import org.springframework.context.ApplicationContextAware
import org.springframework.stereotype.Component

@Component
class JSON : ApplicationContextAware {
    @Throws(BeansException::class)
    override fun setApplicationContext(applicationContext: ApplicationContext) {
        mapper =
            applicationContext.getBean(
                ObjectMapper::class.java,
            )
    }

    companion object {
        var mapper: ObjectMapper? = null

        fun toJSONString(`object`: Any?): String {
            try {
                return mapper!!.writeValueAsString(`object`)
            } catch (e: Exception) {
                throw RuntimeException(e)
            }
        }

        inline fun <reified T> parseObject(json: String?): T {
            try {
                return mapper!!.readValue(json, T::class.java)
            } catch (e: Exception) {
                throw RuntimeException(e)
            }
        }
    }
}
