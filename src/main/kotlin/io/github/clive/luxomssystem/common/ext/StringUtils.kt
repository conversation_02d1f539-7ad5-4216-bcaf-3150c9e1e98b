package io.github.clive.luxomssystem.common.ext

import java.net.URI

fun String.removeExtension(): String {
    val index = this.lastIndexOf(".")
    return if (index == -1) {
        this
    } else {
        this.substring(0, index)
    }
}

fun String.getExtension(): String {
    val index = this.lastIndexOf(".")
    return if (index == -1) {
        ""
    } else {
        this.substring(index + 1)
    }
}

fun String.isValidUrl(): Boolean =
    try {
        val uri = URI(this) // 创建 URI 实例
        uri.isAbsolute && (uri.scheme == "http" || uri.scheme == "https") // 检查 scheme 是否为 http/https
    } catch (e: Exception) {
        false
    }

/**
 * 扩展函数，移除字符串中基本不适合出现在名字和地址中的特殊字符。
 *
 * @param charsToRemove 要移除的字符集，默认为一个通用的不适合字符集。
 * @return 移除特殊字符后的新字符串。
 */
fun String?.removeCommonInvalidChars(charsToRemove: Set<Char> = commonInvalidChars): String? =
    this?.filter { it !in charsToRemove }?.removeAllSpaces()

/**
 * 扩展函数，移除字符串中通常不适合出现在名字中的特殊字符。
 *
 * @param charsToRemove 要移除的字符集，默认为名字中不适合的字符集。
 * @return 移除特殊字符后的新字符串。
 */
fun String?.removeNameSpecialChars(charsToRemove: Set<Char> = nameInvalidChars): String? = this?.filter { it !in charsToRemove }

/**
 * 扩展函数，移除字符串中通常不适合出现在地址中的特殊字符。
 *
 * @param charsToRemove 要移除的字符集，默认为地址中不适合的字符集。
 * @return 移除特殊字符后的新字符串。
 */
fun String.removeAddressSpecialChars(charsToRemove: Set<Char> = addressInvalidChars): String = this.filter { it !in charsToRemove }

// 通用不适合字符集 (基本不适合出现在名字和地址中)
private val commonInvalidChars =
    setOf(
        '!',
        '@',
        '#',
        '$',
        '%',
        '^',
        '&',
        '*',
        '=',
        '+',
        '<',
        '>',
        '[',
        ']',
        '{',
        '}',
        '|',
        '\\',
        '/',
        '?',
        '~',
        '`',
    )

// 名字不适合字符集 (除了通用集，还包括一些在名字中不常见的)
// 名字不适合字符集 (除了通用集，还包括一些在名字中不常见的)
private val nameInvalidChars =
    commonInvalidChars +
        setOf(
            '(',
            ')', // 括号在名字里不太常见
            '_', // 下划线在名字里不太常见
            '0',
            '1',
            '2',
            '3',
            '4',
            '5',
            '6',
            '7',
            '8',
            '9', // 数字通常不直接出现在人名中（除非是昵称或特殊情况）
        )

// 地址不适合字符集 (除了通用集, 允许一些地址中常见的字符)
private val addressInvalidChars =
    commonInvalidChars -
        setOf(
            '#', // 允许井号，用于表示楼层、房间号等
            '-', // 允许连字符，用于门牌号等
            ',',
            '.', // 地址中小数点和逗号可以存在
        )

fun String.removeAllSpaces(): String = this.replace("\\s".toRegex(), "")
