@file:Suppress("PrivatePropertyName")

package io.github.clive.luxomssystem.common.exception

import io.github.oshai.kotlinlogging.KotlinLogging
import org.hibernate.exception.ConstraintViolationException
import org.springframework.http.HttpStatus
import org.springframework.http.MediaType
import org.springframework.http.ResponseEntity
import org.springframework.http.converter.HttpMessageNotReadableException
import org.springframework.validation.BindException
import org.springframework.web.HttpRequestMethodNotSupportedException
import org.springframework.web.bind.MethodArgumentNotValidException
import org.springframework.web.bind.MissingServletRequestParameterException
import org.springframework.web.bind.annotation.ExceptionHandler
import org.springframework.web.bind.annotation.RestControllerAdvice
import java.sql.SQLIntegrityConstraintViolationException

@RestControllerAdvice
class BiminiGlobalExceptionHandler {
    @ExceptionHandler(Throwable::class)
    fun handleThrowable(ex: Throwable): ResponseEntity<*> {
        log.error(ex) { "未特定的异常信息:" }
        return ResponseEntity
            .status(HttpStatus.SERVICE_UNAVAILABLE)
            .contentType(MediaType.APPLICATION_JSON)
            .body(ErrorResult.of(HttpStatus.BAD_REQUEST, ex.message ?: "Service error"))
    }

    @ExceptionHandler(IllegalStateException::class)
    fun handleIllegalStateException(ex: IllegalStateException): ResponseEntity<ErrorResult> {
        log.error(ex) { "未特定的异常信息:" }
        return ResponseEntity
            .status(HttpStatus.BAD_REQUEST)
            .contentType(MediaType.APPLICATION_JSON)
            .body(ErrorResult.of(HttpStatus.BAD_REQUEST, ex.message ?: ""))
    }

    @ExceptionHandler(CognitionWebException::class)
    fun handleCognitionWebException(ex: CognitionWebException): ResponseEntity<ErrorResult> {
        log.error(ex) { "认知系统网络异常:" }
        return ResponseEntity.status(ex.httpCode()).contentType(MediaType.APPLICATION_JSON).body(ErrorResult.of(ex))
    }

    @ExceptionHandler(CognitionException::class)
    fun handleCognitionWebException(ex: CognitionException): ResponseEntity<ErrorResult> {
        log.error(ex) { "认知系统异常:" }
        return ResponseEntity
            .status(HttpStatus.BAD_REQUEST)
            .contentType(MediaType.APPLICATION_JSON)
            .body(ErrorResult.of(HttpStatus.BAD_REQUEST, ex.message))
    }

    @ExceptionHandler(HttpRequestMethodNotSupportedException::class)
    fun handleHttpRequestMethodNotSupportedException(e: HttpRequestMethodNotSupportedException): ResponseEntity<ErrorResult> {
        log.error(e) { "[客户端HTTP请求方法错误]" }
        return ResponseEntity
            .status(HttpStatus.BAD_REQUEST)
            .contentType(MediaType.APPLICATION_JSON)
            .body(ErrorResult.of(HttpStatus.BAD_REQUEST, "Request method not supported"))
    }

    @ExceptionHandler(MethodArgumentNotValidException::class)
    fun handleMethodArgumentNotValidException(e: MethodArgumentNotValidException): ResponseEntity<ErrorResult> {
        log.error(e) { "[客户端请求体参数校验不通过]" }
        val errorMsg = e.bindingResult.fieldErrors.joinToString { "${it.field}=[${it.defaultMessage}]" }
        return ResponseEntity.status(HttpStatus.BAD_REQUEST).contentType(MediaType.APPLICATION_JSON).body(
            ErrorResult.of(HttpStatus.BAD_REQUEST, errorMsg),
        )
    }

    @ExceptionHandler(HttpMessageNotReadableException::class)
    fun handleHttpMessageNotReadableException(e: HttpMessageNotReadableException): ResponseEntity<ErrorResult> {
        log.error(e) { "[客户端请求体JSON格式错误或字段类型不匹配]" }
        return ResponseEntity
            .status(HttpStatus.BAD_REQUEST)
            .contentType(MediaType.APPLICATION_JSON)
            .body(ErrorResult.of(HttpStatus.BAD_REQUEST, "Request body format error"))
    }

    @ExceptionHandler(BindException::class)
    fun handleBindException(e: BindException): ResponseEntity<ErrorResult> {
        log.error(e) { "[客户端URL中的参数类型错误]" }
        val fieldError = e.bindingResult.fieldError
        val errorMsg =
            fieldError?.let {
                if (it.defaultMessage?.contains("java.lang.NumberFormatException") == true) {
                    "${it.field} must be a number"
                } else {
                    it.defaultMessage
                }
            }
        return ResponseEntity
            .status(HttpStatus.BAD_REQUEST)
            .contentType(MediaType.APPLICATION_JSON)
            .body(ErrorResult.of(HttpStatus.BAD_REQUEST, errorMsg ?: "Bind Exception"))
    }

    @ExceptionHandler(ConstraintViolationException::class)
    fun handleConstraintViolationException(e: ConstraintViolationException): ResponseEntity<ErrorResult> {
        log.error(e) { "[客户端请求参数校验不通过]" }
        return ResponseEntity
            .status(HttpStatus.BAD_REQUEST)
            .contentType(MediaType.APPLICATION_JSON)
            .body(ErrorResult.of(HttpStatus.BAD_REQUEST, e.message ?: ""))
    }

    @ExceptionHandler(MissingServletRequestParameterException::class)
    fun handleMissingServletRequestParameterException(e: MissingServletRequestParameterException): ResponseEntity<ErrorResult> {
        log.error(e) { "[客户端请求缺少必填的参数]" }
        val errorMsg = "${e.parameterName} is required"
        return ResponseEntity
            .status(HttpStatus.BAD_REQUEST)
            .contentType(MediaType.APPLICATION_JSON)
            .body(ErrorResult.of(HttpStatus.BAD_REQUEST, errorMsg))
    }

    @ExceptionHandler(IllegalArgumentException::class)
    fun handleIllegalArgumentException(e: IllegalArgumentException): ResponseEntity<ErrorResult> {
        log.error(e) { "[业务方法参数检查不通过]" }
        return ResponseEntity
            .status(HttpStatus.BAD_REQUEST)
            .contentType(MediaType.APPLICATION_JSON)
            .body(ErrorResult.of(HttpStatus.BAD_REQUEST, e.message ?: "Illegal Argument"))
    }

    @ExceptionHandler(SQLIntegrityConstraintViolationException::class)
    fun handleSQLIntegrityConstraintViolationException(e: SQLIntegrityConstraintViolationException): ResponseEntity<ErrorResult> {
        log.error(e) { "[ 唯一键重复 ]" }
        return ResponseEntity
            .status(HttpStatus.BAD_REQUEST)
            .contentType(MediaType.APPLICATION_JSON)
            .body(ErrorResult.of(HttpStatus.BAD_REQUEST, "data already exists"))
    }

    companion object {
        private val log = KotlinLogging.logger { }
    }
}
