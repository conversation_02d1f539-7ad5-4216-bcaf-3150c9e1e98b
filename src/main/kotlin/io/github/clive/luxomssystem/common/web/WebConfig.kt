// package io.github.clive.luxomssystem.common.web
//
// import org.springframework.stereotype.Component
// import org.springframework.web.servlet.config.annotation.CorsRegistry
// import org.springframework.web.servlet.config.annotation.InterceptorRegistry
// import org.springframework.web.servlet.config.annotation.WebMvcConfigurer
//
// @Component
// class WebConfig(
// ) : WebMvcConfigurer {
//
//    override fun addInterceptors(registry: InterceptorRegistry) {
// //        registry.addInterceptor(apiSignatureInterceptor).addPathPatterns("/**")
// //            .excludePathPatterns("/users/login", "/google/callback","/test/**")
// //        registry.addInterceptor(branchAdminInterceptor).addPathPatterns("/**") // 拦截所有请求
//    }
//
//    override fun addCorsMappings(registry: CorsRegistry) {
//        registry.addMapping("/**")
//            .allowedOrigins("*")
//            .allowedMethods("*")
//            .allowedHeaders("*")
//    }
//
// }
//
