package io.github.clive.luxomssystem.common.config

import io.github.clive.luxomssystem.infrastructure.config.auth.UserContextHolder
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import org.springframework.core.task.TaskDecorator
import org.springframework.scheduling.annotation.EnableAsync
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor
import java.util.concurrent.Executor
import java.util.concurrent.ThreadPoolExecutor

@EnableAsync
@Configuration
class AsyncConfig {
    @Bean
    fun taskExecutor(): Executor {
        val executor = ThreadPoolTaskExecutor()
        executor.corePoolSize = 10 // Base number of threads
        executor.maxPoolSize = 100 // Maximum number of threads
        executor.queueCapacity = 10000 // Queue capacity when all threads are busy
        executor.setThreadNamePrefix("lux-async-")
        executor.setTaskDecorator(ContextCopyingDecorator())
        executor.setRejectedExecutionHandler(ThreadPoolExecutor.CallerRunsPolicy()) // 拒绝策略：使用调用线程执行
        executor.initialize()
        return executor
    }

    @Bean("waybillExecutor")
    fun waybillExecutor(): Executor {
        val executor = ThreadPoolTaskExecutor()
        executor.corePoolSize = 20 // Base number of threads
        executor.maxPoolSize = 100 // Maximum number of threads
        executor.queueCapacity = 10000 // Queue capacity when all threads are busy
        executor.setThreadNamePrefix("waybill-async-")
        executor.setTaskDecorator(ContextCopyingDecorator())
        executor.setRejectedExecutionHandler(ThreadPoolExecutor.CallerRunsPolicy()) // 拒绝策略：使用调用线程执行
        executor.initialize()
        return executor
    }

    class ContextCopyingDecorator : TaskDecorator {
        override fun decorate(runnable: Runnable): Runnable {
            val userContext = UserContextHolder.user
            return Runnable {
                try {
                    if (userContext != null) {
                        UserContextHolder.set(userContext)
                    }
                    runnable.run()
                } finally {
                    UserContextHolder.remove()
                }
            }
        }
    }
}
