package io.github.clive.luxomssystem.common.utils

import java.util.regex.Pattern

/**
 * 密码验证工具类
 */
object PasswordValidator {

    // 密码最小长度
    private const val MIN_LENGTH = 8

    // 密码最大长度
    private const val MAX_LENGTH = 32

    // 特殊字符集合
    private const val SPECIAL_CHARS = "!@#$%^&*()_+-=[]{}|;:,.<>?"

    /**
     * 验证密码强度
     * 要求：
     * 1. 长度8-32位
     * 2. 包含大写字母
     * 3. 包含小写字母
     * 4. 包含数字
     * 5. 包含特殊字符
     */
    fun validatePassword(password: String): PasswordValidationResult {
        val errors = mutableListOf<String>()

        // 检查长度
        if (password.length < MIN_LENGTH) {
            errors.add("密码长度不能少于${MIN_LENGTH}位")
        }
        if (password.length > MAX_LENGTH) {
            errors.add("密码长度不能超过${MAX_LENGTH}位")
        }

        // 检查是否包含大写字母
        if (!password.any { it.isUpperCase() }) {
            errors.add("密码必须包含至少一个大写字母")
        }

        // 检查是否包含小写字母
        if (!password.any { it.isLowerCase() }) {
            errors.add("密码必须包含至少一个小写字母")
        }

        // 检查是否包含数字
        if (!password.any { it.isDigit() }) {
            errors.add("密码必须包含至少一个数字")
        }

        // 检查是否包含特殊字符
        if (!password.any { SPECIAL_CHARS.contains(it) }) {
            errors.add("密码必须包含至少一个特殊字符($SPECIAL_CHARS)")
        }

        return PasswordValidationResult(
            isValid = errors.isEmpty(),
            errors = errors
        )
    }

    /**
     * 验证邮箱格式
     */
    fun validateEmail(email: String): Boolean {
        val emailPattern = Pattern.compile(
            "^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$"
        )
        return emailPattern.matcher(email).matches()
    }

    /**
     * 密码验证结果
     */
    data class PasswordValidationResult(
        val isValid: Boolean,
        val errors: List<String>,
    )
}
