package io.github.clive.luxomssystem.common.config

import io.github.oshai.kotlinlogging.KotlinLogging
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import org.springframework.scheduling.TaskScheduler
import org.springframework.scheduling.annotation.EnableScheduling
import org.springframework.scheduling.annotation.SchedulingConfigurer
import org.springframework.scheduling.concurrent.ThreadPoolTaskScheduler
import org.springframework.scheduling.config.ScheduledTaskRegistrar
import java.util.concurrent.RejectedExecutionHandler
import java.util.concurrent.ThreadPoolExecutor

/**
 * 定时任务配置
 * 优化定时任务的线程池配置，防止任务卡死影响其他定时任务
 */
@Configuration
@EnableScheduling
class SchedulingConfig : SchedulingConfigurer {

    private val log = KotlinLogging.logger {}

    override fun configureTasks(taskRegistrar: ScheduledTaskRegistrar) {
        taskRegistrar.setScheduler(taskScheduler())
    }

    @Bean
    fun taskScheduler(): TaskScheduler {
        val scheduler = ThreadPoolTaskScheduler()

        // 设置线程池大小
        scheduler.poolSize = 10 // 增加线程池大小，避免单个任务阻塞其他任务

        // 设置线程名前缀
        scheduler.setThreadNamePrefix("scheduled-task-")

        // 设置拒绝策略：记录日志并丢弃任务
        scheduler.setRejectedExecutionHandler(LoggingRejectedExecutionHandler())

        // 设置等待任务完成的时间
        scheduler.setWaitForTasksToCompleteOnShutdown(true)
        scheduler.setAwaitTerminationSeconds(60)

        // 初始化
        scheduler.initialize()

        log.info { "定时任务调度器已配置 | 线程池大小: ${scheduler.poolSize}" }

        return scheduler
    }

    /**
     * 自定义拒绝策略：记录日志
     */
    class LoggingRejectedExecutionHandler : RejectedExecutionHandler {
        private val log = KotlinLogging.logger {}

        override fun rejectedExecution(r: Runnable, executor: ThreadPoolExecutor) {
            log.warn {
                "定时任务被拒绝执行 | " +
                        "活跃线程数: ${executor.activeCount} | " +
                        "队列大小: ${executor.queue.size} | " +
                        "任务: ${r.javaClass.simpleName}"
            }
        }
    }
}
