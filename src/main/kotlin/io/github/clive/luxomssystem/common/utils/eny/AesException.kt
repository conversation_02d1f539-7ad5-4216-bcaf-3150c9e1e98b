package io.github.clive.luxomssystem.common.utils.eny

class AesException( // public final static int EncodeBase64Error = -40009;
    val code: Int,
) : Exception(getMessage(code)) {
    companion object {
        const val OK: Int = 0
        const val ValidateSignatureError: Int = -40001
        const val ParseXmlError: Int = -40002
        const val ComputeSignatureError: Int = -40003
        const val IllegalAesKey: Int = -40004
        const val ValidateAppidError: Int = -40005
        const val EncryptAESError: Int = -40006
        const val DecryptAESError: Int = -40007
        const val IllegalBuffer: Int = -40008

        private fun getMessage(code: Int): String? =
            when (code) {
                ValidateSignatureError -> "签名验证错误"
                ParseXmlError -> "xml解析失败"
                ComputeSignatureError -> "sha加密生成签名失败"
                IllegalAesKey -> "SymmetricKey非法"
                ValidateAppidError -> "appid校验失败"
                EncryptAESError -> "aes加密失败"
                DecryptAESError -> "aes解密失败"
                IllegalBuffer -> "解密后得到的buffer非法"
                else -> null // cannot be
            }
    }
}
