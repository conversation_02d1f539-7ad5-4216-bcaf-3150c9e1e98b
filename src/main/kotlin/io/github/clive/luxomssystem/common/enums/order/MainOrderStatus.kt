package io.github.clive.luxomssystem.common.enums.order

enum class MainOrderStatus {
    CREATED,
    COMPLETED,
    MATCHING,
    PARTIALLY_MATCHED,
    MATCHED,
    CANCELED,
    FAILED,
}

enum class MainOrderImageDownloadStatus {
    IMAGE_DOWNING,
    IMAGE_DOWNED,
    ZIP_DOING,
    ZIP_DONE,
}

data class MainOrderImageDownloadTask(
    val created: Int = 0,
    val processing: Int = 0,
    val completed: Int = 0,
    val failed: Int = 0,
)
