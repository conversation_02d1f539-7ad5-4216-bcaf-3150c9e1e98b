package io.github.clive.luxomssystem.common.utils

import java.util.Locale.getDefault

/**
 * <AUTHOR>
 * @Date 2023/06/17 10:40
 */
enum class EUCountryEnum(
    private val key: String,
    private val value: String,
    private val enName: String,
) {
    BG("BG", "保加利亚", "Bulgaria"),
    DK("DK", "丹麦", "Denmark"),
    DE("DE", "德国", "Germany"),
    EE("EE", "爱沙尼亚", "Estonia"),
    FI("FI", "芬兰", "Finland"),
    FR("FR", "法国", "France"),
    GR("GR", "希腊", "Greece"),
    IE("IE", "爱尔兰", "Ireland"),
    IT("IT", "意大利", "Italy"),
    HR("HR", "克罗地亚", "Croatia"),
    LV("LV", "拉脱维亚", "Latvia"),
    LT("LT", "立陶宛", "Lithuania"),
    LU("LU", "卢森堡", "Luxembourg"),
    MT("MT", "马耳他", "Malta"),
    NL("NL", "荷兰", "Holland"),
    AT("AT", "奥地利", "Austria"),
    PL("PL", "波兰", "Poland"),
    PT("PT", "葡萄牙", "Portugal"),
    RO("RO", "罗马尼亚", "Romania"),
    SE("SE", "瑞典", "Sweden"),
    SK("SK", "斯洛伐克", "Slovakia"),
    SI("SI", "斯洛文尼亚", "Slovenia"),
    ES("ES", "西班牙", "Spain"),
    CZ("CZ", "捷克", "Czech"),
    HU("HU", "匈牙利", "Hungary"),
    CY("CY", "塞浦路斯", "Cyprus"),
    BE("BE", "比利时", "Belgium"),
    ;

    companion object {
        val allKey: Set<String> = entries.map { it.key.lowercase(getDefault()) }.toSet()
        val allLongKey: Set<String> = entries.map { it.enName.lowercase(getDefault()) }.toSet()

        fun needIoss(country: String?): Boolean {
            if (country == null) return false
            val country = country.lowercase(getDefault())
            return country in allKey || country in allLongKey
        }
    }
}
