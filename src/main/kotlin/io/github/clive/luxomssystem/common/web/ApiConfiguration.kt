package io.github.clive.luxomssystem.common.web

import okhttp3.ConnectionPool
import okhttp3.Dispatcher
import okhttp3.OkHttpClient
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import java.util.concurrent.Executors
import java.util.concurrent.TimeUnit

@Configuration
class ApiConfiguration {
    @Bean
    fun okhttpClient(): OkHttpClient {
        val dispatcher = Dispatcher(Executors.newVirtualThreadPerTaskExecutor())
        dispatcher.maxRequests = 10000
        dispatcher.maxRequestsPerHost = 10000
        return OkHttpClient
            .Builder()
            .dispatcher(dispatcher)
            .connectionPool(ConnectionPool(1000, 5, TimeUnit.MINUTES))
            .connectTimeout(15, TimeUnit.SECONDS)
            .readTimeout(900, TimeUnit.SECONDS)
            .writeTimeout(900, TimeUnit.SECONDS)
            .build()
    }
}
