package io.github.clive.luxomssystem.common.utils

import com.itextpdf.text.Document
import com.itextpdf.text.pdf.PdfCopy
import com.itextpdf.text.pdf.PdfReader
import io.github.clive.luxomssystem.infrastructure.channel.cos.CosInnerRemoteService
import io.github.oshai.kotlinlogging.KotlinLogging
import kotlinx.coroutines.*
import okhttp3.Request
import org.springframework.stereotype.Component
import java.io.ByteArrayInputStream
import java.io.ByteArrayOutputStream

@Component
class PDFMerger(
    private val okHttpClient: okhttp3.OkHttpClient,
    private val ossClient: CosInnerRemoteService,
) {
    suspend fun mergePDFsAndUpload(
        urls: List<String>,
        bizId: Long,
        key: String,
    ): String =
        coroutineScope {
            val mergedPdfBytes = mergePDFs(urls)
            ossClient.uploadFile(bizId, key, ByteArrayInputStream(mergedPdfBytes))
        }

    private suspend fun mergePDFs(urls: List<String>): ByteArray =
        coroutineScope {
            val outputStream = ByteArrayOutputStream()
            val document = Document()
            val copy = PdfCopy(document, outputStream)
            document.open()

            val deferredReaders =
                urls.map { url ->
                    async(Dispatchers.IO) {
                        try {
                            val pdfBytes = downloadPdf(url)
                            if (isPdfValid(pdfBytes)) {
                                PdfReader(pdfBytes)
                            } else {
                                logger.warn { "Invalid PDF content downloaded from $url - skipping this file" }
                                throw Exception("Invalid PDF content downloaded from $url")
                            }
                        } catch (e: Exception) {
                            logger.error(e) { "Failed to process PDF from $url - skipping this file" }
                            throw e
                        }
                    }
                }

            val readers = deferredReaders.awaitAll()

            if (readers.isEmpty()) {
                throw IllegalStateException("No valid PDFs were found from the provided URLs")
            }

            readers.forEach { reader ->
                try {
                    copy.addDocument(reader)
                } finally {
                    reader.close()
                }
            }

            document.close()
            logger.info { "PDFs merged successfully. Total valid PDFs: ${readers.size}" }
            outputStream.toByteArray()
        }

    private fun isPdfValid(bytes: ByteArray): Boolean {
        if (bytes.size < 5) return false

        // Check for PDF header signature - should start with %PDF-
        val header = bytes.sliceArray(0..4).toString(Charsets.US_ASCII)
        return header.startsWith("%PDF-")
    }

    private suspend fun downloadPdf(url: String): ByteArray =
        withContext(Dispatchers.IO) {
            val request = Request.Builder().url(url).build()
            val response = okHttpClient.newCall(request).execute()
            if (!response.isSuccessful) {
                val message = "Failed to download PDF from $url, status: ${response.code}"
                logger.warn { message }
                throw Exception(message)
            }

            response.body?.bytes() ?: throw Exception("Empty response body from $url")
        }

    companion object {
        private val logger = KotlinLogging.logger {}
    }
}
