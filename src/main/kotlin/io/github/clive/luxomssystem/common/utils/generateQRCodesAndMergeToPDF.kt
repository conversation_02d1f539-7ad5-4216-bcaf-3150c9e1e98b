package io.github.clive.luxomssystem.common.utils

import com.google.zxing.BarcodeFormat
import com.google.zxing.qrcode.QRCodeWriter
import com.itextpdf.text.Document
import com.itextpdf.text.Image
import com.itextpdf.text.PageSize
import com.itextpdf.text.pdf.PdfWriter
import java.awt.Color
import java.awt.Font
import java.awt.RenderingHints
import java.awt.image.BufferedImage
import java.io.ByteArrayOutputStream
import javax.imageio.ImageIO

fun generateQRCodesAndMergeToPDF(
    contentList: List<Pair<String, String>>,
    qrSize: Int = 300,
): ByteArray {
    val outputStream = ByteArrayOutputStream()
    val document = Document(PageSize.A4, 50f, 50f, 50f, 50f)
    PdfWriter.getInstance(document, outputStream)
    document.open()

    val pageWidth = document.pageSize.width - document.leftMargin() - document.rightMargin()
    val pageHeight = document.pageSize.height - document.topMargin() - document.bottomMargin()

    for ((content, label) in contentList) {
        val qrCodeImage = generateQRCodeImage(content, qrSize, label)
        val image = Image.getInstance(qrCodeImage)

        val widthRatio = pageWidth / image.width
        val heightRatio = pageHeight / image.height
        val ratio = minOf(widthRatio, heightRatio, 1f)

        image.scaleAbsolute(image.width * ratio, image.height * ratio)
        image.setAbsolutePosition(
            (pageWidth - image.scaledWidth) / 2 + document.leftMargin(),
            (pageHeight - image.scaledHeight) / 2 + document.bottomMargin(),
        )

        document.add(image)
        document.newPage()
    }

    document.close()
    return outputStream.toByteArray()
}

fun generateQRCodeImage(
    content: String,
    size: Int,
    label: String,
): ByteArray {
    val qrCodeWriter = QRCodeWriter()
    val bitMatrix = qrCodeWriter.encode(content, BarcodeFormat.QR_CODE, size, size)

    // 设置字体
    val font = Font("Arial", Font.BOLD, 24) // 稍微减小字体大小以适应更多行

    // 计算label所需的行数和高度
    val labelLines = splitLabel(label, 16)
    val fontMetrics = BufferedImage(1, 1, BufferedImage.TYPE_INT_ARGB).createGraphics().getFontMetrics(font)
    val labelHeight = (labelLines.size * fontMetrics.height) + 20 // 额外的20像素作为底部边距

    val bufferedImage = BufferedImage(size, size + labelHeight, BufferedImage.TYPE_INT_RGB)
    val graphics = bufferedImage.createGraphics()

    // 设置抗锯齿
    graphics.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON)

    // 绘制白色背景
    graphics.color = Color.WHITE
    graphics.fillRect(0, 0, size, size + labelHeight)

    // 绘制二维码
    graphics.color = Color.BLACK
    for (x in 0 until size) {
        for (y in 0 until size) {
            if (bitMatrix[x, y]) {
                graphics.fillRect(x, y, 1, 1)
            }
        }
    }

    // 添加文字
    graphics.font = font
    labelLines.forEachIndexed { index, line ->
        val labelWidth = fontMetrics.stringWidth(line)
        val x = (size - labelWidth) / 2
        val y = size + (index + 1) * fontMetrics.height
        graphics.drawString(line, x, y)
    }

    graphics.dispose()

    val baos = ByteArrayOutputStream()
    ImageIO.write(bufferedImage, "png", baos)
    return baos.toByteArray()
}

private fun splitLabel(
    label: String,
    maxCharsPerLine: Int,
): List<String> = label.chunked(maxCharsPerLine)

// fun main() {
//    val contentList = listOf(
//        "MEG24041620805" to "This is a very long label that will be split into 16-character lines",
//        "MEG24041620806" to "Another long label to demonstrate multi-line functionality",
//        "MEG24041620807" to "Short label"
//    )
//    val outputPath = "qrcodes_with_16char_labels.pdf"
//
//    generateQRCodesAndMergeToPDF(contentList, outputPath)
//    println("PDF with QR codes and 16-character label lines created successfully!")
// }
