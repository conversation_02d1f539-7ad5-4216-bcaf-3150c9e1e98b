package io.github.clive.luxomssystem.common.ext

@JvmInline
value class DataSize(
    val size: Long,
) : Comparable<DataSize> {
    override fun compareTo(other: DataSize): Int = size.compareTo(other.size)

    override fun toString(): String = "$size B"

    fun humanReadable(): String {
        // 超过1024TiB的数据，不再进行转换，否则计算0-1024对应单位的值，并保留2位小数
        return when {
            size >= 1024L * 1024 * 1024 * 1024 -> "${String.format("%.2f", size / 1024.0 / 1024 / 1024 / 1024)} TiB"
            size >= 1024L * 1024 * 1024 -> "${String.format("%.2f", size / 1024.0 / 1024 / 1024)} GiB"
            size >= 1024L * 1024 -> "${String.format("%.2f", size / 1024.0 / 1024)} MiB"
            size >= 1024L -> "${String.format("%.2f", size / 1024.0)} KiB"
            else -> "$size B"
        }
    }
}

val Long.TiB get() = DataSize(this * 1024 * 1024 * 1024 * 1024)

val Long.GiB get() = DataSize(this * 1024 * 1024 * 1024)

val Long.MiB get() = DataSize(this * 1024 * 1024)

val Long.KiB get() = DataSize(this * 1024)

val Long.B get() = DataSize(this)

val Int.TiB get() = toLong().TiB

val Int.GiB get() = toLong().GiB

val Int.MiB get() = toLong().MiB

val Int.KiB get() = toLong().KiB

val Int.B get() = toLong().B

val Double.TiB get() = DataSize((this * 1024 * 1024 * 1024 * 1024).toLong())

val Double.GiB get() = DataSize((this * 1024 * 1024 * 1024).toLong())

val Double.MiB get() = DataSize((this * 1024 * 1024).toLong())

val Double.KiB get() = DataSize((this * 1024).toLong())

val Double.B get() = DataSize(this.toLong())
