package io.github.clive.luxomssystem.common.exception

import org.springframework.http.HttpStatus

data class Error<PERSON><PERSON>ult(
    val code: String,
    val msg: String,
    val data: Any? = null,
) {
    companion object {
        fun of(
            error: CognitionWebException,
            data: Any? = null,
        ) = ErrorResult(error.code(), error.msg(), data)

        fun of(
            httpStatus: HttpStatus,
            msg: String,
            data: Any? = null,
        ) = ErrorResult(httpStatus.value().toString(), msg, data)
    }
}
