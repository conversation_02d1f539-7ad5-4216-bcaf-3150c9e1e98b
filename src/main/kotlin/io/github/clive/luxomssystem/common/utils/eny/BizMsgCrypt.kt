package io.github.clive.luxomssystem.common.utils.eny

import io.github.clive.luxomssystem.common.utils.eny.AesException
import org.apache.commons.codec.binary.Base64
import java.nio.charset.Charset
import java.util.*
import javax.crypto.Cipher
import javax.crypto.spec.IvParameterSpec
import javax.crypto.spec.SecretKeySpec

/**
 * 提供消息的加解密接口(UTF8编码的字符串).
 */
class BizMsgCrypt(
    token: String,
    encodingAesKey: String,
    appKey: String,
) {
    var base64: Base64 = Base64()
    var aesKey: ByteArray
    var token: String
    var appKey: String

    /**
     * 构造函数
     * @param token 获取的access token
     * @param encodingAesKey 创建应用生成的AESKey
     * @param appKey 应用的appKey
     *
     * @throws AesException 执行失败，请查看该异常的错误码和具体的错误信息
     */
    init {
        if (encodingAesKey.length != 43) {
            throw AesException(AesException.IllegalAesKey)
        }

        this.token = token
        this.appKey = appKey
        aesKey = Base64.decodeBase64("$encodingAesKey=")
    }

    // 生成4个字节的网络字节序
    fun getNetworkBytesOrder(sourceNumber: Int): ByteArray {
        val orderBytes = ByteArray(4)
        orderBytes[3] = (sourceNumber and 0xFF).toByte()
        orderBytes[2] = (sourceNumber shr 8 and 0xFF).toByte()
        orderBytes[1] = (sourceNumber shr 16 and 0xFF).toByte()
        orderBytes[0] = (sourceNumber shr 24 and 0xFF).toByte()
        return orderBytes
    }

    // 还原4个字节的网络字节序
    fun recoverNetworkBytesOrder(orderBytes: ByteArray): Int {
        var sourceNumber = 0
        for (i in 0..3) {
            sourceNumber = sourceNumber shl 8
            sourceNumber = sourceNumber or (orderBytes[i].toInt() and 0xff)
        }
        return sourceNumber
    }

    val randomStr: String
        // 随机生成16位字符串
        get() {
            val base = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789"
            val random = Random()
            val sb = StringBuffer()
            for (i in 0..15) {
                val number = random.nextInt(base.length)
                sb.append(base[number])
            }
            return sb.toString()
        }

    /**
     * 对明文进行加密.
     *
     * @param text 需要加密的明文
     * @return 加密后base64编码的字符串
     * @throws AesException aes加密失败
     */
    @Throws(AesException::class)
    fun encrypt(
        randomStr: String,
        text: String,
    ): String {
        val byteCollector: ByteGroup = ByteGroup()
        val randomStrBytes = randomStr.toByteArray(CHARSET)
        val textBytes = text.toByteArray(CHARSET)
        val networkBytesOrder = getNetworkBytesOrder(textBytes.size)
        val appidBytes = appKey.toByteArray(CHARSET)

        // randomStr + networkBytesOrder + text + appid
        byteCollector.addBytes(randomStrBytes)
        byteCollector.addBytes(networkBytesOrder)
        byteCollector.addBytes(textBytes)
        byteCollector.addBytes(appidBytes)

        // ... + pad: 使用自定义的填充方式对明文进行补位填充
        val padBytes: ByteArray = PKCS7Encoder.encode(byteCollector.size())
        byteCollector.addBytes(padBytes)

        // 获得最终的字节流, 未加密
        val unencrypted: ByteArray = byteCollector.toBytes()
        // System.out.println(new String(unencrypted));
        try {
            // 设置加密模式为AES的CBC模式
            val cipher = Cipher.getInstance("AES/CBC/NoPadding")
            val keySpec = SecretKeySpec(aesKey, "AES")
            val iv = IvParameterSpec(aesKey, 0, 16)
            cipher.init(Cipher.ENCRYPT_MODE, keySpec, iv)

            // 加密
            val encrypted = cipher.doFinal(unencrypted)

            // 使用BASE64对加密后的字符串进行编码
            val base64Encrypted = base64.encodeToString(encrypted)

            return base64Encrypted
        } catch (e: Exception) {
            e.printStackTrace()
            throw AesException(AesException.EncryptAESError)
        }
    }

    /**
     * 对密文进行解密.
     *
     * @param text 需要解密的密文
     * @return 解密得到的明文
     * @throws AesException aes解密失败
     */
    @Throws(AesException::class)
    fun decrypt(text: String?): String {
        val original: ByteArray
        try {
            // 设置解密模式为AES的CBC模式
            val cipher = Cipher.getInstance("AES/CBC/NoPadding")
            val key_spec = SecretKeySpec(aesKey, "AES")
            val iv = IvParameterSpec(Arrays.copyOfRange(aesKey, 0, 16))
            cipher.init(Cipher.DECRYPT_MODE, key_spec, iv)

            // 使用BASE64对密文进行解码
            val encrypted = Base64.decodeBase64(text)

            // 解密
            original = cipher.doFinal(encrypted)
        } catch (e: Exception) {
            e.printStackTrace()
            throw AesException(AesException.DecryptAESError)
        }

        val content: String
        val fromAppKey: String
        try {
            // 去除补位字符
            val bytes: ByteArray = PKCS7Encoder.decode(original)

            // 分离16位随机字符串,网络字节序和AppId
            val networkOrder = Arrays.copyOfRange(bytes, 16, 20)

            val xmlLength = recoverNetworkBytesOrder(networkOrder)

            content = String(Arrays.copyOfRange(bytes, 20, 20 + xmlLength), CHARSET)
            fromAppKey =
                String(
                    Arrays.copyOfRange(bytes, 20 + xmlLength, bytes.size),
                    CHARSET,
                )
        } catch (e: Exception) {
            e.printStackTrace()
            throw AesException(AesException.IllegalBuffer)
        }

        // appid不相同的情况
        if (fromAppKey != appKey) {
            throw AesException(AesException.ValidateAppidError)
        }
        return content
    }

    /**
     * 将消息加密打包.
     *
     *  1. 对要发送的消息进行AES-CBC加密
     *  1. 生成安全签名
     *  1. 将消息密文和安全签名打包成对象
     *
     *
     * @param replyMsg 请求报文
     * @param timeStamp 时间戳
     * @param nonce 随机串
     *
     * @return 加密后的密文，包括signature, encrypt的Param对象
     * @throws AesException 执行失败，请查看该异常的错误码和具体的错误信息
     */
    @Throws(AesException::class)
    fun encryptMsg(
        replyMsg: String,
        timeStamp: String,
        nonce: String,
    ): Param {
        // 加密
        var newTimeStamp = timeStamp
        val encrypt = encrypt(randomStr, replyMsg)

        // 生成安全签名
        if (newTimeStamp === "") {
            newTimeStamp = System.currentTimeMillis().toString()
        }

        val signature: String = SHA1.getSHA2(token, newTimeStamp, nonce, encrypt)

        return Param(encrypt, signature)
    }

    /**
     * 获取解密后的明文.
     *
     *  1. 对消息进行解密
     *
     * @param postData 密文
     *
     * @return 解密后的原文
     * @throws AesException 执行失败，请查看该异常的错误码和具体的错误信息
     */
    @Throws(AesException::class)
    fun decryptMsg(postData: String?): String {
        // 解密
        val result = decrypt(postData)
        return result
    }

    companion object {
        var CHARSET: Charset = Charset.forName("utf-8")
    }
}
