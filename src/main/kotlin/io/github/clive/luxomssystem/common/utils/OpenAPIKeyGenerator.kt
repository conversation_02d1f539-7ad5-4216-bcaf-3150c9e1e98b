package io.github.clive.luxomssystem.common.utils

import java.security.SecureRandom
import java.util.*

object OpenAPIKeyGenerator {
    private const val DEFAULT_PREFIX = "sk-"
    private const val DEFAULT_BYTE_LENGTH = 36 // 生成 48 个 Base64 字符 (36 * 4/3 = 48)
    private val secureRandom = SecureRandom()
    private val encoder = Base64.getUrlEncoder().withoutPadding() // 使用 URL 安全的 Base64 编码

    /**
     * 生成一个带有指定前缀的 API Key。
     *
     * @param prefix Key 的前缀，默认为 "sk-"
     * @param byteLength 用于生成随机部分的字节数组长度，默认为 36，这将产生大约 48 个 Base64 字符。
     * @return 生成的 API Key 字符串
     */
    fun generateKey(
        prefix: String = DEFAULT_PREFIX,
        byteLength: Int = DEFAULT_BYTE_LENGTH,
    ): String {
        val randomBytes = ByteArray(byteLength)
        secureRandom.nextBytes(randomBytes)
        val randomPart = encoder.encodeToString(randomBytes)
        return prefix + randomPart
    }
}
