package io.github.clive.luxomssystem.common.utils

import com.auth0.jwt.JWT
import com.auth0.jwt.algorithms.Algorithm
import com.auth0.jwt.exceptions.JWTDecodeException
import com.auth0.jwt.exceptions.JWTVerificationException
import com.auth0.jwt.interfaces.Claim
import io.github.oshai.kotlinlogging.KotlinLogging
import java.util.*

object Jwt {
    fun createToken(userId: Long): String =
        JWT
            .create()
            .withIssuer("cognition-bank")
            .withSubject(userId.toString())
            .withClaim("userId", userId)
            .withIssuedAt(Date())
            .sign(algorithm)

    private fun validateToken(authToken: String?): Boolean {
        val verifier = JWT.require(algorithm).build()
        try {
            verifier.verify(authToken)
            return true
        } catch (e: JWTVerificationException) {
            log.warn("Invalid signature/claims :: {}", e.message)
        }
        return false
    }

    fun extractUserFromJwt(token: String): Map<String, Claim> {
        val removePrefixToken = if (token.startsWith("Bearer ")) token.substring(7) else token
        val verificationSucceeded = validateToken(removePrefixToken)
        if (!verificationSucceeded) {
            throw Exception("Token was unauthorized")
        }
        try {
            val jwt = JWT.decode(removePrefixToken)
            return jwt.claims
        } catch (e: JWTDecodeException) {
            log.error("Failed to decode token", e)
            throw e
        }
    }

    private var algorithm: Algorithm = Algorithm.HMAC256("tzw213131")

    private val log = KotlinLogging.logger {}
}
