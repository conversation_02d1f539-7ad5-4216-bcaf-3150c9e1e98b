

### 选择对应的折扣

spu 有对应的折扣件数 满足了 后面的sku的价格就会变成价格减去折扣的价格

一个面单可能有好几种spu，有两个匹配逻辑:
1. 可以是单个spu数量满足折扣条件 然后进行折扣
2. 如果单个spu数量不满足折扣条件，那么就需要多个spu组合满足折扣条件，综合所有数量，然后去命中对应的spu的折扣，根据折扣计算出最优惠的价格，选择最优惠的价格
    



    例子1: 
    一个面单有两个spu,
    spu1: 价格10元 第二件开始折扣5元
    spu2: 价格20元 第3件开始折扣8元
    现在有一个面单，spu1有2件，spu2有两件，那么这个面单的价格是多少？
    应该有1种命中逻辑,只要满足了一个spu的，不需要综合所有spu的数量：
    - spu1命中逻辑了， 10 +10-5+20+20 = 55


    例子2： 一个面单有两个spu,
    spu1: 价格10元 第二件开始折扣5元
    spu2: 价格20元 第3件开始折扣8元
    现在有一个面单，spu1有1件，spu2有两件，那么这个面单的价格是多少？
    应该有两种命中逻辑,1+2是三件：
    - 命中spu1,spu1的价格是10元，第二件开始折扣5元，所以价格是10+20-5+20-5=40元
    - 命中spu2,spu2的价格是20元，第3件开始折扣10元，所以价格是20+20+10-8=42元
    所以应该选择第一种逻辑，价格是40元

